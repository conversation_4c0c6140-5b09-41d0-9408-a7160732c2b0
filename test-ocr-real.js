const Tesseract = require('tesseract.js');
const fs = require('fs');
const path = require('path');

/**
 * 真实OCR测试脚本
 * 使用Tesseract.js对测试图片进行真实OCR识别，验证OCR功能的真实性
 */

// 测试图片文件夹路径
const TEST_IMAGES_DIR = path.join(__dirname, 'mobile/src/assets/test-images/generated');

/**
 * 获取测试图片列表
 */
function getTestImages() {
  try {
    if (!fs.existsSync(TEST_IMAGES_DIR)) {
      console.error('❌ 测试图片文件夹不存在:', TEST_IMAGES_DIR);
      return [];
    }

    const files = fs.readdirSync(TEST_IMAGES_DIR);
    const imageFiles = files.filter(file => {
      const ext = file.toLowerCase();
      return ext.endsWith('.jpg') || ext.endsWith('.jpeg') || 
             ext.endsWith('.png') || ext.endsWith('.webp');
    });

    console.log(`📁 找到 ${imageFiles.length} 张测试图片`);
    return imageFiles.map(file => ({
      name: file,
      path: path.join(TEST_IMAGES_DIR, file),
      size: fs.statSync(path.join(TEST_IMAGES_DIR, file)).size
    }));
  } catch (error) {
    console.error('❌ 读取测试图片失败:', error);
    return [];
  }
}

/**
 * 使用Tesseract.js进行真实OCR识别
 */
async function performRealOCR(imagePath, imageName) {
  try {
    console.log(`🔍 开始识别图片: ${imageName}`);
    const startTime = Date.now();

    // 使用Tesseract.js进行OCR识别
    const { data } = await Tesseract.recognize(
      imagePath,
      'chi_sim+eng', // 中文简体 + 英文
      {
        logger: m => {
          if (m.status === 'recognizing text') {
            process.stdout.write(`\r   进度: ${(m.progress * 100).toFixed(1)}%`);
          }
        }
      }
    );

    const duration = Date.now() - startTime;
    console.log(`\n✅ 识别完成 (${duration}ms)`);

    // 安全地获取文本内容
    const text = (data && data.text) ? data.text.trim() : '';
    const confidence = (data && typeof data.confidence === 'number') ? data.confidence : 0;
    const blocks = (data && data.blocks) ? data.blocks.length : 0;
    const words = (data && data.words) ? data.words.length : 0;

    return {
      success: true,
      text,
      confidence,
      duration,
      blocks,
      words,
      engine: 'tesseract.js'
    };

  } catch (error) {
    console.log(`\n❌ 识别失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      duration: 0,
      text: '',
      confidence: 0
    };
  }
}

/**
 * 分析OCR结果
 */
function analyzeResults(results) {
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  const totalText = successful.reduce((sum, r) => sum + r.text.length, 0);
  const avgConfidence = successful.length > 0 
    ? successful.reduce((sum, r) => sum + r.confidence, 0) / successful.length 
    : 0;
  const avgDuration = successful.length > 0
    ? successful.reduce((sum, r) => sum + r.duration, 0) / successful.length
    : 0;

  return {
    total: results.length,
    successful: successful.length,
    failed: failed.length,
    successRate: (successful.length / results.length * 100).toFixed(1),
    totalTextLength: totalText,
    avgConfidence: avgConfidence.toFixed(1),
    avgDuration: Math.round(avgDuration)
  };
}

/**
 * 验证OCR结果的真实性
 */
function verifyOCRAuthenticity(results) {
  console.log('\n🔍 验证OCR结果真实性:');
  
  let realOCRCount = 0;
  let suspiciousCount = 0;
  
  results.forEach(result => {
    if (!result.success) return;
    
    const { imageName, text } = result;
    
    // 检查是否是基于文件名生成的模拟文本
    const isSuspicious = (
      // 检查是否包含与文件名相关的关键词
      (imageName.includes('wechat') && text.includes('微信')) ||
      (imageName.includes('meeting') && text.includes('会议')) ||
      (imageName.includes('work_plan') && text.includes('工作计划')) ||
      (imageName.includes('official') && text.includes('政务'))
    );
    
    if (isSuspicious) {
      console.log(`⚠️  ${imageName}: 可能是模拟文本（包含文件名相关关键词）`);
      suspiciousCount++;
    } else {
      console.log(`✅ ${imageName}: 真实OCR识别`);
      realOCRCount++;
    }
  });
  
  return {
    realOCRCount,
    suspiciousCount,
    authenticity: realOCRCount / (realOCRCount + suspiciousCount) * 100
  };
}

/**
 * 主测试函数
 */
async function runOCRTest() {
  console.log('🚀 开始真实OCR测试\n');
  
  // 获取测试图片
  const testImages = getTestImages();
  if (testImages.length === 0) {
    console.log('❌ 没有找到测试图片，测试结束');
    return;
  }

  console.log(`📋 将测试 ${testImages.length} 张图片\n`);

  // 执行OCR测试
  const results = [];
  for (let i = 0; i < testImages.length; i++) {
    const image = testImages[i];
    console.log(`\n[${i + 1}/${testImages.length}] ${image.name} (${Math.round(image.size / 1024)}KB)`);
    
    const result = await performRealOCR(image.path, image.name);
    results.push({
      imageName: image.name,
      imageSize: image.size,
      ...result
    });

    // 显示识别的文本片段
    if (result.success && result.text) {
      const preview = result.text.substring(0, 100).replace(/\n/g, ' ');
      console.log(`   文本预览: "${preview}${result.text.length > 100 ? '...' : ''}"`);
      console.log(`   置信度: ${result.confidence.toFixed(1)}% | 文本长度: ${result.text.length} 字符`);
    }
  }

  // 分析结果
  console.log('\n' + '='.repeat(60));
  console.log('📊 测试结果统计:');
  console.log('='.repeat(60));
  
  const stats = analyzeResults(results);
  console.log(`总图片数: ${stats.total}`);
  console.log(`成功识别: ${stats.successful} (${stats.successRate}%)`);
  console.log(`识别失败: ${stats.failed}`);
  console.log(`平均置信度: ${stats.avgConfidence}%`);
  console.log(`总文字长度: ${stats.totalTextLength} 字符`);
  console.log(`平均耗时: ${stats.avgDuration}ms`);

  // 验证真实性
  const authenticity = verifyOCRAuthenticity(results.filter(r => r.success));
  console.log(`\n🎯 真实性验证:`);
  console.log(`真实OCR: ${authenticity.realOCRCount} 张`);
  console.log(`可疑结果: ${authenticity.suspiciousCount} 张`);
  console.log(`真实性评分: ${authenticity.authenticity.toFixed(1)}%`);

  // 保存详细结果
  const reportPath = path.join(__dirname, 'ocr-test-report.json');
  const report = {
    timestamp: new Date().toISOString(),
    testImages: testImages.length,
    results,
    statistics: stats,
    authenticity
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);

  // 结论
  console.log('\n' + '='.repeat(60));
  if (stats.successful > 0 && authenticity.authenticity > 80) {
    console.log('🎉 结论: OCR功能真实有效！');
    console.log('   - 成功识别了真实图片中的文字内容');
    console.log('   - 识别结果不是基于文件名的模拟数据');
    console.log('   - 证明了OCR引擎的真实性和有效性');
  } else if (stats.successful > 0) {
    console.log('⚠️  结论: OCR功能部分有效，但存在可疑结果');
    console.log('   - 部分识别结果可能是模拟数据');
    console.log('   - 需要进一步验证OCR实现的真实性');
  } else {
    console.log('❌ 结论: OCR功能无效');
    console.log('   - 无法识别任何图片中的文字');
    console.log('   - OCR引擎可能存在问题');
  }
  console.log('='.repeat(60));
}

// 运行测试
if (require.main === module) {
  runOCRTest().catch(error => {
    console.error('❌ 测试过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = { runOCRTest, performRealOCR }; 