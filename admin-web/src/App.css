.App {
  text-align: left;
  min-height: 100vh;
  background: #f5f5f5;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.App-header h1 {
  margin: 0;
  font-size: 1.8em;
}

.user-info {
  display: flex;
  gap: 20px;
}

.user-info span {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 5px;
  transition: background-color 0.3s;
}

.user-info span:hover {
  background-color: rgba(255,255,255,0.2);
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.2em;
  color: #666;
}

.dashboard {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border-left: 4px solid #667eea;
}

.stat-card h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.1em;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-value {
  font-weight: bold;
  color: #667eea;
  font-size: 1.1em;
}

.status-healthy {
  color: #28a745;
  font-weight: bold;
}

.status-warning {
  color: #ffc107;
  font-weight: bold;
}

.status-section {
  margin-top: 30px;
}

.status-card {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border-left: 4px solid #28a745;
}

.status-card h3 {
  margin: 0 0 15px 0;
  color: #28a745;
}

.status-card p {
  margin: 8px 0;
  color: #666;
  font-family: 'Courier New', monospace;
}

@media (max-width: 768px) {
  .App-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .user-info {
    gap: 10px;
  }
  
  .dashboard {
    padding: 20px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
