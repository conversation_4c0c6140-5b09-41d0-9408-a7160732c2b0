import React, { useEffect, useState } from 'react';
import './App.css';

interface DashboardData {
  deviceStats: {
    total: number;
    todayNew: number;
    active: number;
  };
  usage: {
    mobileActive: number;
    desktopActive: number;
    documentsProcessed: number;
  };
  systemStatus: {
    api: string;
    database: string;
    ai: string;
    storage: number;
  };
}

function App() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 获取管理后台数据
    fetch('http://localhost:3001/api/admin/dashboard')
      .then(response => response.json())
      .then(data => {
        setDashboardData(data.data);
        setLoading(false);
      })
      .catch(error => {
        console.error('获取数据失败:', error);
        setLoading(false);
      });
  }, []);

  if (loading) {
    return (
      <div className="App">
        <div className="loading">加载中...</div>
      </div>
    );
  }

  return (
    <div className="App">
      <header className="App-header">
        <h1>🐱 公职猫管理后台</h1>
        <div className="user-info">
          <span>👤 管理员</span>
          <span>🔔 通知</span>
          <span>🚪 退出</span>
        </div>
      </header>

      <main className="dashboard">
        <div className="stats-grid">
          <div className="stat-card">
            <h3>📊 设备统计</h3>
            <div className="stat-item">
              <span>👥 总设备数</span>
              <span className="stat-value">{dashboardData?.deviceStats.total} 台</span>
            </div>
            <div className="stat-item">
              <span>📅 今日新增</span>
              <span className="stat-value">{dashboardData?.deviceStats.todayNew} 台</span>
            </div>
            <div className="stat-item">
              <span>🔄 活跃设备</span>
              <span className="stat-value">{dashboardData?.deviceStats.active} 台</span>
            </div>
          </div>

          <div className="stat-card">
            <h3>📈 使用情况</h3>
            <div className="stat-item">
              <span>📱 移动端活跃</span>
              <span className="stat-value">{dashboardData?.usage.mobileActive} 台/日</span>
            </div>
            <div className="stat-item">
              <span>💻 桌面端活跃</span>
              <span className="stat-value">{dashboardData?.usage.desktopActive} 台/日</span>
            </div>
            <div className="stat-item">
              <span>📄 文档处理量</span>
              <span className="stat-value">{dashboardData?.usage.documentsProcessed} 个/日</span>
            </div>
          </div>

          <div className="stat-card">
            <h3>🔧 系统状态</h3>
            <div className="stat-item">
              <span>🟢 API服务</span>
              <span className="status-healthy">{dashboardData?.systemStatus.api}</span>
            </div>
            <div className="stat-item">
              <span>🟢 数据库</span>
              <span className="status-healthy">{dashboardData?.systemStatus.database}</span>
            </div>
            <div className="stat-item">
              <span>🟢 AI服务</span>
              <span className="status-healthy">{dashboardData?.systemStatus.ai}</span>
            </div>
            <div className="stat-item">
              <span>🟡 存储使用率</span>
              <span className="status-warning">{dashboardData?.systemStatus.storage}%</span>
            </div>
          </div>
        </div>

        <div className="status-section">
          <div className="status-card">
            <h3>✅ 环境搭建完成</h3>
            <p>后端API服务器: http://localhost:3001</p>
            <p>管理后台: http://localhost:3002</p>
            <p>移动端: React Native (开发中)</p>
            <p>桌面端: Electron (已启动)</p>
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;
