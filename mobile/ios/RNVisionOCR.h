//
//  RNVisionOCR.h
//  GongZhiMallMobile
//
//  Created by TaskMaster AI on 2025-01-27.
//  Copyright © 2025 GongZhiMall. All rights reserved.
//

#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>
#import <Vision/Vision.h>
#import <UIKit/UIKit.h>

@interface RNVisionOCR : RCTEventEmitter <RCTBridgeModule>

/**
 * 检查Vision框架可用性
 */
+ (BOOL)isVisionFrameworkAvailable;

/**
 * 获取Vision框架版本信息
 */
+ (NSDictionary *)getVisionFrameworkInfo;

/**
 * 初始化Vision框架
 */
- (void)initializeVisionFramework:(RCTPromiseResolveBlock)resolve
                         rejecter:(RCTPromiseRejectBlock)reject;

/**
 * 检查OCR能力
 */
- (void)checkOCRCapability:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject;

/**
 * 识别图片中的文本
 */
- (void)recognizeText:(NSString *)imagePath
             resolver:(RCTPromiseResolveBlock)resolve
             rejecter:(RCTPromiseRejectBlock)reject;

@end