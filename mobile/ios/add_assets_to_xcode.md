# iOS Assets 配置说明

## 问题
为了让OFD预览功能在iOS上正常工作，需要将assets文件添加到Xcode项目中。

## 解决方案

### 方法1: 手动添加到Xcode (推荐)

1. 打开 `mobile/ios/GongZhiMallMobile.xcworkspace`
2. 在项目导航器中，右键点击 `GongZhiMallMobile` 文件夹
3. 选择 "Add Files to 'GongZhiMallMobile'"
4. 导航到 `mobile/ios/gongzhimallmobile/assets` 文件夹
5. 选择整个 `assets` 文件夹
6. 确保选择了以下选项：
   - ✅ "Copy items if needed"
   - ✅ "Create groups"
   - ✅ Target: GongZhiMallMobile
7. 点击 "Add"

### 方法2: 修改项目文件 (自动化)

如果需要自动化，可以修改 `project.pbxproj` 文件，但手动添加更安全。

## 验证

添加后，在Xcode项目导航器中应该能看到：
```
GongZhiMallMobile/
  ├── assets/
  │   ├── ofd-viewer.html
  │   └── libs/
  │       └── ofd.js
```

## 文件说明

- `ofd-viewer.html`: OFD文档预览的HTML模板
- `libs/ofd.js`: OFD文档解析库

这些文件已经配置了跨平台的动态加载机制，支持Android和iOS。
