# iOS微信SDK接入检查清单

## 当前状态检查

### ✅ 已完成的项目

1. **SDK依赖安装**
   - [x] 在Podfile中添加`pod 'WechatOpenSDK-XCFramework'`
   - [x] 运行`pod install`成功安装
   - [x] 确认SDK版本为2.0.4（最新版本）

2. **原生模块实现**
   - [x] 创建`CustomWeChatSDK.h`头文件
   - [x] 创建`CustomWeChatSDK.m`实现文件
   - [x] 实现所有必要的微信SDK方法
   - [x] 添加WXApiDelegate协议支持

3. **AppDelegate配置**
   - [x] 导入WechatOpenSDK框架
   - [x] 添加URL回调处理方法
   - [x] 添加Universal Link回调处理方法

4. **React Native层配置**
   - [x] 检查SafeWeChatSDK.ts实现
   - [x] 确认配置文件中包含微信开放平台配置
   - [x] 验证wechatBindingConfig.ts配置

### 🔧 需要手动完成的项目

5. **Xcode项目配置**
   - [ ] 将CustomWeChatSDK.h和CustomWeChatSDK.m添加到Xcode项目
   - [ ] 配置Associated Domains
   - [ ] 添加URL Schemes

6. **Universal Links配置**
   - [ ] 创建apple-app-site-association文件
   - [ ] 配置服务器端Universal Links
   - [ ] 测试Universal Links功能

7. **微信开放平台配置**
   - [ ] 注册移动应用
   - [ ] 获取AppID和AppSecret
   - [ ] 配置Universal Links

8. **环境变量配置**
   - [ ] 设置WECHAT_APP_ID
   - [ ] 设置WECHAT_APP_SECRET
   - [ ] 配置Universal Link地址

## 详细操作步骤

### 步骤1：添加文件到Xcode项目

1. 打开Xcode项目：`GongZhiMallMobile.xcworkspace`
2. 右键点击`GongZhiMallMobile`文件夹
3. 选择"Add Files to 'GongZhiMallMobile'"
4. 添加以下文件：
   - `CustomWeChatSDK.h`
   - `CustomWeChatSDK.m`
5. 确保"Add to target"中选中了`GongZhiMallMobile`

### 步骤2：配置Associated Domains

1. 在Xcode中选择项目Target
2. 点击"Signing & Capabilities"标签
3. 点击"+"按钮，添加"Associated Domains"
4. 添加您的Universal Links域名：`applinks:your-domain.com`

### 步骤3：配置URL Schemes

1. 在Xcode中打开`Info.plist`
2. 添加`URL types`（如果不存在）
3. 添加您的微信AppID作为URL Scheme

### 步骤4：创建apple-app-site-association文件

在您的服务器上创建文件：`https://your-domain.com/.well-known/apple-app-site-association`

```json
{
  "applinks": {
    "apps": [],
    "details": [
      {
        "appID": "YOUR_TEAM_ID.com.gongzhimall.mobile",
        "paths": ["/wechat/*"]
      }
    ]
  }
}
```

### 步骤5：微信开放平台配置

1. 访问[微信开放平台](https://open.weixin.qq.com/)
2. 创建移动应用
3. 获取AppID和AppSecret
4. 配置iOS应用的Universal Links：`https://your-domain.com/wechat/*`

### 步骤6：更新环境变量

在`.env`文件中添加：

```env
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
```

### 步骤7：测试配置

1. **测试Universal Links**
   ```bash
   # 在Safari中输入
   https://your-domain.com/wechat/test
   ```

2. **测试微信SDK注册**
   - 运行应用
   - 检查控制台日志
   - 确认没有"自定义微信模块未找到"错误

3. **测试微信功能**
   - 测试微信登录
   - 测试分享功能
   - 测试客服功能

## 验证命令

### 检查SDK安装
```bash
cd mobile/ios
pod list | grep WechatOpenSDK
```

### 检查项目文件
```bash
ls -la GongZhiMallMobile/CustomWeChatSDK.*
```

### 检查Podfile
```bash
grep -n "WechatOpenSDK" Podfile
```

## 常见问题解决

### 问题1：SDK注册失败
**错误信息：** `自定义微信模块未找到`
**解决方案：**
1. 确认CustomWeChatSDK文件已添加到Xcode项目
2. 检查文件是否正确编译
3. 确认AppID配置正确

### 问题2：Universal Links不生效
**解决方案：**
1. 检查apple-app-site-association文件格式
2. 确认域名支持HTTPS
3. 检查Associated Domains配置
4. 清除浏览器缓存重新测试

### 问题3：微信回调无法处理
**解决方案：**
1. 确认AppDelegate中的回调方法已实现
2. 检查URL Schemes配置
3. 确认微信开放平台配置正确

## 完成检查

完成所有步骤后，请运行以下命令验证：

```bash
cd mobile
yarn ios
```

检查控制台输出，确认：
- 没有"自定义微信模块未找到"错误
- 微信SDK注册成功
- Universal Links正常工作

## 下一步

完成iOS配置后，建议：
1. 在真机上完整测试所有功能
2. 提交微信开放平台审核
3. 配置生产环境Universal Links
4. 添加错误监控和日志记录 