# 添加CustomWeChatSDK到Xcode项目

## 问题描述
iOS端微信SDK初始化失败，错误信息：`自定义微信模块未找到，请检查原生模块是否正确注册`

## 解决方案
需要在Xcode项目中添加CustomWeChatSDK原生模块文件。

## 操作步骤

### 1. 打开Xcode项目
```bash
cd mobile/ios
open GongZhiMallMobile.xcworkspace
```

### 2. 添加文件到项目
1. 在Xcode中，右键点击`GongZhiMallMobile`文件夹
2. 选择"Add Files to 'GongZhiMallMobile'"
3. 选择以下两个文件：
   - `GongZhiMallMobile/CustomWeChatSDK.h`
   - `GongZhiMallMobile/CustomWeChatSDK.m`
4. 确保"Add to target"中选中了`GongZhiMallMobile`
5. 点击"Add"

### 3. 验证文件添加
添加完成后，在Xcode项目导航器中应该能看到：
- `CustomWeChatSDK.h`
- `CustomWeChatSDK.m`

### 4. 重新构建项目
1. 在Xcode中按`Cmd + B`重新构建项目
2. 或者在终端中运行：
```bash
cd mobile/ios
xcodebuild -workspace GongZhiMallMobile.xcworkspace -scheme GongZhiMallMobile -configuration Debug build
```

### 5. 重新运行React Native应用
```bash
cd mobile
yarn ios
```

## 文件说明

### CustomWeChatSDK.h
- 头文件，定义了CustomWeChatSDK类的接口
- 继承自RCTEventEmitter并实现RCTBridgeModule协议

### CustomWeChatSDK.m
- 实现文件，包含所有微信SDK相关的方法
- 目前是模拟实现，返回成功状态
- 包含以下方法：
  - `registerApp`: 注册微信应用
  - `isWeChatInstalled`: 检查微信是否安装
  - `openCustomerService`: 打开企业微信客服
  - `getWeChatSDKVersion`: 获取SDK版本
  - `isRegistered`: 检查注册状态
  - `getModuleInfo`: 获取模块信息

## 注意事项

1. **模拟实现**：当前实现是模拟的，返回成功状态，实际项目中需要集成真正的微信SDK
2. **微信SDK集成**：要使用真正的微信功能，需要：
   - 在Podfile中添加微信SDK依赖
   - 在Info.plist中添加URL Scheme配置
   - 在AppDelegate中处理微信回调
3. **测试验证**：添加文件后，iOS端应该不再报"自定义微信模块未找到"的错误

## 后续步骤

1. 验证iOS端不再报错
2. 如果需要真正的微信功能，集成微信官方SDK
3. 配置微信应用的AppID和相关设置 