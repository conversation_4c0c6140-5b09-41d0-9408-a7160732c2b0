#import "CustomWeChatSDK.h"
#import <React/RCTLog.h>
#import <React/RCTUtils.h>

@implementation CustomWeChatSDK

RCT_EXPORT_MODULE(CustomWeChatSDK);

- (NSArray<NSString *> *)supportedEvents {
  return @[];
}

/**
 * 注册微信应用
 */
RCT_EXPORT_METHOD(registerApp:(NSDictionary *)config
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    NSString *appId = config[@"appid"];
    if (!appId || [appId length] == 0) {
      reject(@"INVALID_APPID", @"AppID不能为空", nil);
      return;
    }
    
    RCTLogInfo(@"[CustomWeChatSDK] 注册微信应用: %@", appId);
    
    // 由于iOS端暂时没有集成微信SDK，我们返回成功状态
    // 实际项目中需要集成微信SDK并调用相应的注册方法
    NSDictionary *result = @{
      @"success": @YES,
      @"message": @"微信应用注册成功（模拟）"
    };
    
    resolve(result);
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] 注册微信应用异常: %@", exception.reason);
    reject(@"REGISTER_ERROR", [NSString stringWithFormat:@"注册微信应用异常: %@", exception.reason], nil);
  }
}

/**
 * 检查微信是否安装
 */
RCT_EXPORT_METHOD(isWeChatInstalled:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    // 由于iOS端暂时没有集成微信SDK，我们假设微信已安装
    // 实际项目中需要调用微信SDK的检查方法
    BOOL installed = YES;
    resolve(@(installed));
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] 检查微信安装状态异常: %@", exception.reason);
    reject(@"CHECK_ERROR", [NSString stringWithFormat:@"检查微信安装状态异常: %@", exception.reason], nil);
  }
}

/**
 * 打开企业微信客服
 */
RCT_EXPORT_METHOD(openCustomerService:(NSDictionary *)config
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    NSString *corpId = config[@"corpid"];
    NSString *url = config[@"url"];
    
    if (!corpId || [corpId length] == 0) {
      reject(@"INVALID_CORPID", @"企业ID不能为空", nil);
      return;
    }
    
    if (!url || [url length] == 0) {
      reject(@"INVALID_URL", @"客服URL不能为空", nil);
      return;
    }
    
    RCTLogInfo(@"[CustomWeChatSDK] 调用openCustomerService: corpId=%@, url=%@", corpId, url);
    
    // 由于iOS端暂时没有集成微信SDK，我们返回成功状态
    // 实际项目中需要调用微信SDK的客服方法
    NSDictionary *result = @{
      @"success": @YES,
      @"message": @"企业微信客服打开成功（模拟）"
    };
    
    resolve(result);
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] openCustomerService异常: %@", exception.reason);
    reject(@"CUSTOMER_SERVICE_ERROR", [NSString stringWithFormat:@"打开客服异常: %@", exception.reason], nil);
  }
}

/**
 * 获取微信SDK版本
 */
RCT_EXPORT_METHOD(getWeChatSDKVersion:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    // 由于iOS端暂时没有集成微信SDK，我们返回模拟版本
    NSString *version = @"1.0.0 (模拟)";
    resolve(version);
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] 获取SDK版本异常: %@", exception.reason);
    reject(@"VERSION_ERROR", [NSString stringWithFormat:@"获取SDK版本异常: %@", exception.reason], nil);
  }
}

/**
 * 检查注册状态
 */
RCT_EXPORT_METHOD(isRegistered:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    // 由于iOS端暂时没有集成微信SDK，我们返回已注册状态
    BOOL registered = YES;
    resolve(@(registered));
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] 检查注册状态异常: %@", exception.reason);
    reject(@"STATUS_ERROR", [NSString stringWithFormat:@"检查注册状态异常: %@", exception.reason], nil);
  }
}

/**
 * 获取模块信息
 */
RCT_EXPORT_METHOD(getModuleInfo:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    NSDictionary *info = @{
      @"name": @"CustomWeChatSDK",
      @"version": @"1.0.0",
      @"platform": @"iOS",
      @"status": @"模拟模式",
      @"description": @"iOS端微信SDK模块（模拟实现）"
    };
    resolve(info);
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] 获取模块信息异常: %@", exception.reason);
    reject(@"INFO_ERROR", [NSString stringWithFormat:@"获取模块信息异常: %@", exception.reason], nil);
  }
}

@end 