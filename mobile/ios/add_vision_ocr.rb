#!/usr/bin/env ruby

require 'xcodeproj'

# 打开Xcode项目
project_path = 'GongZhiMallMobile.xcodeproj'
project = Xcodeproj::Project.open(project_path)

# 找到主target
target = project.targets.find { |t| t.name == 'GongZhiMallMobile' }

if target.nil?
  puts "Error: Could not find target 'GongZhiMallMobile'"
  exit 1
end

# 找到主group
main_group = project.main_group.find_subpath('GongZhiMallMobile', true)

# 添加RNVisionOCR文件
header_file = main_group.new_file('RNVisionOCR.h')
implementation_file = main_group.new_file('RNVisionOCR.m')

# 将.m文件添加到编译阶段
target.source_build_phase.add_file_reference(implementation_file)

# 保存项目
project.save

puts "Successfully added RNVisionOCR files to Xcode project"
puts "Header file: #{header_file.path}"
puts "Implementation file: #{implementation_file.path}" 