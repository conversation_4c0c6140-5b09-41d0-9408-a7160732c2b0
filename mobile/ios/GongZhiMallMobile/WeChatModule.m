//
//  WeChatModule.m
//  GongZhiMallMobile
//
//  Created by React Native on 2025/01/23.
//  Copyright © 2025 GongZhiMall. All rights reserved.
//

#import "WeChatModule.h"
#import <React/RCTLog.h>
#import "WXApi.h"

// 微信SDK导入
#ifdef RCT_NEW_ARCH_ENABLED
#import <React/RCTConvert.h>
#endif

@implementation WeChatModule

RCT_EXPORT_MODULE(CustomWeChatSDK);

static BOOL isRegistered = NO;
static NSString *registeredAppId = nil;

/**
 * 注册微信应用
 */
RCT_EXPORT_METHOD(registerApp:(NSDictionary *)config
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        NSString *appId = config[@"appid"];
        NSString *universalLink = config[@"universalLink"];
        
        if (!appId || [appId length] == 0) {
            reject(@"INVALID_APPID", @"AppID不能为空", nil);
            return;
        }
        
        RCTLogInfo(@"[WeChatModule] 注册微信应用: %@", appId);

        // 使用微信SDK注册应用
        BOOL result = [WXApi registerApp:appId universalLink:universalLink];

        if (result) {
            isRegistered = YES;
            registeredAppId = appId;
            RCTLogInfo(@"[WeChatModule] 微信应用注册成功");
        } else {
            RCTLogError(@"[WeChatModule] 微信应用注册失败");
        }
        
        NSDictionary *result = @{
            @"success": @YES,
            @"message": @"微信应用注册成功"
        };
        resolve(result);
        
    } @catch (NSException *exception) {
        RCTLogError(@"[WeChatModule] 注册微信应用异常: %@", exception.reason);
        reject(@"REGISTER_ERROR", [NSString stringWithFormat:@"注册微信应用异常: %@", exception.reason], nil);
    }
}

/**
 * 检查微信是否安装
 */
RCT_EXPORT_METHOD(isWeChatInstalled:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        // 使用微信SDK检查安装状态
        BOOL installed = [WXApi isWXAppInstalled];

        RCTLogInfo(@"[WeChatModule] 微信安装状态: %@", installed ? @"已安装" : @"未安装");
        resolve(@(installed));

    } @catch (NSException *exception) {
        RCTLogError(@"[WeChatModule] 检查微信安装状态异常: %@", exception.reason);
        resolve(@NO); // 出错时假设未安装
    }
}

/**
 * 打开企业微信客服
 */
RCT_EXPORT_METHOD(openCustomerService:(NSDictionary *)config
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        if (!isRegistered) {
            reject(@"NOT_REGISTERED", @"请先注册微信应用", nil);
            return;
        }
        
        NSString *corpId = config[@"corpid"];
        NSString *url = config[@"url"];
        
        if (!corpId || [corpId length] == 0) {
            reject(@"INVALID_CORPID", @"企业ID不能为空", nil);
            return;
        }
        
        if (!url || [url length] == 0) {
            reject(@"INVALID_URL", @"客服链接不能为空", nil);
            return;
        }
        
        RCTLogInfo(@"[WeChatModule] 打开企业微信客服: %@, URL: %@", corpId, url);
        
        // 检查微信是否安装
        NSURL *wechatURL = [NSURL URLWithString:@"weixin://"];
        if (![[UIApplication sharedApplication] canOpenURL:wechatURL]) {
            reject(@"WECHAT_NOT_INSTALLED", @"微信未安装", nil);
            return;
        }
        
        // 方法1：使用正确的企业微信客服API（官方推荐方式）
        RCTLogInfo(@"[WeChatModule] 打开企业微信客服");
        RCTLogInfo(@"[WeChatModule] URL: %@", url);
        RCTLogInfo(@"[WeChatModule] CorpID: %@", corpId);

        // 检查微信版本是否支持客服功能（iOS微信1.9.2+）
        if ([WXApi getWXAppInstallUrl] != nil && [WXApi isWXAppInstalled]) {
            @try {
                // 使用正确的iOS微信客服API
                WXOpenCustomerServiceReq *req = [[WXOpenCustomerServiceReq alloc] init];
                req.corpid = corpId;  // 企业ID
                req.url = url;        // 客服URL

                RCTLogInfo(@"[WeChatModule] 使用WXOpenCustomerServiceReq打开企业微信客服");

                BOOL result = [WXApi sendReq:req completion:^(BOOL success) {
                    if (success) {
                        RCTLogInfo(@"[WeChatModule] 企业微信客服请求发送成功");
                    } else {
                        RCTLogError(@"[WeChatModule] 企业微信客服请求发送失败");
                    }
                }];

                if (result) {
                    RCTLogInfo(@"[WeChatModule] 企业微信客服打开成功");
                    NSDictionary *result = @{
                        @"success": @YES,
                        @"message": @"企业微信客服打开成功"
                    };
                    resolve(result);
                } else {
                    RCTLogError(@"[WeChatModule] WXOpenCustomerServiceReq发送失败");
                    reject(@"OPEN_FAILED", @"企业微信客服打开失败", nil);
                }
            } @catch (NSException *exception) {
                RCTLogError(@"[WeChatModule] WXOpenCustomerServiceReq异常: %@", exception.reason);
                reject(@"OPEN_ERROR", [NSString stringWithFormat:@"企业微信客服打开异常: %@", exception.reason], nil);
            }
        } else {
            RCTLogError(@"[WeChatModule] 微信未安装或版本过低");
            reject(@"WECHAT_NOT_INSTALLED", @"微信未安装或版本过低", nil);
        }
        
    } @catch (NSException *exception) {
        RCTLogError(@"[WeChatModule] 打开企业微信客服异常: %@", exception.reason);
        reject(@"OPEN_ERROR", [NSString stringWithFormat:@"打开企业微信客服异常: %@", exception.reason], nil);
    }
}

/**
 * 获取微信SDK版本
 */
RCT_EXPORT_METHOD(getWeChatSDKVersion:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        // 使用微信SDK获取版本信息
        NSString *version = [WXApi getWXAppInstallUrl];
        if (version && [WXApi isWXAppInstalled]) {
            resolve(@(1920)); // 表示支持1.9.2+版本功能
        } else {
            resolve(@(0)); // 微信未安装
        }
    } @catch (NSException *exception) {
        RCTLogError(@"[WeChatModule] 获取微信SDK版本异常: %@", exception.reason);
        resolve(@(0));
    }
}

/**
 * 检查注册状态
 */
RCT_EXPORT_METHOD(isRegistered:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    resolve(@(isRegistered));
}

/**
 * 获取模块状态信息
 */
RCT_EXPORT_METHOD(getModuleInfo:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        BOOL wechatInstalled = [WXApi isWXAppInstalled];
        NSString *version = [WXApi getWXAppInstallUrl];

        NSDictionary *info = @{
            @"registered": @(isRegistered),
            @"wechatInstalled": @(wechatInstalled),
            @"sdkVersion": @(wechatInstalled ? 1920 : 0),
            @"moduleName": @"CustomWeChatSDK",
            @"platform": @"iOS",
            @"wechatVersion": version ? version : @"未安装"
        };
        resolve(info);
    } @catch (NSException *exception) {
        RCTLogError(@"[WeChatModule] 获取模块信息异常: %@", exception.reason);
        reject(@"GET_INFO_ERROR", [NSString stringWithFormat:@"获取模块信息异常: %@", exception.reason], nil);
    }
}

// 支持同步方法调用
+ (BOOL)requiresMainQueueSetup
{
    return YES;
}

@end
