#import "CustomWeChatSDK.h"
#import <React/RCTLog.h>
#import <React/RCTUtils.h>

@implementation CustomWeChatSDK

RCT_EXPORT_MODULE(CustomWeChatSDK);

- (NSArray<NSString *> *)supportedEvents {
  return @[@"WeChatResponse"];
}

/**
 * 注册微信应用
 */
RCT_EXPORT_METHOD(registerApp:(NSDictionary *)config
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    NSString *appId = config[@"appid"];
    NSString *universalLink = config[@"universalLink"];
    
    if (!appId || [appId length] == 0) {
      reject(@"INVALID_APPID", @"AppID不能为空", nil);
      return;
    }
    
    RCTLogInfo(@"[CustomWeChatSDK] 注册微信应用: %@", appId);
    
    // 注册微信应用
    BOOL success = [WXApi registerApp:appId universalLink:universalLink];
    
    if (success) {
      NSDictionary *result = @{
        @"success": @YES,
        @"message": @"微信应用注册成功"
      };
      resolve(result);
    } else {
      reject(@"REGISTER_FAILED", @"微信应用注册失败", nil);
    }
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] 注册微信应用异常: %@", exception.reason);
    reject(@"REGISTER_ERROR", [NSString stringWithFormat:@"注册微信应用异常: %@", exception.reason], nil);
  }
}

/**
 * 检查微信是否已安装
 */
RCT_EXPORT_METHOD(isWeChatInstalled:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    BOOL isInstalled = [WXApi isWXAppInstalled];
    NSDictionary *result = @{
      @"installed": @(isInstalled),
      @"message": isInstalled ? @"微信已安装" : @"微信未安装"
    };
    resolve(result);
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] 检查微信安装状态异常: %@", exception.reason);
    reject(@"CHECK_ERROR", [NSString stringWithFormat:@"检查微信安装状态异常: %@", exception.reason], nil);
  }
}

/**
 * 打开微信客服会话
 */
RCT_EXPORT_METHOD(openCustomerService:(NSDictionary *)config
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    NSString *corpId = config[@"corpId"];
    NSString *url = config[@"url"];
    
    if (!corpId || [corpId length] == 0) {
      reject(@"INVALID_CORPID", @"企业ID不能为空", nil);
      return;
    }
    
    RCTLogInfo(@"[CustomWeChatSDK] 打开微信客服会话: %@", corpId);
    
    // 创建微信客服会话请求
    WXOpenCustomerServiceChatReq *req = [[WXOpenCustomerServiceChatReq alloc] init];
    req.corpid = corpId;
    req.url = url;
    
    // 发送请求
    BOOL success = [WXApi sendReq:req];
    
    if (success) {
      NSDictionary *result = @{
        @"success": @YES,
        @"message": @"微信客服会话打开成功"
      };
      resolve(result);
    } else {
      reject(@"CUSTOMER_SERVICE_FAILED", @"微信客服会话打开失败", nil);
    }
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] openCustomerService异常: %@", exception.reason);
    reject(@"CUSTOMER_SERVICE_ERROR", [NSString stringWithFormat:@"打开客服异常: %@", exception.reason], nil);
  }
}

/**
 * 发送文本消息到微信
 */
RCT_EXPORT_METHOD(sendTextMessage:(NSDictionary *)message
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    NSString *text = message[@"text"];
    NSInteger scene = [message[@"scene"] integerValue];
    
    if (!text || [text length] == 0) {
      reject(@"INVALID_TEXT", @"文本内容不能为空", nil);
      return;
    }
    
    RCTLogInfo(@"[CustomWeChatSDK] 发送文本消息到微信: %@", text);
    
    // 创建文本消息
    SendMessageToWXReq *req = [[SendMessageToWXReq alloc] init];
    req.text = text;
    req.bText = YES;
    req.scene = (int)scene;
    
    // 发送消息
    BOOL success = [WXApi sendReq:req];
    
    if (success) {
      NSDictionary *result = @{
        @"success": @YES,
        @"message": @"文本消息发送成功"
      };
      resolve(result);
    } else {
      reject(@"SEND_FAILED", @"文本消息发送失败", nil);
    }
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] 发送文本消息异常: %@", exception.reason);
    reject(@"SEND_ERROR", [NSString stringWithFormat:@"发送文本消息异常: %@", exception.reason], nil);
  }
}

/**
 * 发送图片消息到微信
 */
RCT_EXPORT_METHOD(sendImageMessage:(NSDictionary *)message
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    NSString *imagePath = message[@"imagePath"];
    NSInteger scene = [message[@"scene"] integerValue];
    
    if (!imagePath || [imagePath length] == 0) {
      reject(@"INVALID_IMAGE_PATH", @"图片路径不能为空", nil);
      return;
    }
    
    RCTLogInfo(@"[CustomWeChatSDK] 发送图片消息到微信: %@", imagePath);
    
    // 创建图片消息
    WXMediaMessage *mediaMessage = [WXMediaMessage message];
    mediaMessage.title = message[@"title"] ?: @"";
    mediaMessage.description = message[@"description"] ?: @"";
    
    // 设置图片
    UIImage *image = [UIImage imageWithContentsOfFile:imagePath];
    if (image) {
      [mediaMessage setThumbImage:image];
    }
    
    WXImageObject *imageObject = [WXImageObject object];
    imageObject.imageData = [NSData dataWithContentsOfFile:imagePath];
    mediaMessage.mediaObject = imageObject;
    
    SendMessageToWXReq *req = [[SendMessageToWXReq alloc] init];
    req.message = mediaMessage;
    req.scene = (int)scene;
    
    // 发送消息
    BOOL success = [WXApi sendReq:req];
    
    if (success) {
      NSDictionary *result = @{
        @"success": @YES,
        @"message": @"图片消息发送成功"
      };
      resolve(result);
    } else {
      reject(@"SEND_FAILED", @"图片消息发送失败", nil);
    }
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] 发送图片消息异常: %@", exception.reason);
    reject(@"SEND_ERROR", [NSString stringWithFormat:@"发送图片消息异常: %@", exception.reason], nil);
  }
}

/**
 * 获取微信SDK版本
 */
RCT_EXPORT_METHOD(getWeChatSDKVersion:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    NSString *version = [WXApi getApiVersion];
    resolve(version);
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] 获取SDK版本异常: %@", exception.reason);
    reject(@"VERSION_ERROR", [NSString stringWithFormat:@"获取SDK版本异常: %@", exception.reason], nil);
  }
}

/**
 * 检查注册状态
 */
RCT_EXPORT_METHOD(isRegistered:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    BOOL registered = [WXApi isWXAppInstalled];
    resolve(@(registered));
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] 检查注册状态异常: %@", exception.reason);
    reject(@"STATUS_ERROR", [NSString stringWithFormat:@"检查注册状态异常: %@", exception.reason], nil);
  }
}

/**
 * 获取模块信息
 */
RCT_EXPORT_METHOD(getModuleInfo:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  @try {
    NSDictionary *info = @{
      @"name": @"CustomWeChatSDK",
      @"version": @"1.0.0",
      @"platform": @"iOS",
      @"status": @"生产模式",
      @"description": @"iOS端微信SDK模块（真实实现）",
      @"sdkVersion": [WXApi getApiVersion] ?: @"未知"
    };
    resolve(info);
  } @catch (NSException *exception) {
    RCTLogError(@"[CustomWeChatSDK] 获取模块信息异常: %@", exception.reason);
    reject(@"INFO_ERROR", [NSString stringWithFormat:@"获取模块信息异常: %@", exception.reason], nil);
  }
}

/**
 * 处理微信回调
 */
- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
  return [WXApi handleOpenURL:url delegate:self];
}

/**
 * 处理Universal Link回调
 */
- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler {
  return [WXApi handleOpenUniversalLink:userActivity delegate:self];
}

#pragma mark - WXApiDelegate

/**
 * 微信回调处理
 */
- (void)onResp:(BaseResp *)resp {
  NSMutableDictionary *result = [NSMutableDictionary dictionary];
  
  if ([resp isKindOfClass:[SendMessageToWXResp class]]) {
    SendMessageToWXResp *sendResp = (SendMessageToWXResp *)resp;
    result[@"type"] = @"SendMessage";
    result[@"errCode"] = @(sendResp.errCode);
    result[@"errStr"] = sendResp.errStr ?: @"";
  } else if ([resp isKindOfClass:[WXOpenCustomerServiceChatResp class]]) {
    WXOpenCustomerServiceChatResp *serviceResp = (WXOpenCustomerServiceChatResp *)resp;
    result[@"type"] = @"CustomerService";
    result[@"errCode"] = @(serviceResp.errCode);
    result[@"errStr"] = serviceResp.errStr ?: @"";
  }
  
  // 发送事件到React Native
  [self sendEventWithName:@"WeChatResponse" body:result];
}

@end 