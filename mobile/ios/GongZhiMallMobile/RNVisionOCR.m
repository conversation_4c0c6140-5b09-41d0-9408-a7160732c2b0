//
//  RNVisionOCR.m
//  GongZhiMallMobile
//
//  Created by TaskMaster AI on 2025-01-27.
//  Copyright © 2025 GongZhiMall. All rights reserved.
//

#import "RNVisionOCR.h"
#import <React/RCTLog.h>
#import <React/RCTUtils.h>
#import <Vision/Vision.h>

@implementation RNVisionOCR

RCT_EXPORT_MODULE();

+ (BOOL)requiresMainQueueSetup
{
    return NO;
}

- (NSArray<NSString *> *)supportedEvents
{
    return @[@"OCRProgress", @"OCRError"];
}

#pragma mark - Public Methods

+ (BOOL)isVisionFrameworkAvailable
{
    if (@available(iOS 13.0, *)) {
        return [VNRecognizeTextRequest class] != nil;
    }
    return NO;
}

+ (NSDictionary *)getVisionFrameworkInfo
{
    NSMutableDictionary *info = [NSMutableDictionary dictionary];
    
    if (@available(iOS 13.0, *)) {
        info[@"version"] = @"iOS 13.0+";
        info[@"supportedFeatures"] = @[@"textRecognition", @"documentDetection"];
        info[@"supportedLanguages"] = @[@"zh-Hans", @"zh-Hant", @"en", @"ja", @"ko"];
        info[@"available"] = @([self isVisionFrameworkAvailable]);
    } else {
        info[@"version"] = @"Not Available";
        info[@"available"] = @NO;
        info[@"error"] = @"Requires iOS 13.0 or later";
    }
    
    return [info copy];
}

#pragma mark - React Native Exported Methods

RCT_EXPORT_METHOD(initializeVisionFramework:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    if (![RNVisionOCR isVisionFrameworkAvailable]) {
        reject(@"VISION_NOT_AVAILABLE", @"Vision framework is not available", nil);
        return;
    }
    
    // Vision框架不需要显式初始化，检查可用性即可
    resolve(@YES);
}

RCT_EXPORT_METHOD(checkOCRCapability:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    NSDictionary *visionInfo = [RNVisionOCR getVisionFrameworkInfo];
    
    NSMutableDictionary *capability = [NSMutableDictionary dictionary];
    capability[@"engine"] = @"ios_vision";
    capability[@"available"] = visionInfo[@"available"];
    capability[@"supportedLanguages"] = visionInfo[@"supportedLanguages"];
    capability[@"version"] = visionInfo[@"version"];
    
    if (![visionInfo[@"available"] boolValue]) {
        capability[@"error"] = visionInfo[@"error"] ?: @"Vision framework not available";
    }
    
    resolve([capability copy]);
}

RCT_EXPORT_METHOD(recognizeText:(NSString *)imagePath
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    if (![RNVisionOCR isVisionFrameworkAvailable]) {
        reject(@"VISION_NOT_AVAILABLE", @"Vision framework is not available", nil);
        return;
    }
    
    if (!imagePath || imagePath.length == 0) {
        reject(@"INVALID_IMAGE_PATH", @"Image path cannot be empty", nil);
        return;
    }
    
    // 在后台队列执行OCR处理
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [self performVisionOCRWithImagePath:imagePath resolver:resolve rejecter:reject];
    });
}

#pragma mark - Private Methods

- (void)performVisionOCRWithImagePath:(NSString *)imagePath
                             resolver:(RCTPromiseResolveBlock)resolve
                             rejecter:(RCTPromiseRejectBlock)reject API_AVAILABLE(ios(13.0))
{
    @try {
        // 加载图片
        UIImage *image = [self loadImageFromPath:imagePath];
        if (!image) {
            reject(@"IMAGE_LOAD_FAILED", @"Failed to load image from path", nil);
            return;
        }
        
        // 创建Vision请求
        VNRecognizeTextRequest *request = [[VNRecognizeTextRequest alloc] initWithCompletionHandler:^(VNRequest *request, NSError *error) {
            if (error) {
                reject(@"VISION_OCR_ERROR", error.localizedDescription, error);
                return;
            }
            
            // 处理识别结果
            NSArray<VNRecognizedTextObservation *> *observations = request.results;
            [self processVisionResults:observations resolver:resolve rejecter:reject];
        }];
        
        // 配置识别选项
        request.recognitionLevel = VNRequestTextRecognitionLevelAccurate;
        request.usesLanguageCorrection = YES;
        
        // 设置支持的语言（中文优先）
        NSArray *supportedLanguages = @[@"zh-Hans", @"zh-Hant", @"en"];
        request.recognitionLanguages = supportedLanguages;
        
        // 创建图片请求处理器
        VNImageRequestHandler *handler = [[VNImageRequestHandler alloc] initWithCGImage:image.CGImage options:@{}];
        
        // 执行OCR请求
        NSError *handlerError;
        BOOL success = [handler performRequests:@[request] error:&handlerError];
        
        if (!success) {
            reject(@"VISION_HANDLER_ERROR", handlerError.localizedDescription ?: @"Vision handler failed", handlerError);
        }
        
    } @catch (NSException *exception) {
        reject(@"VISION_OCR_EXCEPTION", exception.reason ?: @"Unknown exception occurred", nil);
    }
}

- (UIImage *)loadImageFromPath:(NSString *)imagePath
{
    // 处理不同类型的图片路径
    if ([imagePath hasPrefix:@"file://"]) {
        // 文件URL路径
        NSURL *imageURL = [NSURL URLWithString:imagePath];
        NSData *imageData = [NSData dataWithContentsOfURL:imageURL];
        return [UIImage imageWithData:imageData];
    } else if ([imagePath hasPrefix:@"/"]) {
        // 绝对路径
        return [UIImage imageWithContentsOfFile:imagePath];
    } else if ([imagePath hasPrefix:@"ph://"]) {
        // Photos框架资源（暂不支持，返回nil触发错误处理）
        RCTLogWarn(@"Photos framework assets not yet supported: %@", imagePath);
        return nil;
    } else {
        // 尝试作为bundle资源
        NSString *bundlePath = [[NSBundle mainBundle] pathForResource:imagePath ofType:nil];
        if (bundlePath) {
            return [UIImage imageWithContentsOfFile:bundlePath];
        }
        
        // 最后尝试直接加载
        return [UIImage imageWithContentsOfFile:imagePath];
    }
}

- (void)processVisionResults:(NSArray<VNRecognizedTextObservation *> *)observations
                    resolver:(RCTPromiseResolveBlock)resolve
                    rejecter:(RCTPromiseRejectBlock)reject API_AVAILABLE(ios(13.0))
{
    NSMutableArray *blocks = [NSMutableArray array];
    NSMutableString *fullText = [NSMutableString string];
    CGFloat totalConfidence = 0.0;
    NSInteger blockCount = 0;
    
    for (VNRecognizedTextObservation *observation in observations) {
        // 获取最佳识别结果
        VNRecognizedText *recognizedText = [observation topCandidates:1].firstObject;
        if (!recognizedText) continue;
        
        NSString *text = recognizedText.string;
        if (text.length == 0) continue;
        
        // 获取置信度
        CGFloat confidence = recognizedText.confidence;
        totalConfidence += confidence;
        blockCount++;
        
        // 添加到完整文本
        [fullText appendString:text];
        [fullText appendString:@"\n"];
        
        // 获取边界框
        CGRect boundingBox = observation.boundingBox;
        
        // 创建文本块
        NSDictionary *block = @{
            @"text": text,
            @"confidence": @(confidence),
            @"boundingBox": @{
                @"x": @(boundingBox.origin.x),
                @"y": @(boundingBox.origin.y),
                @"width": @(boundingBox.size.width),
                @"height": @(boundingBox.size.height)
            }
        };
        
        [blocks addObject:block];
    }
    
    // 计算平均置信度
    CGFloat averageConfidence = blockCount > 0 ? totalConfidence / blockCount : 0.0;
    
    // 清理完整文本
    NSString *cleanedText = [fullText stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    
    // 构建结果
    NSDictionary *result = @{
        @"text": cleanedText,
        @"confidence": @(averageConfidence),
        @"engine": @"ios_vision",
        @"processingTime": @(1500), // 模拟处理时间
        @"blocks": blocks,
        @"keywords": [self extractKeywords:cleanedText]
    };
    
    // 在主线程返回结果
    dispatch_async(dispatch_get_main_queue(), ^{
        resolve(result);
    });
}

- (NSArray *)extractKeywords:(NSString *)text
{
    if (text.length == 0) return @[];
    
    // 简单的关键词提取（基于常见政务词汇）
    NSArray *officialKeywords = @[@"通知", @"会议", @"部门", @"工作", @"报告", @"安排", @"要求", @"落实", @"领导", @"同志"];
    NSMutableArray *foundKeywords = [NSMutableArray array];
    
    for (NSString *keyword in officialKeywords) {
        if ([text containsString:keyword]) {
            [foundKeywords addObject:keyword];
        }
    }
    
    return [foundKeywords copy];
}

@end