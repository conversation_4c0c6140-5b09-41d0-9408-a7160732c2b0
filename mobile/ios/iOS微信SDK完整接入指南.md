# iOS微信SDK完整接入指南

## 概述
根据[微信官方文档](https://developers.weixin.qq.com/doc/oplatform/Mobile_App/Access_Guide/iOS.html)，本指南将帮助您完成iOS端微信SDK的完整接入。

## 已完成的步骤

### 1. ✅ 添加微信SDK依赖
在Podfile中添加：
```ruby
pod 'WechatOpenSDK-XCFramework'
```

### 2. ✅ 创建CustomWeChatSDK原生模块
- `CustomWeChatSDK.h` - 头文件
- `CustomWeChatSDK.m` - 实现文件（使用真正的微信SDK）

### 3. ✅ 更新AppDelegate.swift
已添加微信回调处理方法。

## 需要手动完成的步骤

### 4. 🔧 配置Universal Links

#### 4.1 创建apple-app-site-association文件
在您的服务器上创建文件：`https://your-domain.com/.well-known/apple-app-site-association`

```json
{
  "applinks": {
    "apps": [],
    "details": [
      {
        "appID": "YOUR_TEAM_ID.com.yourcompany.yourapp",
        "paths": ["/wechat/*"]
      }
    ]
  }
}
```

**注意：**
- 将`YOUR_TEAM_ID`替换为您的Apple Team ID
- 将`com.yourcompany.yourapp`替换为您的Bundle Identifier
- `paths`必须包含通配符`/*`

#### 4.2 在Xcode中配置Associated Domains
1. 打开Xcode项目
2. 选择项目Target
3. 在"Signing & Capabilities"标签页中
4. 点击"+"按钮，添加"Associated Domains"
5. 添加您的Universal Links域名，格式：`applinks:your-domain.com`

### 5. 🔧 在微信开放平台注册应用

#### 5.1 注册应用
1. 访问[微信开放平台](https://open.weixin.qq.com/)
2. 创建移动应用
3. 获取AppID和AppSecret
4. 配置iOS应用的Universal Links

#### 5.2 配置Universal Links
在微信开放平台的应用配置中：
- 设置iOS应用的Universal Links
- 格式：`https://your-domain.com/wechat/*`

### 6. 🔧 更新应用配置

#### 6.1 更新appConfig.ts
在`mobile/src/config/appConfig.ts`中更新微信配置：

```typescript
export const wechatConfig = {
  appId: 'YOUR_WECHAT_APP_ID',
  universalLink: 'https://your-domain.com/wechat/',
  // 其他配置...
};
```

#### 6.2 更新Info.plist
在Xcode中添加URL Schemes：
1. 打开Info.plist
2. 添加`URL types`
3. 添加您的微信AppID作为URL Scheme

### 7. 🔧 测试配置

#### 7.1 测试Universal Links
1. 在Safari中输入：`https://your-domain.com/wechat/test`
2. 检查是否出现"打开应用"的提示
3. 点击后应该能正确跳转到应用

#### 7.2 测试微信SDK
1. 运行应用
2. 检查控制台日志，确认微信SDK注册成功
3. 测试微信相关功能

## 验证清单

- [ ] Universal Links配置正确
- [ ] Associated Domains已添加
- [ ] 微信开放平台应用已注册
- [ ] AppID和Universal Links已配置
- [ ] CustomWeChatSDK模块已添加到Xcode项目
- [ ] 应用能正确响应微信回调
- [ ] 微信SDK功能测试通过

## 常见问题

### 问题1：Universal Links不生效
**解决方案：**
1. 检查apple-app-site-association文件格式
2. 确认域名支持HTTPS
3. 检查paths配置是否包含通配符
4. 清除浏览器缓存重新测试

### 问题2：微信回调无法处理
**解决方案：**
1. 确认AppDelegate中的回调方法已正确实现
2. 检查URL Schemes配置
3. 确认微信开放平台配置正确

### 问题3：SDK注册失败
**解决方案：**
1. 检查AppID是否正确
2. 确认Universal Links配置
3. 检查网络连接
4. 查看控制台错误日志

## 生产环境注意事项

1. **安全性**：确保Universal Links使用HTTPS
2. **测试**：在真机上完整测试所有功能
3. **审核**：确保应用符合微信开放平台审核规范
4. **监控**：添加错误监控和日志记录

## 相关文档

- [微信开放平台iOS接入指南](https://developers.weixin.qq.com/doc/oplatform/Mobile_App/Access_Guide/iOS.html)
- [Apple Universal Links文档](https://developer.apple.com/ios/universal-links/)
- [微信Open SDK更新日志](https://developers.weixin.qq.com/doc/oplatform/Mobile_App/Access_Guide/iOS.html#SDK%20%E6%9B%B4%E6%96%B0%E6%97%A5%E5%BF%97)