require_relative '../node_modules/react-native/scripts/react_native_pods'
platform :ios, '15.1'

# Enable Hermes JS engine for better performance, required for Frame Processors
ENV['USE_HERMES'] = '1'
# Enable VisionCamera Frame Processors
ENV['VC_ENABLE_FRAME_PROCESSORS'] = '1'

prepare_react_native_project!

target 'GongZhiMallMobile' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  # 微信SDK
  pod 'WechatOpenSDK', '~> 1.9.2'

  # WatermelonDB dependencies
  pod 'simdjson', path: '../node_modules/@nozbe/simdjson', modular_headers: true
  
  
  post_install do |installer|
    react_native_post_install(installer)
    
    # Fix for Xcode 14+
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.1'
      end
    end
  end
end
