# 已知问题 (Known Issues)

## 📱 iOS 相册权限相关

### Issue #001: iOS 受限相册权限不生效

**问题描述:**
- 当用户在iOS设备上选择"受限相册权限"（只选择部分照片）时，App仍然可以访问所有照片
- 相册选择器显示所有照片，而不是用户授权的受限照片

**影响范围:**
- iOS 14+ 设备
- 使用相册选择功能的用户

**根本原因:**
- `react-native-image-picker` 库的已知Bug (GitHub Issue #1749)
- 该库尚未完全支持iOS 14+的新受限相册权限机制
- 库可能还没有适配Apple推荐的PHPicker API

**当前状态:**
- ✅ 多选功能正常工作
- ✅ 照片预览功能正常工作  
- ❌ 受限权限功能受到第三方库限制

**临时解决方案:**
1. 已添加完整的iOS权限配置到Info.plist
2. 用户可以通过系统设置手动管理照片访问权限
3. 引导用户选择"允许访问所有照片"以获得最佳体验

**长期解决方案:**
1. 等待 `react-native-image-picker` 官方修复 (监控Issue #1749)
2. 考虑替换为其他支持受限权限的图片选择库：
   - `@react-native-camera-roll/camera-roll`
   - `react-native-image-crop-picker`
3. 升级到支持PHPicker的新版本库

**相关链接:**
- [react-native-image-picker Issue #1749](https://github.com/react-native-image-picker/react-native-image-picker/issues/1749)
- [iOS 14 Photo Library Changes](https://developer.apple.com/documentation/photokit/phphotolibrary)

**更新时间:** 2025-01-18
**优先级:** Medium (不影响核心功能)

---

## 📝 如何添加新的已知问题

当发现新的已知问题时，请按以下格式添加：

```markdown
### Issue #XXX: 问题标题

**问题描述:** 详细描述问题现象
**影响范围:** 说明影响的功能和用户群体
**根本原因:** 分析问题的技术原因
**当前状态:** 列出各功能的工作状态
**临时解决方案:** 当前的缓解措施
**长期解决方案:** 计划的根本解决方案
**相关链接:** 相关的GitHub Issue、文档等
**更新时间:** YYYY-MM-DD
**优先级:** High/Medium/Low
``` 