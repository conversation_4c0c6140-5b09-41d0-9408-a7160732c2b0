# Vision Camera 集成与专业级拍照模块实施计划

本文档记录了为"公职猫"项目集成 `react-native-vision-camera` 并实现专业级拍照体验的完整技术方案。

## 核心目标

1. **替换现有引擎**：使用 `react-native-vision-camera` 替换 `react-native-image-picker`，以支持完全自定义的相机UI和高级功能。
2. **实现原生级功能**：支持 0.5 倍超广角变焦、手势平滑缩放、高分辨率照片捕捉。
3. **优化核心场景**：针对拍摄文件、PPT、电子屏幕和投影仪等核心场景进行深度优化。
4. **提升交互体验**：实现以大快门为中心的UI布局，并支持连续拍摄。
5. **确保项目稳定**：采用隔离验证、分步实施的策略，确保新功能不影响现有项目稳定性和编译。

---

## 实施方案详情

### 第一阶段：深入研究与可行性分析

- **冲突分析**：

  - **`react-native-reanimated`**：v3+版本在Android上可能存在JSI冲突。项目使用的 `reanimated v3.18.0` 与 `vision-camera` 最新版已良好适配，遵循官方文档配置可规避。
  - **原生环境要求**：
    - **iOS**: 需要 iOS 11+，项目 `Podfile` 设置的 `15.1` 完全满足。
    - **Android**: 需要 `minSdkVersion >= 23` 和特定 `kotlinVersion`，将在 `build.gradle` 中对齐。
- **功能可行性**：

  - **✅ 0.5倍变焦 (iOS)**：通过 `useCameraDevice` hook 搜索并选用 `ultra-wide-angle-camera` 设备即可实现。
  - **✅ 高清晰度**：使用 `useCameraFormat` hook 选择最佳分辨率，`photo: true` 捕获高质量静态图片。
  - **✅ 场景自适应优化 (智能替代手动控件)**：
    - **默认模式**：相机采用自动曝光、自动白平衡。
    - **屏幕拍摄智能识别**：利用 `vision-camera` 的 Frame Processor 插件，通过分析图像特征（亮度、对比度、摩尔纹）来判断是否在拍摄电子屏幕。
    - **自动切换"屏幕模式"**：当识别为拍摄屏幕时，相机自动执行以下操作：
      - 降低曝光补偿，防止过曝。
      - 调整白平衡至冷色温。
      - 在支持的设备上开启抗频闪模式 (`androidImageStabilizationMode`)。
    - **用户微调入口**：保留一个不显眼的"高级设置"入口，作为备用方案。

### 第二阶段：稳妥的环境准备与安全安装

1. **安装依赖**：`yarn add react-native-vision-camera`
2. **Android 配置**：
   - 在 `mobile/android/app/src/main/AndroidManifest.xml` 中，声明 `CAMERA` 和 `RECORD_AUDIO` 权限。
   - 在 `mobile/android/build.gradle` 中，检查并按需提升 `minSdkVersion` (至23+) 和 `kotlinVersion`。
3. **iOS 配置**：
   - 在 `mobile/ios/GongZhiMallMobile/Info.plist` 中，确保 `NSCameraUsageDescription` 和 `NSMicrophoneUsageDescription` 权限描述存在。
   - 在 `mobile/ios` 目录下，执行 `pod install`，利用自动链接机制安装原生模块。

### 第三阶段：隔离验证与高级功能实现

1. **创建隔离测试组件**：新建 `VisionCameraTest.tsx`，不直接修改现有代码。
2. **基础功能验证**：在测试组件中，使用 `useCameraDevice` 获取摄像头并渲染 `<Camera>` 组件，确认实时取景器正常工作。
3. **重构 `AdvancedImageInput.tsx`**：
   - 移除 `react-native-image-picker` 的逻辑。
   - 使用 `vision-camera` 的 `<Camera>` 组件作为取景器。
   - **实现0.5倍变焦**：添加设备切换逻辑，允许用户选择"超广角"摄像头。
   - **实现手势缩放**：集成 `react-native-gesture-handler`，监听捏合手势并绑定到 `<Camera>` 的 `zoom` 属性。
   - **重构UI**：实现以大快门为中心的新布局。
   - **实现连续拍摄**：改造快门逻辑，拍照后图片进入下方缩略图，但相机保持激活状态。

### 第四阶段：场景优化与最终集成

1. **实现场景自适应**：利用 Frame Processor 实现前述的"屏幕模式"自动切换逻辑。
2. **最终集成**：在所有功能于隔离环境中稳定运行后，将 `HomeScreen` 中调用的组件彻底切换到重构后的 `AdvancedImageInput`。

---

## 附录：`react-native-worklets-core` 安全集成方案

为了实现第四阶段的"场景自适应优化"（依赖Frame Processor），必须安装和配置 `react-native-worklets-core`。本附录详细说明了相关的风险及安全集成步骤。

### 1. 必要性

- **Frame Processor 依赖**: `vision-camera` 最强大的实时图像处理功能完全依赖此库。
- **智能场景识别**: 若无此库，我们将无法实现对拍摄内容的实时分析（如识别屏幕、调整参数），"秘书般懂你"的核心体验将无法兑现。

### 2. 潜在风险与解决方案（非与Taskmaster冲突）

- **真正的风险点**: 冲突并非来自开发工具链(Taskmaster)，而是来自与 `react-native-reanimated` 共享的底层原生技术。
- **Babel 编译时注入**:
  - **风险**: `worklets` 插件若配置不当或与 `reanimated` 插件顺序错误，会导致App编译失败或崩溃。
  - **解决方案**: 严格遵循官方文档，将 `react-native-worklets-core/plugin` 添加到 `babel.config.js` 的 `plugins` 数组的**最后一位**。
- **JSI 原生绑定**:
  - **风险**: `worklets` 和 `reanimated` 共享JSI运行时。若原生代码（iOS/Android）中初始化不当，会导致闪退。
  - **解决方案**: 在Android的 `MainApplication.java` 中手动添加初始化代码，确保JSI绑定正确。iOS端可通过 `pod install` 自动处理。

### 3. 安全实施步骤

1. **安装依赖**: `yarn add react-native-worklets-core`
2. **配置 Babel**: 修改 `babel.config.js`，将 `react-native-worklets-core/plugin` 添加到 `plugins` 数组末尾。
3. **配置 Android**: 修改 `MainApplication.java`，添加 JSI 绑定初始化代码。
4. **配置 iOS**: 重新运行 `pod install`，完成自动链接。

---

本文档将作为项目技术决策的重要依据，并随开发进展持续更新。
