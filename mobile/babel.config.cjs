module.exports = {
  presets: ['module:@react-native/babel-preset', '@babel/preset-typescript'],
  plugins: [
    ['module:react-native-dotenv', {
      moduleName: '@env',
      path: '.env',
      blacklist: null,
      whitelist: null,
      safe: false,
      allowUndefined: true,
    }],
    '@babel/plugin-transform-export-namespace-from',
    'react-native-reanimated/plugin',
    'react-native-worklets-core/plugin',
    ['@babel/plugin-proposal-decorators', { legacy: true }],
  ],
};
