import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ActivityIndicator,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { defaultTheme } from '../styles/theme';

// 单个文件的上传状态
export interface FileUploadItem {
  id: string;
  name: string;
  size: number;
  status: 'waiting' | 'uploading' | 'completed' | 'failed';
  progress: number; // 0-1
  error?: string;
}

interface BatchFileUploadProgressProps {
  visible: boolean;
  files: FileUploadItem[];
  onCancel?: () => void;
  onRetry?: (fileId: string) => void;
}

const BatchFileUploadProgress: React.FC<BatchFileUploadProgressProps> = ({
  visible,
  files,
  onCancel,
  onRetry,
}) => {
  const theme = defaultTheme;

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) {return `${bytes} B`;}
    if (bytes < 1024 * 1024) {return `${(bytes / 1024).toFixed(1)} KB`;}
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  /**
   * 获取状态图标
   */
  const getStatusIcon = (status: FileUploadItem['status']) => {
    switch (status) {
      case 'waiting':
        return <Ionicons name="time-outline" size={20} color={theme.colors.textSecondary} />;
      case 'uploading':
        return <ActivityIndicator size="small" color={theme.colors.primary} />;
      case 'completed':
        return <Ionicons name="checkmark-circle" size={20} color={theme.colors.success} />;
      case 'failed':
        return <Ionicons name="close-circle" size={20} color={theme.colors.error} />;
      default:
        return null;
    }
  };

  /**
   * 获取状态文本
   */
  const getStatusText = (item: FileUploadItem): string => {
    switch (item.status) {
      case 'waiting':
        return '等待中';
      case 'uploading':
        return `上传中 ${Math.round(item.progress * 100)}%`;
      case 'completed':
        return '已完成';
      case 'failed':
        return item.error || '上传失败';
      default:
        return '';
    }
  };

  /**
   * 计算总体进度
   */
  const getTotalProgress = (): { completed: number; total: number; percentage: number } => {
    const total = files.length;
    const completed = files.filter(f => f.status === 'completed').length;
    const percentage = total > 0 ? (completed / total) * 100 : 0;
    return { completed, total, percentage };
  };

  /**
   * 检查是否全部完成
   */
  const isAllCompleted = (): boolean => {
    return files.length > 0 && files.every(f => f.status === 'completed' || f.status === 'failed');
  };

  /**
   * 渲染单个文件项
   */
  const renderFileItem = ({ item }: { item: FileUploadItem }) => (
    <View style={styles.fileItem}>
      <View style={styles.fileInfo}>
        <View style={styles.fileHeader}>
          <Text style={styles.fileName} numberOfLines={1}>
            {item.name}
          </Text>
          <View style={styles.statusContainer}>
            {getStatusIcon(item.status)}
          </View>
        </View>

        <View style={styles.fileDetails}>
          <Text style={styles.fileSize}>
            {formatFileSize(item.size)}
          </Text>
          <Text style={[
            styles.statusText,
            item.status === 'failed' && styles.errorText,
          ]}>
            {getStatusText(item)}
          </Text>
        </View>

        {/* 进度条 */}
        {item.status === 'uploading' && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: `${item.progress * 100}%` },
                ]}
              />
            </View>
          </View>
        )}

        {/* 重试按钮 */}
        {item.status === 'failed' && onRetry && (
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => onRetry(item.id)}
          >
            <Ionicons name="refresh" size={16} color={theme.colors.primary} />
            <Text style={styles.retryText}>重试</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const totalProgress = getTotalProgress();
  const allCompleted = isAllCompleted();

  if (!visible || files.length === 0) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      statusBarTranslucent={true}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          {/* 头部 */}
          <View style={styles.header}>
            <Text style={styles.title}>
              批量上传文件
            </Text>
            <Text style={styles.subtitle}>
              {totalProgress.completed} / {totalProgress.total} 已完成
            </Text>
          </View>

          {/* 总体进度条 */}
          <View style={styles.totalProgressContainer}>
            <View style={styles.totalProgressBar}>
              <View
                style={[
                  styles.totalProgressFill,
                  { width: `${totalProgress.percentage}%` },
                ]}
              />
            </View>
            <Text style={styles.totalProgressText}>
              {Math.round(totalProgress.percentage)}%
            </Text>
          </View>

          {/* 文件列表 */}
          <FlatList
            data={files}
            renderItem={renderFileItem}
            keyExtractor={(item) => item.id}
            style={styles.fileList}
            showsVerticalScrollIndicator={false}
          />

          {/* 底部按钮 */}
          <View style={styles.footer}>
            {allCompleted ? (
              <TouchableOpacity style={styles.doneButton} onPress={onCancel}>
                <Text style={styles.doneButtonText}>完成</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
                <Text style={styles.cancelButtonText}>取消</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  container: {
    backgroundColor: defaultTheme.colors.backgroundPrimary,
    borderRadius: 16,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: defaultTheme.colors.backgroundTertiary,
  },
  title: {
    fontSize: defaultTheme.fonts.bodyLarge,
    color: defaultTheme.colors.textPrimary,
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: defaultTheme.fonts.bodySmall,
    color: defaultTheme.colors.textSecondary,
  },
  totalProgressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: defaultTheme.colors.backgroundTertiary,
  },
  totalProgressBar: {
    flex: 1,
    height: 8,
    backgroundColor: defaultTheme.colors.backgroundTertiary,
    borderRadius: 4,
    marginRight: 12,
    overflow: 'hidden',
  },
  totalProgressFill: {
    height: '100%',
    backgroundColor: defaultTheme.colors.primary,
    borderRadius: 4,
  },
  totalProgressText: {
    fontSize: defaultTheme.fonts.bodySmall,
    color: defaultTheme.colors.textPrimary,
    fontWeight: '600',
    minWidth: 40,
    textAlign: 'right',
  },
  fileList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  fileItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: defaultTheme.colors.backgroundTertiary,
  },
  fileInfo: {
    flex: 1,
  },
  fileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  fileName: {
    flex: 1,
    fontSize: defaultTheme.fonts.bodyMedium,
    color: defaultTheme.colors.textPrimary,
    fontWeight: '500',
    marginRight: 8,
  },
  statusContainer: {
    minWidth: 24,
    alignItems: 'center',
  },
  fileDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  fileSize: {
    fontSize: defaultTheme.fonts.bodySmall,
    color: defaultTheme.colors.textSecondary,
  },
  statusText: {
    fontSize: defaultTheme.fonts.bodySmall,
    color: defaultTheme.colors.textSecondary,
  },
  errorText: {
    color: defaultTheme.colors.error,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: defaultTheme.colors.backgroundTertiary,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: defaultTheme.colors.primary,
    borderRadius: 2,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: defaultTheme.colors.primary,
  },
  retryText: {
    fontSize: defaultTheme.fonts.bodySmall,
    color: defaultTheme.colors.primary,
    marginLeft: 4,
    fontWeight: '500',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: defaultTheme.colors.backgroundTertiary,
  },
  doneButton: {
    backgroundColor: defaultTheme.colors.primary,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  doneButtonText: {
    fontSize: defaultTheme.fonts.bodyMedium,
    color: defaultTheme.colors.backgroundPrimary,
    fontWeight: '600',
  },
  cancelButton: {
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: defaultTheme.colors.textSecondary,
  },
  cancelButtonText: {
    fontSize: defaultTheme.fonts.bodyMedium,
    color: defaultTheme.colors.textSecondary,
    fontWeight: '500',
  },
});

export default BatchFileUploadProgress;
