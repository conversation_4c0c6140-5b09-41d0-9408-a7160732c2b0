import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Alert,
  TextInput,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { defaultTheme } from '../styles/theme';
import { FileMessageData } from './FileMessageBubble';

interface FileManagementDialogProps {
  visible: boolean;
  fileData: FileMessageData | null;
  onClose: () => void;
  onDelete: (fileData: FileMessageData) => void;
  onRename: (fileData: FileMessageData, newName: string) => void;
  onShare: (fileData: FileMessageData) => void;
}

const FileManagementDialog: React.FC<FileManagementDialogProps> = ({
  visible,
  fileData,
  onClose,
  onDelete,
  onRename,
  onShare,
}) => {
  const theme = defaultTheme;
  const [isRenaming, setIsRenaming] = useState(false);
  const [newFileName, setNewFileName] = useState('');

  /**
   * 处理重命名
   */
  const handleRename = () => {
    if (!fileData) {return;}

    // 获取文件扩展名
    const extension = fileData.name.split('.').pop();
    const nameWithoutExtension = fileData.name.replace(`.${extension}`, '');

    setNewFileName(nameWithoutExtension);
    setIsRenaming(true);
  };

  /**
   * 确认重命名
   */
  const confirmRename = () => {
    if (!fileData || !newFileName.trim()) {
      Alert.alert('提示', '请输入有效的文件名');
      return;
    }

    // 获取原始扩展名
    const extension = fileData.name.split('.').pop();
    const finalName = `${newFileName.trim()}.${extension}`;

    onRename(fileData, finalName);
    setIsRenaming(false);
    setNewFileName('');
    onClose();
  };

  /**
   * 取消重命名
   */
  const cancelRename = () => {
    setIsRenaming(false);
    setNewFileName('');
  };

  /**
   * 处理删除
   */
  const handleDelete = () => {
    if (!fileData) {return;}

    Alert.alert(
      '删除文件',
      `确定要删除文件"${fileData.name}"吗？此操作不可撤销。`,
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => {
            onDelete(fileData);
            onClose();
          },
        },
      ]
    );
  };

  /**
   * 处理分享
   */
  const handleShare = () => {
    if (!fileData) {return;}
    onShare(fileData);
    onClose();
  };

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) {return `${bytes} B`;}
    if (bytes < 1024 * 1024) {return `${(bytes / 1024).toFixed(1)} KB`;}
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  if (!visible || !fileData) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      statusBarTranslucent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          {/* 头部 */}
          <View style={styles.header}>
            <Text style={styles.title}>文件管理</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* 文件信息 */}
          <View style={styles.fileInfo}>
            <View style={styles.fileHeader}>
              <Ionicons name="document" size={20} color={theme.colors.primary} />
              <Text style={styles.fileName} numberOfLines={2}>
                {fileData.name}
              </Text>
            </View>
            <Text style={styles.fileSize}>
              {formatFileSize(fileData.size)}
            </Text>
          </View>

          {/* 重命名输入框 */}
          {isRenaming && (
            <View style={styles.renameContainer}>
              <Text style={styles.renameLabel}>新文件名：</Text>
              <TextInput
                style={styles.renameInput}
                value={newFileName}
                onChangeText={setNewFileName}
                placeholder="请输入新的文件名"
                autoFocus={true}
                selectTextOnFocus={true}
              />
              <View style={styles.renameButtons}>
                <TouchableOpacity
                  style={[styles.renameButton, styles.cancelButton]}
                  onPress={cancelRename}
                >
                  <Text style={styles.cancelButtonText}>取消</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.renameButton, styles.confirmButton]}
                  onPress={confirmRename}
                >
                  <Text style={styles.confirmButtonText}>确认</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {/* 操作按钮 */}
          {!isRenaming && (
            <View style={styles.actions}>
              <TouchableOpacity style={styles.actionButton} onPress={handleRename}>
                <Ionicons name="create-outline" size={20} color={theme.colors.primary} />
                <Text style={styles.actionText}>重命名</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
                <Ionicons name="share-outline" size={20} color={theme.colors.primary} />
                <Text style={styles.actionText}>分享</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>
                <Ionicons name="trash-outline" size={20} color={theme.colors.error} />
                <Text style={[styles.actionText, { color: theme.colors.error }]}>删除</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  container: {
    backgroundColor: defaultTheme.colors.backgroundPrimary,
    borderRadius: 16,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: defaultTheme.colors.backgroundTertiary,
  },
  title: {
    fontSize: defaultTheme.fonts.bodyLarge,
    color: defaultTheme.colors.textPrimary,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  fileInfo: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: defaultTheme.colors.backgroundTertiary,
  },
  fileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  fileName: {
    fontSize: defaultTheme.fonts.bodyMedium,
    color: defaultTheme.colors.textPrimary,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  fileSize: {
    fontSize: defaultTheme.fonts.bodySmall,
    color: defaultTheme.colors.textSecondary,
  },
  renameContainer: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: defaultTheme.colors.backgroundTertiary,
  },
  renameLabel: {
    fontSize: defaultTheme.fonts.bodyMedium,
    color: defaultTheme.colors.textPrimary,
    marginBottom: 8,
    fontWeight: '500',
  },
  renameInput: {
    borderWidth: 1,
    borderColor: defaultTheme.colors.backgroundTertiary,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: defaultTheme.fonts.bodyMedium,
    color: defaultTheme.colors.textPrimary,
    marginBottom: 16,
  },
  renameButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
  },
  renameButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 60,
    alignItems: 'center',
  },
  cancelButton: {
    borderWidth: 1,
    borderColor: defaultTheme.colors.textSecondary,
  },
  confirmButton: {
    backgroundColor: defaultTheme.colors.primary,
  },
  cancelButtonText: {
    fontSize: defaultTheme.fonts.bodySmall,
    color: defaultTheme.colors.textSecondary,
    fontWeight: '500',
  },
  confirmButtonText: {
    fontSize: defaultTheme.fonts.bodySmall,
    color: defaultTheme.colors.backgroundPrimary,
    fontWeight: '500',
  },
  actions: {
    padding: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 4,
  },
  actionText: {
    fontSize: defaultTheme.fonts.bodyMedium,
    color: defaultTheme.colors.primary,
    marginLeft: 12,
    fontWeight: '500',
  },
});

export default FileManagementDialog;
