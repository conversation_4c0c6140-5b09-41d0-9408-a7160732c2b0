/**
 * iOS Vision框架测试组件
 * 用于在真机上验证原生模块集成
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { VisionOCRTest, VisionOCRTestResult } from '../services/VisionOCRTest';
import { VisionOCRValidator, ValidationResult } from '../utils/VisionOCRValidator';

interface TestResults {
  [key: string]: VisionOCRTestResult;
}

export const VisionOCRTestComponent: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResults>({});
  const [overallResult, setOverallResult] = useState<boolean | null>(null);
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);

  const runQuickValidation = async () => {
    setIsRunning(true);
    try {
      const results = await VisionOCRValidator.runFullValidation();
      setValidationResults(results);
      const allSuccess = results.every(r => r.success);

      // 显示详细的验证结果
      const resultDetails = results.map((r, index) =>
        `${r.success ? '✅' : '❌'} 验证${index + 1}: ${r.message}`
      ).join('\n');

      Alert.alert(
        '快速验证结果',
        `${allSuccess ? '✅ 所有验证通过！' : '❌ 部分验证失败'}\n\n详细结果:\n${resultDetails}`,
        [{ text: '确定' }]
      );
    } catch (error) {
      Alert.alert('验证错误', `验证过程中发生错误: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsRunning(false);
    }
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults({});
    setOverallResult(null);

    try {
      const { overall, results } = await VisionOCRTest.runFullTestSuite();
      setTestResults(results);
      setOverallResult(overall);

      // 显示总体结果
      Alert.alert(
        '测试完成',
        overall ? '✅ 所有测试通过！' : '❌ 部分测试失败，请查看详细结果',
        [{ text: '确定' }]
      );
    } catch (error) {
      Alert.alert(
        '测试错误',
        `运行测试时发生错误: ${error instanceof Error ? error.message : '未知错误'}`,
        [{ text: '确定' }]
      );
    } finally {
      setIsRunning(false);
    }
  };

  const renderTestResult = (testName: string, result: VisionOCRTestResult) => {
    return (
      <View key={testName} style={styles.testResultContainer}>
        <View style={styles.testHeader}>
          <Text style={styles.testName}>{testName}</Text>
          <Text style={[styles.testStatus, result.success ? styles.success : styles.failure]}>
            {result.success ? '✅ 通过' : '❌ 失败'}
          </Text>
        </View>
        <Text style={styles.testMessage}>{result.message}</Text>
        {result.error && (
          <Text style={styles.errorText}>错误: {result.error}</Text>
        )}
        {result.data && (
          <Text style={styles.dataText}>
            数据: {JSON.stringify(result.data, null, 2)}
          </Text>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* 简化的标题 */}
      <Text style={styles.title}>Vision OCR 测试</Text>
      <Text style={styles.subtitle}>验证iOS Vision框架集成</Text>

      {/* 按钮区域 */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.primaryButton, isRunning && styles.buttonDisabled]}
          onPress={runQuickValidation}
          disabled={isRunning}
        >
          <Text style={styles.primaryButtonText}>
            {isRunning ? '验证中...' : '快速验证'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.secondaryButton, isRunning && styles.buttonDisabled]}
          onPress={runTests}
          disabled={isRunning}
        >
          <Text style={styles.secondaryButtonText}>
            {isRunning ? '测试中...' : '完整测试'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* 结果区域 */}
      <ScrollView style={styles.resultsContainer} showsVerticalScrollIndicator={false}>
        {/* 总体结果 */}
        {overallResult !== null && (
          <View style={styles.resultCard}>
            <Text style={[styles.resultText, overallResult ? styles.success : styles.failure]}>
              总体结果: {overallResult ? '✅ 全部通过' : '❌ 部分失败'}
            </Text>
          </View>
        )}

        {/* 验证结果 */}
        {validationResults.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>验证结果</Text>
            {validationResults.map((result, index) => (
              <View key={index} style={styles.resultItem}>
                <Text style={[styles.statusIcon, result.success ? styles.success : styles.failure]}>
                  {result.success ? '✅' : '❌'}
                </Text>
                <View style={styles.resultContent}>
                  <Text style={styles.resultTitle}>验证 {index + 1}</Text>
                  <Text style={styles.resultMessage}>{result.message}</Text>
                </View>
              </View>
            ))}
          </View>
        )}

        {/* 测试结果 */}
        {Object.keys(testResults).length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>测试结果</Text>
            {Object.entries(testResults).map(([testName, result]) =>
              renderTestResult(testName, result)
            )}
          </View>
        )}

        {/* 说明 */}
        <View style={styles.instructionsCard}>
          <Text style={styles.instructionsTitle}>📋 测试说明</Text>
          <Text style={styles.instructionsText}>
            • 快速验证：检查原生模块加载和基础功能{'\n'}
            • 完整测试：运行所有OCR功能测试{'\n'}
            • 验证Vision框架是否正确集成{'\n'}
            • 测试文本识别基础能力
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent', // 让模态框背景透过
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#CCCCCC',
    textAlign: 'center',
    marginBottom: 20,
  },
  buttonContainer: {
    gap: 12,
    marginBottom: 20,
  },
  primaryButton: {
    backgroundColor: '#007bff',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  primaryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  secondaryButton: {
    backgroundColor: '#6c757d',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonDisabled: {
    backgroundColor: '#adb5bd',
  },
  resultsContainer: {
    flex: 1,
  },
  resultCard: {
    backgroundColor: '#2d2d2d',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#404040',
  },
  resultText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  resultItem: {
    backgroundColor: '#2d2d2d',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderWidth: 1,
    borderColor: '#404040',
  },
  statusIcon: {
    fontSize: 16,
    marginRight: 12,
    marginTop: 2,
    color: '#FFFFFF',
  },
  resultContent: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  resultMessage: {
    fontSize: 14,
    color: '#CCCCCC',
    lineHeight: 20,
  },
  testResultContainer: {
    backgroundColor: '#2d2d2d',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#404040',
  },
  testHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  testName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
  },
  testStatus: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  testMessage: {
    fontSize: 14,
    color: '#CCCCCC',
    marginBottom: 4,
    lineHeight: 18,
  },
  errorText: {
    fontSize: 12,
    color: '#ff6b6b',
    marginBottom: 4,
    fontFamily: 'Courier',
  },
  dataText: {
    fontSize: 12,
    color: '#74c0fc',
    fontFamily: 'Courier',
  },
  success: {
    color: '#51cf66',
  },
  failure: {
    color: '#ff6b6b',
  },
  instructionsCard: {
    backgroundColor: '#2d2d2d',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#404040',
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    color: '#CCCCCC',
    lineHeight: 20,
  },
});
