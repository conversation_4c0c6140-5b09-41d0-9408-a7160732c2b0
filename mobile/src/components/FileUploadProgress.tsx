import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { defaultTheme } from '../styles/theme';
import { FileUploadProgress as ProgressData } from '../services/FileStorageService';

interface FileUploadProgressProps {
  visible: boolean;
  fileName: string;
  progress: ProgressData | null;
  onCancel?: () => void;
}

const FileUploadProgress: React.FC<FileUploadProgressProps> = ({
  visible,
  fileName,
  progress,
  onCancel,
}) => {
  const theme = defaultTheme;

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) {return `${bytes} B`;}
    if (bytes < 1024 * 1024) {return `${(bytes / 1024).toFixed(1)} KB`;}
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  /**
   * 格式化进度百分比
   */
  const formatProgress = (progressValue: number): string => {
    return `${Math.round(progressValue * 100)}%`;
  };

  if (!visible || !progress) {
    return null;
  }

  const progressPercentage = progress.progress * 100;
  const isCompleted = progress.progress >= 1;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      statusBarTranslucent={true}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          {/* 文件图标 */}
          <View style={styles.iconContainer}>
            {isCompleted ? (
              <Ionicons name="checkmark-circle" size={48} color={theme.colors.success} />
            ) : (
              <View style={styles.loadingIconContainer}>
                <Ionicons name="document-outline" size={32} color={theme.colors.primary} />
                <ActivityIndicator
                  size="small"
                  color={theme.colors.primary}
                  style={styles.loadingIndicator}
                />
              </View>
            )}
          </View>

          {/* 文件名 */}
          <Text style={styles.fileName} numberOfLines={2}>
            {fileName}
          </Text>

          {/* 状态文本 */}
          <Text style={styles.statusText}>
            {isCompleted ? '上传完成' : '正在上传...'}
          </Text>

          {/* 进度条 */}
          <View style={styles.progressBarContainer}>
            <View style={styles.progressBarBackground}>
              <View
                style={[
                  styles.progressBarFill,
                  { width: `${progressPercentage}%` },
                ]}
              />
            </View>
          </View>

          {/* 进度信息 */}
          <View style={styles.progressInfo}>
            <Text style={styles.progressText}>
              {formatProgress(progress.progress)}
            </Text>
            <Text style={styles.sizeText}>
              {formatFileSize(progress.bytesWritten)} / {formatFileSize(progress.totalBytes)}
            </Text>
          </View>

          {/* 取消按钮（仅在上传中显示） */}
          {!isCompleted && onCancel && (
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>取消</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  container: {
    backgroundColor: defaultTheme.colors.backgroundPrimary,
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 320,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  iconContainer: {
    marginBottom: 16,
  },
  loadingIconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingIndicator: {
    position: 'absolute',
    top: -8,
    right: -8,
  },
  fileName: {
    fontSize: defaultTheme.fonts.bodyMedium,
    color: defaultTheme.colors.textPrimary,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 8,
  },
  statusText: {
    fontSize: defaultTheme.fonts.bodySmall,
    color: defaultTheme.colors.textSecondary,
    marginBottom: 16,
  },
  progressBarContainer: {
    width: '100%',
    marginBottom: 12,
  },
  progressBarBackground: {
    height: 6,
    backgroundColor: defaultTheme.colors.backgroundTertiary,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: defaultTheme.colors.primary,
    borderRadius: 3,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 16,
  },
  progressText: {
    fontSize: defaultTheme.fonts.bodySmall,
    color: defaultTheme.colors.textPrimary,
    fontWeight: '600',
  },
  sizeText: {
    fontSize: defaultTheme.fonts.bodySmall,
    color: defaultTheme.colors.textSecondary,
  },
  cancelButton: {
    paddingHorizontal: 24,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: defaultTheme.colors.textSecondary,
  },
  cancelButtonText: {
    fontSize: defaultTheme.fonts.bodySmall,
    color: defaultTheme.colors.textSecondary,
    fontWeight: '500',
  },
});

export default FileUploadProgress;
