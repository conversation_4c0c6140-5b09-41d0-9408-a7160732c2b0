import React, { useRef } from 'react';
import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import ActionButton from './ActionButton';
import { sharedStyles } from './styles';

// Copied from ScrollAwareBottomContainer, assuming same nav structure
type RootStackParamList = {
  Home: undefined;
  Explore: undefined;
  Schedule: undefined;
};
type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

export interface BottomActionBarProps {
  onOpenCamera: () => void;
  onOpenVoice: () => void;
  onNotesPress: (position: { x: number; y: number; width: number }) => void;
  showNotesMenu: boolean;
}

const BottomActionBar = ({
  onOpenCamera,
  onOpenVoice,
  onNotesPress,
  showNotesMenu,
}: BottomActionBarProps) => {
  const navigation = useNavigation<NavigationProp>();
  const textButtonRef = useRef<View>(null);

  const handleNotesPress = () => {
    if (textButtonRef.current) {
      textButtonRef.current.measure((
        x: number,
        y: number,
        width: number,
        height: number,
        pageX: number,
        pageY: number
      ) => {
        onNotesPress({ x: pageX, y: pageY, width });
      });
    }
  };

  return (
    <View style={sharedStyles.floatingBottomContainer}>
      <View style={sharedStyles.actionBarWrapper}>
        <View style={[
          sharedStyles.actionBarContainer,
          showNotesMenu && sharedStyles.actionBarContainerBlurred,
        ]}>
          {/* 左侧按钮组 */}
          <View style={sharedStyles.leftButtonGroup}>
            <ActionButton
              iconName="camera-outline"
              label="拍照"
              onPress={onOpenCamera}
              disabled={showNotesMenu}
            />
            <ActionButton
              ref={textButtonRef}
              iconName="text-outline"
              label="文字"
              onPress={handleNotesPress}
              isHighlighted={showNotesMenu}
              disabled={showNotesMenu}
            />
            <ActionButton
              iconName="mic-outline"
              label="语音"
              onPress={onOpenVoice}
              isHighlighted={false}
              disabled={false}
            />
          </View>

          {/* 右侧按钮 */}
          <View style={sharedStyles.rightButtonGroup}>
            <ActionButton
              iconName="compass-outline"
              label="探索"
              onPress={() => navigation.navigate('Explore')}
              disabled={showNotesMenu}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default BottomActionBar;
