import { StyleSheet } from 'react-native';

export const sharedStyles = StyleSheet.create({
  floatingBottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: 34, // 增加底部间距，更接近 iOS 原生位置
    pointerEvents: 'box-none',
  },
  actionBarWrapper: {
    alignItems: 'center',
    paddingBottom: 0,
  },
  actionBarContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16, // 减小左右边距
    width: '100%',
  },
  leftButtonGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 50,
    paddingVertical: 8,
    paddingHorizontal: 28, // 增加水平内边距
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 15,
  },
  rightButtonGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // 半透明背景
    borderRadius: 50,
    paddingVertical: 8,
    paddingHorizontal: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 15,
  },
  actionBarContainerBlurred: {
    opacity: 0.7, // 当菜单打开时，底部栏变暗
  },
});
