import React, { useRef } from 'react';
import { View, StyleSheet, Animated, TouchableOpacity, Text } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';

export interface ActionButtonProps {
  iconName: string;
  label: string;
  onPress: () => void;
  isHighlighted?: boolean;
  disabled?: boolean;
}

const ActionButton = React.forwardRef<View, ActionButtonProps>(({
  iconName,
  label,
  onPress,
  isHighlighted = false,
  disabled = false,
}, ref) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (!disabled) {
      ReactNativeHapticFeedback.trigger('impactLight');
      Animated.timing(scaleAnim, { toValue: 0.95, duration: 100, useNativeDriver: true }).start();
    }
  };

  const handlePressOut = () => {
    if (!disabled) {
      Animated.timing(scaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }).start(() => {
        onPress();
      });
    }
  };

  return (
    <TouchableOpacity
      ref={ref}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={disabled ? 1 : 0.7}
      style={styles.actionButtonTouch}
    >
      <Animated.View style={[
        styles.actionButtonContent,
        { transform: [{ scale: scaleAnim }] },
        disabled && !isHighlighted && { opacity: 0.5 },
      ]}>
        <Ionicons
          name={iconName}
          size={22}
          color={isHighlighted ? '#FFA500' : '#374151'}
        />
        <Text style={[
          styles.actionButtonLabel,
          isHighlighted && styles.actionButtonLabelHighlighted,
          disabled && !isHighlighted && { opacity: 0.5 },
        ]}>
          {label}
        </Text>
      </Animated.View>
    </TouchableOpacity>
  );
});

const styles = StyleSheet.create({
  actionButtonTouch: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 6,
    paddingHorizontal: 16, // 增加按钮的水平交互空间
    marginHorizontal: 2, // 增加按钮间的间距
  },
  actionButtonContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonLabel: {
    fontSize: 12,
    color: '#374151',
    marginTop: 4,
    fontWeight: '500',
  },
  actionButtonLabelHighlighted: {
    color: '#FFA500',
  },
});

export default ActionButton;
