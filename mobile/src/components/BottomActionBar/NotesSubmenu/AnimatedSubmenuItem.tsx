import React, { useRef, useState } from 'react';
import { StyleSheet, Animated, TouchableOpacity, Text } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

export interface AnimatedSubmenuItemProps {
  iconName: string;
  label: string;
  onPress: () => void;
  delay?: number;
  visible: boolean;
}

const AnimatedSubmenuItem = ({
  iconName,
  label,
  onPress,
  delay = 0,
  visible,
}: AnimatedSubmenuItemProps) => {
  const itemTranslateY = useRef(new Animated.Value(15)).current;
  const itemOpacity = useRef(new Animated.Value(0)).current;
  const [isPressed, setIsPressed] = useState(false);

  React.useEffect(() => {
    if (visible) {
      Animated.sequence([
        Animated.delay(delay),
        Animated.parallel([
          Animated.spring(itemTranslateY, {
            toValue: 0,
            tension: 120,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.timing(itemOpacity, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }),
        ]),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(itemTranslateY, {
          toValue: 10,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(itemOpacity, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [delay, itemOpacity, itemTranslateY, visible]);

  const handlePressIn = () => {
    setIsPressed(true);
    Animated.timing(itemTranslateY, {
      toValue: 2,
      duration: 50,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    setIsPressed(false);
    Animated.timing(itemTranslateY, {
      toValue: 0,
      duration: 100,
      useNativeDriver: true,
    }).start();

    // 立即执行点击事件
    onPress();
  };

  return (
    <TouchableOpacity
      style={[
        styles.submenuItem,
        isPressed && styles.submenuItemPressed,
      ]}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
    >
      <Animated.View style={{
        flexDirection: 'row',
        alignItems: 'center',
        transform: [{ translateY: itemTranslateY }],
        opacity: itemOpacity,
      }}>
        <Ionicons
          name={iconName}
          size={22}
          color={isPressed ? '#FFA500' : '#374151'}
        />
        <Text style={styles.submenuText}>{label}</Text>
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  submenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16, // 进一步增加纵向交互区域
    paddingHorizontal: 16, // 增加水平内边距
    borderRadius: 8,
    minHeight: 48, // 增加最小高度，提供更大的点击区域
  },
  submenuItemPressed: {
    backgroundColor: 'rgba(255, 165, 0, 0.1)', // 按压时的背景色
  },
  submenuText: {
    marginLeft: 10, // 增加图标和文字间距
    fontSize: 16, // 增加字体大小，从14增加到16
    color: '#374151',
    fontWeight: '600', // 增加字重，提升可读性
    letterSpacing: 0.2, // 增加字间距，提升可读性
  },
});

export default AnimatedSubmenuItem;
