import React, { useRef } from 'react';
import { View, StyleSheet, Animated, TouchableOpacity } from 'react-native';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import AnimatedSubmenuItem from './AnimatedSubmenuItem';

export interface NotesSubmenuProps {
  visible: boolean;
  onClose: () => void;
  buttonPosition: { x: number; y: number; width: number } | null;
  onSelectOption: (option: string) => void;
}

const NotesSubmenu = ({
  visible,
  onClose,
  buttonPosition,
  onSelectOption,
}: NotesSubmenuProps) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateAnim = useRef(new Animated.Value(20)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const itemsTranslateAnim = useRef(new Animated.Value(30)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(translateAnim, {
          toValue: 0,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 120,
          friction: 7,
          useNativeDriver: true,
        }),
        Animated.stagger(50, [
          Animated.spring(itemsTranslateAnim, {
            toValue: 0,
            tension: 100,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(translateAnim, {
          toValue: 15,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.9,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(itemsTranslateAnim, {
          toValue: 20,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, fadeAnim, translateAnim, scaleAnim, itemsTranslateAnim]);

  if (!visible || !buttonPosition) {
    return null;
  }

  const menuStyle = {
    position: 'absolute' as const,
    left: buttonPosition.x + (buttonPosition.width / 2) - 65, // 调整水平位置适应新宽度
    bottom: 110, // 从90增加到110，往上移动20pt
  };

  const handleOptionPress = (option: string) => {
    ReactNativeHapticFeedback.trigger('impactLight');
    onSelectOption(option);
  };

  return (
    <>
      <View style={styles.overlayContainer}>
        <TouchableOpacity
          style={[styles.submenuOverlay]}
          onPress={onClose}
          activeOpacity={1}
        />
      </View>
      <Animated.View
        style={[
          styles.submenuContainer,
          menuStyle,
          {
            opacity: fadeAnim,
            transform: [
              { translateY: translateAnim },
              { scale: scaleAnim },
            ],
            zIndex: 1000,
          },
        ]}
      >
        <AnimatedSubmenuItem
          iconName="document-text-outline"
          label="便签"
          onPress={() => handleOptionPress('note')}
          delay={0}
          visible={visible}
        />
        <AnimatedSubmenuItem
          iconName="create-outline"
          label="笔记"
          onPress={() => handleOptionPress('edit-3')}
          delay={50}
          visible={visible}
        />
        <AnimatedSubmenuItem
          iconName="calendar-outline"
          label="日程"
          onPress={() => handleOptionPress('calendar')}
          delay={100}
          visible={visible}
        />
      </Animated.View>
    </>
  );
};

const styles = StyleSheet.create({
  overlayContainer: {
    position: 'absolute',
    top: -1000, // 确保覆盖整个屏幕
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,
  },
  submenuOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  submenuContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    paddingVertical: 4, // 进一步减少容器内边距
    paddingHorizontal: 2, // 减少水平内边距，让子项padding发挥作用
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 12,
    width: 130, // 增加宽度以适应更大的文字和图标
  },
});

export default NotesSubmenu;
