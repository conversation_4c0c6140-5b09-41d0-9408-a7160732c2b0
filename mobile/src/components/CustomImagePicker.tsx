import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  Modal,
  SafeAreaView,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  Dimensions,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import type { PhotoIdentifier } from '@react-native-camera-roll/camera-roll';
import { ensurePermission } from '../services/PermissionService';

interface CustomImagePickerProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (uris: string[]) => void;
  selectionLimit?: number;
  mode?: 'multiple' | 'single';
}

const { width } = Dimensions.get('window');
const imageSize = (width - 15) / 4; // 4 images per row with 5px total spacing

const CustomImagePicker: React.FC<CustomImagePickerProps> = ({
  isVisible,
  onClose,
  onSelect,
  selectionLimit = 10,
  mode = 'multiple',
}) => {
  const [photos, setPhotos] = useState<PhotoIdentifier[]>([]);
  const [selectedUris, setSelectedUris] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 使用useRef来存储分页信息，避免触发不必要的重渲染
  const pageInfoRef = useRef({
    hasNextPage: true,
    nextCursor: undefined as string | undefined,
  });



  const fetchPhotos = useCallback(async () => {
    setIsLoading(true);
    try {
      const { edges, page_info } = await CameraRoll.getPhotos({
        first: 40, // 增加每次加载的数量，提升体验
        after: pageInfoRef.current.nextCursor,
        assetType: 'Photos',
      });

      // 数据去重逻辑
      setPhotos(prevPhotos => {
        const existingUris = new Set(prevPhotos.map(p => p.node.image.uri));
        const newPhotos = edges.filter(edge => !existingUris.has(edge.node.image.uri));
        return [...prevPhotos, ...newPhotos];
      });

      pageInfoRef.current.hasNextPage = page_info.has_next_page;
      pageInfoRef.current.nextCursor = page_info.end_cursor;

    } catch (error) {
      console.error('获取照片失败:', error);
      Alert.alert('错误', '无法加载照片');
    } finally {
      setIsLoading(false);
    }
    // `useCallback` 的依赖数组为空，确保 `fetchPhotos` 是一个稳定的函数
  }, []);

  useEffect(() => {
    if (isVisible) {
      // 重置所有状态
      setPhotos([]);
      setSelectedUris([]);
      pageInfoRef.current = { hasNextPage: true, nextCursor: undefined };

      const checkPermissionsAndFetch = async () => {
        const hasPermission = await ensurePermission('photoLibrary');
        if (!hasPermission) {
          onClose(); // 没有权限直接关闭，PermissionService已经显示了权限弹窗
          return;
        }
        fetchPhotos();
      };

      checkPermissionsAndFetch();
    }
    // 依赖项中包含 fetchPhotos，但因为它是稳定的，所以 effect 只在 isVisible 变化时运行
  }, [isVisible, fetchPhotos, onClose]);

  const handleSelectPhoto = (photo: PhotoIdentifier) => {
    const uri = photo.node.image.uri;
    if (mode === 'single') {
      onSelect([uri]);
      onClose();
      return;
    }

    const isSelected = selectedUris.includes(uri);

    if (isSelected) {
      setSelectedUris(prev => prev.filter(item => item !== uri));
    } else {
      if (selectedUris.length >= selectionLimit) {
        Alert.alert('提示', `最多只能选择 ${selectionLimit} 张图片`);
        return;
      }
      setSelectedUris(prev => [...prev, uri]);
    }
  };

  const handleLoadMore = () => {
    if (!isLoading && pageInfoRef.current.hasNextPage) {
      fetchPhotos();
    }
  };

  const handleDone = () => {
    onSelect(selectedUris);
    onClose();
  };

  const renderPhoto = ({ item }: { item: PhotoIdentifier }) => {
    const uri = item.node.image.uri;
    const isSelected = selectedUris.includes(uri);
    const selectionIndex = selectedUris.indexOf(uri) + 1;

    return (
      <TouchableOpacity onPress={() => handleSelectPhoto(item)} style={styles.imageContainer}>
        <Image
          source={{ uri }}
          style={styles.image}
        />
        {isSelected && (
          <View style={styles.selectionOverlay}>
            <View style={styles.selectionBadge}>
              <Text style={styles.selectionText}>{selectionIndex}</Text>
            </View>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <Modal visible={isVisible} animationType="slide" onRequestClose={onClose}>
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.headerButton}>
            <Text style={styles.buttonText}>取消</Text>
          </TouchableOpacity>
          {mode === 'multiple' ? (
            <TouchableOpacity onPress={handleDone} disabled={selectedUris.length === 0} style={styles.headerButton}>
              <Text style={[styles.buttonText, selectedUris.length > 0 ? styles.doneButtonActive : styles.doneButtonInactive]}>
                完成 ({selectedUris.length})
              </Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.headerButton} />
          )}
          <Text style={styles.title}>选择图片</Text>
        </View>
        <FlatList
          data={photos}
          renderItem={renderPhoto}
          keyExtractor={item => item.node.image.uri}
          numColumns={4}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={isLoading && photos.length > 0 ? <ActivityIndicator style={{ marginVertical: 20 }} /> : null}
          ListEmptyComponent={isLoading ? <ActivityIndicator style={{ marginTop: 100 }}/> : <View><Text style={styles.emptyText}>相册为空</Text></View>}
        />
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#EFEFEF',
    position: 'relative',
  },
  headerButton: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    position: 'absolute',
    left: 0,
    right: 0,
    textAlign: 'center',
    fontSize: 18,
    fontWeight: 'bold',
    zIndex: -1,
  },
  buttonText: {
    fontSize: 16,
  },
  doneButtonActive: {
     color: '#007AFF',
     fontWeight: 'bold',
  },
  doneButtonInactive: {
     color: '#BDBDBD',
  },
  imageContainer: {
    width: imageSize,
    height: imageSize,
    margin: 2,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  selectionOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
  },
  selectionBadge: {
    position: 'absolute',
    top: 5,
    right: 5,
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#fff',
  },
  selectionText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 100,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
});

export default CustomImagePicker;
