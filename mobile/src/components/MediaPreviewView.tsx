import React, { useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  Dimensions,
  Alert,
  Platform,
} from 'react-native';
import { BlurView } from '@react-native-community/blur';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { getDisplayUri } from '../utils/media';
import { useCameraStore } from '../stores/cameraStore';


interface MediaItem {
  id: string;
  uri: string;
}

interface MediaPreviewViewProps {
  mediaList: MediaItem[];
  currentIndex: number;
  onChangeIndex: (index: number) => void;
  onClose: () => void;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const ActionsBarBackground = () => {
  if (Platform.OS === 'ios') {
    return (
      <BlurView
        style={styles.absoluteFill}
        blurType="dark"
        blurAmount={10}
      />
    );
  }
  return <View style={[styles.absoluteFill, styles.androidBlurView]} />;
};

const MediaPreviewView: React.FC<MediaPreviewViewProps> = ({
  mediaList,
  currentIndex,
  onChangeIndex,
  onClose,
}) => {
  const flatListRef = useRef<FlatList<any>>(null);
  const viewRef = useRef<View>(null);
  const removeMedia = useCameraStore(state => state.removeMedia);

  const handleDelete = () => {
    const mediaToDelete = mediaList[currentIndex];
    if (!mediaToDelete) {return;}

    Alert.alert(
      '删除照片',
      '您确定要删除这张照片吗？此操作无法撤销。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => {
            removeMedia(mediaToDelete.id);
          },
        },
      ],
    );
  };

  const renderMainItem = ({ item }: { item: any }) => (
    <View style={styles.mainImageWrapper}>
      <Image
        source={{ uri: getDisplayUri(item.uri) }}
        style={styles.mainImage}
        resizeMode="contain"
      />
    </View>
  );

  const renderThumbnail = ({ item, index }: { item: any; index: number }) => {
    const isSelected = index === currentIndex;
    return (
      <TouchableOpacity
        onPress={() => onChangeIndex(index)}
        style={[styles.thumbnailTouchable, isSelected && styles.thumbnailSelected]}
      >
        <Image
          source={{ uri: getDisplayUri(item.uri) }}
          style={styles.thumbnail}
        />
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container} ref={viewRef}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.headerButton}>
          <Text style={styles.headerButtonText}>关闭</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{`${currentIndex + 1} / ${mediaList.length}`}</Text>
        <View style={{ width: 50 }} />
      </View>

      <FlatList
        ref={flatListRef}
        data={mediaList}
        renderItem={renderMainItem}
        keyExtractor={item => item.id}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={event => {
          const newIndex = Math.round(event.nativeEvent.contentOffset.x / screenWidth);
          onChangeIndex(newIndex);
        }}
        initialScrollIndex={currentIndex}
        getItemLayout={(data, index) => ({
          length: screenWidth,
          offset: screenWidth * index,
          index,
        })}
      />

      <View style={styles.bottomContainer}>
        <FlatList
          data={mediaList}
          renderItem={renderThumbnail}
          keyExtractor={item => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.thumbnailList}
          contentContainerStyle={styles.thumbnailListContent}
        />
        <View style={styles.actionsContainer}>
          <ActionsBarBackground />
          <TouchableOpacity style={[styles.actionButton, styles.disabledButton]} disabled>
            <Ionicons name="copy-outline" size={24} color="#8E8E93" />
            <Text style={[styles.actionButtonText, styles.disabledButtonText]}>复制文字</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleDelete} style={styles.actionButton}>
            <Ionicons name="trash-outline" size={24} color="#ff453a" />
            <Text style={[styles.actionButtonText, { color: '#ff453a' }]}>删除</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 90,
    paddingTop: 40,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    zIndex: 10,
    paddingHorizontal: 15,
  },
  headerButton: {
    padding: 5,
  },
  headerButtonText: {
    color: 'white',
    fontSize: 16,
  },
  headerTitle: {
    color: 'white',
    fontSize: 17,
    fontWeight: '600',
  },
  mainImageWrapper: {
    width: screenWidth,
    height: screenHeight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mainImage: {
    width: '100%',
    height: '100%',
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  thumbnailList: {
    marginBottom: 20,
  },
  thumbnailListContent: {
    paddingHorizontal: 10,
  },
  thumbnailTouchable: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginHorizontal: 5,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  thumbnailSelected: {
    borderColor: '#FF9500',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
    borderRadius: 6,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    height: 80,
    paddingBottom: 20,
    borderTopWidth: Platform.OS === 'ios' ? 0 : 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  absoluteFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
  },
  androidBlurView: {
    backgroundColor: 'rgba(20, 20, 20, 0.8)',
  },
  actionButton: {
    alignItems: 'center',
    padding: 10,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    marginTop: 4,
  },
  disabledButton: {
    opacity: 0.7,
  },
  disabledButtonText: {
    color: '#8E8E93',
  },
  resultContainer: {
    position: 'absolute',
    top: 20,
    left: 20,
    right: 20,
    bottom: 120,
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 10,
    padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  ocrText: {
    color: 'white',
    fontSize: 16,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  button: {
    backgroundColor: '#FF7A00',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default MediaPreviewView;
