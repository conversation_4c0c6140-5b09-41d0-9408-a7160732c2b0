import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { defaultTheme } from '../styles/theme';
import { getFileIcon } from '../utils/fileTypes';

// 文件消息数据接口
export interface FileMessageData {
  uri: string;
  name: string;
  size: number;
  type: string;
  originalUri?: string;
}

interface FileMessageBubbleProps {
  fileData: FileMessageData;
  onPress: () => void;
  onLongPress?: () => void;
  isUser?: boolean;
}

const FileMessageBubble: React.FC<FileMessageBubbleProps> = ({
  fileData,
  onPress,
  onLongPress,
  isUser = true,
}) => {
  const theme = defaultTheme;

  /**
   * 格式化文件大小显示
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) {return `${bytes} B`;}
    if (bytes < 1024 * 1024) {return `${(bytes / 1024).toFixed(1)} KB`;}
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  /**
   * 获取文件类型描述
   */
  const getFileTypeDescription = (mimeType: string, fileName: string): string => {
    const extension = fileName?.toLowerCase().split('.').pop();

    if (mimeType.includes('pdf')) {return 'PDF';}
    if (mimeType.includes('ofd')) {return 'OFD';}
    if (mimeType.includes('word') || extension === 'doc' || extension === 'docx') {return 'Word';}
    if (mimeType.includes('excel') || extension === 'xls' || extension === 'xlsx') {return 'Excel';}
    if (mimeType.includes('powerpoint') || extension === 'ppt' || extension === 'pptx') {return 'PowerPoint';}
    if (extension === 'wps' || extension === 'et' || extension === 'dps') {return 'WPS';}
    if (mimeType.includes('text') || extension === 'txt') {return '文本';}
    if (extension === 'rtf') {return '富文本';}
    if (extension === 'csv') {return 'CSV数据';}

    return '文档';
  };

  /**
   * 根据文件类型获取主题色
   */
  const getFileTypeColor = (mimeType: string, fileName: string): string => {
    const extension = fileName?.toLowerCase().split('.').pop();

    // PDF - 红色
    if (mimeType.includes('pdf')) {
      return '#FF4D4F';
    }

    // OFD - 蓝色
    if (mimeType.includes('ofd')) {
      return '#1890FF';
    }

    // Word文档 - 蓝色
    if (mimeType.includes('word') || extension === 'doc' || extension === 'docx') {
      return '#1890FF';
    }

    // Excel和PowerPoint文档 - 绿色
    if (mimeType.includes('excel') || mimeType.includes('powerpoint') ||
        extension === 'xls' || extension === 'xlsx' ||
        extension === 'ppt' || extension === 'pptx') {
      return '#52C41A';
    }

    // WPS文档 - 橙色
    if (extension === 'wps' || extension === 'et' || extension === 'dps') {
      return '#FA8C16';
    }

    // 文本文件 - 紫色
    if (mimeType.includes('text') || extension === 'txt' || extension === 'rtf' || extension === 'csv') {
      return '#722ED1';
    }

    // 默认 - 灰色
    return '#8C8C8C';
  };

  const fileIcon = getFileIcon(fileData.type || '', fileData.name || '');
  const fileTypeColor = getFileTypeColor(fileData.type || '', fileData.name || '');
  const fileTypeDescription = getFileTypeDescription(fileData.type || '', fileData.name || '');

  return (
    <TouchableOpacity
      style={[
        styles.container,
        isUser ? styles.userContainer : styles.assistantContainer,
      ]}
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.7}
    >
      {/* 文件图标 */}
      <View style={[styles.iconContainer, { backgroundColor: `${fileTypeColor}15` }]}>
        <Ionicons
          name={fileIcon}
          size={28}
          color={fileTypeColor}
        />
      </View>

      {/* 文件信息 */}
      <View style={styles.fileInfo}>
        <Text
          style={[
            styles.fileName,
            { color: isUser ? theme.colors.textPrimary : theme.colors.textPrimary },
          ]}
          numberOfLines={2}
        >
          {fileData.name || '未知文件'}
        </Text>

        <View style={styles.fileDetails}>
          <Text style={styles.fileType}>
            {fileTypeDescription}
          </Text>
          <Text style={styles.fileSeparator}>•</Text>
          <Text style={styles.fileSize}>
            {formatFileSize(fileData.size)}
          </Text>
        </View>
      </View>

      {/* 预览指示器 */}
      <View style={styles.previewIndicator}>
        <Ionicons
          name="chevron-forward"
          size={16}
          color={theme.colors.textSecondary}
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: defaultTheme.colors.backgroundPrimary,
    borderRadius: 12,
    padding: 12,
    marginVertical: 4,
    borderWidth: 1,
    borderColor: defaultTheme.colors.backgroundTertiary,
    minHeight: 70,
    maxWidth: 280,
  },
  userContainer: {
    alignSelf: 'flex-end',
    backgroundColor: defaultTheme.colors.backgroundSecondary,
  },
  assistantContainer: {
    alignSelf: 'flex-start',
    backgroundColor: defaultTheme.colors.backgroundPrimary,
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  fileInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  fileName: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 18,
    marginBottom: 4,
  },
  fileDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fileType: {
    fontSize: 12,
    color: defaultTheme.colors.textSecondary,
    fontWeight: '400',
  },
  fileSeparator: {
    fontSize: 12,
    color: defaultTheme.colors.textSecondary,
    marginHorizontal: 6,
  },
  fileSize: {
    fontSize: 12,
    color: defaultTheme.colors.textSecondary,
    fontWeight: '400',
  },
  previewIndicator: {
    marginLeft: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default FileMessageBubble;
