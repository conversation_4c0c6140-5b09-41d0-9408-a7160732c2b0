import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
} from 'react-native';

interface SplashScreenProps {
  onFinish: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onFinish }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const breatheAnim = useRef(new Animated.Value(1)).current;
  const dotsAnim = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ]).current;

  useEffect(() => {
    // 主要动画序列
    Animated.sequence([
      // 淡入和缩放动画
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      // 延迟后开始加载动画
      Animated.delay(200),
    ]).start();

    // 呼吸动画（图标）
    const breatheAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(breatheAnim, {
          toValue: 1.05,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(breatheAnim, {
          toValue: 0.95,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    );
    breatheAnimation.start();

    // 加载点动画
    const createDotAnimation = (index: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.delay(index * 100),
          Animated.timing(dotsAnim[index], {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(dotsAnim[index], {
            toValue: 0.3,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.delay((dotsAnim.length - index - 1) * 100),
        ])
      );
    };

    const dotAnimations = dotsAnim.map((_, index) => createDotAnimation(index));
    Animated.parallel(dotAnimations).start();

    // 自动跳转
    const timer = setTimeout(() => {
      onFinish();
    }, 3000);

    return () => {
      clearTimeout(timer);
      breatheAnimation.stop();
      dotAnimations.forEach(anim => anim.stop());
    };
  }, [fadeAnim, scaleAnim, breatheAnim, dotsAnim, onFinish]);

  const handleSkip = () => {
    onFinish();
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handleSkip} activeOpacity={1}>
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {/* 品牌图标 */}
        <Animated.View
          style={[
            styles.iconContainer,
            {
              transform: [{ scale: breatheAnim }],
            },
          ]}
        >
          <View style={styles.catIcon}>
            <Text style={styles.catText}>🐱</Text>
          </View>
        </Animated.View>

        {/* 产品名称 */}
        <Text style={styles.productName}>公职猫</Text>

        {/* 核心价值主张 */}
        <Text style={styles.slogan}>U盘级私密，秘书般懂你</Text>

        {/* 加载动画 */}
        <View style={styles.loadingContainer}>
          {dotsAnim.map((anim, index) => (
            <Animated.View
              key={index}
              style={[
                styles.dot,
                {
                  opacity: anim,
                  transform: [
                    {
                      scale: anim.interpolate({
                        inputRange: [0.3, 1],
                        outputRange: [0.8, 1.2],
                        extrapolate: 'clamp',
                      }),
                    },
                  ],
                },
              ]}
            />
          ))}
        </View>

        {/* 目标用户说明 */}
        <Text style={styles.targetUser}>为体制内中青年干部而生</Text>
      </Animated.View>
    </TouchableOpacity>
  );
};

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    width: width * 0.8,
  },
  iconContainer: {
    marginBottom: 24,
  },
  catIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#FF8C00',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#FF8C00',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  catText: {
    fontSize: 48,
    textAlign: 'center',
  },
  productName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
    textAlign: 'center',
  },
  slogan: {
    fontSize: 18,
    color: '#FF8C00',
    marginBottom: 32,
    textAlign: 'center',
    textShadowColor: '#FF8C00',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 8,
  },
  loadingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 60,
    height: 20,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF8C00',
    marginHorizontal: 3,
    shadowColor: '#FF8C00',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 4,
  },
  targetUser: {
    fontSize: 14,
    color: '#CCCCCC',
    textAlign: 'center',
    position: 'absolute',
    bottom: -height * 0.15,
  },
});

export default SplashScreen;
