import React, { useState, useCallback } from 'react';
import { View, StyleSheet, TouchableWithoutFeedback } from 'react-native';
import DraggableFlatList, {
  RenderItemParams,
  DragEndParams,
} from 'react-native-draggable-flatlist';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';


import TaskCard from './TaskCard';
import ShowAllTasksCard from './ShowAllTasksCard';
import { StickyNote } from '../screens/AllStickyNotesScreen'; // Reuse type

// Mock Data - 优化为体制内表达习惯
const MOCK_TASKS: StickyNote[] = [
  { id: '1', title: '党校青干班\n培训学习', category: 'study', order: 0 },
  { id: '2', title: '年度考核\n述职准备', category: 'work', order: 1 },
  { id: '3', title: '季度工作\n总结报告', category: 'report', order: 2 },
  { id: '4', title: '省厅调研组\n接待工作', category: 'meeting', order: 3 },
  { id: '5', title: '数字政府峰会\n参会学习', category: 'trip', order: 4 },
  { id: '6', title: '会议新闻稿\n撰写发布', category: 'report', order: 5 },
  { id: '7', title: '组织部讲座\n线上参与', category: 'study', order: 6 },
];

const MAX_VISIBLE_TASKS = 4;

// Param list for navigation
type RootStackParamList = {
  AllStickyNotes: { notes: StickyNote[] };
  // other screens
};
type NavigationProp = NativeStackNavigationProp<RootStackParamList>;


const ImportantTasksContainer = () => {
  const [tasks, setTasks] = useState(MOCK_TASKS);
  const [isDragging, setIsDragging] = useState(false);
  const navigation = useNavigation<NavigationProp>();

  const tasksToShow = tasks.slice(0, MAX_VISIBLE_TASKS);
  const showMoreButton = tasks.length > MAX_VISIBLE_TASKS;

  const renderItem = useCallback(
    ({ item, drag, isActive }: RenderItemParams<StickyNote>) => {
      const handleLongPress = () => {
        ReactNativeHapticFeedback.trigger('impactMedium', {
          enableVibrateFallback: true,
          ignoreAndroidSystemSettings: false,
        });
        setIsDragging(true);
        drag?.(); // `drag` can be undefined if `DraggableFlatList` is not ready
      };

      const handlePress = () => {
        if (isDragging) {
          setIsDragging(false);
        } else {
          console.log('Task pressed:', item.id);
        }
      };

      return (
        <TaskCard
          task={item}
          onPress={handlePress}
          onLongPress={handleLongPress}
          isActive={isActive}
          isWobbling={isDragging}
        />
      );
    },
    [isDragging],
  );

  const renderFooter = useCallback(() => {
    return showMoreButton ? (
      <ShowAllTasksCard
        onPress={() => navigation.navigate('AllStickyNotes', { notes: tasks })}
      />
    ) : null;
  }, [showMoreButton, navigation, tasks]);

  const handleDragEnd = ({ data }: DragEndParams<StickyNote>) => {
    // Note: The `data` from onDragEnd only contains the draggable items.
    // We must merge this with the non-visible items to get the full new list.
    const updatedVisibleTasks = data.map((task, index) => ({ ...task, order: index }));
    const nonVisibleTasks = tasks.slice(tasksToShow.length); // Use tasksToShow to get correct slice
    const newFullTaskList = [...updatedVisibleTasks, ...nonVisibleTasks];

    setTasks(newFullTaskList);
    setIsDragging(false);
    // Here you would typically also persist this new order to your state management or backend.
  };

  if (!tasks || tasks.length === 0) {
    return null; // Don't render anything if there are no tasks
  }

  return (
    <TouchableWithoutFeedback onPress={() => setIsDragging(false)}>
      <View style={styles.container}>
        <DraggableFlatList
          horizontal
          data={tasksToShow}
          onDragEnd={handleDragEnd}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          showsHorizontalScrollIndicator={false}
          dragItemOverflow={false} // 防止拖拽时overflow
          ListFooterComponent={renderFooter}
          contentContainerStyle={styles.listContent}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 112, // 从104增加到112，匹配TaskCard的新高度96 + padding
    backgroundColor: '#F7F8FA', // 添加不透明背景色，与ScheduleList背景色一致
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    paddingBottom: 8,
    // paddingTop: 4, // 添加顶部内边距
  },
  listContent: {
    paddingLeft: 16,
    paddingRight: 20, // 从120减少到20，因为卡片间距已经增加
    alignItems: 'center',
  },
  // No longer need taskCardContainer, margin is applied in TaskCard now
});

export default ImportantTasksContainer;
