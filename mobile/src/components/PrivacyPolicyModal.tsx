/**
 * 隐私政策弹框组件
 * 用于首次启动时显示隐私政策同意界面
 * 优化版本：简洁布局，遵循公职猫设计系统
 */

import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { PrivacyPolicyManager } from '../utils/privacyPolicyManager';
import { defaultTheme } from '../styles/theme';

interface PrivacyPolicyModalProps {
  visible: boolean;
  onAgree: () => void;
  onDisagree: () => void;
  onViewFullPolicy?: () => void; // 新增可选的回调函数
}


export const PrivacyPolicyModal: React.FC<PrivacyPolicyModalProps> = ({
  visible,
  onAgree,
  onDisagree,
  onViewFullPolicy,
}) => {
  const [isAgreeing, setIsAgreeing] = useState(false);
  const theme = defaultTheme;

  const handleAgree = async () => {
    try {
      ReactNativeHapticFeedback.trigger('impactMedium');
      setIsAgreeing(true);
      await PrivacyPolicyManager.recordUserAgreement();
      ReactNativeHapticFeedback.trigger('notificationSuccess');
      onAgree();
    } catch (error) {
      console.error('[PrivacyPolicyModal] 保存同意状态失败:', error);
      ReactNativeHapticFeedback.trigger('notificationError');
      Alert.alert('温馨提示', '保存设置时遇到问题，请重试');
    } finally {
      setIsAgreeing(false);
    }
  };

  const handleViewFullPolicy = () => {
    ReactNativeHapticFeedback.trigger('impactLight');
    // 如果提供了回调函数，使用回调；否则使用默认的外部链接
    if (onViewFullPolicy) {
      onViewFullPolicy();
    } else {
      // 默认行为：打开外部链接
      const url = PrivacyPolicyManager.getPrivacyPolicyUrl();
      import('react-native').then(({ Linking }) => {
        Linking.openURL(url).catch(() => {
          ReactNativeHapticFeedback.trigger('notificationError');
          Alert.alert('温馨提示', '无法打开隐私政策页面，请检查网络连接');
        });
      });
    }
  };

  const handleDisagree = () => {
    ReactNativeHapticFeedback.trigger('impactLight');
    Alert.alert(
      '温馨提示',
      '为了保护您的权益，使用公职猫需要您同意隐私政策。如果不同意，将无法使用本应用。',
      [
        {
          text: '重新考虑',
          style: 'cancel',
        },
        {
          text: '退出应用',
          style: 'destructive',
          onPress: () => {
            ReactNativeHapticFeedback.trigger('impactHeavy');
            onDisagree();
          },
        },
      ]
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={false}
      statusBarTranslucent={true}
    >
      <SafeAreaView style={styles(theme).container}>
        {/* 精致化Header */}
        <View style={styles(theme).header}>
          <View style={styles(theme).headerContent}>
            <View style={styles(theme).logoContainer}>
              <Text style={styles(theme).logoIcon}>🐱</Text>
            </View>
            <Text style={styles(theme).title}>欢迎使用公职猫</Text>
            <Text style={styles(theme).subtitle}>U盘级私密，秘书般懂你</Text>
          </View>
        </View>

        <ScrollView style={styles(theme).content} showsVerticalScrollIndicator={false}>
          {/* 价值主张描述 */}
          <View style={styles(theme).valueProposition}>
            <Text style={styles(theme).valueText}>
              公职猫致力于帮助您高效管理时间资产和数字资产，让繁忙的工作变得井然有序。通过智能化的文档处理、日程管理和信息整理，助您获得更多成功和更美好的生活。
            </Text>
          </View>



          {/* 核心承诺 - 精致卡片 */}
          <View style={styles(theme).section}>
            <Text style={styles(theme).sectionTitle}>我们承诺不会：</Text>
            <View style={styles(theme).promiseCard}>
              <View style={styles(theme).promiseItem}>
                <Text style={styles(theme).promiseIcon}>🚫</Text>
                <Text style={styles(theme).promiseText}>将您的个人信息出售给第三方</Text>
              </View>
              <View style={styles(theme).promiseItem}>
                <Text style={styles(theme).promiseIcon}>📢</Text>
                <Text style={styles(theme).promiseText}>用于商业广告推送</Text>
              </View>
              <View style={styles(theme).promiseItem}>
                <Text style={styles(theme).promiseIcon}>🔒</Text>
                <Text style={styles(theme).promiseText}>与其他应用共享您的个人数据</Text>
              </View>
            </View>
          </View>

          {/* 查看完整政策链接 */}
          <TouchableOpacity
            style={styles(theme).viewFullPolicy}
            onPress={handleViewFullPolicy}
            activeOpacity={0.7}
          >
            <Text style={styles(theme).viewFullPolicyText}>查看完整隐私政策</Text>
          </TouchableOpacity>
        </ScrollView>

        {/* 底部操作区域 */}
        <View style={styles(theme).footer}>
          <Text style={styles(theme).footerText}>
            继续使用即表示您同意我们的隐私政策
          </Text>
          <View style={styles(theme).buttonContainer}>
            <TouchableOpacity
              style={styles(theme).disagreeButton}
              onPress={handleDisagree}
              disabled={isAgreeing}
              activeOpacity={0.8}
            >
              <Text style={styles(theme).disagreeButtonText}>不同意</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles(theme).agreeButton, isAgreeing && styles(theme).agreeButtonDisabled]}
              onPress={handleAgree}
              disabled={isAgreeing}
              activeOpacity={0.8}
            >
              <Text style={styles(theme).agreeButtonText}>
                {isAgreeing ? '处理中...' : '同意并继续'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = (theme: typeof defaultTheme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.backgroundSecondary,
  },
  header: {
    backgroundColor: '#FF8C00', // 温暖橙色
    borderBottomLeftRadius: theme.borderRadius.large,
    borderBottomRightRadius: theme.borderRadius.large,
    ...theme.shadows.medium,
  },
  headerContent: {
    alignItems: 'center',
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
  },
  logoContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.lg,
    ...theme.shadows.small,
  },
  logoIcon: {
    fontSize: 32,
  },
  title: {
    fontSize: theme.fonts.titleLarge,
    fontWeight: theme.fontWeights.bold,
    color: theme.colors.backgroundSecondary,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: theme.fonts.bodyLarge,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: theme.fontWeights.medium,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing.lg,
  },
  valueProposition: {
    marginVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.sm,
  },
  valueText: {
    fontSize: theme.fonts.bodyMedium,
    color: theme.colors.textSecondary,
    lineHeight: 20,
    textAlign: 'center',
  },
  privacyCore: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.lg,
    marginVertical: theme.spacing.lg,
    ...theme.shadows.small,
  },
  coreTitle: {
    color: theme.colors.backgroundSecondary,
    fontSize: theme.fonts.bodyLarge,
    fontWeight: theme.fontWeights.bold,
    marginBottom: theme.spacing.sm,
  },
  coreText: {
    color: theme.colors.backgroundSecondary,
    fontSize: theme.fonts.bodyMedium,
    lineHeight: 20,
  },
  section: {
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.fonts.bodyLarge,
    fontWeight: theme.fontWeights.bold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.md,
  },
  permissionNotice: {
    backgroundColor: 'rgba(255, 140, 0, 0.1)', // 淡橙色背景
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    borderLeftWidth: 3,
    borderLeftColor: '#FF8C00',
  },
  permissionNoticeText: {
    fontSize: theme.fonts.bodySmall,
    color: theme.colors.textSecondary,
    lineHeight: 18,
    fontStyle: 'italic',
  },
  permissionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: theme.spacing.md,
  },
  permissionCard: {
    width: '48%',
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: theme.borderRadius.large,
    padding: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    alignItems: 'center',
    ...theme.shadows.medium,
    borderWidth: 1,
    borderColor: 'rgba(255, 140, 0, 0.1)', // 淡橙色边框
  },
  permissionIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 140, 0, 0.1)', // 淡橙色背景
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.md,
  },
  permissionIcon: {
    fontSize: 24,
  },
  permissionTitle: {
    fontSize: theme.fonts.bodyLarge,
    fontWeight: theme.fontWeights.bold,
    color: theme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: theme.spacing.xs,
  },
  permissionDesc: {
    fontSize: theme.fonts.bodySmall,
    color: '#FF8C00', // 橙色，突出显示获取时机
    textAlign: 'center',
    lineHeight: 16,
    fontWeight: theme.fontWeights.medium,
    marginBottom: theme.spacing.xs,
  },
  permissionTiming: {
    fontSize: theme.fonts.bodySmall,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
  promiseCard: {
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: theme.borderRadius.large,
    padding: theme.spacing.lg,
    ...theme.shadows.small,
    borderLeftWidth: 4,
    borderLeftColor: '#FF8C00', // 温暖橙色
  },
  promiseItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  promiseIcon: {
    fontSize: 18,
    marginRight: theme.spacing.md,
    width: 24,
    textAlign: 'center',
  },
  promiseText: {
    flex: 1,
    fontSize: theme.fonts.bodyMedium,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  viewFullPolicy: {
    backgroundColor: '#FF8C00', // 温暖橙色
    borderRadius: theme.borderRadius.large,
    padding: theme.spacing.lg,
    alignItems: 'center',
    marginVertical: theme.spacing.lg,
    ...theme.shadows.medium,
    borderWidth: 0,
  },
  viewFullPolicyText: {
    color: theme.colors.backgroundSecondary,
    fontSize: theme.fonts.bodyLarge,
    fontWeight: theme.fontWeights.bold,
  },
  footer: {
    padding: theme.spacing.xl,
    backgroundColor: theme.colors.backgroundSecondary,
    borderTopLeftRadius: theme.borderRadius.large,
    borderTopRightRadius: theme.borderRadius.large,
    ...theme.shadows.medium,
  },
  footerText: {
    fontSize: theme.fonts.bodySmall,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    lineHeight: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.xs,
  },
  disagreeButton: {
    flex: 0.4,
    backgroundColor: theme.colors.backgroundTertiary,
    borderRadius: theme.borderRadius.large,
    paddingVertical: theme.spacing.lg,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.backgroundTertiary,
    ...theme.shadows.small,
    marginRight: theme.spacing.sm,
  },
  disagreeButtonText: {
    color: theme.colors.textSecondary,
    fontSize: theme.fonts.bodyMedium,
    fontWeight: theme.fontWeights.medium,
  },
  agreeButton: {
    flex: 0.55,
    backgroundColor: '#FF8C00', // 温暖橙色
    borderRadius: theme.borderRadius.large,
    paddingVertical: theme.spacing.lg,
    alignItems: 'center',
    ...theme.shadows.medium,
  },
  agreeButtonDisabled: {
    backgroundColor: theme.colors.textTertiary,
    shadowOpacity: 0,
    elevation: 0,
  },
  agreeButtonText: {
    color: theme.colors.backgroundSecondary,
    fontSize: theme.fonts.bodyMedium,
    fontWeight: theme.fontWeights.medium,
  },
});

export default PrivacyPolicyModal;