import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Modal,
} from 'react-native';

export interface ErrorInfo {
  code: string;
  message: string;
  originalError?: string;
  attempts?: number;
  shouldRetry?: boolean;
  suggestedAction?: string;
  timestamp?: string;
}

interface ErrorHandlerProps {
  error: ErrorInfo | null;
  visible: boolean;
  onRetry?: () => void;
  onDismiss: () => void;
  onContactSupport?: () => void;
}

const ErrorHandler: React.FC<ErrorHandlerProps> = ({
  error,
  visible,
  onRetry,
  onDismiss,
  onContactSupport,
}) => {
  if (!error) {return null;}

  const getErrorIcon = (code: string): string => {
    switch (code) {
      case 'NETWORK_ERROR':
      case 'TIMEOUT_ERROR':
        return '🌐';
      case 'AUTH_ERROR':
      case 'TOKEN_EXPIRED':
      case 'SIGNATURE_ERROR':
        return '🔐';
      case 'PERMISSION_ERROR':
        return '⚠️';
      case 'SERVER_ERROR':
        return '🔧';
      default:
        return '❌';
    }
  };

  const getErrorTitle = (code: string): string => {
    switch (code) {
      case 'NETWORK_ERROR':
        return '网络连接问题';
      case 'TIMEOUT_ERROR':
        return '请求超时';
      case 'AUTH_ERROR':
      case 'TOKEN_EXPIRED':
      case 'SIGNATURE_ERROR':
        return '身份验证失败';
      case 'PERMISSION_ERROR':
        return '权限不足';
      case 'SERVER_ERROR':
        return '服务器错误';
      default:
        return '操作失败';
    }
  };

  const handlePrimaryAction = () => {
    if (error.shouldRetry && onRetry) {
      onRetry();
    } else if (error.code === 'AUTH_ERROR' || error.code === 'TOKEN_EXPIRED' || error.code === 'SIGNATURE_ERROR') {
      // 引导用户重新绑定
      Alert.alert(
        '需要重新绑定',
        '身份验证失败，请重新绑定微信账号',
        [
          { text: '取消', style: 'cancel' },
          { text: '重新绑定', onPress: () => {
            // 这里应该导航到绑定页面
            onDismiss();
          }},
        ]
      );
    } else if (error.code === 'PERMISSION_ERROR') {
      // 联系技术支持
      if (onContactSupport) {
        onContactSupport();
      } else {
        Alert.alert('联系技术支持', '请联系技术支持解决权限问题');
      }
    } else {
      onDismiss();
    }
  };

  const getPrimaryButtonText = (): string => {
    if (error.shouldRetry) {
      return '重试';
    } else if (error.code === 'AUTH_ERROR' || error.code === 'TOKEN_EXPIRED' || error.code === 'SIGNATURE_ERROR') {
      return '重新绑定';
    } else if (error.code === 'PERMISSION_ERROR') {
      return '联系技术支持';
    } else {
      return '确定';
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onDismiss}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.icon}>{getErrorIcon(error.code)}</Text>
            <Text style={styles.title}>{getErrorTitle(error.code)}</Text>
          </View>

          <View style={styles.content}>
            <Text style={styles.message}>{error.message}</Text>

            {error.suggestedAction && (
              <Text style={styles.suggestion}>{error.suggestedAction}</Text>
            )}

            {error.attempts && error.attempts > 1 && (
              <Text style={styles.attempts}>
                已尝试 {error.attempts} 次
              </Text>
            )}
          </View>

          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.button, styles.secondaryButton]}
              onPress={onDismiss}
            >
              <Text style={styles.secondaryButtonText}>取消</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={handlePrimaryAction}
            >
              <Text style={styles.primaryButtonText}>
                {getPrimaryButtonText()}
              </Text>
            </TouchableOpacity>
          </View>

          {__DEV__ && error.originalError && (
            <View style={styles.debug}>
              <Text style={styles.debugTitle}>调试信息：</Text>
              <Text style={styles.debugText}>{error.originalError}</Text>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    alignItems: 'center',
    marginBottom: 16,
  },
  icon: {
    fontSize: 48,
    marginBottom: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    marginBottom: 20,
  },
  message: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 8,
  },
  suggestion: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 8,
  },
  attempts: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: '#F0F0F0',
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '600',
  },
  debug: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  debugTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 4,
  },
  debugText: {
    fontSize: 10,
    color: '#888',
    fontFamily: 'monospace',
  },
});

export default ErrorHandler;
