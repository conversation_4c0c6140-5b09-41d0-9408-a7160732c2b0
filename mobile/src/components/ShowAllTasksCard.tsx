import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface ShowAllTasksCardProps {
  onPress: () => void;
}

const ShowAllTasksCard = ({ onPress }: ShowAllTasksCardProps) => {
  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <View style={styles.iconContainer}>
        <Ionicons name="ellipsis-horizontal" size={24} color="#FF8C00" />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 76,  // 匹配TaskCard的新宽度
    height: 96, // 匹配TaskCard的新高度
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12, // 匹配TaskCard的右边距
  },
  iconContainer: {
    width: 40,  // 匹配TaskCard的新icon尺寸
    height: 40, // 匹配TaskCard的新icon尺寸
    borderRadius: 10, // 匹配TaskCard的新圆角
    backgroundColor: '#F8F9FA', // 匹配TaskCard的背景色
    alignItems: 'center',
    justifyContent: 'center',
    // 添加与TaskCard一致的阴影
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
});

export default ShowAllTasksCard;
