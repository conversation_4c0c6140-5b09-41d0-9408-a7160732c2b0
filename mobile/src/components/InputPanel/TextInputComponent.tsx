import React, { useState } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Text,
} from 'react-native';

export interface TextInputComponentProps {
  onTextChange: (text: string) => void;
  value: string;
  placeholder?: string;
  disabled?: boolean;
  selectionColor?: string;
}

export const TextInputComponent: React.FC<TextInputComponentProps> = ({
  onTextChange,
  value,
  placeholder = '请输入内容...',
  disabled = false,
  selectionColor = '#FF8C00',
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleTextChange = (inputText: string) => {
    onTextChange(inputText);
  };

  return (
    <View style={[styles.inputContainer, isFocused && styles.inputContainerFocused]}>
      <TextInput
        style={styles.textInput}
        value={value}
        onChangeText={handleTextChange}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        placeholder={placeholder}
        placeholderTextColor="#999"
        multiline
        textAlignVertical="top"
        maxLength={2000}
        editable={!disabled}
        selectionColor={selectionColor}
      />
      <Text style={styles.charCount}>{value.length}/2000</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 8,
    backgroundColor: '#fff',
    minHeight: 150,
  },
  inputContainerFocused: {
    borderColor: '#FF8C00',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  charCount: {
    fontSize: 12,
    color: '#999',
    alignSelf: 'flex-end',
    marginTop: 4,
  },
});

export default TextInputComponent;
