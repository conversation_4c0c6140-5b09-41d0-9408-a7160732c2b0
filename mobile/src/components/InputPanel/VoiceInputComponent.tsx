import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  AppState,
} from 'react-native';
// import Voice, {
//   SpeechResultsEvent,
//   SpeechErrorEvent,
//   SpeechStartEvent,
//   SpeechEndEvent,
// } from '@react-native-community/voice';
import { ensurePermission, showPermissionDeniedAlert } from '../../services/PermissionService';

export interface VoiceInputComponentProps {
  onVoiceResult: (text: string, duration: number) => void;
  onVoiceSubmit: (text: string, duration: number) => void;
  onError?: (error: string) => void;
  maxRecordingTime?: number;
  disabled?: boolean;
  theme?: any;
}

type VoiceState = 'idle' | 'recording' | 'processing' | 'error';

export const VoiceInputComponent: React.FC<VoiceInputComponentProps> = ({
  onVoiceResult,
  onVoiceSubmit,
  onError,
  maxRecordingTime = 60000, // 最大录音时间60秒
  disabled = false,
  theme: _theme,
}) => {
  const [voiceState, setVoiceState] = useState<VoiceState>('idle');
  const [recognizedText, setRecognizedText] = useState<string>('');
  const [recordingTime, setRecordingTime] = useState<number>(0);
  const [hasPermission, setHasPermission] = useState<boolean>(false);

  const recordingStartTime = useRef<number>(0);
  const recordingTimer = useRef<NodeJS.Timeout | null>(null);

  // 1. 先声明 stopRecording
  const stopRecording = React.useCallback(async () => {
    if (voiceState !== 'recording') {return;}
    try {
      // await Voice.stop(); (暂时禁用)
    } catch (error) {
      showPermissionDeniedAlert('microphone');
    }
  }, [voiceState]);

  // 2. 声明语音回调函数 (暂时禁用)
  // const onSpeechStart = React.useCallback((_unused: any) => {
  //   setVoiceState('recording');
  //   recordingStartTime.current = Date.now();
  //   recordingTimer.current = setInterval(() => {
  //     const elapsed = Date.now() - recordingStartTime.current;
  //     setRecordingTime(elapsed);
  //     if (elapsed >= maxRecordingTime) {
  //       stopRecording();
  //     }
  //   }, 100);
  // }, [maxRecordingTime, stopRecording]);

  // const onSpeechEnd = React.useCallback((_unused: any) => {
  //   setVoiceState('processing');
  //   if (recordingTimer.current) {
  //     clearInterval(recordingTimer.current);
  //     recordingTimer.current = null;
  //   }
  // }, []);

  // const onSpeechResults = React.useCallback((e: any) => {
  //   const result = e.value?.[0] || '';
  //   setRecognizedText(result);
  //   const duration = Date.now() - recordingStartTime.current;
  //   setRecordingTime(duration);
  //   setVoiceState('idle');
  //   onVoiceResult(result, duration);
  //   if (result.trim()) {
  //     onVoiceSubmit(result, duration);
  //   }
  // }, [onVoiceResult, onVoiceSubmit]);

  // const onSpeechError = React.useCallback((_unused: any) => {
  //   setVoiceState('error');
  //   if (recordingTimer.current) {
  //     clearInterval(recordingTimer.current);
  //     recordingTimer.current = null;
  //   }
  //   const errorMessage = '语音识别失败';
  //   onError?.(errorMessage);
  //   setTimeout(() => {
  //     setVoiceState('idle');
  //     setRecognizedText('');
  //     setRecordingTime(0);
  //   }, 3000);
  // }, [onError]);

  // 3. 再声明 initializeVoice 和 cleanupVoice (暂时禁用)
  const initializeVoice = React.useCallback(() => {
    // Voice.onSpeechStart = onSpeechStart;
    // Voice.onSpeechEnd = onSpeechEnd;
    // Voice.onSpeechResults = onSpeechResults;
    // Voice.onSpeechError = onSpeechError;
    console.log('Voice component temporarily disabled');
  }, []);

  const cleanupVoice = React.useCallback(() => {
    // Voice.destroy().then(Voice.removeAllListeners);
    if (recordingTimer.current) {
      clearInterval(recordingTimer.current);
    }
  }, []);

  // 初始化权限检查
  useEffect(() => {
    (async () => {
      const hasPerms = await ensurePermission('microphone');
      setHasPermission(hasPerms);
      if (!hasPerms) {
        showPermissionDeniedAlert('microphone');
      }
    })();
    initializeVoice();
    return () => {
      cleanupVoice();
    };
  }, [initializeVoice, cleanupVoice]);

  // 运行时权限监控：回到前台时自动检查麦克风权限
  useEffect(() => {
    const handleAppStateChange = async (nextAppState: string) => {
      if (nextAppState === 'active') {
        const hasPerms = await ensurePermission('microphone');
        setHasPermission(hasPerms);
        if (!hasPerms) {
          showPermissionDeniedAlert('microphone');
          setVoiceState('idle');
          setRecognizedText('');
          setRecordingTime(0);
        }
      }
    };
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  const startRecording = async () => {
    if (!hasPermission) {
      showPermissionDeniedAlert('microphone');
      return;
    }
    if (disabled || voiceState !== 'idle') {return;}
    try {
      setRecognizedText('');
      setRecordingTime(0);
      // await Voice.start('zh-CN'); // 使用中文语音识别 (暂时禁用)
      setVoiceState('recording');
      onError?.('语音功能暂时禁用');
    } catch (error) {
      showPermissionDeniedAlert('microphone');
    }
  };

  const cancelRecording = async () => {
    try {
      // await Voice.cancel(); (暂时禁用)
      setVoiceState('idle');
      setRecognizedText('');
      setRecordingTime(0);

      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }
    } catch (error) {
      console.error('取消语音识别失败:', error);
    }
  };

  const formatTime = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    return `${seconds}s`;
  };

  const getButtonColor = (): string => {
    switch (voiceState) {
      case 'recording':
        return '#FF6B6B';
      case 'processing':
        return '#4ECDC4';
      case 'error':
        return '#FF9F43';
      default:
        return disabled ? '#999999' : '#FF7900';
    }
  };

  const getStatusText = (): string => {
    switch (voiceState) {
      case 'recording':
        return `正在录音... ${formatTime(recordingTime)}`;
      case 'processing':
        return '正在识别...';
      case 'error':
        return '识别失败，请重试';
      default:
        return disabled ? '语音功能已禁用' : '点击开始语音输入';
    }
  };

  const getButtonText = (): string => {
    switch (voiceState) {
      case 'recording':
        return '🔴';
      case 'processing':
        return '⏳';
      case 'error':
        return '❌';
      default:
        return '🎤';
    }
  };

  if (!hasPermission) {
    return (
      <View style={styles.container}>
        <View style={styles.voiceContainer}>
          <Text style={styles.statusText}>
            需要麦克风权限，请在系统设置中授权
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.voiceContainer}>
        <TouchableOpacity
          style={[styles.voiceButton, { backgroundColor: getButtonColor() }]}
          onPress={voiceState === 'recording' ? stopRecording : startRecording}
          disabled={disabled || voiceState === 'processing'}
        >
          <Text style={styles.buttonText}>{getButtonText()}</Text>
        </TouchableOpacity>

        {voiceState === 'recording' && (
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={cancelRecording}
          >
            <Text style={styles.cancelText}>取消</Text>
          </TouchableOpacity>
        )}

        <Text style={styles.statusText}>
          {getStatusText()}
        </Text>

        {recognizedText ? (
          <View style={styles.resultContainer}>
            <Text style={styles.resultLabel}>识别结果：</Text>
            <Text style={styles.resultText}>{recognizedText}</Text>
          </View>
        ) : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  voiceContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120,
  },
  voiceButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cancelButton: {
    marginTop: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#666666',
    borderRadius: 16,
  },
  cancelText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  statusText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginTop: 8,
  },
  resultContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    maxWidth: '100%',
  },
  resultLabel: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 4,
  },
  resultText: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 22,
  },
});

export default VoiceInputComponent;
