import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import { useCameraStore } from '../../stores/cameraStore';

const { height: screenHeight } = Dimensions.get('window');

const CameraZoomHandler: React.FC = () => {
  const {
    zoom,
    zoomRange,
    setZoom,
    isZoomIndicatorVisible,
    showZoomIndicator,
    hideZoomIndicator,
    sceneMode,
  } = useCameraStore();

  // Animation values
  const startZoom = useSharedValue(1);
  const indicatorOpacity = useSharedValue(0);
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate zoom percentage for visual feedback
  const zoomPercentage = ((zoom - zoomRange.min) / (zoomRange.max - zoomRange.min)) * 100;

  // Format zoom value for display
  const formatZoomValue = (value: number): string => {
    if (value < 1) {
      return `${value.toFixed(1)}x`;
    } else if (value < 10) {
      return `${value.toFixed(1)}x`;
    } else {
      return `${Math.round(value)}x`;
    }
  };

  // Show indicator and auto-hide after delay
  const showIndicatorWithDelay = () => {
    showZoomIndicator();
    indicatorOpacity.value = withTiming(1, { duration: 200 });

    // Clear existing timeout
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }

    // Set new timeout
    hideTimeoutRef.current = setTimeout(() => {
      indicatorOpacity.value = withTiming(0, { duration: 300 });
      setTimeout(() => {
        hideZoomIndicator();
      }, 300);
    }, 1500);
  };

  // Pinch gesture
  const pinchGesture = Gesture.Pinch()
    .onBegin(() => {
      startZoom.value = zoom;
      runOnJS(showIndicatorWithDelay)();
    })
    .onUpdate((event) => {
      const newZoom = startZoom.value * event.scale;
      const clampedZoom = Math.max(zoomRange.min, Math.min(newZoom, zoomRange.max));
      runOnJS(setZoom)(clampedZoom);
    })
    .onEnd(() => {
      // Gesture ended, indicator will auto-hide
    });

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, []);

  // Animated styles
  const indicatorAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: indicatorOpacity.value,
    };
  });

  return (
    <>
      {/* Gesture Detection Layer - 覆盖整个相机区域但排除头部和底部控制区 */}
      <GestureDetector gesture={pinchGesture}>
        <View style={styles.gestureArea} />
      </GestureDetector>

      {/* Zoom Indicator Overlay */}
      {isZoomIndicatorVisible && (
        <Animated.View style={[styles.zoomIndicator, indicatorAnimatedStyle]}>
          <View style={styles.zoomIndicatorContent}>
            {/* Zoom Value */}
            <Text style={styles.zoomValue}>{formatZoomValue(zoom)}</Text>

            {/* Scene Mode - Only display when not in default '广角' mode */}
            {sceneMode !== '广角' && <Text style={styles.sceneMode}>{sceneMode}</Text>}

            {/* Zoom Progress Bar */}
            <View style={styles.zoomProgressContainer}>
              <View style={styles.zoomProgressTrack}>
                <View
                  style={[
                    styles.zoomProgressFill,
                    { width: `${zoomPercentage}%` },
                  ]}
                />
              </View>
            </View>
          </View>
        </Animated.View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  gestureArea: {
    position: 'absolute',
    top: 120, // Exclude header area
    bottom: 140, // Exclude controls area
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 5, // Above camera but below UI elements
  },
  zoomIndicator: {
    position: 'absolute',
    top: screenHeight * 0.3,
    left: 20,
    right: 20,
    alignItems: 'center',
    zIndex: 15, // Above all other elements
  },
  zoomIndicatorContent: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
    minWidth: 120,
  },
  zoomValue: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sceneMode: {
    color: '#FFFFFF',
    fontSize: 12,
    opacity: 0.8,
    marginBottom: 8,
  },
  zoomProgressContainer: {
    width: 100,
    alignItems: 'center',
  },
  zoomProgressTrack: {
    width: '100%',
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 1.5,
    overflow: 'hidden',
  },
  zoomProgressFill: {
    height: '100%',
    backgroundColor: '#FF6B35',
    borderRadius: 1.5,
  },
});

export default CameraZoomHandler;
