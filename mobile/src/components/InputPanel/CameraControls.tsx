import React, { useCallback, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Camera } from 'react-native-vision-camera';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { useCameraStore } from '../../stores/cameraStore';
import CustomImagePicker from '../CustomImagePicker';

// --- Types ---
export interface CameraControlsProps {
  cameraRef: React.RefObject<Camera | null>;
}

// --- Main Component ---
const CameraControls: React.FC<CameraControlsProps> = ({ cameraRef }) => {
  const {
    capturedMedia,
    isRecording,
    addMedia,
    isTakingPhoto,
    showActionSheet,
    setIsTakingPhoto,
  } = useCameraStore();

  const [isPickerVisible, setIsPickerVisible] = useState(false);

  const handleTakePhoto = async () => {
    if (isTakingPhoto || cameraRef.current == null) {return;}

    setIsTakingPhoto(true);
    try {
      const photo = await cameraRef.current.takePhoto();
      addMedia(photo);

      // 添加触觉反馈，示意拍照成功
      ReactNativeHapticFeedback.trigger('impactMedium', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
    } catch (e) {
      console.error('Failed to take photo:', e);

      // 拍照失败时的轻微震动反馈
      ReactNativeHapticFeedback.trigger('impactLight', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
    } finally {
      setIsTakingPhoto(false);
    }
  };

  const handleOpenGallery = useCallback(() => {
    setIsPickerVisible(true);
  }, []);

  const handleImagesSelected = (uris: string[]) => {
    if (uris.length > 0) {
      uris.forEach((uri) => {
        const photoFile = {
          path: uri,
          width: 0, // Note: We might not get width/height directly, adjust as needed
          height: 0,
          mime: 'image/jpeg', // Assume jpeg for now
        } as any;
        addMedia(photoFile);
      });
      console.log(`[自建相册] 成功选择 ${uris.length} 张图片`);
    }
  };

  return (
    <View style={styles.container}>
      {/* 录像计时器 - 在快门按钮正上方 */}
      {isRecording && (
        <View style={styles.recordingTimer}>
          <View style={styles.recordingDot} />
          {/* <Text style={styles.recordingTime}>{formatRecordingTime(recordingDuration)}</Text> */}
        </View>
      )}

      <CustomImagePicker
        isVisible={isPickerVisible}
        onClose={() => setIsPickerVisible(false)}
        onSelect={handleImagesSelected}
        selectionLimit={10}
      />

      {/* 主控制区域 - 底部三个一级动作 */}
      <View style={styles.mainControlContainer}>
        {/* 左下角 - 选照片按钮 */}
        <View style={styles.leftSection}>
          <TouchableOpacity style={styles.galleryButton} onPress={handleOpenGallery}>
            <Ionicons name="folder-outline" size={24} color="white" />
          </TouchableOpacity>
        </View>

        {/* 居中 - 拍照/录像按钮 */}
        <TouchableOpacity
          style={styles.shutterButton}
          onPress={handleTakePhoto}
          // onLongPress={onStartRecording}
          // onPressOut={isRecording ? onStopRecording : undefined}
          disabled={isTakingPhoto}
        >
          <View
            style={[
              styles.shutterButtonInner,
              isRecording && styles.shutterButtonInnerRecording,
            ]}
          />
        </TouchableOpacity>

        {/* 右下角 - 完成按钮 */}
        <View style={styles.rightSection}>
          {capturedMedia.length > 0 ? (
            <TouchableOpacity
              style={styles.completeButton}
              onPress={showActionSheet}
            >
              <Text style={styles.completeButtonText}>完成</Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.rightPlaceholder} />
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    top: 0,
    backgroundColor: 'transparent',
    justifyContent: 'flex-end',
  },

  recordingTimer: {
    position: 'absolute',
    bottom: 220,
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
    zIndex: 10,
  },

  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ff3b30',
    marginRight: 6,
  },

  recordingTime: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'monospace',
  },

  mainControlContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 40,
    backgroundColor: 'rgba(0,0,0,0.2)',
  },

  leftSection: {
    flex: 1,
    alignItems: 'flex-start',
  },

  rightSection: {
    flex: 1,
    alignItems: 'flex-end',
  },

  galleryButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  shutterButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 20,
  },

  shutterButtonInner: {
    width: 62,
    height: 62,
    borderRadius: 31,
    borderWidth: 2,
    borderColor: '#333',
  },

  shutterButtonInnerRecording: {
    backgroundColor: '#ff3b30',
    borderWidth: 0,
  },

  completeButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 60,
  },

  completeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

  rightPlaceholder: {
    width: 60,
    height: 44,
  },
});

export default CameraControls;
