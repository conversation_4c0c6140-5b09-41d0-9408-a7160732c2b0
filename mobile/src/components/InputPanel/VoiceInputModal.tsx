import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  SafeAreaView,
  ActivityIndicator,
  AppState,
} from 'react-native';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { defaultTheme } from '../../styles/theme';
import { ensurePermission, showPermissionDeniedAlert } from '../../services/PermissionService';

export interface VoiceInputModalProps {
  isVisible: boolean;
  onClose: (text: string) => void;
}

type VoiceState = 'idle' | 'requesting_permission' | 'recording' | 'processing' | 'error';

const VoiceInputModal: React.FC<VoiceInputModalProps> = ({ isVisible, onClose }) => {
  const [voiceState, setVoiceState] = useState<VoiceState>('idle');
  const [recognizedText, setRecognizedText] = useState<string>('');
  const [recordingTime, setRecordingTime] = useState<number>(0);

  const recordingTimer = useRef<NodeJS.Timeout | null>(null);

  const handleClose = useCallback((text: string) => {
    if (recordingTimer.current) {
      clearInterval(recordingTimer.current);
    }
    onClose(text);
  }, [onClose]);

  const checkAndRequestMicrophonePermission = useCallback(async () => {
    setVoiceState('requesting_permission');
    try {
      const hasPermission = await ensurePermission('microphone');
      if (hasPermission) {
        setVoiceState('idle');
      } else {
        showPermissionDeniedAlert('microphone');
        handleClose('权限被拒绝');
      }
    } catch (error) {
      showPermissionDeniedAlert('microphone');
      handleClose('权限请求异常');
    }
  }, [handleClose]);

  useEffect(() => {
    if (isVisible) {
      checkAndRequestMicrophonePermission();
    }
    let timer = recordingTimer.current;
    return () => {
      if (isVisible && timer) {
        clearInterval(timer);
      }
    };
  }, [isVisible, checkAndRequestMicrophonePermission]);

  // 运行时权限监控：回到前台时自动检查麦克风权限
  useEffect(() => {
    const handleAppStateChange = async (nextAppState: string) => {
      if (nextAppState === 'active' && isVisible) {
        const hasPerms = await ensurePermission('microphone');
        if (!hasPerms) {
          showPermissionDeniedAlert('microphone');
          handleClose('');
        }
      }
    };
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [isVisible, handleClose]);

  const renderContent = () => {
    switch (voiceState) {
      case 'requesting_permission':
        return <ActivityIndicator size="large" color={defaultTheme.colors.primary} />;
      case 'recording':
        return (
          <>
            <Text style={styles.statusText}>正在聆听...</Text>
            <Text style={styles.timerText}>{`${(recordingTime / 1000).toFixed(1)}s`}</Text>
          </>
        );
      case 'processing':
        return <Text style={styles.statusText}>正在分析...</Text>;
      case 'error':
        return <Text style={[styles.statusText, styles.errorText]}>识别出错了</Text>;
      case 'idle':
      default:
        return <Text style={styles.statusText}>请开始说话</Text>;
    }
  };

  const getMicButtonColor = () => {
    if (voiceState === 'recording') {return '#FF3B30';}
    if (voiceState === 'processing') {return '#FF9500';}
    return defaultTheme.colors.primary;
  };

  return (
    <Modal visible={isVisible} animationType="fade" transparent={true}>
      <SafeAreaView style={styles.container}>
        <View style={styles.backdrop} onTouchEnd={() => handleClose(recognizedText)} />
        <View style={styles.modalContent}>
          <TouchableOpacity style={styles.closeButton} onPress={() => handleClose(recognizedText)}>
            <Ionicons name="close" size={24} color={defaultTheme.colors.textSecondary} />
          </TouchableOpacity>
          <View style={styles.statusContainer}>
            {renderContent()}
          </View>
          <View style={styles.micButtonContainer}>
            <TouchableOpacity
              style={[styles.micButton, { backgroundColor: getMicButtonColor() }]}
              onPressIn={() => {
                ReactNativeHapticFeedback.trigger('impactLight');
                setRecognizedText('');
                setRecordingTime(0);
              }}
              activeOpacity={0.7}
            >
              <Ionicons name="mic" size={40} color="white" />
            </TouchableOpacity>
          </View>
          <Text style={styles.recognizedText}>{recognizedText}</Text>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.6)',
  },
  modalContent: {
    width: '85%',
    backgroundColor: defaultTheme.colors.backgroundPrimary,
    borderRadius: defaultTheme.borderRadius.large,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 20,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  statusContainer: {
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  statusText: {
    fontSize: defaultTheme.fonts.titleLarge,
    color: defaultTheme.colors.textPrimary,
    fontWeight: '600',
  },
  errorText: {
    color: defaultTheme.colors.error,
  },
  timerText: {
    fontSize: defaultTheme.fonts.bodyMedium,
    color: defaultTheme.colors.textSecondary,
    marginTop: 8,
  },
  micButtonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  micButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recognizedText: {
    fontSize: defaultTheme.fonts.bodyMedium,
    color: defaultTheme.colors.textPrimary,
    textAlign: 'center',
    minHeight: 50,
    paddingHorizontal: 10,
  },
});

export default VoiceInputModal;
