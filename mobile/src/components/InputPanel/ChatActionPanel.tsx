import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, FlatList, Dimensions } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { defaultTheme } from '../../styles/theme';

interface ActionItem {
  key: string;
  title: string;
  icon: string;
}

const actionItems: ActionItem[] = [
  { key: 'album', title: '相册', icon: 'images-outline' },
  // { key: 'camera', title: '拍摄', icon: 'camera-outline' },
  { key: 'file', title: '文件', icon: 'document-text-outline' },
  // { key: 'location', title: '位置', icon: 'location-outline' },
  // { key: 'schedule', title: '日程', icon: 'calendar-outline' },
];

const numColumns = 4;
const screenWidth = Dimensions.get('window').width;
const itemHeight = 90; // Approximate height of one item + margin

export const getActionPanelHeight = () => {
  const numRows = Math.ceil(actionItems.length / numColumns);
  return numRows * itemHeight + 32; // +32 for vertical padding
};

interface ChatActionPanelProps {
  onOptionSelect: (key: string) => void;
}

const ChatActionPanel: React.FC<ChatActionPanelProps> = ({ onOptionSelect }) => {
  const theme = defaultTheme;

  const renderItem = ({ item }: { item: ActionItem }) => (
    <TouchableOpacity style={styles.itemContainer} onPress={() => onOptionSelect(item.key)}>
      <View style={styles.iconWrapper}>
        <Ionicons name={item.icon} size={32} color={theme.colors.textSecondary} />
      </View>
      <Text style={styles.itemText}>{item.title}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={actionItems}
        renderItem={renderItem}
        keyExtractor={(item) => item.key}
        numColumns={numColumns}
        scrollEnabled={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: defaultTheme.colors.backgroundSecondary,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: defaultTheme.colors.backgroundTertiary,
    paddingVertical: 16,
    paddingHorizontal: 8,
  },
  itemContainer: {
    width: (screenWidth - 16) / numColumns, // 16 is horizontal padding
    alignItems: 'center',
    marginBottom: 16,
  },
  iconWrapper: {
    width: 60,
    height: 60,
    borderRadius: 12,
    backgroundColor: defaultTheme.colors.backgroundPrimary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemText: {
    fontSize: 12,
    color: defaultTheme.colors.textSecondary,
  },
});

export default ChatActionPanel;
