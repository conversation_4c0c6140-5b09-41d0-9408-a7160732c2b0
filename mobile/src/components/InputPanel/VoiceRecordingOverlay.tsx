import React from 'react';
import { View, Text, StyleSheet, Modal, SafeAreaView } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface VoiceRecordingOverlayProps {
  isVisible: boolean;
}

const VoiceRecordingOverlay: React.FC<VoiceRecordingOverlayProps> = ({
  isVisible,
}) => {
  return (
    <Modal visible={isVisible} transparent animationType="fade">
      <SafeAreaView style={styles.container}>
        <View style={styles.content}>
          <View style={styles.micContainer}>
            <Ionicons name="mic" size={40} color="white" />
          </View>
          <Text style={styles.statusText}>手指上滑，取消发送</Text>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  content: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  micContainer: {
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusText: {
    marginTop: 16,
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default VoiceRecordingOverlay;
