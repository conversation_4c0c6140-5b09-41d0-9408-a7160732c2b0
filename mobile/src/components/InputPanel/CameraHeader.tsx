import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
  StatusBar,
} from 'react-native';
import { getDisplayUri } from '../../utils/media';

// --- Types ---
export interface CameraHeaderProps {
  onPreview?: () => void;
  onClose?: () => void;
  mediaCount: number;
  latestPhotoUri?: string | null;
}

// --- Main Component ---
const CameraHeader: React.FC<CameraHeaderProps> = ({
  onPreview,
  onClose,
  mediaCount,
  latestPhotoUri,
}) => {
  const handlePreview = () => {
    if (onPreview) {
      onPreview();
    }
  };

  return (
    <View style={styles.header}>
      <TouchableOpacity onPress={onClose} style={styles.button}>
        <Text style={styles.buttonText}>关闭</Text>
      </TouchableOpacity>

      <View style={styles.titleContainer} />

      {mediaCount > 0 && latestPhotoUri && (
        <TouchableOpacity
          style={styles.capturedPhotosIndicator}
          onPress={handlePreview}
        >
          <Image
            source={{ uri: getDisplayUri(latestPhotoUri) }}
            style={styles.latestPhotoThumbnail}
          />
          <View style={styles.countBadge}>
            <Text style={styles.capturedPhotosCount}>
              {mediaCount}
            </Text>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

// --- Styles ---
const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.select({ ios: 44, android: StatusBar.currentHeight || 0 }),
    paddingBottom: 15,
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  button: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 16,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  titleContainer: {
    flex: 1,
  },
  capturedPhotosIndicator: {
    width: 44,
    height: 44,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderWidth: 1,
    borderColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  latestPhotoThumbnail: {
    width: '100%',
    height: '100%',
    borderRadius: 7,
  },
  countBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#ff3b30',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  capturedPhotosCount: {
    color: 'white',
    fontSize: 12,
    fontWeight: '700',
  },
});

export default CameraHeader;
