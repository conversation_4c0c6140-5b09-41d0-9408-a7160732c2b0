import React from 'react';
import { Text, StyleSheet, TouchableOpacity } from 'react-native';
import { defaultTheme } from '../../styles/theme';

interface VoiceInputBarProps {
  onPressIn: () => void;
  onPressOut: () => void;
}

const VoiceInputBar: React.FC<VoiceInputBarProps> = ({
  onPressIn,
  onPressOut,
}) => {
  return (
    <TouchableOpacity
      style={styles.container}
      onPressIn={onPressIn}
      onPressOut={onPressOut}
      activeOpacity={0.7}
    >
      <Text style={styles.text}>按住 说话</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: 40,
    borderRadius: 20,
    backgroundColor: defaultTheme.colors.backgroundPrimary,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: defaultTheme.colors.backgroundTertiary,
  },
  text: {
    fontSize: 16,
    fontWeight: 'bold',
    color: defaultTheme.colors.textSecondary,
  },
});

export default VoiceInputBar;
