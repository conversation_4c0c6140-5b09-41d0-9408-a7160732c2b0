import React from 'react';
import { ActionSheetIOS, Platform, Modal, View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { defaultTheme } from '../../styles/theme';

interface CaptureActionSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onOptionSelect: (index: number) => void;
}

const options = ['存档备用', '安排工作', '笔记创作'];
const destructiveButtonIndex = -1; // No destructive button in this case

const CaptureActionSheet: React.FC<CaptureActionSheetProps> = ({
  isVisible,
  onClose,
  onOptionSelect,
}) => {
  const insets = useSafeAreaInsets();

  if (Platform.OS === 'ios') {
    if (!isVisible) {return null;}

    ActionSheetIOS.showActionSheetWithOptions(
      {
        options: [...options, '取消'],
        cancelButtonIndex: options.length,
        destructiveButtonIndex,
        title: '下一步',
        message: '照片已保存，请选择后续操作',
      },
      (buttonIndex) => {
        // buttonIndex will be undefined if the user taps outside the ActionSheet to dismiss it.
        if (buttonIndex !== undefined && buttonIndex < options.length) {
          onOptionSelect(buttonIndex);
        }
        onClose();
      }
    );
    return null; // ActionSheetIOS is imperative and doesn't render a component
  }

  // Android Custom Implementation
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <TouchableOpacity style={styles.modalOverlay} activeOpacity={1} onPress={onClose}>
        <View style={[styles.modalContainer, { paddingBottom: insets.bottom }]}>
          <View style={styles.sheet}>
            <View style={styles.header}>
              <Text style={styles.title}>下一步</Text>
              <Text style={styles.message}>照片已保存，请选择后续操作</Text>
            </View>
            {options.map((option, index) => (
              <TouchableOpacity
                key={option}
                style={styles.optionButton}
                onPress={() => {
                  onOptionSelect(index);
                }}
              >
                <Text style={styles.optionText}>{option}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    marginHorizontal: 8,
  },
  sheet: {
    backgroundColor: defaultTheme.colors.backgroundSecondary,
    borderRadius: 14,
    overflow: 'hidden',
  },
  header: {
    padding: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: defaultTheme.colors.backgroundTertiary,
    alignItems: 'center',
  },
  title: {
    fontSize: 13,
    fontWeight: '500',
    color: defaultTheme.colors.textSecondary,
    marginBottom: 4,
  },
  message: {
    fontSize: 13,
    color: defaultTheme.colors.textSecondary,
    textAlign: 'center',
  },
  optionButton: {
    padding: 16,
    alignItems: 'center',
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: defaultTheme.colors.backgroundTertiary,
  },
  optionText: {
    fontSize: 18,
    color: defaultTheme.colors.primary,
  },
});

export default CaptureActionSheet;
