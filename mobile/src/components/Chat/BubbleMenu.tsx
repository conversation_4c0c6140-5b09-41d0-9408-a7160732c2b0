import React from 'react';
import { View, Text, TouchableOpacity, Pressable, StyleSheet } from 'react-native';
import { defaultTheme } from '../../styles/theme';

interface BubbleMenuProps {
  isVisible: boolean;
  position: { x: number; y: number };
  onClose: () => void;
  onSelect: (action: string) => void;
  messageType: 'text' | 'image' | 'url' | 'card' | 'audio' | 'file';
}

const BubbleMenu = ({ isVisible, position, onClose, onSelect, messageType }: BubbleMenuProps) => {
  if (!isVisible) { return null; }
  const theme = defaultTheme;

  const getMenuItems = () => {
    const items = ['分享'];
    if (messageType === 'text' || messageType === 'url') {
      items.unshift('复制');
    }
    return items;
  };

  const menuItems = getMenuItems();

  return (
    <Pressable style={styles(theme).modalOverlay} onPress={onClose}>
      <View style={[styles(theme).bubbleMenuContainer, { top: position.y, left: position.x }]}>
        {menuItems.map(item => (
          <TouchableOpacity key={item} style={styles(theme).bubbleMenuItem} onPress={() => onSelect(item)}>
            <Text style={styles(theme).bubbleMenuText}>{item}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </Pressable>
  );
};

const styles = (theme: typeof defaultTheme) => StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bubbleMenuContainer: {
    position: 'absolute',
    flexDirection: 'row',
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: theme.borderRadius.large,
    padding: theme.spacing.xs,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  bubbleMenuItem: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  bubbleMenuText: {
    color: theme.colors.textPrimary,
    fontSize: theme.fonts.bodyMedium,
  },
});

export default BubbleMenu;
