import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { defaultTheme } from '../../styles/theme';

interface MessageCardProps {
  data: {
    title: string;
    items: string[];
    action: {
      label: string;
      done: boolean;
    };
  } | undefined;
}

const MessageCard = ({ data }: MessageCardProps) => {
  const theme = defaultTheme;
  return (
    <View style={styles(theme).cardContainer}>
      <Text style={styles(theme).cardTitle}>{data?.title}</Text>
      {data?.items.map((item, index) => (
        <Text key={index} style={styles(theme).cardItem}>{item}</Text>
      ))}
      <TouchableOpacity style={styles(theme).cardButton}>
        <Text style={styles(theme).cardButtonText}>{data?.action.label}</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = (theme: typeof defaultTheme) => StyleSheet.create({
  cardContainer: {
    padding: theme.spacing.md,
    width: 250,
  },
  cardTitle: {
    fontSize: theme.fonts.bodyLarge,
    fontWeight: theme.fontWeights.medium,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.sm,
  },
  cardItem: {
    fontSize: theme.fonts.bodyMedium,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  cardButton: {
    marginTop: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.backgroundTertiary,
    borderRadius: theme.borderRadius.small,
    alignItems: 'center',
  },
  cardButtonText: {
    color: theme.colors.textPrimary,
    fontWeight: theme.fontWeights.medium,
  },
});

export default MessageCard;
