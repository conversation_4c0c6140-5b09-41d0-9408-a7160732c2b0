import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { defaultTheme } from '../../styles/theme';
import type { RootStackParamList } from '../../../App';

interface MessageUrlProps {
  data: {
    url: string;
    title: string;
    description: string;
    image: string;
  } | undefined;
}

const MessageUrl = ({ data }: MessageUrlProps) => {
  const theme = defaultTheme;
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const [isImageError, setIsImageError] = React.useState(false);

  const domain = React.useMemo(() => {
    if (!data?.url) {return '';}
    try {
      // 使用URL构造函数安全地提取主机名
      return new URL(data.url).hostname.replace(/^www\./, '');
    } catch (e) {
      // 如果URL格式不正确，尝试从字符串中提取
      const match = data.url.match(/^(?:https?:\/\/)?(?:[^@\n]+@)?(?:www\.)?([^:\/\\n?]+)/im);
      return match ? match[1] : '';
    }
  }, [data?.url]);

  // 当 data 变化时，重置图片错误状态
  React.useEffect(() => {
    setIsImageError(false);
  }, [data]);

  // 如果没有数据或没有任何可显示的内容，则不渲染组件
  if (!data || (!data.image && !data.title && !data.description)) {
    return null;
  }

  const handlePress = () => {
    if (data?.url) {
      navigation.navigate('WebViewScreen', { url: data.url });
    }
  };

  const hasTextContent = !!data.title || !!data.description || !!domain;
  const showImage = !!data.image && !isImageError;

  return (
    <TouchableOpacity style={styles(theme).urlContainer} onPress={handlePress}>
      {showImage ? (
        <Image
          source={{ uri: data.image }}
          style={styles(theme).urlImage}
          onError={() => setIsImageError(true)}
        />
      ) : null}
      {hasTextContent && (
        <View style={styles(theme).urlTextContainer}>
          {!!data.title && <Text style={styles(theme).urlTitle} numberOfLines={showImage ? 2 : 3}>{data.title}</Text>}
          {!!data.description && <Text style={styles(theme).urlDescription} numberOfLines={showImage ? 3 : 5}>{data.description}</Text>}
          {!!domain && <Text style={styles(theme).urlDomain}>来自 {domain}</Text>}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = (theme: typeof defaultTheme) => StyleSheet.create({
  urlContainer: {
    width: 280,
    backgroundColor: theme.colors.backgroundSecondary, // 使用更干净的白色背景
    borderRadius: theme.borderRadius.medium,
    borderWidth: 1,
    borderColor: theme.colors.backgroundTertiary, // 保留边框以示区分
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08, // 降低阴影不透明度
    shadowRadius: 3,
    elevation: 2,
    overflow: 'hidden',
  },
  urlImage: {
    width: '100%',
    height: 100, // 适当增加图片高度
    borderTopLeftRadius: theme.borderRadius.medium,
    borderTopRightRadius: theme.borderRadius.medium,
  },
  urlTextContainer: {
    padding: theme.spacing.md, // 统一内边距
  },
  urlTitle: {
    fontSize: theme.fonts.bodyLarge, // 增大标题字号
    fontWeight: theme.fontWeights.medium,
    color: theme.colors.textPrimary,
    lineHeight: 22, // 调整行高
    marginBottom: theme.spacing.xs,
  },
  urlDescription: {
    fontSize: theme.fonts.bodyMedium, // 增大描述字号
    color: theme.colors.textSecondary,
    lineHeight: 20, // 调整行高
  },
  urlDomain: {
    fontSize: theme.fonts.bodySmall, // 域名使用小号字体
    color: theme.colors.textTertiary,
    marginTop: theme.spacing.sm,
  },
});

export default MessageUrl;
