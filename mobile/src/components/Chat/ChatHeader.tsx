import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RootStackParamList } from '../../../App';
import { defaultTheme } from '../../styles/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const ChatHeader = ({ onBack }: { onBack: () => void }) => {
  const theme = defaultTheme;
  const navigation = useNavigation<NavigationProp>();
  const [showMenu, setShowMenu] = useState(false);

  const handleMenuPress = () => {
    setShowMenu(true);
  };

  const handleMenuItemPress = (action: string) => {
    setShowMenu(false);

    switch (action) {
      case 'wechat_binding':
        // 导航到简化的微信绑定页面
        navigation.navigate('SimpleWeChatBinding' as any);
        break;
    }
  };

  return (
    <View style={styles(theme).headerContainer}>
      <TouchableOpacity onPress={onBack} style={styles(theme).headerButton}>
        <Ionicons name="chevron-back" size={24} color={theme.colors.textPrimary} />
      </TouchableOpacity>
      <Text style={styles(theme).headerTitle} numberOfLines={1}>公职猫</Text>
      <TouchableOpacity style={styles(theme).headerButton} onPress={handleMenuPress}>
        <Ionicons name="ellipsis-horizontal-outline" size={22} color={theme.colors.textPrimary} />
      </TouchableOpacity>

      {/* 下拉菜单 */}
      <Modal
        visible={showMenu}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowMenu(false)}
      >
        <TouchableOpacity
          style={styles(theme).modalOverlay}
          onPress={() => setShowMenu(false)}
        >
          <View style={styles(theme).menuContainer}>
            <TouchableOpacity
              style={styles(theme).menuItem}
              onPress={() => handleMenuItemPress('wechat_binding')}
            >
              <Ionicons name="logo-wechat" size={20} color={theme.colors.primary} />
              <Text style={styles(theme).menuItemText}>从微信转发消息</Text>
              <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
            </TouchableOpacity>
            <View style={styles(theme).menuSeparator} />

          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

// 使用主题系统的样式
const styles = (theme: typeof defaultTheme) => StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 56,
    backgroundColor: theme.colors.backgroundPrimary,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: theme.colors.backgroundTertiary,
    paddingHorizontal: 0,
  },
  headerButton: {
    minWidth: 44,
    minHeight: 44,
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 16,
    paddingRight: 16,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: theme.fonts.titleSmall,
    fontWeight: theme.fontWeights.medium,
    color: theme.colors.textPrimary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingTop: 56, // 头部高度
    paddingRight: 16,
  },
  menuContainer: {
    backgroundColor: theme.colors.backgroundPrimary,
    borderRadius: 12,
    minWidth: 200,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  menuItemText: {
    flex: 1,
    fontSize: theme.fonts.bodyMedium,
    color: theme.colors.textPrimary,
    marginLeft: 12,
  },
  menuSeparator: {
    height: StyleSheet.hairlineWidth,
    backgroundColor: theme.colors.backgroundTertiary,
    marginHorizontal: 16,
    marginVertical: 4,
  },
});

export default ChatHeader;
