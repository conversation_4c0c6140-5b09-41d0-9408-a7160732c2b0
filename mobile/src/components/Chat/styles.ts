import { StyleSheet } from 'react-native';
import { defaultTheme, Theme } from '../../styles/theme';

export const styles = (theme: Theme = defaultTheme) => StyleSheet.create({
  flex: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.backgroundPrimary,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    height: 44,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.backgroundTertiary,
  },
  headerButton: {
    minWidth: 50,
  },
  headerButtonText: {
    color: theme.colors.textPrimary,
    fontSize: theme.fonts.bodyLarge,
  },
  headerTitle: {
    color: theme.colors.textPrimary,
    fontSize: theme.fonts.titleSmall,
    fontWeight: theme.fontWeights.medium,
  },
  messageList: {
    flex: 1,
    paddingHorizontal: theme.spacing.md,
  },
  messageRow: {
    flexDirection: 'row',
    marginVertical: theme.spacing.sm,
    alignItems: 'flex-end',
  },
  userMessageRow: {
    justifyContent: 'flex-end',
  },
  assistantMessageRow: {
    justifyContent: 'flex-start',
  },
  avatarContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: theme.colors.backgroundTertiary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.sm,
  },
  avatar: {
    fontSize: 24,
  },
  messageContent: {
    maxWidth: 280,
    minWidth: 48,
    flexDirection: 'row',
    alignItems: 'center',
  },
  messageBubble: {
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.medium,
    maxWidth: 280,
    minWidth: 48,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  bubbleNoPadding: {
    padding: 0,
    overflow: 'hidden',
  },
  userMessageBubble: {
    backgroundColor: theme.colors.bubbleUser,
    alignSelf: 'flex-end',
    borderRadius: 16,
    padding: theme.spacing.md,
    maxWidth: 280,
    minWidth: 48,
  },
  assistantMessageBubble: {
    backgroundColor: theme.colors.backgroundSecondary,
    alignSelf: 'flex-start',
  },
  messageText: {
    fontSize: theme.fonts.bodyLarge,
    color: theme.colors.textPrimary,
    textAlign: 'left',
    lineHeight: 22,
  },
  messageImageSingle: {
    width: 200,
    height: 200,
    borderRadius: theme.borderRadius.medium,
  },
  imageGridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: 248,
  },
  messageImageGridItem: {
    width: 80,
    height: 80,
    borderRadius: theme.borderRadius.small,
    margin: 2,
  },
  timestampContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: theme.spacing.xs,
  },
  statusText: {
    fontSize: theme.fonts.bodySmall,
    color: theme.colors.textTertiary,
    marginRight: theme.spacing.sm,
  },
  cardContainer: {
    padding: theme.spacing.md,
    width: 250,
  },
  cardTitle: {
    fontSize: theme.fonts.bodyLarge,
    fontWeight: theme.fontWeights.medium,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.sm,
  },
  cardItem: {
    fontSize: theme.fonts.bodyMedium,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  cardButton: {
    marginTop: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.backgroundTertiary,
    borderRadius: theme.borderRadius.small,
    alignItems: 'center',
  },
  cardButtonText: {
    color: theme.colors.textPrimary,
    fontWeight: theme.fontWeights.medium,
  },
  urlContainer: {
    width: 240,
    backgroundColor: theme.colors.backgroundTertiary,
    borderRadius: theme.borderRadius.medium,
  },
  urlImage: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: theme.borderRadius.medium,
    borderTopRightRadius: theme.borderRadius.medium,
  },
  urlTextContainer: {
    padding: theme.spacing.md,
  },
  urlTitle: {
    fontSize: theme.fonts.bodyMedium,
    fontWeight: theme.fontWeights.medium,
    color: theme.colors.textPrimary,
  },
  urlDescription: {
    fontSize: theme.fonts.bodySmall,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  inputBar: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: theme.colors.backgroundSecondary,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: theme.colors.backgroundTertiary,
  },
  input: {
    flex: 1,
    maxHeight: 120,
    backgroundColor: '#F6F6F6',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    color: theme.colors.textPrimary,
    lineHeight: 22,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  sendButton: {
    marginLeft: 8,
    paddingHorizontal: 16,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.sendButton,
    borderRadius: 18,
    minWidth: 60,
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  sendButtonText: {
    color: '#FFFFFF',
    fontSize: theme.fonts.button,
    fontWeight: theme.fontWeights.medium,
  },
  audioContainer: {
    height: 44,
    borderRadius: theme.borderRadius.large,
    paddingHorizontal: theme.spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
  },
  audioDurationText: {
    color: theme.colors.textPrimary,
    marginLeft: theme.spacing.sm,
    fontWeight: '500',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bubbleMenuContainer: {
    position: 'absolute',
    flexDirection: 'row',
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: theme.borderRadius.large,
    padding: theme.spacing.xs,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  bubbleMenuItem: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  bubbleMenuText: {
    color: theme.colors.textPrimary,
    fontSize: theme.fonts.bodyMedium,
  },
  overlayContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlayBox: {
    width: 180,
    height: 180,
    backgroundColor: '#222',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.95,
  },
  overlayTime: {
    color: '#fff',
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  overlayTip: {
    color: '#fff',
    fontSize: 16,
    marginTop: 8,
  },
  timestampRow: {
    alignItems: 'center',
    marginVertical: 12,
  },
  timestampText: {
    color: '#999999',
    fontSize: 13,
    backgroundColor: 'rgba(153, 153, 153, 0.12)',
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 4,
    overflow: 'hidden',
    fontWeight: '400',
    letterSpacing: 0.2,
  },
});
