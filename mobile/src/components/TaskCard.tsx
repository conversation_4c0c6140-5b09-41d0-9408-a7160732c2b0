import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { defaultTheme as theme } from '../styles/theme';
import { StickyNote } from '../screens/AllStickyNotesScreen'; // Reuse type for now

interface TaskCardProps {
  task: StickyNote;
  onPress: () => void;
  onLongPress?: () => void;
  isActive?: boolean;
  isWobbling?: boolean;
}

const getIconForCategory = (category?: string) => {
  switch (category) {
    case 'study':
      return 'book-outline';
    case 'work':
      return 'briefcase-outline';
    case 'report':
      return 'document-text-outline';
    case 'meeting':
      return 'people-outline';
    case 'trip':
      return 'location-outline';
    default:
      return 'checkmark-circle-outline';
  }
};

const TaskCard = ({
  task,
  onPress,
  onLongPress,
  isActive = false,
  isWobbling = false,
}: TaskCardProps) => {
  const wobbleAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Controls the wobble animation for all cards when dragging is active
    if (isWobbling) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(wobbleAnim, { toValue: 1, duration: 120, useNativeDriver: true }),
          Animated.timing(wobbleAnim, { toValue: -1, duration: 240, useNativeDriver: true }),
          Animated.timing(wobbleAnim, { toValue: 0, duration: 120, useNativeDriver: true }),
        ])
      ).start();
    } else {
      wobbleAnim.stopAnimation();
      wobbleAnim.setValue(0);
    }
  }, [isWobbling, wobbleAnim]);

  useEffect(() => {
    // Controls the scale of the single card that is being actively dragged
    Animated.spring(scaleAnim, {
      toValue: isActive ? 1.1 : 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    }).start();
  }, [isActive, scaleAnim]);

  const wobbleInterpolate = wobbleAnim.interpolate({
    inputRange: [-1, 0, 1],
    outputRange: ['-2deg', '0deg', '2deg'],
  });

  return (
    <View style={styles.wrapper}>
      <Animated.View
        style={[
          styles.container,
          {
            transform: [{ rotate: wobbleInterpolate }, { scale: scaleAnim }],
          },
        ]}>
        <TouchableOpacity
          onPress={onPress}
          onLongPress={onLongPress}
          style={styles.touchable}>
          <View style={styles.iconContainer}>
            <Ionicons
              name={getIconForCategory(task.category)}
              size={22}
              color={theme.colors.primary}
            />
          </View>
          <View style={styles.titleContainer}>
            <Text style={styles.title} numberOfLines={2}>
              {task.title}
            </Text>
          </View>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
    marginRight: 12,
  },
  container: {
    width: 76,
    height: 96,
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: 0,
    paddingHorizontal: 2,
  },
  touchable: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: 6,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 10,
    backgroundColor: '#F8F9FA',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    paddingHorizontal: 2,
  },
  title: {
    fontSize: 13,
    color: '#374151',
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 18,
    letterSpacing: 0.2,
    includeFontPadding: false,
  },
});

export default TaskCard;
