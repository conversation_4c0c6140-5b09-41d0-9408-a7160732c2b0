import { Model } from '@nozbe/watermelondb';
import { field, text, relation } from '@nozbe/watermelondb/decorators';
import CaptureSession from './CaptureSession';

export default class Media extends Model {
  static table = 'media';

  static associations = {
    capture_sessions: { type: 'belongs_to', key: 'session_id' },
  } as const;

  @text('type') type: 'photo' | 'video';
  @text('uri') uri: string;
  @field('width') width: number;
  @field('height') height: number;
  @field('duration') duration?: number;
  @field('is_temporary') isTemporary: boolean;
  @text('metadata_json') metadataJson?: string;
  @text('session_id') sessionId: string;

  @relation('capture_sessions', 'session_id') session: CaptureSession;
}
