import { Model, Query } from '@nozbe/watermelondb';
import { text, children, date } from '@nozbe/watermelondb/decorators';
import Media from './Media';

export default class CaptureSession extends Model {
  static table = 'capture_sessions';

  static associations = {
    media: { type: 'has_many', foreignKey: 'session_id' },
  } as const;

  @text('status') status: 'active' | 'paused' | 'archived' | 'processed' | 'completed';
  @date('started_at') startedAt: Date;
  @date('ended_at') endedAt?: Date;

  @children('media') media: Query<Media>;
}
