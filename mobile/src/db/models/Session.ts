import { Model, Query } from '@nozbe/watermelondb';
import { field, text, children } from '@nozbe/watermelondb/decorators';
import Message from '../models/Message';

export default class Session extends Model {
  static table = 'sessions';

  static associations = {
    messages: { type: 'has_many', foreignKey: 'session_id' },
  } as const;

  @text('name') name?: string;
  @text('ext') ext?: string;
  @field('created_at') createdAt: number;

  @children('messages') messages: Query<Message>;
}
