import { Model } from '@nozbe/watermelondb';
import { field, text, relation } from '@nozbe/watermelondb/decorators';

export default class MediaProcessingQueue extends Model {
  static table = 'media_processing_queue';

  static associations = {
    media: { type: 'belongs_to', key: 'media_id' },
  } as const;

  @field('media_id') mediaId: string;
  @text('task_type') taskType: 'compress' | 'ocr' | 'tagging';
  @text('status') status: 'pending' | 'processing' | 'done' | 'failed';
  @field('priority') priority: number;
  @field('created_at') createdAt: number;

  @relation('media', 'media_id') media: any;
}
