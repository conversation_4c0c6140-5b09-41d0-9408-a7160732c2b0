import { Model } from '@nozbe/watermelondb';
import { field, text, relation } from '@nozbe/watermelondb/decorators';
import Session from './Session';

export default class Message extends Model {
  static table = 'messages';

  static associations = {
    sessions: { type: 'belongs_to', key: 'session_id' },
  } as const;

  @text('sender') sender: 'user' | 'assistant';
  @text('type') type: 'text' | 'image' | 'audio' | 'card' | 'url' | 'file';
  @text('content') content: string;
  @text('media_uri') mediaUri?: string;
  @text('status') status: 'sent' | 'received' | 'failed' | 'pending' | 'sending';
  /**
   * participants 字段用于存储消息所有参与方，采用 JSON 字符串序列化，如 ["user","assistant"]。
   * 便于未来多方扩展和精确筛选点对点消息。
   */
  @text('participants') participants: string;
  /**
   * ext 字段用于存储复杂消息类型的扩展数据，采用 JSON 字符串序列化。
   * 规范如下：
   * - 语音消息（audio）：{ audioData: { uri: string, duration: number } }
   * - 卡片消息（card）：{ cardData: { title: string, items: string[], action: { label: string, done: boolean } } }
   * - URL消息（url）：{ urlData: { url: string, title: string, description: string, image: string } }
   * - 图片消息（image）：{ mediaUris: string[] }
   * - 文件消息（file）：{ fileData: { uri: string, name: string, size: number, type: string, originalUri: string } }
   * 其他类型、参与方扩展等可通过ext或participants实现。
   * 存取时请使用 JSON.parse/JSON.stringify，避免直接存储对象。
   */
  @text('ext') ext?: string;
  @field('created_at') createdAt: number;
  @text('session_id') sessionId: string;

  @relation('sessions', 'session_id') session: Session;
}
