import { Database } from '@nozbe/watermelondb';
import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite';

import schema from './schema';
import CaptureSession from './models/CaptureSession';
import Media from './models/Media';
import MediaProcessingQueue from './models/MediaProcessingQueue';
import Message from './models/Message';
import Session from './models/Session';
import { ensurePermission } from '../services/PermissionService';

const adapter = new SQLiteAdapter({
  schema,
  // 临时禁用JSI避免初始化问题
  jsi: false,
  onSetUpError: error => {
    // Database failed to load -- a good place to recover
    console.error('Database setup error:', error);
  },
});

export const database = new Database({
  adapter,
  modelClasses: [
    CaptureSession,
    Media,
    MediaProcessingQueue,
    Message,
    Session,
  ],
});

export async function initializeDatabase() {
  const hasStorage = await ensurePermission('storage');
  if (!hasStorage) {
    throw new Error('存储权限未授权，无法初始化数据库');
  }
  // ...后续数据库初始化逻辑
}
