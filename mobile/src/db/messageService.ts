import { database } from './index';
import Message from './models/Message';

/**
 * 查询指定会话的所有消息，按时间倒序排列
 */
export async function getMessagesBySession(sessionId: string): Promise<Message[]> {
  const collection = database.get<Message>('messages');
  const records = await collection.query().fetch();
  // 过滤并排序
  return records
    .filter(msg => msg.sessionId === sessionId)
    .sort((a, b) => b.createdAt - a.createdAt);
}

/**
 * 新增消息
 * @param data 消息数据，participants字段自动补全，ext字段自动序列化
 */
export async function addMessage(data: Partial<Message> & { sessionId: string }): Promise<Message> {
  const collection = database.get<Message>('messages');
  let ext = data.ext;
  if (ext && typeof ext !== 'string') {
    ext = JSON.stringify(ext);
  }
  let participants = data.participants;
  if (!participants) {
    participants = JSON.stringify(['user', 'assistant']);
  } else if (typeof participants !== 'string') {
    participants = JSON.stringify(participants);
  }
  return await database.write(async () => {
    return await collection.create(msg => {
      Object.assign(msg, { ...data, ext, participants });
    });
  });
}

/**
 * 删除消息
 */
export async function deleteMessage(id: string): Promise<void> {
  const collection = database.get<Message>('messages');
  const msg = await collection.find(id);
  if (msg) {
    await database.write(async () => {
      await msg.markAsDeleted();
    });
  }
}

/**
 * 更新消息
 */
export async function updateMessage(id: string, updates: Partial<Message>): Promise<void> {
  const collection = database.get<Message>('messages');
  const msg = await collection.find(id);
  if (msg) {
    await database.write(async () => {
      Object.assign(msg, updates);
    });
  }
}

/**
 * 工具：反序列化ext字段
 */
export function parseExt(ext?: string): any {
  if (!ext) {return undefined;}
  try {
    return JSON.parse(ext);
  } catch {
    return undefined;
  }
}

/**
 * 获取所有用户与App之间的消息，按时间倒序
 * 只包含 sender 为 'user' 或 'assistant'，且仅这两者互相通信的消息
 */
export async function getAllUserAppMessages(): Promise<Message[]> {
  const collection = database.get<Message>('messages');
  // 假设 sender 字段只有 'user' 和 'assistant' 才是点对点消息
  // 如有 receiver 字段，需加 receiver === 'user' || receiver === 'assistant'
  const records = await collection.query().fetch();
  return records
    .filter(msg =>
      (msg.sender === 'user' || msg.sender === 'assistant')
      // 可扩展：如有 receiver 字段，需判断双方仅为 user/assistant
    )
    .sort((a, b) => b.createdAt - a.createdAt);
}

export function parseParticipants(participants?: string): string[] | undefined {
  if (!participants) {return undefined;}
  try {
    return JSON.parse(participants);
  } catch {
    return undefined;
  }
}
