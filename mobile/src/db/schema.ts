import { appSchema, tableSchema } from '@nozbe/watermelondb';

export default appSchema({
  version: 1,
  tables: [
    tableSchema({
      name: 'capture_sessions',
      columns: [
        { name: 'status', type: 'string' },
        { name: 'started_at', type: 'number' },
        { name: 'ended_at', type: 'number', isOptional: true },
        { name: 'created_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'sessions',
      columns: [
        { name: 'name', type: 'string', isOptional: true },
        { name: 'ext', type: 'string', isOptional: true },
        { name: 'created_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'messages',
      columns: [
        { name: 'session_id', type: 'string', isIndexed: true },
        { name: 'sender', type: 'string' },
        { name: 'type', type: 'string' },
        { name: 'participants', type: 'string' },
        { name: 'content', type: 'string' },
        { name: 'media_uri', type: 'string', isOptional: true },
        { name: 'status', type: 'string', isIndexed: true },
        { name: 'ext', type: 'string', isOptional: true },
        { name: 'created_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'media',
      columns: [
        { name: 'session_id', type: 'string', isIndexed: true },
        { name: 'type', type: 'string' },
        { name: 'uri', type: 'string' },
        { name: 'width', type: 'number' },
        { name: 'height', type: 'number' },
        { name: 'duration', type: 'number', isOptional: true },
        { name: 'is_temporary', type: 'boolean' },
        { name: 'metadata_json', type: 'string', isOptional: true },
        { name: 'created_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'media_processing_queue',
      columns: [
        { name: 'media_id', type: 'string', isIndexed: true },
        { name: 'task_type', type: 'string' },
        { name: 'status', type: 'string', isIndexed: true },
        { name: 'priority', type: 'number', isIndexed: true },
        { name: 'created_at', type: 'number' },
      ],
    }),
  ],
});
