/**
 * 文件存储安全性验证工具
 * 专门用于检测和防止iOS文件重复显示问题
 */

import RNFS from 'react-native-fs';
import { Platform } from 'react-native';

export interface FileStorageValidationResult {
  isSecure: boolean;
  issues: string[];
  recommendations: string[];
  pathAnalysis: {
    isInAppSandbox: boolean;
    isUserVisible: boolean;
    actualPath: string;
    pathType: string;
  };
}

/**
 * 验证文件路径是否安全，不会导致重复显示
 */
export async function validateFileStorageSecurity(filePath: string): Promise<FileStorageValidationResult> {
  const result: FileStorageValidationResult = {
    isSecure: true,
    issues: [],
    recommendations: [],
    pathAnalysis: {
      isInAppSandbox: false,
      isUserVisible: false,
      actualPath: filePath,
      pathType: 'unknown',
    },
  };

  try {
    // 分析文件路径
    await analyzeFilePath(filePath, result);

    // 检查iOS特定问题
    if (Platform.OS === 'ios') {
      await checkiOSSpecificIssues(filePath, result);
    }

    // 检查Android特定问题
    if (Platform.OS === 'android') {
      await checkAndroidSpecificIssues(filePath, result);
    }

    // 生成建议
    generateRecommendations(result);

  } catch (error: any) {
    result.isSecure = false;
    result.issues.push(`验证过程出错: ${error.message}`);
  }

  return result;
}

/**
 * 分析文件路径的安全性
 */
async function analyzeFilePath(filePath: string, result: FileStorageValidationResult): Promise<void> {
  const { pathAnalysis } = result;
  pathAnalysis.actualPath = filePath;

  // 检查是否在应用沙盒内
  const documentPath = RNFS.DocumentDirectoryPath;
  const cachePath = RNFS.CachesDirectoryPath;
  const tempPath = RNFS.TemporaryDirectoryPath;

  if (filePath.startsWith(documentPath)) {
    pathAnalysis.pathType = 'DocumentDirectory';
    pathAnalysis.isInAppSandbox = true;
    pathAnalysis.isUserVisible = false; // iOS DocumentDirectory对用户不可见
  } else if (filePath.startsWith(cachePath)) {
    pathAnalysis.pathType = 'CachesDirectory';
    pathAnalysis.isInAppSandbox = true;
    pathAnalysis.isUserVisible = false; // 缓存目录对用户不可见
  } else if (filePath.startsWith(tempPath)) {
    pathAnalysis.pathType = 'TemporaryDirectory';
    pathAnalysis.isInAppSandbox = true;
    pathAnalysis.isUserVisible = false; // 临时目录对用户不可见
  } else {
    pathAnalysis.pathType = 'External';
    pathAnalysis.isInAppSandbox = false;
    pathAnalysis.isUserVisible = true; // 外部路径可能对用户可见

    result.isSecure = false;
    result.issues.push('文件存储在应用沙盒外，可能导致重复显示问题');
  }
}

/**
 * 检查iOS特定的安全问题
 */
async function checkiOSSpecificIssues(filePath: string, result: FileStorageValidationResult): Promise<void> {
  // 检查是否在用户可访问的位置
  const userAccessiblePaths = [
    '/var/mobile/Containers/Shared/AppGroup',
    '/var/mobile/Documents',
    '/Users/', // 模拟器路径
  ];

  for (const userPath of userAccessiblePaths) {
    if (filePath.includes(userPath) && !filePath.includes('Documents/')) {
      result.isSecure = false;
      result.issues.push(`文件可能存储在用户可访问位置: ${userPath}`);
    }
  }

  // 检查是否包含可疑的临时路径标识
  const suspiciousPatterns = [
    '/tmp/',
    '/Inbox/',
    '/Library/Caches/com.apple.documentpicker',
  ];

  for (const pattern of suspiciousPatterns) {
    if (filePath.includes(pattern)) {
      result.issues.push(`检测到可疑路径模式: ${pattern}`);
      if (pattern.includes('documentpicker')) {
        result.isSecure = false;
        result.issues.push('⚠️ 检测到文档选择器缓存路径，这可能导致文件重复显示');
      }
    }
  }
}

/**
 * 检查Android特定的安全问题
 */
async function checkAndroidSpecificIssues(filePath: string, result: FileStorageValidationResult): Promise<void> {
  // 检查是否在外部存储
  const externalPaths = [
    '/storage/emulated/0/',
    '/sdcard/',
    '/storage/sdcard',
  ];

  for (const extPath of externalPaths) {
    if (filePath.startsWith(extPath)) {
      result.isSecure = false;
      result.issues.push(`文件存储在外部存储: ${extPath}`);
    }
  }
}

/**
 * 生成安全建议
 */
function generateRecommendations(result: FileStorageValidationResult): void {
  if (!result.pathAnalysis.isInAppSandbox) {
    result.recommendations.push('建议将文件存储在应用沙盒内（DocumentDirectory或CachesDirectory）');
  }

  if (result.pathAnalysis.isUserVisible) {
    result.recommendations.push('避免将文件存储在用户可见位置，以防止重复显示');
  }

  if (result.issues.some(issue => issue.includes('documentpicker'))) {
    result.recommendations.push('配置react-native-documents-picker的copyTo参数，避免使用默认缓存位置');
  }

  if (result.isSecure) {
    result.recommendations.push('✅ 当前文件存储策略安全，符合最佳实践');
  }
}

/**
 * 获取推荐的安全存储路径
 */
export function getSecureStoragePath(fileName: string): string {
  // 使用DocumentDirectory作为主要存储位置
  return `${RNFS.DocumentDirectoryPath}/ChatFiles/${fileName}`;
}

/**
 * 获取临时安全路径（用于文件选择器）
 */
export function getSecureTempPath(fileName: string): string {
  // 使用CachesDirectory作为临时存储位置
  return `${RNFS.CachesDirectoryPath}/temp_${Date.now()}_${fileName}`;
}

/**
 * 清理临时文件
 */
export async function cleanupTempFiles(): Promise<void> {
  try {
    const tempDir = RNFS.CachesDirectoryPath;
    const files = await RNFS.readDir(tempDir);

    for (const file of files) {
      if (file.name.startsWith('temp_') && file.mtime) {
        const fileAge = Date.now() - file.mtime.getTime();
        // 清理超过1小时的临时文件
        if (fileAge > 60 * 60 * 1000) {
          await RNFS.unlink(file.path);
          console.log(`[FileStorageValidator] 清理临时文件: ${file.name}`);
        }
      }
    }
  } catch (error) {
    console.warn('[FileStorageValidator] 清理临时文件失败:', error);
  }
}

/**
 * 运行完整的存储安全检查
 */
export async function runStorageSecurityCheck(): Promise<FileStorageValidationResult[]> {
  const results: FileStorageValidationResult[] = [];

  // 检查主要存储目录
  const mainStoragePath = `${RNFS.DocumentDirectoryPath}/ChatFiles`;
  const mainResult = await validateFileStorageSecurity(mainStoragePath);
  results.push(mainResult);

  // 检查缓存目录
  const cachePath = RNFS.CachesDirectoryPath;
  const cacheResult = await validateFileStorageSecurity(cachePath);
  results.push(cacheResult);

  return results;
}
