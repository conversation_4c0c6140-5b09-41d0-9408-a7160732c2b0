/**
 * 自定义微信SDK包装器（原生模块）
 * 使用我们自己封装的原生模块，避免第三方库的兼容性问题（iOS和Android）
 */

import { Platform, NativeModules } from 'react-native';

// 获取自定义微信模块
const { CustomWeChatSDK } = NativeModules;

// SDK状态
let sdkInitialized = false;
let sdkError: Error | null = null;

/**
 * 初始化微信SDK
 */
const initializeSDK = () => {
  if (sdkInitialized) {
    return;
  }

  try {
    if (!CustomWeChatSDK) {
      throw new Error('自定义微信模块未找到，请检查原生模块是否正确注册');
    }

    sdkInitialized = true;
    console.log('[CustomWeChatSDK] 微信SDK初始化成功');
    console.log('[CustomWeChatSDK] 平台:', Platform.OS);
    console.log('[CustomWeChatSDK] 可用方法:', Object.keys(CustomWeChatSDK));
  } catch (error) {
    sdkError = error as Error;
    console.error('[CustomWeChatSDK] 微信SDK初始化失败:', error);
  }
};

/**
 * 检查SDK是否可用
 */
export const isSDKAvailable = (): boolean => {
  initializeSDK();
  return sdkInitialized && !sdkError;
};

/**
 * 安全的registerApp
 */
export const safeRegisterApp = async (config: {
  appid: string;
  universalLink?: string;
  log?: boolean;
}): Promise<boolean> => {
  try {
    if (!isSDKAvailable()) {
      console.warn('[CustomWeChatSDK] SDK不可用，跳过registerApp');
      return false;
    }

    console.log('[CustomWeChatSDK] 开始注册微信应用:', config.appid);

    const result = await CustomWeChatSDK.registerApp(config);

    if (result && result.success) {
      console.log('[CustomWeChatSDK] 微信应用注册成功');
      return true;
    } else {
      console.warn('[CustomWeChatSDK] 微信应用注册失败');
      return false;
    }
  } catch (error) {
    console.error('[CustomWeChatSDK] registerApp失败:', error);
    return false;
  }
};

/**
 * 安全的isWechatInstalled
 */
export const safeIsWechatInstalled = async (): Promise<boolean> => {
  try {
    if (!isSDKAvailable()) {
      console.warn('[CustomWeChatSDK] SDK不可用，假设微信已安装');
      return true; // 假设已安装，避免阻止功能
    }

    const result = await CustomWeChatSDK.isWeChatInstalled();
    console.log('[CustomWeChatSDK] 微信安装状态:', result);
    return result;
  } catch (error) {
    console.error('[CustomWeChatSDK] isWechatInstalled失败:', error);
    return true; // 出错时假设已安装
  }
};

/**
 * 安全的openCustomerService
 */
export const safeOpenCustomerService = async (config: {
  corpid: string;
  url: string;
}): Promise<boolean> => {
  try {
    if (!isSDKAvailable()) {
      console.warn('[CustomWeChatSDK] SDK不可用，无法打开客服');
      return false;
    }

    console.log('[CustomWeChatSDK] 调用openCustomerService:', config);

    const result = await CustomWeChatSDK.openCustomerService(config);

    if (result && result.success) {
      console.log('[CustomWeChatSDK] 企业微信客服打开成功');
      return true;
    } else {
      console.warn('[CustomWeChatSDK] 企业微信客服打开失败');
      return false;
    }
  } catch (error) {
    console.error('[CustomWeChatSDK] openCustomerService失败:', error);
    return false;
  }
};

/**
 * 获取SDK错误信息
 */
export const getSDKError = (): Error | null => {
  return sdkError;
};

/**
 * 获取SDK状态信息
 */
export const getSDKStatus = () => {
  return {
    initialized: sdkInitialized,
    available: isSDKAvailable(),
    error: sdkError?.message || null,
    platform: Platform.OS,
  };
};

/**
 * 获取模块详细信息
 */
export const getModuleInfo = async () => {
  try {
    if (!isSDKAvailable()) {
      return {
        available: false,
        error: 'SDK不可用',
      };
    }

    const info = await CustomWeChatSDK.getModuleInfo();
    return info;
  } catch (error) {
    console.error('[CustomWeChatSDK] 获取模块信息失败:', error);
    return {
      available: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

/**
 * 检查注册状态
 */
export const checkRegistrationStatus = async (): Promise<boolean> => {
  try {
    if (!isSDKAvailable()) {
      return false;
    }

    const isRegistered = await CustomWeChatSDK.isRegistered();
    return isRegistered;
  } catch (error) {
    console.error('[CustomWeChatSDK] 检查注册状态失败:', error);
    return false;
  }
};
