/**
 * 设备OCR测试工具
 * 用于在真机上验证OCR功能是否正常工作
 */

import { Platform, Alert } from 'react-native';
import OCRService from '../services/OCRService';

export interface DeviceTestResult {
  testName: string;
  success: boolean;
  message: string;
  data?: any;
  error?: string;
  duration: number;
}

export class DeviceOCRTest {
  private static ocrService = OCRService;

  /**
   * 运行设备OCR能力测试
   */
  static async runDeviceCapabilityTest(): Promise<DeviceTestResult> {
    const startTime = Date.now();

    try {
      console.log('🔍 开始设备OCR能力测试...');

      const capability = await this.ocrService.checkOCRCapability();
      const duration = Date.now() - startTime;

      if (capability.available) {
        return {
          testName: '设备OCR能力检查',
          success: true,
          message: `✅ OCR功能可用，引擎: ${capability.primaryEngine}`,
          data: capability,
          duration,
        };
      } else {
        return {
          testName: '设备OCR能力检查',
          success: false,
          message: `❌ OCR功能不可用: ${capability.reason || '未知原因'}`,
          data: capability,
          duration,
        };
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        testName: '设备OCR能力检查',
        success: false,
        message: '❌ 测试过程中发生错误',
        error: error instanceof Error ? error.message : '未知错误',
        duration,
      };
    }
  }

  /**
   * 测试简单文本识别
   */
  static async runSimpleTextTest(): Promise<DeviceTestResult> {
    const startTime = Date.now();

    try {
      console.log('📝 开始简单文本识别测试...');

      // 使用bundle中的测试图片
      const testImagePath = 'simple-chinese.jpg';

      const result = await this.ocrService.recognizeFromImageUri(testImagePath, {
        language: 'zh-Hans',
        quality: 0.8,
      });

      const duration = Date.now() - startTime;

      if (result.text && result.text.length > 0) {
        return {
          testName: '简单文本识别',
          success: true,
          message: `✅ 识别成功，文本长度: ${result.text.length}字符`,
          data: {
            text: result.text.substring(0, 100) + (result.text.length > 100 ? '...' : ''),
            confidence: result.confidence,
            engine: result.engine,
            processingTime: result.processingTime,
          },
          duration,
        };
      } else {
        return {
          testName: '简单文本识别',
          success: false,
          message: '❌ 未识别到文本内容',
          data: result,
          duration,
        };
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        testName: '简单文本识别',
        success: false,
        message: '❌ 文本识别测试失败',
        error: error instanceof Error ? error.message : '未知错误',
        duration,
      };
    }
  }

  /**
   * 测试中文政务文档识别
   */
  static async runGovernmentDocTest(): Promise<DeviceTestResult> {
    const startTime = Date.now();

    try {
      console.log('📄 开始政务文档识别测试...');

      // 使用工作证件测试图片
      const testImagePath = 'work-permit.jpg';

      const result = await this.ocrService.recognizeFromImageUri(testImagePath, {
        language: 'zh-Hans',
        quality: 0.9,
        optimizeForWeChatScreenshot: false,
      });

      const duration = Date.now() - startTime;

      // 检查是否包含政务关键词
      const governmentKeywords = ['工作证', '单位', '部门', '职务', '证件', '有效期'];
      const foundKeywords = governmentKeywords.filter(keyword =>
        result.text.includes(keyword)
      );

      if (result.text && result.text.length > 0) {
        return {
          testName: '政务文档识别',
          success: foundKeywords.length > 0,
          message: foundKeywords.length > 0
            ? `✅ 识别成功，发现${foundKeywords.length}个政务关键词`
            : '⚠️ 识别到文本但未发现政务关键词',
          data: {
            text: result.text.substring(0, 150) + (result.text.length > 150 ? '...' : ''),
            confidence: result.confidence,
            engine: result.engine,
            foundKeywords,
            processingTime: result.processingTime,
          },
          duration,
        };
      } else {
        return {
          testName: '政务文档识别',
          success: false,
          message: '❌ 未识别到文档内容',
          data: result,
          duration,
        };
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        testName: '政务文档识别',
        success: false,
        message: '❌ 政务文档识别测试失败',
        error: error instanceof Error ? error.message : '未知错误',
        duration,
      };
    }
  }

  /**
   * 运行完整的设备测试套件
   */
  static async runFullDeviceTestSuite(): Promise<{
    overall: boolean;
    results: DeviceTestResult[];
    summary: string;
  }> {
    console.log('🚀 开始完整设备OCR测试套件...');

    const results: DeviceTestResult[] = [];

    // 测试1: 设备能力检查
    results.push(await this.runDeviceCapabilityTest());

    // 测试2: 简单文本识别
    results.push(await this.runSimpleTextTest());

    // 测试3: 政务文档识别
    results.push(await this.runGovernmentDocTest());

    // 计算总体结果
    const passedTests = results.filter(test => test.success).length;
    const totalTests = results.length;
    const overall = passedTests === totalTests;

    const summary = `测试完成: ${passedTests}/${totalTests} 通过 (${Math.round(passedTests / totalTests * 100)}%)`;

    console.log(`📊 ${summary}`);
    console.log(`🎯 总体结果: ${overall ? '✅ 全部通过' : '❌ 部分失败'}`);

    return { overall, results, summary };
  }

  /**
   * 显示测试结果对话框
   */
  static showTestResults(results: DeviceTestResult[], summary: string) {
    const resultText = results.map(result =>
      `${result.success ? '✅' : '❌'} ${result.testName}\n   ${result.message}\n   耗时: ${result.duration}ms`
    ).join('\n\n');

    Alert.alert(
      '设备OCR测试结果',
      `${summary}\n\n${resultText}`,
      [
        { text: '查看详情', onPress: () => console.log('详细结果:', results) },
        { text: '确定' },
      ]
    );
  }

  /**
   * 快速验证OCR功能
   */
  static async quickValidation(): Promise<boolean> {
    try {
      const capability = await this.ocrService.checkOCRCapability();
      return capability.available && Platform.OS === 'ios';
    } catch (error) {
      console.error('快速验证失败:', error);
      return false;
    }
  }
}
