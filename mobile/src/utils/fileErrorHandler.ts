import { Alert } from 'react-native';

// 文件操作错误类型
export enum FileErrorType {
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  UNSUPPORTED_FORMAT = 'UNSUPPORTED_FORMAT',
  STORAGE_FULL = 'STORAGE_FULL',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  PREVIEW_FAILED = 'PREVIEW_FAILED',
  SAVE_FAILED = 'SAVE_FAILED',
  SELECTION_CANCELLED = 'SELECTION_CANCELLED'
}

// 🔧 政务友好的错误消息 - 温和专业语调，体现政务助手亲和力
export const friendlyErrorMessages = {
  [FileErrorType.FILE_NOT_FOUND]: {
    title: '文件暂时无法找到',
    message: '很抱歉，您要查看的文件暂时无法找到。这可能是因为文件位置发生了变化。建议您重新选择文件，我们将为您妥善处理。',
    suggestion: '您可以重新选择文件，或联系我们为您提供帮助',
  },
  [FileErrorType.FILE_TOO_LARGE]: {
    title: '文件稍大了一些',
    message: '您选择的文件稍大了一些（超过100MB限制）。为了给您提供更流畅的使用体验，建议您选择稍小一些的文件。',
    suggestion: '您可以压缩文件后重试，或选择其他文件',
  },
  [FileErrorType.UNSUPPORTED_FORMAT]: {
    title: '文件格式暂不支持',
    message: '很抱歉，暂时还不支持这种文件格式。目前我们支持PDF、OFD、Word、Excel、PowerPoint、WPS文档等常用办公格式。',
    suggestion: '建议您转换为支持的格式，或使用其他应用打开',
  },
  [FileErrorType.STORAGE_FULL]: {
    title: '存储空间需要整理',
    message: '您的设备存储空间有些紧张，暂时无法保存新文件。建议您整理一下设备空间，为重要文件腾出位置。',
    suggestion: '您可以清理一些不需要的文件，然后重新尝试',
  },
  [FileErrorType.PERMISSION_DENIED]: {
    title: '需要文件访问权限',
    message: '为了更好地为您服务，需要获得文件访问权限。您可以在设备设置中为公职猫开启文件访问权限。',
    suggestion: '请前往设置 > 应用权限 > 公职猫，开启文件访问权限',
  },
  [FileErrorType.NETWORK_ERROR]: {
    title: '网络暂时不稳定',
    message: '网络连接暂时不太稳定，但请放心，公职猫的本地功能完全不受影响。您可以继续使用文件管理、预览等功能。',
    suggestion: '您可以稍后重试网络功能，或继续使用离线功能',
  },
  [FileErrorType.PREVIEW_FAILED]: {
    title: '文件预览遇到小问题',
    message: '很抱歉，暂时无法为您展示此文件内容。这可能是文件格式特殊或文件损坏导致的。建议您稍后重试，或使用其他应用查看文件。',
    suggestion: '您可以稍后重试，或使用其他应用打开文件',
  },
  [FileErrorType.SAVE_FAILED]: {
    title: '文件保存遇到问题',
    message: '很抱歉，文件保存时遇到了一些问题。这通常是因为存储空间不足或文件正在被其他程序使用。',
    suggestion: '建议您检查存储空间后重试，或稍后再试',
  },
  [FileErrorType.SELECTION_CANCELLED]: {
    title: '操作已取消',
    message: '您已取消文件选择操作。',
    suggestion: '如需继续，请重新选择文件',
  },
  [FileErrorType.UNKNOWN_ERROR]: {
    title: '遇到意外情况',
    message: '很抱歉，遇到了一个意外情况。我们正在努力改进服务质量。建议您稍后重试，如问题持续存在，欢迎联系我们。',
    suggestion: '您可以稍后重试，或联系我们获得帮助',
  },
};

// 错误处理类
export class FileErrorHandler {
  /**
   * 显示友好的错误提示
   */
  static showError(errorType: FileErrorType, customMessage?: string): void {
    const errorInfo = friendlyErrorMessages[errorType];
    const message = customMessage || errorInfo.message;

    Alert.alert(
      errorInfo.title,
      message,
      [
        {
          text: '我知道了',
          style: 'default',
        },
      ]
    );
  }

  /**
   * 显示带建议的错误提示
   */
  static showErrorWithSuggestion(errorType: FileErrorType, customMessage?: string): void {
    const errorInfo = friendlyErrorMessages[errorType];
    const message = customMessage || errorInfo.message;
    const fullMessage = errorInfo.suggestion
      ? `${message}\n\n${errorInfo.suggestion}`
      : message;

    Alert.alert(
      errorInfo.title,
      fullMessage,
      [
        {
          text: '我知道了',
          style: 'default',
        },
      ]
    );
  }

  /**
   * 显示确认对话框
   */
  static showConfirmation(
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
  ): void {
    Alert.alert(
      title,
      message,
      [
        {
          text: '暂不操作',
          style: 'cancel',
          onPress: onCancel,
        },
        {
          text: '继续操作',
          style: 'default',
          onPress: onConfirm,
        },
      ]
    );
  }

  /**
   * 根据错误信息自动识别错误类型
   */
  static identifyErrorType(error: any): FileErrorType {
    const errorMessage = error?.message?.toLowerCase() || '';

    if (errorMessage.includes('not found') || errorMessage.includes('不存在')) {
      return FileErrorType.FILE_NOT_FOUND;
    }

    if (errorMessage.includes('too large') || errorMessage.includes('太大')) {
      return FileErrorType.FILE_TOO_LARGE;
    }

    if (errorMessage.includes('unsupported') || errorMessage.includes('不支持')) {
      return FileErrorType.UNSUPPORTED_FORMAT;
    }

    if (errorMessage.includes('storage') || errorMessage.includes('存储')) {
      return FileErrorType.STORAGE_FULL;
    }

    if (errorMessage.includes('permission') || errorMessage.includes('权限')) {
      return FileErrorType.PERMISSION_DENIED;
    }

    if (errorMessage.includes('network') || errorMessage.includes('网络')) {
      return FileErrorType.NETWORK_ERROR;
    }

    if (errorMessage.includes('preview') || errorMessage.includes('预览')) {
      return FileErrorType.PREVIEW_FAILED;
    }

    if (errorMessage.includes('save') || errorMessage.includes('保存')) {
      return FileErrorType.SAVE_FAILED;
    }

    if (errorMessage.includes('cancel') || errorMessage.includes('取消')) {
      return FileErrorType.SELECTION_CANCELLED;
    }

    return FileErrorType.UNKNOWN_ERROR;
  }

  /**
   * 智能错误处理 - 自动识别错误类型并显示友好提示
   */
  static handleError(error: any, context?: string): void {
    console.error(`[FileErrorHandler] ${context || '文件操作'} 错误:`, error);

    const errorType = this.identifyErrorType(error);

    // 对于取消操作，不显示错误提示
    if (errorType === FileErrorType.SELECTION_CANCELLED) {
      return;
    }

    this.showErrorWithSuggestion(errorType);
  }

  /**
   * 文件大小验证
   */
  static validateFileSize(fileSize: number, maxSize: number = 50 * 1024 * 1024): boolean {
    if (fileSize > maxSize) {
      this.showErrorWithSuggestion(FileErrorType.FILE_TOO_LARGE);
      return false;
    }
    return true;
  }

  /**
   * 文件格式验证 - 现在支持所有文件类型
   */
  static validateFileFormat(fileName: string): boolean {
    // 🆕 支持所有文件类型，不再限制格式
    // 只检查文件名是否有效
    const extension = fileName.toLowerCase().split('.').pop();

    if (!extension || extension.length === 0) {
      console.warn('[FileErrorHandler] 文件没有扩展名，但仍然允许:', fileName);
    }

    // 总是返回true，支持所有文件类型
    return true;
  }

  /**
   * 存储空间检查
   */
  static async checkStorageSpace(_requiredSpace: number): Promise<boolean> {
    // 这里可以添加实际的存储空间检查逻辑
    // 目前返回true，实际项目中应该检查可用空间
    return true;
  }
}

// 导出常用的错误处理函数
export const showFileError = FileErrorHandler.showError;
export const showFileErrorWithSuggestion = FileErrorHandler.showErrorWithSuggestion;
export const handleFileError = FileErrorHandler.handleError;
export const validateFileSize = FileErrorHandler.validateFileSize;
export const validateFileFormat = FileErrorHandler.validateFileFormat;
