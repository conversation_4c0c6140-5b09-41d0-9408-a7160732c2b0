import { startOfMonth, endOfMonth, eachDayOfInterval, getDay, startOfWeek, endOfWeek } from 'date-fns';

export const getCalendarGridData = (date: Date): (Date | null)[] => {
    const firstDayOfMonth = startOfMonth(date);
    const daysInMonth = eachDayOfInterval({ start: firstDayOfMonth, end: endOfMonth(date) });

    const dayOfWeekForFirst = getDay(firstDayOfMonth); // 0=Sun, 1=Mon, ...

    // If week starts on Monday, Monday should have 0 padding, and Sunday should have 6.
    const startPaddingCount = (dayOfWeekForFirst === 0) ? 6 : dayOfWeekForFirst - 1;
    const startPadding = Array(startPaddingCount).fill(null);

    const combined = [...startPadding, ...daysInMonth];

    // Pad end to ensure 6 rows for a consistent layout (42 cells total)
    const endPaddingCount = 42 - combined.length;
    const endPadding = Array(endPaddingCount > 0 ? endPaddingCount : 0).fill(null);

    return [...combined, ...endPadding];
};

export const getWeekGridData = (date: Date): Date[] => {
    const start = startOfWeek(date, { weekStartsOn: 1 }); // Monday
    const end = endOfWeek(date, { weekStartsOn: 1 });
    return eachDayOfInterval({start, end});
};
