/**
 * 统一的设备信息管理工具
 * 提供用户标识、设备标识、平台信息、应用版本等统一管理
 * 避免在多个文件中重复实现相同功能
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// 用户设备信息接口
interface UserDeviceInfo {
  userUuid: string;
  deviceId: string;
  platform: 'android' | 'ios';
  createdAt: number;
  lastUpdated: number;
}

// 设备信息接口
interface DeviceInfo {
  platform: 'android' | 'ios';
  osVersion: string;
  appVersion: string;
  deviceModel?: string;
  deviceName?: string;
}

/**
 * 统一的存储键名配置
 */
const STORAGE_KEYS = {
  userUuid: 'user_uuid',
  deviceId: 'app_device_id',
  userUuidInfo: 'user_uuid_info',
  deviceIdInfo: 'device_id_info',
} as const;

/**
 * 获取当前平台
 */
function getCurrentPlatform(): 'android' | 'ios' {
  return Platform.OS === 'ios' ? 'ios' : 'android';
}

/**
 * 获取应用版本（从package.json）
 */
function getAppVersion(): string {
  try {
    // 修复：使用正确的相对路径
    const { version } = require('../../package.json');
    return version || '1.0.0';
  } catch (error) {
    console.warn('[UserDeviceManager] 无法读取package.json版本，使用默认版本:', error);
    return '1.0.0';
  }
}

/**
 * 获取设备信息
 */
async function getDeviceInfo(): Promise<DeviceInfo> {
  try {
    const DeviceInfo = require('react-native-device-info');

    const [osVersion, appVersion, deviceModel, deviceName] = await Promise.all([
      DeviceInfo.getSystemVersion(),
      DeviceInfo.getVersion(),
      DeviceInfo.getModel(),
      DeviceInfo.getDeviceName(),
    ]);

    return {
      platform: getCurrentPlatform(),
      osVersion,
      appVersion,
      deviceModel,
      deviceName,
    };
  } catch (error) {
    console.warn('[UserDeviceManager] 获取设备信息失败，使用降级方案:', error);

    // 降级方案
    return {
      platform: getCurrentPlatform(),
      osVersion: 'unknown',
      appVersion: getAppVersion(),
      deviceModel: 'unknown',
      deviceName: 'unknown',
    };
  }
}

/**
 * 生成UUID
 */
function generateUuid(): string {
  try {
    const { v4: uuidv4 } = require('uuid');
    return uuidv4();
  } catch (error) {
    console.warn('[UserDeviceManager] UUID库不可用，使用降级方案:', error);

    // 降级方案：使用时间戳和随机数生成类似UUID的字符串
    const timestamp = Date.now().toString(36);
    const random1 = Math.random().toString(36).substring(2, 8);
    const random2 = Math.random().toString(36).substring(2, 8);
    const random3 = Math.random().toString(36).substring(2, 8);

    return `${timestamp}-${random1}-${random2}-${random3}`;
  }
}

/**
 * 生成设备ID
 */
async function generateDeviceId(): Promise<string> {
  const platform = getCurrentPlatform();
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);

  return `${platform}_${timestamp}_${random}`;
}

/**
 * 用户UUID管理器
 */
export class UserUuidManager {
  /**
   * 获取用户UUID
   */
  static async getUserUuid(): Promise<string> {
    try {
      const userUuid = await AsyncStorage.getItem(STORAGE_KEYS.userUuid);

      if (!userUuid) {
        // 生成新的UUID
        const newUuid = generateUuid();
        await AsyncStorage.setItem(STORAGE_KEYS.userUuid, newUuid);

        // 保存UUID信息
        await this.saveUserUuidInfo(newUuid);

        console.log('[UserUuidManager] 生成新的用户UUID:', newUuid);
        return newUuid;
      }

      return userUuid;
    } catch (error) {
      console.error('[UserUuidManager] 获取用户UUID失败:', error);

      // 降级方案：使用时间戳生成
      const fallbackUuid = `user_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
      console.warn('[UserUuidManager] 使用降级UUID:', fallbackUuid);
      return fallbackUuid;
    }
  }

  /**
   * 保存用户UUID信息
   */
  private static async saveUserUuidInfo(userUuid: string): Promise<void> {
    try {
      const info = {
        userUuid,
        createdAt: Date.now(),
        lastUpdated: Date.now(),
      };

      await AsyncStorage.setItem(STORAGE_KEYS.userUuidInfo, JSON.stringify(info));
    } catch (error) {
      console.error('[UserUuidManager] 保存用户UUID信息失败:', error);
    }
  }

  /**
   * 获取用户UUID信息
   */
  static async getUserUuidInfo(): Promise<{ userUuid: string; createdAt: number; lastUpdated: number } | null> {
    try {
      const infoStr = await AsyncStorage.getItem(STORAGE_KEYS.userUuidInfo);
      if (!infoStr) {
        return null;
      }

      return JSON.parse(infoStr);
    } catch (error) {
      console.error('[UserUuidManager] 获取用户UUID信息失败:', error);
      return null;
    }
  }

  /**
   * 重新生成用户UUID
   */
  static async regenerateUserUuid(): Promise<string> {
    try {
      const newUuid = generateUuid();

      await AsyncStorage.setItem(STORAGE_KEYS.userUuid, newUuid);
      await this.saveUserUuidInfo(newUuid);

      console.log('[UserUuidManager] 重新生成用户UUID:', newUuid);
      return newUuid;
    } catch (error) {
      console.error('[UserUuidManager] 重新生成用户UUID失败:', error);
      throw error;
    }
  }

  /**
   * 清除用户UUID
   */
  static async clearUserUuid(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.userUuid);
      await AsyncStorage.removeItem(STORAGE_KEYS.userUuidInfo);

      console.log('[UserUuidManager] 用户UUID已清除');
    } catch (error) {
      console.error('[UserUuidManager] 清除用户UUID失败:', error);
    }
  }
}

/**
 * 设备ID管理器
 */
export class DeviceIdManager {
  /**
   * 获取设备ID
   */
  static async getDeviceId(): Promise<string> {
    try {
      const deviceId = await AsyncStorage.getItem(STORAGE_KEYS.deviceId);

      if (!deviceId) {
        // 生成新的设备ID
        const newDeviceId = await generateDeviceId();
        await AsyncStorage.setItem(STORAGE_KEYS.deviceId, newDeviceId);

        // 保存设备ID信息
        await this.saveDeviceIdInfo(newDeviceId);

        console.log('[DeviceIdManager] 生成新的设备ID:', newDeviceId);
        return newDeviceId;
      }

      return deviceId;
    } catch (error) {
      console.error('[DeviceIdManager] 获取设备ID失败:', error);

      // 降级方案：使用时间戳
      const fallbackDeviceId = `${getCurrentPlatform()}_${Date.now()}`;
      console.warn('[DeviceIdManager] 使用降级设备ID:', fallbackDeviceId);
      return fallbackDeviceId;
    }
  }

  /**
   * 保存设备ID信息
   */
  private static async saveDeviceIdInfo(deviceId: string): Promise<void> {
    try {
      const deviceInfo = await getDeviceInfo();
      const info = {
        deviceId,
        ...deviceInfo,
        createdAt: Date.now(),
        lastUpdated: Date.now(),
      };

      await AsyncStorage.setItem(STORAGE_KEYS.deviceIdInfo, JSON.stringify(info));
    } catch (error) {
      console.error('[DeviceIdManager] 保存设备ID信息失败:', error);
    }
  }

  /**
   * 获取设备ID信息
   */
  static async getDeviceIdInfo(): Promise<(DeviceInfo & { deviceId: string; createdAt: number; lastUpdated: number }) | null> {
    try {
      const infoStr = await AsyncStorage.getItem(STORAGE_KEYS.deviceIdInfo);
      if (!infoStr) {
        return null;
      }

      return JSON.parse(infoStr);
    } catch (error) {
      console.error('[DeviceIdManager] 获取设备ID信息失败:', error);
      return null;
    }
  }

  /**
   * 重新生成设备ID
   */
  static async regenerateDeviceId(): Promise<string> {
    try {
      const newDeviceId = await generateDeviceId();

      await AsyncStorage.setItem(STORAGE_KEYS.deviceId, newDeviceId);
      await this.saveDeviceIdInfo(newDeviceId);

      console.log('[DeviceIdManager] 重新生成设备ID:', newDeviceId);
      return newDeviceId;
    } catch (error) {
      console.error('[DeviceIdManager] 重新生成设备ID失败:', error);
      throw error;
    }
  }

  /**
   * 清除设备ID
   */
  static async clearDeviceId(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.deviceId);
      await AsyncStorage.removeItem(STORAGE_KEYS.deviceIdInfo);

      console.log('[DeviceIdManager] 设备ID已清除');
    } catch (error) {
      console.error('[DeviceIdManager] 清除设备ID失败:', error);
    }
  }
}

/**
 * 用户设备管理器（统一管理）
 */
export class UserDeviceManager {
  private static instance: UserDeviceManager;

  private constructor() {}

  public static getInstance(): UserDeviceManager {
    if (!UserDeviceManager.instance) {
      UserDeviceManager.instance = new UserDeviceManager();
    }
    return UserDeviceManager.instance;
  }

  /**
   * 获取当前平台
   */
  getCurrentPlatform(): 'android' | 'ios' {
    return getCurrentPlatform();
  }

  /**
   * 获取应用版本
   */
  getAppVersion(): string {
    return getAppVersion();
  }

  /**
   * 获取设备信息
   */
  async getDeviceInfo(): Promise<DeviceInfo> {
    return getDeviceInfo();
  }

  /**
   * 获取用户设备信息
   */
  async getUserDeviceInfo(): Promise<UserDeviceInfo> {
    const [userUuid, deviceId] = await Promise.all([
      UserUuidManager.getUserUuid(),
      DeviceIdManager.getDeviceId(),
    ]);

    const deviceInfo = await getDeviceInfo();

    return {
      userUuid,
      deviceId,
      platform: deviceInfo.platform,
      createdAt: Date.now(),
      lastUpdated: Date.now(),
    };
  }

  /**
   * 获取用户UUID
   */
  async getUserUuid(): Promise<string> {
    return UserUuidManager.getUserUuid();
  }

  /**
   * 获取设备ID
   */
  async getDeviceId(): Promise<string> {
    return DeviceIdManager.getDeviceId();
  }

  /**
   * 重新生成所有标识
   */
  async regenerateAll(): Promise<UserDeviceInfo> {
    const [userUuid, deviceId] = await Promise.all([
      UserUuidManager.regenerateUserUuid(),
      DeviceIdManager.regenerateDeviceId(),
    ]);

    const deviceInfo = await getDeviceInfo();

    return {
      userUuid,
      deviceId,
      platform: deviceInfo.platform,
      createdAt: Date.now(),
      lastUpdated: Date.now(),
    };
  }

  /**
   * 清除所有标识
   */
  async clearAll(): Promise<void> {
    await Promise.all([
      UserUuidManager.clearUserUuid(),
      DeviceIdManager.clearDeviceId(),
    ]);
  }

  /**
   * 获取详细信息（用于调试）
   */
  async getDetailedInfo(): Promise<{
    userUuid: { userUuid: string; createdAt: number; lastUpdated: number } | null;
    deviceId: (DeviceInfo & { deviceId: string; createdAt: number; lastUpdated: number }) | null;
  }> {
    const [userUuidInfo, deviceIdInfo] = await Promise.all([
      UserUuidManager.getUserUuidInfo(),
      DeviceIdManager.getDeviceIdInfo(),
    ]);

    return {
      userUuid: userUuidInfo,
      deviceId: deviceIdInfo,
    };
  }
}

// 导出单例实例
export const userDeviceManager = UserDeviceManager.getInstance();

// 导出类型
export type { UserDeviceInfo, DeviceInfo };
