/**
 * Vision OCR 验证工具
 * 用于测试iOS Vision框架集成的完整性
 */

import { NativeModules, Platform } from 'react-native';

const { RNVisionOCR } = NativeModules;

export interface ValidationResult {
  success: boolean;
  message: string;
  details?: any;
  timestamp: string;
}

export class VisionOCRValidator {
  /**
   * 验证原生模块是否正确加载
   */
  static validateNativeModule(): ValidationResult {
    const timestamp = new Date().toISOString();

    if (Platform.OS !== 'ios') {
      return {
        success: false,
        message: 'Vision OCR 仅支持iOS平台',
        timestamp,
      };
    }

    if (!RNVisionOCR) {
      return {
        success: false,
        message: 'RNVisionOCR 原生模块未找到',
        details: {
          availableModules: Object.keys(NativeModules),
          platform: Platform.OS,
          version: Platform.Version,
        },
        timestamp,
      };
    }

    return {
      success: true,
      message: 'RNVisionOCR 原生模块加载成功',
      details: {
        module: 'RNVisionOCR',
        platform: Platform.OS,
        version: Platform.Version,
        methods: Object.keys(RNVisionOCR),
      },
      timestamp,
    };
  }

  /**
   * 验证OCR方法是否可用
   */
  static async validateOCRMethods(): Promise<ValidationResult> {
    const timestamp = new Date().toISOString();
    const moduleValidation = this.validateNativeModule();
    if (!moduleValidation.success) {
      return moduleValidation;
    }

    try {
      // 直接测试recognizeText方法是否存在并可调用
      if (typeof RNVisionOCR.recognizeText !== 'function') {
        return {
          success: false,
          message: '缺少必需的OCR方法: recognizeText',
          details: {
            moduleType: typeof RNVisionOCR,
            recognizeTextType: typeof RNVisionOCR.recognizeText,
            availableProperties: Object.keys(RNVisionOCR),
          },
          timestamp,
        };
      }

      return {
        success: true,
        message: '所有必需的OCR方法都可用',
        details: {
          recognizeTextAvailable: true,
          moduleProperties: Object.keys(RNVisionOCR),
        },
        timestamp,
      };
    } catch (error: any) {
      return {
        success: false,
        message: `验证OCR方法时发生错误: ${error?.message || '未知错误'}`,
        details: {
          error: error?.message || '未知错误',
          stack: error?.stack,
        },
        timestamp,
      };
    }
  }

  /**
   * 测试OCR功能（使用测试图片）
   */
  static async testOCRFunctionality(): Promise<ValidationResult> {
    const timestamp = new Date().toISOString();

    const methodValidation = await this.validateOCRMethods();
    if (!methodValidation.success) {
      return methodValidation;
    }

    try {
      // 创建一个简单的测试图片URI（base64编码的简单文本图片）
      const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

      const result = await RNVisionOCR.recognizeText(testImageBase64);

      return {
        success: true,
        message: 'OCR功能测试成功',
        details: {
          testImage: 'base64测试图片',
          result: result,
          resultType: typeof result,
        },
        timestamp,
      };
    } catch (error: any) {
      return {
        success: false,
        message: `OCR功能测试失败: ${error?.message || '未知错误'}`,
        details: {
          error: error?.message || '未知错误',
          stack: error?.stack,
        },
        timestamp,
      };
    }
  }

  /**
   * 运行完整的验证套件
   */
  static async runFullValidation(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    console.log('🔍 开始Vision OCR完整验证...');

    // 1. 验证原生模块
    console.log('1️⃣ 验证原生模块...');
    const moduleResult = this.validateNativeModule();
    results.push(moduleResult);
    console.log(moduleResult.success ? '✅' : '❌', moduleResult.message);

    // 2. 验证OCR方法
    console.log('2️⃣ 验证OCR方法...');
    const methodResult = await this.validateOCRMethods();
    results.push(methodResult);
    console.log(methodResult.success ? '✅' : '❌', methodResult.message);

    // 3. 测试OCR功能
    if (methodResult.success) {
      console.log('3️⃣ 测试OCR功能...');
      const functionalityResult = await this.testOCRFunctionality();
      results.push(functionalityResult);
      console.log(functionalityResult.success ? '✅' : '❌', functionalityResult.message);
    } else {
      console.log('3️⃣ 跳过OCR功能测试（前置条件未满足）');
    }

    const allSuccess = results.every(r => r.success);
    console.log('🏁 验证完成:', allSuccess ? '全部通过' : '存在问题');

    return results;
  }

  /**
   * 生成验证报告
   */
  static generateReport(results: ValidationResult[]): string {
    const timestamp = new Date().toLocaleString('zh-CN');
    const allSuccess = results.every(r => r.success);

    let report = '# Vision OCR 验证报告\n\n';
    report += `**生成时间**: ${timestamp}\n`;
    report += `**总体状态**: ${allSuccess ? '✅ 通过' : '❌ 失败'}\n\n`;

    results.forEach((result, index) => {
      report += `## ${index + 1}. ${result.success ? '✅' : '❌'} ${result.message}\n\n`;
      report += `**时间**: ${new Date(result.timestamp).toLocaleString('zh-CN')}\n\n`;

      if (result.details) {
        report += '**详细信息**:\n';
        report += `\`\`\`json\n${JSON.stringify(result.details, null, 2)}\n\`\`\`\n\n`;
      }
    });

    return report;
  }
}

export default VisionOCRValidator;
