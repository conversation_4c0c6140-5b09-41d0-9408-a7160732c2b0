/**
 * 文件存储路径验证工具
 * 确保文件存储符合政务应用安全要求
 */

import RNFS from 'react-native-fs';
import { Platform } from 'react-native';

export interface StorageValidationResult {
  isValid: boolean;
  isInAppSandbox: boolean;
  isHiddenFromUser: boolean;
  actualPath: string;
  recommendations: string[];
}

/**
 * 验证文件存储路径的安全性
 */
export async function validateStoragePath(storagePath: string): Promise<StorageValidationResult> {
  const result: StorageValidationResult = {
    isValid: true,
    isInAppSandbox: false,
    isHiddenFromUser: false,
    actualPath: storagePath,
    recommendations: [],
  };

  try {
    // 检查路径是否在应用沙盒内
    const documentPath = RNFS.DocumentDirectoryPath;
    const isInDocuments = storagePath.startsWith(documentPath);

    if (Platform.OS === 'ios') {
      // iOS: DocumentDirectoryPath 是应用沙盒内的安全路径
      result.isInAppSandbox = isInDocuments;
      result.isHiddenFromUser = isInDocuments; // iOS Documents目录对用户不可见

      if (!isInDocuments) {
        result.isValid = false;
        result.recommendations.push('iOS应用应使用DocumentDirectoryPath存储文件');
      }
    } else if (Platform.OS === 'android') {
      // Android: DocumentDirectoryPath 通常指向应用私有目录
      result.isInAppSandbox = isInDocuments;
      result.isHiddenFromUser = isInDocuments; // Android应用私有目录对用户不可见

      if (!isInDocuments) {
        result.isValid = false;
        result.recommendations.push('Android应用应使用DocumentDirectoryPath存储文件');
      }
    }

    // 检查是否使用了外部存储路径（不安全）
    const unsafePaths = [
      '/storage/emulated/0/', // Android外部存储
      '/sdcard/', // Android SD卡
      RNFS.ExternalDirectoryPath, // React Native外部目录
    ].filter(Boolean);

    for (const unsafePath of unsafePaths) {
      if (storagePath.startsWith(unsafePath)) {
        result.isValid = false;
        result.isInAppSandbox = false;
        result.isHiddenFromUser = false;
        result.recommendations.push(`避免使用外部存储路径: ${unsafePath}`);
      }
    }

    // 政务应用特殊要求检查
    if (result.isValid) {
      result.recommendations.push('✅ 存储路径符合政务应用安全要求');
      result.recommendations.push('✅ 文件仅在应用内可见，不会污染用户文件系统');
    }

  } catch (error) {
    result.isValid = false;
    result.recommendations.push(`路径验证失败: ${error}`);
  }

  return result;
}

/**
 * 获取推荐的安全存储路径
 */
export function getRecommendedStoragePath(subDir: string = 'ChatFiles'): string {
  return `${RNFS.DocumentDirectoryPath}/${subDir}`;
}

/**
 * 验证当前FileStorageService配置
 */
export async function validateCurrentStorageConfig(): Promise<StorageValidationResult> {
  const currentPath = getRecommendedStoragePath('ChatFiles');
  return validateStoragePath(currentPath);
}
