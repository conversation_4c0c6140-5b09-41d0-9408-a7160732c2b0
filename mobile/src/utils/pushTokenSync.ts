/**
 * Push Token 同步工具
 * 用于修复数据库中缺失的push_token问题
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { DeviceRegistrationService } from '../services/DeviceRegistrationService';
import JPushService from '../services/JPushService';

export class PushTokenSyncUtil {
  /**
   * 检查并同步push_token
   * 如果JPush有注册ID但设备注册服务没有，则进行同步
   */
  static async checkAndSyncPushToken(): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      console.log('[PushTokenSync] 开始检查push_token同步状态...');

      // 1. 检查JPush注册ID
      const jpushRegistrationId = JPushService.getRegistrationID();
      const localStorageId = await AsyncStorage.getItem('jpush_registration_id');
      
      console.log('[PushTokenSync] JPush服务中的注册ID:', jpushRegistrationId);
      console.log('[PushTokenSync] 本地存储中的注册ID:', localStorageId);

      // 2. 检查设备注册服务中的push_token
      const deviceService = DeviceRegistrationService.getInstance();
      const currentPushToken = await deviceService.getPushToken();
      
      console.log('[PushTokenSync] 设备注册服务中的push_token:', currentPushToken);

      // 3. 确定有效的注册ID
      const validRegistrationId = jpushRegistrationId || localStorageId;
      
      if (!validRegistrationId) {
        return {
          success: false,
          message: 'JPush注册ID不存在，无法同步',
          details: {
            jpushRegistrationId,
            localStorageId,
            currentPushToken,
          }
        };
      }

      // 4. 检查是否需要同步
      if (currentPushToken === validRegistrationId) {
        return {
          success: true,
          message: 'push_token已同步，无需更新',
          details: {
            registrationId: validRegistrationId,
            alreadySynced: true,
          }
        };
      }

      // 5. 执行同步
      console.log('[PushTokenSync] 开始同步push_token:', validRegistrationId);
      await deviceService.setPushToken(validRegistrationId);

      // 6. 验证同步结果
      const updatedPushToken = await deviceService.getPushToken();
      const syncSuccess = updatedPushToken === validRegistrationId;

      return {
        success: syncSuccess,
        message: syncSuccess ? 'push_token同步成功' : 'push_token同步失败',
        details: {
          registrationId: validRegistrationId,
          beforeSync: currentPushToken,
          afterSync: updatedPushToken,
          syncSuccess,
        }
      };

    } catch (error) {
      console.error('[PushTokenSync] 同步过程中发生错误:', error);
      return {
        success: false,
        message: `同步失败: ${error instanceof Error ? error.message : '未知错误'}`,
        details: { error }
      };
    }
  }

  /**
   * 强制重新获取JPush注册ID并同步
   */
  static async forceRefreshAndSync(): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      console.log('[PushTokenSync] 开始强制刷新JPush注册ID...');

      // 1. 检查JPush是否已初始化
      if (!JPushService.isReady()) {
        return {
          success: false,
          message: 'JPush服务未初始化，无法刷新注册ID',
        };
      }

      // 2. 等待一段时间让JPush完成初始化
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 3. 检查是否获取到了注册ID
      const registrationId = JPushService.getRegistrationID();
      
      if (registrationId) {
        // 4. 如果有注册ID，执行同步
        const syncResult = await this.checkAndSyncPushToken();
        return {
          success: syncResult.success,
          message: `刷新成功，${syncResult.message}`,
          details: {
            registrationId,
            syncResult,
          }
        };
      } else {
        return {
          success: false,
          message: 'JPush注册ID仍然为空，可能需要更长时间或网络连接',
          details: {
            jpushReady: JPushService.isReady(),
            registrationId: null,
          }
        };
      }

    } catch (error) {
      console.error('[PushTokenSync] 强制刷新过程中发生错误:', error);
      return {
        success: false,
        message: `刷新失败: ${error instanceof Error ? error.message : '未知错误'}`,
        details: { error }
      };
    }
  }

  /**
   * 获取详细的push_token状态信息
   */
  static async getDetailedStatus(): Promise<{
    jpush: {
      isReady: boolean;
      registrationId: string | null;
    };
    localStorage: {
      registrationId: string | null;
    };
    deviceService: {
      pushToken: string | null;
      isRegistered: boolean;
    };
    sync: {
      isInSync: boolean;
      needsSync: boolean;
    };
  }> {
    try {
      // JPush状态
      const jpushReady = JPushService.isReady();
      const jpushRegistrationId = JPushService.getRegistrationID();

      // 本地存储状态
      const localStorageId = await AsyncStorage.getItem('jpush_registration_id');

      // 设备注册服务状态
      const deviceService = DeviceRegistrationService.getInstance();
      const devicePushToken = await deviceService.getPushToken();
      const isDeviceRegistered = await deviceService.isDeviceRegistered();

      // 同步状态分析
      const validId = jpushRegistrationId || localStorageId;
      const isInSync = validId && devicePushToken === validId;
      const needsSync = validId && !isInSync;

      return {
        jpush: {
          isReady: jpushReady,
          registrationId: jpushRegistrationId,
        },
        localStorage: {
          registrationId: localStorageId,
        },
        deviceService: {
          pushToken: devicePushToken,
          isRegistered: isDeviceRegistered,
        },
        sync: {
          isInSync: !!isInSync,
          needsSync: !!needsSync,
        },
      };

    } catch (error) {
      console.error('[PushTokenSync] 获取状态信息失败:', error);
      throw error;
    }
  }
}

export default PushTokenSyncUtil;
