/**
 * 文件类型检测测试
 * 验证移动端文件类型检测的一致性
 */

import { detectFileTypeFromName, FILE_CATEGORIES } from '../fileTypes';

// 测试用例
const testCases = [
  // 图片文件
  { fileName: 'photo.jpg', expected: { category: FILE_CATEGORIES.IMAGE, mimeType: 'image/jpeg', description: 'JPEG图片' } },
  { fileName: 'image.png', expected: { category: FILE_CATEGORIES.IMAGE, mimeType: 'image/png', description: 'PNG图片' } },
  { fileName: 'animation.gif', expected: { category: FILE_CATEGORIES.IMAGE, mimeType: 'image/gif', description: 'GIF图片' } },
  { fileName: 'webp_image.webp', expected: { category: FILE_CATEGORIES.IMAGE, mimeType: 'image/webp', description: 'WebP图片' } },
  { fileName: 'bitmap.bmp', expected: { category: FILE_CATEGORIES.IMAGE, mimeType: 'image/bmp', description: 'BMP图片' } },
  { fileName: 'vector.svg', expected: { category: FILE_CATEGORIES.IMAGE, mimeType: 'image/svg+xml', description: 'SVG图片' } },

  // 视频文件
  { fileName: 'video.mp4', expected: { category: FILE_CATEGORIES.VIDEO, mimeType: 'video/mp4', description: 'MP4视频' } },
  { fileName: 'movie.avi', expected: { category: FILE_CATEGORIES.VIDEO, mimeType: 'video/avi', description: 'AVI视频' } },
  { fileName: 'quicktime.mov', expected: { category: FILE_CATEGORIES.VIDEO, mimeType: 'video/mov', description: 'MOV视频' } },
  { fileName: 'windows_media.wmv', expected: { category: FILE_CATEGORIES.VIDEO, mimeType: 'video/wmv', description: 'WMV视频' } },
  { fileName: 'flash.flv', expected: { category: FILE_CATEGORIES.VIDEO, mimeType: 'video/flv', description: 'FLV视频' } },
  { fileName: 'web_video.webm', expected: { category: FILE_CATEGORIES.VIDEO, mimeType: 'video/webm', description: 'WebM视频' } },

  // 音频文件
  { fileName: 'music.mp3', expected: { category: FILE_CATEGORIES.AUDIO, mimeType: 'audio/mpeg', description: 'MP3音频' } },
  { fileName: 'voice.amr', expected: { category: FILE_CATEGORIES.AUDIO, mimeType: 'audio/amr', description: 'AMR音频' } },
  { fileName: 'audio.wav', expected: { category: FILE_CATEGORIES.AUDIO, mimeType: 'audio/wav', description: 'WAV音频' } },
  { fileName: 'aac_audio.aac', expected: { category: FILE_CATEGORIES.AUDIO, mimeType: 'audio/aac', description: 'AAC音频' } },
  { fileName: 'm4a_audio.m4a', expected: { category: FILE_CATEGORIES.AUDIO, mimeType: 'audio/m4a', description: 'M4A音频' } },
  { fileName: 'ogg_audio.ogg', expected: { category: FILE_CATEGORIES.AUDIO, mimeType: 'audio/ogg', description: 'OGG音频' } },

  // 文档文件
  { fileName: 'document.pdf', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/pdf', description: 'PDF文档' } },
  { fileName: 'ofd_document.ofd', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/ofd', description: 'OFD文档' } },
  
  // Microsoft Office
  { fileName: 'word.doc', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/msword', description: 'Word文档' } },
  { fileName: 'word.docx', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', description: 'Word文档' } },
  { fileName: 'word_macro.docm', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/vnd.ms-word.document.macroEnabled.12', description: 'Word文档(宏)' } },
  { fileName: 'excel.xls', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/vnd.ms-excel', description: 'Excel表格' } },
  { fileName: 'excel.xlsx', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', description: 'Excel表格' } },
  { fileName: 'excel_macro.xlsm', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/vnd.ms-excel.sheet.macroEnabled.12', description: 'Excel表格(宏)' } },
  { fileName: 'powerpoint.ppt', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/vnd.ms-powerpoint', description: 'PowerPoint演示' } },
  { fileName: 'powerpoint.pptx', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation', description: 'PowerPoint演示' } },
  { fileName: 'powerpoint_macro.pptm', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/vnd.ms-powerpoint.presentation.macroEnabled.12', description: 'PowerPoint演示(宏)' } },

  // WPS Office
  { fileName: 'wps_document.wps', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/wps-office.wps', description: 'WPS文字' } },
  { fileName: 'wps_spreadsheet.et', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/wps-office.et', description: 'WPS表格' } },
  { fileName: 'wps_presentation.dps', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/wps-office.dps', description: 'WPS演示' } },

  // 文本文件
  { fileName: 'text.txt', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'text/plain', description: '纯文本' } },
  { fileName: 'rich_text.rtf', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/rtf', description: '富文本' } },
  { fileName: 'data.csv', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'text/csv', description: 'CSV数据' } },
  { fileName: 'markdown.md', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'text/markdown', description: 'Markdown文档' } },
  { fileName: 'json_data.json', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/json', description: 'JSON数据' } },
  { fileName: 'xml_document.xml', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/xml', description: 'XML文档' } },

  // 压缩文件
  { fileName: 'archive.zip', expected: { category: FILE_CATEGORIES.ARCHIVE, mimeType: 'application/zip', description: 'ZIP压缩包' } },
  { fileName: 'archive.rar', expected: { category: FILE_CATEGORIES.ARCHIVE, mimeType: 'application/x-rar-compressed', description: 'RAR压缩包' } },
  { fileName: 'archive.7z', expected: { category: FILE_CATEGORIES.ARCHIVE, mimeType: 'application/x-7z-compressed', description: '7Z压缩包' } },

  // 边界情况
  { fileName: '', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/octet-stream', description: '未知文件' } },
  { fileName: 'no_extension', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/octet-stream', description: 'NO_EXTENSION文件' } },
  { fileName: 'unknown.xyz', expected: { category: FILE_CATEGORIES.DOCUMENT, mimeType: 'application/octet-stream', description: 'XYZ文件' } },
];

describe('文件类型检测测试', () => {
  test('应该正确检测所有文件类型', () => {
    testCases.forEach(({ fileName, expected }) => {
      const result = detectFileTypeFromName(fileName);
      
      expect(result.category).toBe(expected.category);
      expect(result.mimeType).toBe(expected.mimeType);
      expect(result.description).toBe(expected.description);
    });
  });

  test('应该正确处理空文件名', () => {
    const result = detectFileTypeFromName('');
    expect(result.category).toBe(FILE_CATEGORIES.DOCUMENT);
    expect(result.mimeType).toBe('application/octet-stream');
    expect(result.description).toBe('未知文件');
  });

  test('应该正确处理无扩展名的文件名', () => {
    const result = detectFileTypeFromName('no_extension');
    expect(result.category).toBe(FILE_CATEGORIES.DOCUMENT);
    expect(result.mimeType).toBe('application/octet-stream');
    expect(result.description).toBe('NO_EXTENSION文件');
  });

  test('应该正确处理未知扩展名', () => {
    const result = detectFileTypeFromName('unknown.xyz');
    expect(result.category).toBe(FILE_CATEGORIES.DOCUMENT);
    expect(result.mimeType).toBe('application/octet-stream');
    expect(result.description).toBe('XYZ文件');
  });

  test('应该正确处理大小写不敏感', () => {
    const result1 = detectFileTypeFromName('document.PDF');
    const result2 = detectFileTypeFromName('document.pdf');
    
    expect(result1.category).toBe(result2.category);
    expect(result1.mimeType).toBe(result2.mimeType);
    expect(result1.description).toBe(result2.description);
  });

  test('应该正确处理带路径的文件名', () => {
    const result = detectFileTypeFromName('/path/to/document.pdf');
    expect(result.category).toBe(FILE_CATEGORIES.DOCUMENT);
    expect(result.mimeType).toBe('application/pdf');
    expect(result.description).toBe('PDF文档');
  });
});

describe('文件类型分类测试', () => {
  test('应该正确分类图片文件', () => {
    const imageFiles = ['photo.jpg', 'image.png', 'animation.gif', 'webp_image.webp'];
    
    imageFiles.forEach(fileName => {
      const result = detectFileTypeFromName(fileName);
      expect(result.category).toBe(FILE_CATEGORIES.IMAGE);
    });
  });

  test('应该正确分类视频文件', () => {
    const videoFiles = ['video.mp4', 'movie.avi', 'quicktime.mov'];
    
    videoFiles.forEach(fileName => {
      const result = detectFileTypeFromName(fileName);
      expect(result.category).toBe(FILE_CATEGORIES.VIDEO);
    });
  });

  test('应该正确分类音频文件', () => {
    const audioFiles = ['music.mp3', 'voice.amr', 'audio.wav'];
    
    audioFiles.forEach(fileName => {
      const result = detectFileTypeFromName(fileName);
      expect(result.category).toBe(FILE_CATEGORIES.AUDIO);
    });
  });

  test('应该正确分类文档文件', () => {
    const documentFiles = ['document.pdf', 'word.doc', 'excel.xls'];
    
    documentFiles.forEach(fileName => {
      const result = detectFileTypeFromName(fileName);
      expect(result.category).toBe(FILE_CATEGORIES.DOCUMENT);
    });
  });
}); 