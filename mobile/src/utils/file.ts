// 文件通用工具函数

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes < 1024) {return `${bytes} B`;}
  if (bytes < 1024 * 1024) {return `${(bytes / 1024).toFixed(1)} KB`;}
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
}

/**
 * iOS下文件名和URI解码
 * @param name 文件名
 * @param uri 文件uri
 * @returns [解码后的文件名, 解码后的uri]
 */
export function decodeIOSFileNameAndUri(name: string, uri: string): [string, string] {
  let processedName = name;
  let processedUri = uri;
  try {
    processedName = decodeURIComponent(processedName);
    if (processedUri.includes('%')) {
      const uriParts = processedUri.split('/');
      const lastPart = uriParts[uriParts.length - 1];
      if (lastPart.includes('%')) {
        uriParts[uriParts.length - 1] = decodeURIComponent(lastPart);
        processedUri = uriParts.join('/');
      }
    }
  } catch (e) {
    // 解码失败时使用原始名称
  }
  return [processedName, processedUri];
}
