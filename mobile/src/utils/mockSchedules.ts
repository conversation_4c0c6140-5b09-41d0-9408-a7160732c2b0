import { addDays, subDays, startOfDay } from 'date-fns';

export interface Schedule {
  id: string;
  date: Date;
  startTime: string; // "HH:mm"
  endTime?: string; // Optional "HH:mm"
  title: string;
  role: 'meeting' | 'document' | 'inspection' | 'research' | 'coordination' | 'personal';
  isCompleted: boolean;
  location?: string;
  participants?: string[];
  description?: string;
}

const today = startOfDay(new Date());
const yesterday = subDays(today, 1);
const tomorrow = addDays(today, 1);

const mockSchedules: Schedule[] = [
  // --- Yesterday's Data ---
  {
    id: 'y1',
    date: yesterday,
    startTime: '10:00',
    endTime: '11:00',
    title: '【昨日】完成项目周报',
    role: 'document',
    isCompleted: true,
    location: '办公室',
  },
  {
    id: 'y2',
    date: yesterday,
    startTime: '15:00',
    title: '【昨日】与设计团队沟通',
    role: 'coordination',
    isCompleted: true,
  },

  // --- Today's Data ---
  {
    id: 't0',
    date: today,
    startTime: '02:00',
    endTime: '03:00',
    title: '【凌晨】处理紧急服务器维护',
    role: 'personal',
    isCompleted: false,
    location: '线上',
  },
  {
    id: 't1',
    date: today,
    startTime: '09:00',
    endTime: '10:30',
    title: '市委常委会会议',
    role: 'meeting',
    isCompleted: false,
    location: '市委常委会议室',
    participants: ['市委书记', '市长', '常委会成员'],
    description: '研究讨论全市经济发展战略',
  },
  {
    id: 't2',
    date: today,
    startTime: '10:45',
    endTime: '12:00',
    title: '起草政府工作报告初稿',
    role: 'document',
    isCompleted: false,
    description: '根据年度工作要点，完成报告初稿',
  },
    {
    id: 't3',
    date: today,
    startTime: '14:30',
    title: '重点项目推进调度会',
    role: 'meeting',
    isCompleted: false,
    location: '市政府第一会议室',
    participants: ['分管副市长', '发改委', '财政局', '项目单位'],
  },
  {
    id: 't4',
    date: today,
    startTime: '16:30',
    endTime: '18:00',
    title: '安全生产专项督查 (已完成)',
    role: 'inspection',
    isCompleted: true,
    location: '经开区工业园',
  },
  {
    id: 't5',
    date: today,
    startTime: '19:30',
    title: '与省厅对接汇报工作',
    role: 'coordination',
    isCompleted: false,
    location: '线上会议',
  },
  {
    id: 't6',
    date: today,
    startTime: '21:00',
    title: '学习新的技术文档',
    role: 'research',
    isCompleted: false,
  },

  // --- Tomorrow's Data ---
  {
    id: 'tm1',
    date: tomorrow,
    startTime: '09:30',
    endTime: '11:30',
    title: '【明日】跨部门项目协调会',
    role: 'coordination',
    isCompleted: false,
    location: '三号会议室',
  },
  {
    id: 'tm2',
    date: tomorrow,
    startTime: '14:00',
    title: '【明日】准备周五汇报材料',
    role: 'document',
    isCompleted: false,
  },
  {
    id: 'tm3',
    date: tomorrow,
    startTime: '17:00',
    title: '【明日】个人发展规划',
    role: 'personal',
    isCompleted: false,
  },
];


export const generateMockSchedules = (): Schedule[] => {
  return mockSchedules;
};
