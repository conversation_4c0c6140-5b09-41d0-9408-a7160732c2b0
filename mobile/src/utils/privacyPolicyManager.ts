/**
 * 隐私政策管理工具
 * 用于管理用户隐私政策同意状态
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

export interface PrivacyPolicyStatus {
  hasAgreed: boolean;
  agreedAt: string | null;
  version: string;
}

export class PrivacyPolicyManager {
  private static readonly STORAGE_KEY = '@privacy_policy_status';
  private static readonly CURRENT_VERSION = '1.0';
  
  /**
   * 检查用户是否已同意当前版本的隐私政策
   */
  static async hasUserAgreedToCurrentPolicy(): Promise<boolean> {
    try {
      const statusStr = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (!statusStr) {
        return false;
      }
      
      const status: PrivacyPolicyStatus = JSON.parse(statusStr);
      
      // 检查是否已同意且版本匹配
      return status.hasAgreed && status.version === this.CURRENT_VERSION;
    } catch (error) {
      console.error('[PrivacyPolicyManager] 检查隐私政策状态失败:', error);
      return false;
    }
  }
  
  /**
   * 记录用户同意隐私政策
   */
  static async recordUserAgreement(): Promise<void> {
    try {
      const status: PrivacyPolicyStatus = {
        hasAgreed: true,
        agreedAt: new Date().toISOString(),
        version: this.CURRENT_VERSION,
      };
      
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(status));
      console.log('[PrivacyPolicyManager] 用户隐私政策同意状态已保存');
    } catch (error) {
      console.error('[PrivacyPolicyManager] 保存隐私政策同意状态失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取隐私政策状态详情
   */
  static async getPrivacyPolicyStatus(): Promise<PrivacyPolicyStatus | null> {
    try {
      const statusStr = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (!statusStr) {
        return null;
      }
      
      return JSON.parse(statusStr);
    } catch (error) {
      console.error('[PrivacyPolicyManager] 获取隐私政策状态失败:', error);
      return null;
    }
  }
  
  /**
   * 清除隐私政策同意状态（用于测试或重置）
   */
  static async clearPrivacyPolicyStatus(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.STORAGE_KEY);
      console.log('[PrivacyPolicyManager] 隐私政策状态已清除');
    } catch (error) {
      console.error('[PrivacyPolicyManager] 清除隐私政策状态失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取隐私政策URL
   */
  static getPrivacyPolicyUrl(): string {
    return 'https://www.gongzhimall.com/privacy-policy.html';
  }
  
  /**
   * 获取当前隐私政策版本
   */
  static getCurrentVersion(): string {
    return this.CURRENT_VERSION;
  }
} 