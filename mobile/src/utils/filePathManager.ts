/**
 * 文件路径管理器
 * 解决iOS覆盖安装导致的文件路径失效问题
 */

import RNFS from 'react-native-fs';

export interface FilePathInfo {
  absolutePath: string;
  relativePath: string;
  fileName: string;
  isValid: boolean;
  migrationNeeded: boolean;
}

export interface PathMigrationResult {
  success: boolean;
  originalPath: string;
  newPath?: string;
  error?: string;
}

/**
 * 文件路径管理器类
 * 提供智能的路径解析、验证和迁移功能
 */
export class FilePathManager {
  private static readonly CHAT_FILES_DIR = 'ChatFiles';
  private static readonly PATH_SEPARATOR = '/';

  /**
   * 获取当前的ChatFiles目录路径
   */
  static getCurrentChatFilesDir(): string {
    return `${RNFS.DocumentDirectoryPath}/${this.CHAT_FILES_DIR}`;
  }

  /**
   * 从绝对路径提取相对路径
   * @param absolutePath 绝对路径
   * @returns 相对路径（相对于ChatFiles目录）
   */
  static extractRelativePath(absolutePath: string): string | null {
    try {
      // 查找ChatFiles目录的位置
      const chatFilesIndex = absolutePath.indexOf(`${this.PATH_SEPARATOR}${this.CHAT_FILES_DIR}${this.PATH_SEPARATOR}`);
      if (chatFilesIndex === -1) {
        console.warn('[FilePathManager] 路径中未找到ChatFiles目录:', absolutePath);
        return null;
      }

      // 提取ChatFiles之后的相对路径
      const startIndex = chatFilesIndex + `${this.PATH_SEPARATOR}${this.CHAT_FILES_DIR}${this.PATH_SEPARATOR}`.length;
      const relativePath = absolutePath.substring(startIndex);

      return relativePath || null;
    } catch (error) {
      console.error('[FilePathManager] 提取相对路径失败:', error);
      return null;
    }
  }

  /**
   * 从相对路径构建当前的绝对路径
   * @param relativePath 相对路径
   * @returns 当前环境下的绝对路径
   */
  static buildAbsolutePath(relativePath: string): string {
    return `${this.getCurrentChatFilesDir()}${this.PATH_SEPARATOR}${relativePath}`;
  }

  /**
   * 智能解析文件路径
   * 自动处理绝对路径和相对路径
   */
  static async resolvePath(inputPath: string): Promise<FilePathInfo> {
    const result: FilePathInfo = {
      absolutePath: '',
      relativePath: '',
      fileName: '',
      isValid: false,
      migrationNeeded: false,
    };

    try {
      // 提取文件名
      result.fileName = inputPath.split(this.PATH_SEPARATOR).pop() || '';

      // 判断是否为绝对路径
      if (inputPath.startsWith(this.PATH_SEPARATOR)) {
        // 绝对路径处理
        result.absolutePath = inputPath;
        result.relativePath = this.extractRelativePath(inputPath) || '';

        // 检查原路径是否有效
        const originalExists = await RNFS.exists(inputPath);
        if (originalExists) {
          result.isValid = true;
        } else {
          // 尝试使用当前路径
          if (result.relativePath) {
            const currentPath = this.buildAbsolutePath(result.relativePath);
            const currentExists = await RNFS.exists(currentPath);
            if (currentExists) {
              result.absolutePath = currentPath;
              result.isValid = true;
              result.migrationNeeded = true;
            }
          }
        }
      } else {
        // 相对路径处理
        result.relativePath = inputPath;
        result.absolutePath = this.buildAbsolutePath(inputPath);
        result.isValid = await RNFS.exists(result.absolutePath);
      }

    } catch (error) {
      console.error('[FilePathManager] 路径解析失败:', error);
    }

    return result;
  }

  /**
   * 检查并修复文件路径
   * @param originalPath 原始路径（可能失效）
   * @returns 修复结果
   */
  static async migrateFilePath(originalPath: string): Promise<PathMigrationResult> {
    const result: PathMigrationResult = {
      success: false,
      originalPath,
    };

    try {
      console.log('[FilePathManager] 开始路径迁移:', originalPath);

      // 首先检查原路径是否仍然有效
      const originalExists = await RNFS.exists(originalPath);
      if (originalExists) {
        result.success = true;
        result.newPath = originalPath;
        console.log('[FilePathManager] 原路径仍然有效，无需迁移');
        return result;
      }

      // 提取相对路径
      const relativePath = this.extractRelativePath(originalPath);
      if (!relativePath) {
        result.error = '无法从原路径提取相对路径';
        console.error('[FilePathManager] 路径迁移失败:', result.error);
        return result;
      }

      // 构建新的绝对路径
      const newPath = this.buildAbsolutePath(relativePath);
      const newExists = await RNFS.exists(newPath);

      if (newExists) {
        result.success = true;
        result.newPath = newPath;
        console.log('[FilePathManager] 路径迁移成功:', originalPath, '->', newPath);
      } else {
        result.error = '新路径下也未找到文件';
        console.warn('[FilePathManager] 路径迁移失败，文件可能已丢失:', originalPath);
      }

    } catch (error: any) {
      result.error = `路径迁移异常: ${error.message}`;
      console.error('[FilePathManager] 路径迁移异常:', error);
    }

    return result;
  }

  /**
   * 批量迁移文件路径
   * @param paths 路径数组
   * @returns 迁移结果数组
   */
  static async batchMigratePaths(paths: string[]): Promise<PathMigrationResult[]> {
    console.log(`[FilePathManager] 开始批量路径迁移，共${paths.length}个路径`);

    const results: PathMigrationResult[] = [];
    let successCount = 0;
    let failureCount = 0;

    for (const path of paths) {
      const result = await this.migrateFilePath(path);
      results.push(result);

      if (result.success) {
        successCount++;
      } else {
        failureCount++;
      }
    }

    console.log(`[FilePathManager] 批量迁移完成: 成功${successCount}个，失败${failureCount}个`);
    return results;
  }

  /**
   * 验证ChatFiles目录结构
   */
  static async validateChatFilesDirectory(): Promise<{
    exists: boolean;
    path: string;
    fileCount: number;
    issues: string[];
  }> {
    const chatFilesDir = this.getCurrentChatFilesDir();
    const result = {
      exists: false,
      path: chatFilesDir,
      fileCount: 0,
      issues: [] as string[],
    };

    try {
      // 检查目录是否存在
      result.exists = await RNFS.exists(chatFilesDir);

      if (!result.exists) {
        result.issues.push('ChatFiles目录不存在');
        return result;
      }

      // 统计文件数量
      const files = await RNFS.readDir(chatFilesDir);
      result.fileCount = files.filter(file => file.isFile()).length;

      // 检查权限
      try {
        const testFile = `${chatFilesDir}/.test_${Date.now()}`;
        await RNFS.writeFile(testFile, 'test', 'utf8');
        await RNFS.unlink(testFile);
      } catch (error) {
        result.issues.push('目录写入权限异常');
      }

    } catch (error: any) {
      result.issues.push(`目录验证失败: ${error.message}`);
    }

    return result;
  }

  /**
   * 扫描并报告孤立文件
   * 找出存在于文件系统但没有对应聊天消息的文件
   */
  static async scanOrphanedFiles(): Promise<{
    totalFiles: number;
    orphanedFiles: string[];
    totalSize: number;
  }> {
    const result = {
      totalFiles: 0,
      orphanedFiles: [] as string[],
      totalSize: 0,
    };

    try {
      const chatFilesDir = this.getCurrentChatFilesDir();
      const exists = await RNFS.exists(chatFilesDir);

      if (!exists) {
        console.warn('[FilePathManager] ChatFiles目录不存在');
        return result;
      }

      const files = await RNFS.readDir(chatFilesDir);
      result.totalFiles = files.filter(file => file.isFile()).length;

      // 计算总大小
      for (const file of files) {
        if (file.isFile()) {
          result.totalSize += file.size;
        }
      }

      // TODO: 这里需要与聊天消息数据库进行对比
      // 暂时返回所有文件作为潜在的孤立文件
      result.orphanedFiles = files
        .filter(file => file.isFile())
        .map(file => file.name);

    } catch (error) {
      console.error('[FilePathManager] 扫描孤立文件失败:', error);
    }

    return result;
  }

  /**
   * 生成路径管理报告
   */
  static async generatePathReport(): Promise<string> {
    const report = [];
    report.push('# 文件路径管理报告');
    report.push('');

    // 目录验证
    const dirValidation = await this.validateChatFilesDirectory();
    report.push('## ChatFiles目录状态');
    report.push(`- 路径: ${dirValidation.path}`);
    report.push(`- 存在: ${dirValidation.exists ? '✅' : '❌'}`);
    report.push(`- 文件数量: ${dirValidation.fileCount}`);

    if (dirValidation.issues.length > 0) {
      report.push('- 问题:');
      dirValidation.issues.forEach(issue => {
        report.push(`  - ❌ ${issue}`);
      });
    }
    report.push('');

    // 孤立文件扫描
    const orphanedScan = await this.scanOrphanedFiles();
    report.push('## 文件存储统计');
    report.push(`- 总文件数: ${orphanedScan.totalFiles}`);
    report.push(`- 总大小: ${(orphanedScan.totalSize / 1024 / 1024).toFixed(2)}MB`);
    report.push(`- 潜在孤立文件: ${orphanedScan.orphanedFiles.length}`);

    if (orphanedScan.orphanedFiles.length > 0) {
      report.push('- 孤立文件列表:');
      orphanedScan.orphanedFiles.slice(0, 10).forEach(file => {
        report.push(`  - ${file}`);
      });
      if (orphanedScan.orphanedFiles.length > 10) {
        report.push(`  - ... 还有${orphanedScan.orphanedFiles.length - 10}个文件`);
      }
    }

    report.push('');
    report.push('## 建议');
    if (dirValidation.exists && dirValidation.issues.length === 0) {
      report.push('- ✅ 文件路径管理正常');
    } else {
      report.push('- ❌ 需要修复文件路径问题');
    }

    return report.join('\n');
  }
}
