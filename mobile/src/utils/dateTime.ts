/**
 * 统一的日期时间工具函数
 * 整合项目中所有时间格式化需求
 */

import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

/**
 * 标准日期时间格式化
 * 格式: YYYY-MM-DD HH:mm
 */
export function formatDateTime(timestamp?: number | Date): string {
  if (!timestamp) {
    return '未知时间';
  }

  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return '无效时间';
  }

  return format(date, 'yyyy-MM-dd HH:mm', { locale: zhCN });
}

/**
 * 绑定时间格式化（兼容原有格式）
 * 格式: YYYY-MM-DD HH:mm
 */
export function formatBindingTime(timestamp?: number): string {
  return formatDateTime(timestamp);
}

/**
 * 聊天时间戳格式化（智能显示）
 * 今天: HH:mm
 * 昨天: 昨天 HH:mm  
 * 本年: MM-DD HH:mm
 * 其他: YYYY-MM-DD HH:mm
 */
export function formatChatTimestamp(date: Date): string {
  const now = new Date();
  const isToday = date.toDateString() === now.toDateString();
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  const isYesterday = date.toDateString() === yesterday.toDateString();

  if (isToday) {
    return format(date, 'HH:mm');
  } else if (isYesterday) {
    return `昨天 ${format(date, 'HH:mm')}`;
  } else if (date.getFullYear() === now.getFullYear()) {
    return format(date, 'MM-dd HH:mm');
  } else {
    return format(date, 'yyyy-MM-dd HH:mm');
  }
}

/**
 * 日期格式化（用于首页等场景）
 * 返回: { day: string, weekday: string, month: string, fullDate: string }
 */
export function formatDateInfo(date: Date): {
  day: string;
  weekday: string; 
  month: string;
  fullDate: string;
} {
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const weekday = weekdays[date.getDay()];
  const month = `${date.getMonth() + 1}月`;
  const fullDate = `${date.getMonth() + 1}月${date.getDate()}日`;

  const today = new Date();
  const tomorrow = new Date();
  tomorrow.setDate(today.getDate() + 1);
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);

  const checkDateIsSame = (d1: Date, d2: Date) =>
    d1.getFullYear() === d2.getFullYear() &&
    d1.getMonth() === d2.getMonth() &&
    d1.getDate() === d2.getDate();

  if (checkDateIsSame(date, today)) {
    return { day: '今天', weekday, month, fullDate };
  } else if (checkDateIsSame(date, tomorrow)) {
    return { day: '明天', weekday, month, fullDate };
  } else if (checkDateIsSame(date, yesterday)) {
    return { day: '昨天', weekday, month, fullDate };
  } else {
    return { day: date.getDate().toString(), weekday, month, fullDate };
  }
}

/**
 * 简单日期格式化
 * 格式: YYYY-MM-DD
 */
export function formatDate(timestamp?: number | Date): string {
  if (!timestamp) {
    return '';
  }

  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  
  if (isNaN(date.getTime())) {
    return '';
  }

  return format(date, 'yyyy-MM-dd');
}

/**
 * 简单时间格式化
 * 格式: HH:mm
 */
export function formatTime(timestamp?: number | Date): string {
  if (!timestamp) {
    return '';
  }

  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  
  if (isNaN(date.getTime())) {
    return '';
  }

  return format(date, 'HH:mm');
}

/**
 * 相对时间格式化
 * 刚刚、几分钟前、几小时前、几天前等
 */
export function formatRelativeTime(timestamp: number | Date): string {
  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMinutes < 1) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return formatDateTime(date);
  }
}

/**
 * 检查是否为今天
 */
export function isToday(date: Date): boolean {
  const today = new Date();
  return date.toDateString() === today.toDateString();
}

/**
 * 检查是否为昨天
 */
export function isYesterday(date: Date): boolean {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return date.toDateString() === yesterday.toDateString();
}

/**
 * 获取当前时间戳
 */
export function getCurrentTimestamp(): number {
  return Date.now();
}

/**
 * 时间戳转Date对象
 */
export function timestampToDate(timestamp: number): Date {
  return new Date(timestamp);
}

/**
 * Date对象转时间戳
 */
export function dateToTimestamp(date: Date): number {
  return date.getTime();
}
