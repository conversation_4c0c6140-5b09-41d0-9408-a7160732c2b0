/**
 * 应用配置文件
 * 集中管理所有配置项，支持环境变量和默认值
 */
import {
  WECHAT_APP_ID,
  WECHAT_APP_SECRET,
  WECHAT_PUSH_TOKEN,
  WECHAT_PUSH_ENCODING_AES_KEY,
  JPUSH_APP_KEY,
  JPUSH_CHANNEL,
  NODE_ENV,
} from '@env';

// React Native 全局变量声明
declare var __DEV__: boolean;

/**
 * 微信相关配置
 */
// 修复：统一使用生产环境API地址，避免真机环境下的网络连接问题
const getAPIBaseURL = (): string => {
  // 统一使用生产环境API地址，确保真机和开发环境的一致性
  return 'https://wechat.api.gongzhimall.com';
};

export const weChatConfig = {
  // 开发环境配置，生产环境使用自定义域名
  // 修复：真机环境下使用本机IP地址而非localhost
  API_BASE_URL: getAPIBaseURL(),

  // 绑定轮询配置（绑定过程仍需要轮询确认）
  POLL_INTERVAL: 2000, // 2秒轮询一次
  POLL_TIMEOUT: 300000, // 5分钟超时

  // 消息处理配置（已改为纯推送驱动，移除SYNC_INTERVAL）
  BATCH_SIZE: 50, // 每次处理最多50条消息

  // 重试配置
  MAX_RETRIES: 3,
  RETRY_DELAY: 5000, // 5秒后重试

  // 微信企业应用配置（完全从环境变量读取）
  WECHAT_CORP_ID: process.env.WECHAT_CORP_ID || '', // 企业ID
  WECHAT_AGENT_ID: process.env.WECHAT_AGENT_ID || '', // 应用AgentID
  WECHAT_CORP_SECRET: process.env.WECHAT_CORP_SECRET || '', // 企业微信Secret

  // 企业微信客服配置（使用正确的客服ID）
  WECHAT_CUSTOMER_SERVICE_URL: 'https://work.weixin.qq.com/kfid/kfce062e9c6b4dc4f49', // 企业微信客服链接

  // 微信开放平台配置（公职猫文件传输助手）
  WECHAT_OPEN_PLATFORM: {
    APP_ID: WECHAT_APP_ID || '', // 微信开放平台AppID
    APP_SECRET: WECHAT_APP_SECRET || '', // 微信开放平台AppSecret
    UNIVERSAL_LINK: 'https://app.gongzhimall.com/app/', // 微信开放平台配置的Universal Link
  },

  // 消息推送配置
  WECHAT_PUSH_TOKEN: WECHAT_PUSH_TOKEN || '', // 消息推送Token
  WECHAT_PUSH_ENCODING_AES_KEY: WECHAT_PUSH_ENCODING_AES_KEY || '', // 消息推送加密Key

  // 新增：安全配置
  security: {
    tokenExpiry: 600000, // Token过期时间：10分钟
    maxRetries: 3, // 最大重试次数
    retryDelay: 5000, // 重试延迟：5秒
    signatureTimeout: 300000, // 签名超时：5分钟
  },

  // 新增：API配置
  api: {
    timeout: 30000, // API调用超时：30秒
    retryAttempts: 3, // API重试次数
    batchSize: 50, // 批量处理大小
    maxConcurrent: 5, // 最大并发请求数
  },

  // 新增：监控配置
  monitoring: {
    enableMetrics: !__DEV__, // 生产环境启用指标收集
    logLevel: __DEV__ ? 'debug' : 'info', // 日志级别
    errorReporting: true, // 启用错误报告
    performanceTracking: !__DEV__, // 性能追踪
  },

  // 新增：消息处理配置
  message: {
    maxSize: 10 * 1024 * 1024, // 最大消息大小：10MB
    supportedTypes: ['text', 'image', 'file', 'voice', 'video'], // 支持的消息类型
    downloadTimeout: 60000, // 媒体文件下载超时：60秒
    localStoragePath: 'wechat_media', // 本地存储路径
  },
};

// 新增：极光推送配置（从环境变量读取，避免硬编码）
export const jpushConfig = {
  // 从环境变量读取AppKey
  appKey: JPUSH_APP_KEY || '', // 极光推送AppKey
  channel: JPUSH_CHANNEL || 'gongzhimall-official', // 推送渠道
  production: NODE_ENV === 'production', // 基于NODE_ENV判断生产环境

  // 别名配置
  alias: {
    prefix: 'gzm_user_', // 用户别名前缀
    maxLength: 40, // 别名最大长度
  },

  // 推送配置
  notification: {
    title: '公职猫', // 默认通知标题
    sound: 'default', // 通知声音
    badge: 1, // iOS角标
    priority: 'high', // 推送优先级
  },

  // 重试配置
  retry: {
    maxAttempts: 3, // 最大重试次数
    backoffDelay: 1000, // 退避延迟基数：1秒
    maxDelay: 30000, // 最大延迟：30秒
  },
};

// 新增：配置验证工具
export const configValidator = {
  /**
   * 验证极光推送配置
   */
  validateJPushConfig(): boolean {
    const errors: string[] = [];

    console.log('[Config] 开始验证JPush配置...');
    console.log('[Config] AppKey:', jpushConfig.appKey);
    console.log('[Config] Channel:', jpushConfig.channel);
    console.log('[Config] Production:', jpushConfig.production);
    console.log('[Config] __DEV__:', __DEV__);

    // 检查AppKey
    if (!jpushConfig.appKey) {
      errors.push('JPush AppKey未配置');
      if (!__DEV__) {
        errors.push('生产环境必须设置环境变量 JPUSH_APP_KEY');
      }
    } else if (jpushConfig.appKey.length < 16) {
      errors.push('JPush AppKey格式可能不正确（长度过短）');
    }

    // 检查渠道配置
    if (!jpushConfig.channel) {
      errors.push('JPush channel未配置');
    }

    // 检查别名配置
    if (!jpushConfig.alias.prefix) {
      errors.push('JPush别名前缀未配置');
    }

    // 生产环境额外检查
    if (!__DEV__ && !jpushConfig.appKey) {
      errors.push('生产环境必须设置环境变量 JPUSH_APP_KEY');
    }

    // 检查AppKey格式（极光推送AppKey通常是24位字符）
    if (jpushConfig.appKey && jpushConfig.appKey.length !== 24) {
      errors.push(`JPush AppKey格式可能不正确（当前长度为${jpushConfig.appKey.length}位，标准长度为24位）`);
    }

    if (errors.length > 0) {
      console.warn('[Config] JPush配置问题:', errors.join(', '));
      return false;
    }

    console.log('[Config] JPush配置验证通过');
    return true;
  },

  /**
   * 验证微信配置
   */
  validateWeChatConfig(): boolean {
    // 验证企业微信基础配置
    if (!weChatConfig.API_BASE_URL || weChatConfig.API_BASE_URL.includes('xxx')) {
      console.warn('[Config] 微信API地址未正确配置');
      return false;
    }

    if (weChatConfig.WECHAT_CORP_ID === 'your-corp-id') {
      console.warn('[Config] 企业微信CorpID未配置');
      return false;
    }

    // 验证微信开放平台配置
    if (!__DEV__ && (!weChatOpenPlatformConfig.APP_ID || weChatOpenPlatformConfig.APP_ID.includes('XXX'))) {
      console.warn('[Config] 微信开放平台AppID未正确配置');
      return false;
    }

    if (!__DEV__ && !weChatOpenPlatformConfig.APP_SECRET) {
      console.warn('[Config] 微信开放平台AppSecret未配置');
      return false;
    }

    // 验证微信服务号配置
    if (!__DEV__ && (!weChatServiceConfig.APP_ID || weChatServiceConfig.APP_ID.includes('XXX'))) {
      console.warn('[Config] 微信服务号AppID未正确配置');
      return false;
    }

    if (!__DEV__ && !weChatServiceConfig.APP_SECRET) {
      console.warn('[Config] 微信服务号AppSecret未配置');
      return false;
    }

    console.log('[Config] 微信配置验证通过');
    return true;
  },

  /**
   * 验证微信开放平台配置
   */
  validateWeChatOpenPlatformConfig(): boolean {
    if (!weChatOpenPlatformConfig.APP_ID || weChatOpenPlatformConfig.APP_ID.includes('XXX')) {
      console.warn('[Config] 微信开放平台AppID未正确配置');
      return false;
    }

    if (!__DEV__ && !weChatOpenPlatformConfig.APP_SECRET) {
      console.warn('[Config] 微信开放平台AppSecret未配置（生产环境必需）');
      return false;
    }

    if (!weChatOpenPlatformConfig.REDIRECT_URI) {
      console.warn('[Config] 微信OAuth重定向URI未配置');
      return false;
    }

    console.log('[Config] 微信开放平台配置验证通过');
    return true;
  },

  /**
   * 验证微信服务号配置
   */
  validateWeChatServiceConfig(): boolean {
    if (!weChatServiceConfig.APP_ID || weChatServiceConfig.APP_ID.includes('XXX')) {
      console.warn('[Config] 微信服务号AppID未正确配置');
      return false;
    }

    if (!__DEV__ && !weChatServiceConfig.APP_SECRET) {
      console.warn('[Config] 微信服务号AppSecret未配置（生产环境必需）');
      return false;
    }

    if (!__DEV__ && !weChatServiceConfig.TOKEN) {
      console.warn('[Config] 微信服务号Token未配置（生产环境必需）');
      return false;
    }

    console.log('[Config] 微信服务号配置验证通过');
    return true;
  },

  /**
   * 验证所有配置
   */
  validateAllConfigs(): boolean {
    const jpushValid = this.validateJPushConfig();
    const wechatValid = this.validateWeChatConfig();

    if (!jpushValid || !wechatValid) {
      console.error('[Config] 配置验证失败，部分功能可能无法正常使用');
      return false;
    }

    console.log('[Config] 所有配置验证通过');
    return true;
  },

  /**
   * 初始化配置验证（应用启动时调用）
   */
  initializeAndValidate(): boolean {
    console.log('[Config] 开始配置验证...');
    console.log('[Config] 当前环境:', __DEV__ ? 'development' : 'production');

    const isValid = this.validateAllConfigs();

    if (!isValid) {
      console.error('[Config] 配置验证失败，请检查配置后重启应用');
    }

    return isValid;
  },

  /**
   * 生成配置报告（用于调试）
   */
  generateConfigReport(): object {
    return {
      environment: __DEV__ ? 'development' : 'production',
      jpush: {
        hasAppKey: !!jpushConfig.appKey,
        appKeyLength: jpushConfig.appKey.length,
        channel: jpushConfig.channel,
        production: jpushConfig.production,
      },
      wechat: {
        hasApiUrl: !!weChatConfig.API_BASE_URL,
        apiUrl: weChatConfig.API_BASE_URL,
        hasCorpId: weChatConfig.WECHAT_CORP_ID !== 'your-corp-id',
      },
      validation: {
        jpushValid: this.validateJPushConfig(),
        wechatValid: this.validateWeChatConfig(),
        allValid: this.validateAllConfigs(),
      },
    };
  },
};

// 新增：配置工具函数
export const configUtils = {
  /**
   * 获取当前环境标识
   */
  getCurrentEnvironment(): 'development' | 'production' {
    return __DEV__ ? 'development' : 'production';
  },

  /**
   * 检查是否为调试模式
   */
  isDebugMode(): boolean {
    return __DEV__ && weChatConfig.monitoring.logLevel === 'debug';
  },

  /**
   * 获取用户别名（用于极光推送）
   */
  getUserAlias(userUuid: string): string {
    const alias = `${jpushConfig.alias.prefix}${userUuid}`;
    return alias.length > jpushConfig.alias.maxLength
      ? alias.substring(0, jpushConfig.alias.maxLength)
      : alias;
  },

  /**
   * 获取API超时配置
   */
  getAPITimeout(apiType: 'default' | 'upload' | 'download' = 'default'): number {
    switch (apiType) {
      case 'upload':
      case 'download':
        return weChatConfig.message.downloadTimeout;
      default:
        return weChatConfig.api.timeout;
    }
  },
};

/**
 * 微信开放平台配置
 */
export const weChatOpenPlatformConfig = {
  // 微信开放平台AppID（从环境变量或默认值读取）
  APP_ID: process.env.WECHAT_OPEN_APP_ID || 'wxXXXXXXXXXXXXXXXX',

  // 微信开放平台AppSecret（从环境变量读取，生产环境必须配置）
  APP_SECRET: process.env.WECHAT_OPEN_APP_SECRET || '',

  // OAuth2.0重定向URI (修复：统一使用生产环境地址)
  REDIRECT_URI: 'https://api.gongzhimall.com/auth/wechat/callback',

  // 授权作用域
  SCOPE: 'snsapi_userinfo',

  // 授权状态参数（用于防CSRF攻击）
  STATE: 'gongzhimall_oauth_state',

  // OAuth URL模板
  OAUTH_URL_TEMPLATE: 'https://open.weixin.qq.com/connect/oauth2/authorize?appid={APP_ID}&redirect_uri={REDIRECT_URI}&response_type=code&scope={SCOPE}&state={STATE}#wechat_redirect',

  // Access Token API
  ACCESS_TOKEN_URL: 'https://api.weixin.qq.com/sns/oauth2/access_token',

  // 用户信息API
  USERINFO_URL: 'https://api.weixin.qq.com/sns/userinfo',
};

/**
 * 微信服务号配置
 */
export const weChatServiceConfig = {
  // 服务号AppID
  APP_ID: process.env.WECHAT_SERVICE_APP_ID || 'wxXXXXXXXXXXXXXXXX',

  // 服务号AppSecret
  APP_SECRET: process.env.WECHAT_SERVICE_APP_SECRET || '',

  // 服务号Token
  TOKEN: process.env.WECHAT_SERVICE_TOKEN || '',

  // 服务号EncodingAESKey
  ENCODING_AES_KEY: process.env.WECHAT_SERVICE_ENCODING_AES_KEY || '',

  // 服务号消息推送URL (修复：统一使用生产环境地址)
  CALLBACK_URL: 'https://api.gongzhimall.com/wechat/service',
};

// 应用环境变量配置
export const appEnv = {
  isDev: __DEV__,
  version: '1.0.0',
  buildNumber: '1',
};

// Image selection limits
export const imageSelectionConfig = {
  maxSelection: 10,
  proMaxSelection: 50,
};

// User Tier
export const currentUserTier: 'free' | 'pro' = 'free'; // 'free' or 'pro'
