// React Native 全局变量声明
declare var __DEV__: boolean;

// 环境变量类型定义
declare module '@env' {
  export const WECHAT_APP_ID: string;
  export const WECHAT_APP_SECRET: string;
  export const WECHAT_PUSH_TOKEN: string;
  export const WECHAT_PUSH_ENCODING_AES_KEY: string;
  export const JPUSH_APP_KEY: string;
  export const JPUSH_CHANNEL: string;
  export const NODE_ENV: string;
  export const TOKEN_SECRET: string;
  export const WECHAT_API_TOKEN: string;
  export const WECHAT_BINDING_SECRET: string;
  export const WECHAT_BINDING_API_BASE_URL: string;
  export const WECHAT_CORP_ID: string;
  export const WECHAT_CORP_SECRET: string;
  export const WECHAT_AGENT_ID: string;
  export const WECHAT_CUSTOMER_SERVICE_URL: string;
  export const JPUSH_APP_SECRET: string;
  export const DEBUG_MODE: string;
  export const LOG_LEVEL: string;
  export const WECHAT_SYNC_BATCH_SIZE: string;
  export const WECHAT_SYNC_MAX_CONCURRENT_MEDIA: string;
  export const WECHAT_SYNC_TIMEOUT: string;
}

declare module 'react-native-vector-icons/Ionicons';
declare module 'react-native-vector-icons/AntDesign';
declare module 'react-native-vector-icons/Entypo';
declare module 'react-native-vector-icons/EvilIcons';
declare module 'react-native-vector-icons/Feather';
declare module 'react-native-vector-icons/FontAwesome';
declare module 'react-native-vector-icons/Foundation';
declare module 'react-native-vector-icons/MaterialIcons';
declare module 'react-native-vector-icons/MaterialCommunityIcons';
declare module 'react-native-vector-icons/SimpleLineIcons';
declare module 'react-native-vector-icons/Octicons';
declare module 'react-native-vector-icons/Zocial';
declare module 'react-native-vector-icons/Fontisto';

// 为react-native-haptic-feedback添加类型声明
declare module 'react-native-haptic-feedback' {
  export interface HapticFeedbackTypes {
    impactLight: string;
    impactMedium: string;
    impactHeavy: string;
    notificationSuccess: string;
    notificationWarning: string;
    notificationError: string;
    selection: string;
  }

  export interface HapticOptions {
    enableVibrateFallback?: boolean;
    ignoreAndroidSystemSettings?: boolean;
  }

  export function trigger(type: keyof HapticFeedbackTypes, options?: HapticOptions): void;

  export const HapticFeedbackTypes: HapticFeedbackTypes;
}
