// React Native 全局变量声明
declare var __DEV__: boolean;

declare module 'react-native-vector-icons/Ionicons';
declare module 'react-native-vector-icons/AntDesign';
declare module 'react-native-vector-icons/Entypo';
declare module 'react-native-vector-icons/EvilIcons';
declare module 'react-native-vector-icons/Feather';
declare module 'react-native-vector-icons/FontAwesome';
declare module 'react-native-vector-icons/Foundation';
declare module 'react-native-vector-icons/MaterialIcons';
declare module 'react-native-vector-icons/MaterialCommunityIcons';
declare module 'react-native-vector-icons/SimpleLineIcons';
declare module 'react-native-vector-icons/Octicons';
declare module 'react-native-vector-icons/Zocial';
declare module 'react-native-vector-icons/Fontisto';

// 为react-native-haptic-feedback添加类型声明
declare module 'react-native-haptic-feedback' {
  export interface HapticFeedbackTypes {
    impactLight: string;
    impactMedium: string;
    impactHeavy: string;
    notificationSuccess: string;
    notificationWarning: string;
    notificationError: string;
    selection: string;
  }

  export interface HapticOptions {
    enableVibrateFallback?: boolean;
    ignoreAndroidSystemSettings?: boolean;
  }

  export function trigger(type: keyof HapticFeedbackTypes, options?: HapticOptions): void;

  export const HapticFeedbackTypes: HapticFeedbackTypes;
}
