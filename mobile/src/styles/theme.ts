// 公职猫设计系统主题配置
// 基于设计系统规范 docs/03_design/设计系统规范.md

export interface Theme {
  colors: {
    // 主品牌色
    primary: string;
    primaryLight: string;
    primaryDark: string;

    // 背景色系
    backgroundPrimary: string;
    backgroundSecondary: string;
    backgroundTertiary: string;

    // 文字色系
    textPrimary: string;
    textSecondary: string;
    textTertiary: string;
    textDisabled: string;

    // 功能色系
    success: string;
    warning: string;
    error: string;
    info: string;

    // 优先级色系
    priorityHigh: string;
    priorityMedium: string;
    priorityLow: string;

    headerBackground: string;
    headerDivider: string;
    bubbleUser: string;
    bubbleAssistant: string;
    sendButton: string;
    timestampBg: string;
    timestampText: string;
  };

  fonts: {
    // 标题字体
    titleLarge: number;
    titleMedium: number;
    titleSmall: number;

    // 正文字体
    bodyLarge: number;
    bodyMedium: number;
    bodySmall: number;

    // 特殊字体
    caption: number;
    button: number;
  };

  fontWeights: {
    bold: '700';
    medium: '500';
    regular: '400';
  };

  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
    xxxl: number;
  };

  borderRadius: {
    small: number;
    medium: number;
    large: number;
  };

  shadows: {
    small: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
    medium: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
  };

  animation: {
    durationFast: number;
    durationNormal: number;
    durationSlow: number;
  };
}

export const defaultTheme: Theme = {
  colors: {
    // 主品牌色 - 温暖橙色
    primary: '#07C160', // 微信主绿
    primaryLight: '#95EC69', // 微信浅绿
    primaryDark: '#07C160',

    // 背景色系 - 聊天窗口浅色风格
    backgroundPrimary: '#F7F8FA', // 聊天主背景
    backgroundSecondary: '#FFFFFF',
    backgroundTertiary: '#E0E0E6', // header分割线/次级区域

    headerBackground: '#FFFFFF',
    headerDivider: '#ECECF2',
    bubbleUser: '#95EC69', // 微信浅绿
    bubbleAssistant: '#FFFFFF',
    sendButton: '#07C160', // 微信主绿
    timestampBg: 'transparent',
    timestampText: '#999999',

    // 文字色系
    textPrimary: '#374151',
    textSecondary: '#999999',
    textTertiary: '#999999',
    textDisabled: '#666666',

    // 功能色系
    success: '#4CAF50',
    warning: '#FF9500',
    error: '#FF4444',
    info: '#2196F3',

    // 优先级色系
    priorityHigh: '#FF4444',
    priorityMedium: '#FF8C00',
    priorityLow: '#4CAF50',
  },

  fonts: {
    // 标题字体
    titleLarge: 24,
    titleMedium: 20,
    titleSmall: 16,

    // 正文字体
    bodyLarge: 16,
    bodyMedium: 14,
    bodySmall: 12,

    // 特殊字体
    caption: 10,
    button: 14,
  },

  fontWeights: {
    bold: '700',
    medium: '500',
    regular: '400',
  },

  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },

  borderRadius: {
    small: 8,
    medium: 12,
    large: 16,
  },

  shadows: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
      elevation: 4,
    },
  },

  animation: {
    durationFast: 200,
    durationNormal: 300,
    durationSlow: 500,
  },
};

// 工具函数：获取优先级颜色
export const getPriorityColor = (priority: 'high' | 'medium' | 'low', theme: Theme = defaultTheme): string => {
  switch (priority) {
    case 'high':
      return theme.colors.priorityHigh;
    case 'medium':
      return theme.colors.priorityMedium;
    case 'low':
      return theme.colors.priorityLow;
    default:
      return theme.colors.priorityMedium;
  }
};

// 工具函数：获取状态图标
export const getStatusIcon = (status: 'pending' | 'in-progress' | 'completed'): string => {
  switch (status) {
    case 'completed':
      return '✅';
    case 'in-progress':
      return '🔄';
    case 'pending':
    default:
      return '⏳';
  }
};

// 工具函数：获取优先级图标
export const getPriorityIcon = (priority: 'high' | 'medium' | 'low'): string => {
  switch (priority) {
    case 'high':
      return '🔴';
    case 'medium':
      return '🟡';
    case 'low':
      return '🟢';
    default:
      return '🟡';
  }
};

// 常用图标集合
export const icons = {
  // 功能图标
  voice: '🎤',
  camera: '📷',
  document: '📄',
  calendar: '📅',
  search: '🔍',
  settings: '⚙️',
  user: '👤',

  // 状态图标
  pending: '⏳',
  inProgress: '🔄',
  completed: '✅',
  cancelled: '❌',

  // 优先级图标
  high: '🔴',
  medium: '🟡',
  low: '🟢',

  // 导航图标
  home: '🏠',
  tasks: '📋',
  calendarNav: '📅',
  profile: '👤',

  // 操作图标
  add: '➕',
  edit: '✏️',
  delete: '🗑️',
  share: '📤',
  download: '⬇️',
  upload: '⬆️',
};

export default defaultTheme;
