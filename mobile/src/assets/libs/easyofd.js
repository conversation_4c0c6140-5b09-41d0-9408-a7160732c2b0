!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jszip"),require("x2js"),require("opentype.js"),require("jb2")):"function"==typeof define&&define.amd?define(["exports","jszip","x2js","opentype.js","jb2"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self)["JSZip,X2JS,JB2"]={},t.JSZip,t.X2JS,t.opentype,t.JB2)}(this,(function(t,e,i,s,n){"use strict";function a(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=a(e),o=a(i),l=a(s),h=a(n);function u(t,e,i){return e=y(e),function(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,c()?Reflect.construct(e,i||[],y(t).constructor):e.apply(t,i))}function c(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(c=function(){return!!t})()}function f(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var s=i.call(t,e||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}function m(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function d(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,f(s.key),s)}}function p(t,e,i){return e&&d(t.prototype,e),i&&d(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function g(t,e,i){return(e=f(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function v(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&P(t,e)}function y(t){return y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},y(t)}function P(t,e){return P=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},P(t,e)}function w(){return w="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,i){var s=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=y(t)););return t}(t,e);if(s){var n=Object.getOwnPropertyDescriptor(s,e);return n.get?n.get.call(arguments.length<3?t:i):n.value}},w.apply(this,arguments)}function D(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var s,n,a,r,o=[],l=!0,h=!1;try{if(a=(i=i.call(t)).next,0===e){if(Object(i)!==i)return;l=!1}else for(;!(l=(s=a.call(i)).done)&&(o.push(s.value),o.length!==e);l=!0);}catch(t){h=!0,n=t}finally{try{if(!l&&null!=i.return&&(r=i.return(),Object(r)!==r))return}finally{if(h)throw n}}return o}}(t,e)||b(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(t,e){if(t){if("string"==typeof t)return F(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?F(t,e):void 0}}function F(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,s=new Array(e);i<e;i++)s[i]=t[i];return s}var S=function(){function t(){m(this,t),this.docID="",this.title="",this.author="",this.subject="",this.abstract="",this.creationDate="",this.modDate="",this.docUsage="",this.cover="",this.creator="",this.creatorVersion=""}return p(t,[{key:"ParseFromXml",value:function(t){null!=t.DocID&&(this.docID=t.DocID.toString()),null!=t.Title&&(this.title=t.Title.toString()),null!=t.Author&&(this.author=t.Author.toString()),null!=t.Subject&&(this.subject=t.Subject.toString()),null!=t.Abstract&&(this.abstract=t.Abstract.toString()),null!=t.CreationDate&&(this.creationDate=t.CreationDate.toString()),null!=t.ModDate&&(this.modDate=t.ModDate.toString()),null!=t.DocUsage&&(this.docUsage=t.DocUsage.toString()),null!=t.Cover&&(this.cover=t.Cover.toString()),null!=t.Creator&&(this.creator=t.Creator.toString()),null!=t.CreatorVersion&&(this.creatorVersion=t.CreatorVersion.toString())}}]),t}(),x=function(){function t(){m(this,t),this.physicalBox=null,this.applicationlBox=null,this.contentBox=null,this.bleedBox=null}return p(t,[{key:"ParseFromXml",value:function(t){null!=t.PhysicalBox&&(this.physicalBox={x:0,y:0,w:0,h:0},this.physicalBox=this.Parse(t.PhysicalBox.toString())),null!=t.applicationlBox&&(this.applicationlBox={x:0,y:0,w:0,h:0},this.applicationlBox=this.Parse(t.ApplicationlBox.toString())),null!=t.ContentBox&&(this.contentBox={x:0,y:0,w:0,h:0},this.contentBox=this.Parse(t.ContentBox.toString())),null!=t.BleedBox&&(this.bleedBox={x:0,y:0,w:0,h:0},this.bleedBox=this.Parse(t.BleedBox.toString()))}},{key:"Parse",value:function(t){try{var e=t.split(" ");return{x:parseFloat(e[0]),y:parseFloat(e[1]),w:parseFloat(e[2]),h:parseFloat(e[3])}}catch(t){return null}}}]),t}(),k=function(){function t(){m(this,t),this.width=0,this.height=0,this.xStep=this.width,this.yStep=this.height,this.reflectMethod="Noraml",this.relativeTo="Object",this.cTM=[],this.thumbnail=""}return p(t,[{key:"ParseFromXml",value:function(t){}}]),t}(),I=function(){function t(){m(this,t),this.value=[],this.index=0,this.colorSpace="RGB",this.alpha=255,this.pattern=new k}return p(t,[{key:"GetCtxDrawColor",value:function(){return"RGB"==this.colorSpace||"5"==this.colorSpace||"4"==this.colorSpace?"rgba("+this.value[0]+","+this.value[1]+","+this.value[2]+","+this.alpha/255+")":"Gray"==this.colorSpace?this.value:(this.value.length=3)?"rgba("+this.value[0]+","+this.value[1]+","+this.value[2]+","+this.alpha/255+")":"rgba(255, 255, 255, 0.0)"}},{key:"ParseFromXml",value:function(t){if(this.value=[],null!=t._Index&&(this.index=parseInt(t._Index)),null!=t._ColorSpace&&(this.colorSpace=t._ColorSpace),null!=t._Alpha&&(this.alpha=t._Alpha),null!=t.Pattern&&this.pattern.ParseFromXml(t.Pattern),null!=t._Value)for(var e=t._Value.split(" "),i=0;i<e.length;i++){var s=e[i].toString(),n=0;"#"===s[0]?(s=s.replace("#",""),n=parseInt(s,16),this.value.push(n)):this.value.push(parseInt(e[i]))}}}]),t}(),C=function(){function t(){m(this,t),this.boundary={x:0,y:0,h:0,w:0},this.pBoundary={x:0,y:0,h:0,w:0},this.name="",this.visible=!0,this.cTM=null,this.drawParam="",this.lineWidth=.353,this.cap="Butt",this.join="Miter",this.miterLimit=3.528,this.dashOffset=0,this.dashPattern=[],this.actions=[],this.clips=[]}return p(t,[{key:"setpBoundary",value:function(t){this.pBoundary=t}},{key:"ParseFromXml",value:function(t){null!=t._Boundary&&(this.boundary=this.Parse(t._Boundary)),null!=t._Name&&(this.name=t._Name.toString()),null!=t._Visible&&(this.visible=!0),null!=t._CTM&&(this.cTM=this.ParseCTM(t._CTM)),null!=t._DrawParam&&(this.drawParam=t._DrawParam.toString()),null!=t._LineWidth&&(this.lineWidth=parseFloat(t._LineWidth.toString())),null!=t._Cap&&(this.cap=t._Cap.toString()),null!=t._Join&&(this.join=t._Join.toString()),null!=t._MiterLimit&&(this.miterLimit=parseFloat(t._MiterLimit.toString())),null!=t._ID&&(this.id=t._ID.toString()),null!=t._DashOffset&&(this.dashOffset=parseFloat(t._DashOffset.toString()))}},{key:"Parse",value:function(t){try{var e=t.split(" ");return{x:parseFloat(e[0]),y:parseFloat(e[1]),w:parseFloat(e[2]),h:parseFloat(e[3])}}catch(t){return{x:0,y:0,w:0,h:0}}}},{key:"ParseCTM",value:function(t){try{var e=t.split(" ");return{a:parseFloat(e[0]),b:parseFloat(e[1]),c:parseFloat(e[2]),d:parseFloat(e[3]),e:parseFloat(e[4]),f:parseFloat(e[5])}}catch(t){return null}}},{key:"setCTM",value:function(t){t.transform(this.cTM.a,this.cTM.b,this.cTM.c,this.cTM.d,this.MM2PX(this.cTM.e),this.MM2PX(this.cTM.f))}},{key:"MM2PX",value:function(t){return parseFloat(parseFloat(t)/25.4*96)}},{key:"GetBoundaryX",value:function(){return parseFloat((this.boundary.x+this.pBoundary.x)/25.4*96)}},{key:"GetBoundaryY",value:function(){return parseFloat((this.boundary.y+this.pBoundary.y)/25.4*96)}},{key:"StringToMM",value:function(t){for(var e=t.split(" "),i=0,s="";!(i>=e.length);){var n=e[i++],a=/^[a-zA-Z]$/.test(n);if(a&&"C"==n)s+=" Z";else if(a&&"B"==n)s+=" C";else if(a&&"S"==n)s+=" M";else if(a&&"A"==n){s=s+" "+n;var r=e[i++],o=e[i++],l=e[i++],h=e[i++],u=e[i++],c=e[i++],f=e[i++];s=s+" "+this.MM2PX(r)+" "+this.MM2PX(o)+" "+l+" "+h+" "+u+" "+this.MM2PX(c)+" "+this.MM2PX(f)}else a?s=s+" "+n:""==n?i++:s=s+" "+this.MM2PX(n)}return s}}]),t}(),B=function(){function t(){m(this,t),this.x=0,this.y=0,this.deltaX=[],this.deltaY=[],this.textValue=""}return p(t,[{key:"DrawGlyph",value:function(t,e,i,s){var n=e.cTM;t.save();try{var a=e.GetBoundaryX(),r=e.GetBoundaryY();t.translate(a,r),null!=n&&e.setCTM(t);for(var o=e.MM2PX(this.x),l=e.MM2PX(this.y),h=0;h<this.textValue.length;h++){this.textValue[h];var u=i[h],c=s.glyphs.get(u).getPath(o,l,e.fontSize);null!=e.Fontfill&&(c.fill=e.Fontfill),c.draw(t),this.deltaX.length>0&&this.deltaX.length>h?o+=e.MM2PX(this.deltaX[h]):this.deltaX.length>0&&h>=this.deltaX.length&&(o+=e.MM2PX(this.deltaX[this.deltaX.length-1])),this.deltaY.length>0&&this.deltaY.length>h?l+=e.MM2PX(this.deltaY[h]):this.deltaY.length>0&&h>=this.deltaY.length&&(l+=e.MM2PX(this.deltaY[this.deltaY.length-1]))}}catch(t){}t.restore()}},{key:"Draw",value:function(t,e){var i=e.cTM;t.save();try{var s=e.GetBoundaryX(),n=e.GetBoundaryY();t.translate(s,n),null!=i&&e.setCTM(t);for(var a=e.MM2PX(this.x),r=e.MM2PX(this.y),o=0;o<this.textValue.length;o++){var l=this.textValue[o];t.fillText(l,a,r),e.strocke&&t.fillStyle!=t.strokeStyle&&t.strokeText(l,a,r),this.deltaX.length>0&&this.deltaX.length>o?a+=e.MM2PX(this.deltaX[o]):this.deltaX.length>0&&o>=this.deltaX.length&&(a+=e.MM2PX(this.deltaX[this.deltaX.length-1])),this.deltaY.length>0&&this.deltaY.length>o?r+=e.MM2PX(this.deltaY[o]):this.deltaY.length>0&&o>=this.deltaY.length&&(r+=e.MM2PX(this.deltaY[this.deltaY.length-1]))}}catch(t){}t.restore()}},{key:"ParseFromXml",value:function(t){if(null!=t._X&&(this.x=parseFloat(t._X.toString())),null!=t._Y&&(this.y=parseFloat(t._Y.toString())),null!=t.__text&&(this.textValue=t.__text.toString()),null!=t._DeltaX)for(var e=t._DeltaX.split(" "),i=0;i<e.length;i++){var s=e[i];if("G"==s||"g"==s)for(var n=e[++i],a=parseFloat(e[++i]),r=0;r<n;r++)this.deltaX.push(a);else this.deltaX.push(parseFloat(e[i]))}if(null!=t._DeltaY){var o=t._DeltaY.split(" ");for(i=0;i<o.length;i++){var l=o[i];if("G"==l||"g"==l){var h=o[++i],u=parseFloat(o[++i]);for(r=0;r<h;r++)this.deltaY.push(u)}else this.deltaY.push(parseFloat(o[i]))}}}}]),t}(),_=function(){function t(){m(this,t),this.CodePosition=0,this.CodeCount=1,this.GlyphCount=1,this.Glyphs=[]}return p(t,[{key:"Draw",value:function(t,e,i,s){null!=s&&i.DrawGlyph(t,e,this.Glyphs,s)}},{key:"ParseFromXml",value:function(t){if(null!=t._CodePosition&&(this.CodePosition=parseInt(t._CodePosition.toString())),null!=t._CodeCount&&(this.CodeCount=parseInt(t._CodeCount.toString())),null!=t._GlyphCount&&(this.GlyphCount=parseInt(t._GlyphCount.toString())),null!=t.Glyphs)for(var e=t.Glyphs.toString().split(" "),i=0;i<e.length;i++)this.Glyphs.push(parseFloat(e[i]))}}]),t}(),X=function(t){function e(t){var i;return m(this,e),(i=u(this,e)).glyfile=!1,i.commonData=t,i.font="",i.size=0,i.stroke=!1,i.fill=!1,i.hScale=1,i.readDirection=0,i.charDircection=!1,i.weight=400,i.italic=!1,i.fillColor=new I,i.fillColor.value.push(0),i.fillColor.value.push(0),i.fillColor.value.push(0),i.strokeColor=null,i.cgTransFrom=[],i.textCode=[],i}return v(e,t),p(e,[{key:"Draw",value:function(t){if(t.save(),this.SetPublicDrawParam(t),t.font=this.GetFont(),this.fill&&null!=this.fillColor&&(t.fillStyle=this.fillColor.GetCtxDrawColor(),this.Fontfill=t.fillStyle),this.stroke&&null!=this.strokeColor&&(t.strokeStyle=this.strokeColor.GetCtxDrawColor(),this.fillStyle=t.fillStyle),this.cgTransFrom.length>0&&this.glyfile)for(var e=0;e<this.cgTransFrom.length;e++){var i=this.cgTransFrom[e],s=this.textCode[e];i.Draw(t,this,s,window.glyFont.get(this.fontFile))}else for(e=0;e<this.textCode.length;e++){(s=this.textCode[e]).Draw(t,this)}t.restore()}},{key:"SetPublicDrawParam",value:function(t){if(null!=this.drawParam&&""!=this.drawParam&&null!=this.commonData.GetPublicRes()){var e=this.commonData.GetDrawParamByID(this.drawParam);null!=e&&(""!=e.relative&&this.GetParentParam(t,e.relative),e.SetCtx(t),this.strokeColor=e.strokeColor,this.fillColor=e.fillColor)}}},{key:"GetParentParam",value:function(t,e){if(""!=e){var i=this.commonData.GetDrawParamByID(e);null!=i&&(""!=i.relative&&this.GetParentParam(t,i.relative),i.SetCtx(t))}}},{key:"GetFont",value:function(){var t=this.weight,e="宋体";if(this.fontSize=parseInt(this.MM2PX(this.size)),null!=this.font&&""!=this.font&&null!=this.commonData.GetPublicRes()){var i=this.commonData.GetPublicRes()[0];if(null!=i){var s=i.GetFontByID(this.font);null!=s.fontFile&&(this.glyfile=!0,this.fontFile=s.fontFile),null!=s&&(""!=s.familyName&&(e=s.familyName),""!=s.fontname&&(e=s.fontname),s.italic&&"italic",s.bold&&(t="bold"))}}return(this.italic?"italic":"")+" normal "+t+" "+parseInt(this.MM2PX(this.size))+"px "+e}},{key:"ParseFromXml",value:function(t){if(w(y(e.prototype),"ParseFromXml",this).call(this,t),null!=t._Font&&(this.font=t._Font.toString()),null!=t._Size&&(this.size=parseFloat(t._Size.toString())),null!=t._Stroke&&"true"==t._Stroke?this.stroke=!0:"false"==t._Stroke&&(this.stroke=!1),null!=t._Fill&&"true"==t._Fill&&(this.fill=!0),null!=t._Italic&&"true"==t._Italic.toString().toLowerCase()&&(this.italic=!0),null!=t._HScale&&(this.hScale=parseFloat(t._HScale.toString())),null!=t._ReadDirection&&(this.readDirection=parseInt(t._ReadDirection.toString())),null!=t._CharDircection&&(this.charDircection=parseInt(t._CharDircection.toString())),null!=t._Weight&&(this.weight=parseInt(t._Weight.toString())),null!=t.FillColor&&(this.fill=!0,this.fillColor=new I,this.fillColor.ParseFromXml(t.FillColor)),null!=t.StrokeColor&&(this.stroke=!0,this.strokeColor=new I,this.strokeColor.ParseFromXml(t.StrokeColor)),null!=t.TextCode&&Array.isArray(t.TextCode))for(var i=0;i<t.TextCode.length;i++){var s=t.TextCode[i],n=new B;n.ParseFromXml(s),this.textCode.push(n)}else if(null!=t.TextCode){var a=new B;a.ParseFromXml(t.TextCode),this.textCode.push(a)}if(null!=t.CGTransform&&Array.isArray(t.CGTransform))for(i=0;i<t.CGTransform.length;i++){s=t.CGTransform[i];var r=new _;r.ParseFromXml(s),this.cgTransFrom.push(r)}else if(null!=t.CGTransform){var o=new _;o.ParseFromXml(t.CGTransform),this.cgTransFrom.push(o)}}}]),e}(C),M=function(t){function e(){var t;return m(this,e),(t=u(this,e)).stocke=!0,t.fill=!1,t.rule="NonZero",t.fillColor=new I,t.strokeColor=new I,t.strokeColor.value=[0,0,0],t.abbreviatedData="",t.dashOffset=0,t.dashPattern=[],t}return v(e,t),p(e,[{key:"Draw",value:function(t){t.save();try{t.lineCap=this.cap.toLowerCase(),t.lineJoin=this.join.toLowerCase(),"miter"==this.join.toLowerCase()&&(t.miterLimit=this.miterLimit),this.dashPattern.length>0&&(t.setLineDash(this.dashPattern),t.lineDashOffset=this.dashOffset);var e=this.GetBoundaryX(),i=this.GetBoundaryY();t.translate(e,i),null!=this.cTM&&this.setCTM(t);var s=this.StringToMM(this.abbreviatedData);this.stroke&&(t.strokeStyle=this.strokeColor.GetCtxDrawColor()),this.fill&&(t.fillStyle=this.fillColor.GetCtxDrawColor()),t.lineWidth=parseInt(this.MM2PX(this.lineWidth)),t.beginPath();var n=new Path2D(s);t.closePath(),this.fill&&t.fill(n),this.stocke&&t.stroke(n)}catch(t){}t.restore()}},{key:"ParseFromXml",value:function(t){if(w(y(e.prototype),"ParseFromXml",this).call(this,t),null!=t._Stroke&&"true"==t._Stroke?this.stroke=!0:"false"==t._Stroke&&(this.stroke=!1),null!=t._Fill&&"true"==t._Fill&&(this.fill=!0),null!=t._Rule&&(this.rule=t._Rule.toString()),null!=t.FillColor?(this.fillColor.ParseFromXml(t.FillColor),this.fill=!0):this.fill=!1,null!=t.StrokeColor&&(this.strokeColor.ParseFromXml(t.StrokeColor),this.stroke=!0),null!=t._DashOffset&&(this.dashOffset=this.MM2PX(parseFloat(t._DashOffset.toString()))),null!=t._DashPattern)for(var i=t._DashPattern.split(" "),s=0;s<i.length;s++)this.dashPattern.push(this.MM2PX(parseInt(i[s])));null!=t.AbbreviatedData&&(this.abbreviatedData=t.AbbreviatedData.toString())}}]),e}(C),O=function(){function t(){m(this,t),this.lineWidth=.353,this.HorizonalCornerRadius=0,this.VerticalCornerRadius="",this.dashOffset=0,this.dashPattern=[],this.borderColor=new I}return p(t,[{key:"ParseFromXml",value:function(t){}}]),t}(),T=function(t){function e(t){var i;return m(this,e),(i=u(this,e)).commonData=t,i.resourceID="",i.substitution="",i.imageMask="",i.borer=new O,i}return v(e,t),p(e,[{key:"Draw",value:function(t){t.save();try{var e=this.GetBoundaryX(),i=this.GetBoundaryY();t.translate(e,i),this.cTM;var s=this.MM2PX(0),n=this.MM2PX(0),a=this.MM2PX(this.boundary.w),r=this.MM2PX(this.boundary.h),o=this.commonData.GetMultiMediasByID(this.resourceID);t.globalAlpha=1,null!=o&&null!=o.image&&t.drawImage(o.image,s,n,a,r)}catch(t){}t.restore()}},{key:"ParseFromXml",value:function(t){w(y(e.prototype),"ParseFromXml",this).call(this,t),null!=t._Substitution&&(this.substitution=t._Substitution.toString()),null!=t._Size&&(this.size=parseFloat(t._Size.toString())),null!=t._ResourceID&&(this.resourceID=parseFloat(t._ResourceID.toString())),null!=t._ImageMask&&(this.imageMask=parseFloat(t._ImageMask.toString())),null!=t.Border&&this.borer.ParseFromXml(t.Border)}}]),e}(C),A=function(){function t(e){m(this,t),this.commonData=e,this.type="Body",this.drawParam="",this.id=""}return p(t,[{key:"Draw",value:function(t){}},{key:"ParseFromXml",value:function(t){null!=t._ID&&(this.id=t._ID.toString()),null!=t._Type&&(this.type=t._Type.toString()),null!=t._DrawParam&&(this.drawParam=t._DrawParam.toString())}}]),t}(),N=function(){function t(){m(this,t),this.ID="",this.boundary={x:0,y:0,h:0,w:0},this.resourceID=""}return p(t,[{key:"ParseFromXml",value:function(t){null!=t._Boundary&&(this.boundary=this.Parse(t._Boundary)),null!=t._ID&&(this.ID=t._ID),null!=t._ResourceID&&(this.resourceID=t._ResourceID)}},{key:"Parse",value:function(t){try{var e=t.split(" ");return{x:parseFloat(e[0]),y:parseFloat(e[1]),w:parseFloat(e[2]),h:parseFloat(e[3])}}catch(t){return{x:0,y:0,w:0,h:0}}}}]),t}(),R=function(t){function e(t){var i;return m(this,e),(i=u(this,e,[t])).boundary={x:0,y:0,h:0,w:0},i.pageBlock=[],i.textObject=[],i.pathObject=[],i.imageObject=[],i.compostieObject=[],i}return v(e,t),p(e,[{key:"Draw",value:function(t){t.save(),this.SetPublicDrawParam(t);for(var e=0;e<this.pathObject.length;e++){var i=this.pathObject[e];i.setpBoundary(this.boundary),i.Draw(t)}for(e=0;e<this.textObject.length;e++){var s=this.textObject[e];s.setpBoundary(this.boundary),s.Draw(t)}for(e=0;e<this.imageObject.length;e++){var n=this.imageObject[e];n.setpBoundary(this.boundary),n.Draw(t)}for(e=0;e<this.compostieObject.length;e++){var a=this.compostieObject[e];this.commonData.GetCompositeGraphicUnitByID(a.resourceID).Draw(t,a)}for(e=0;e<this.pageBlock.length;e++){(a=this.pageBlock[e]).setBoundary(this.boundary),a.Draw(t)}t.restore()}},{key:"setBoundary",value:function(t){this.boundary=t}},{key:"SetPublicDrawParam",value:function(t){if(null!=this.drawParam&&""!=this.drawParam&&null!=this.commonData.GetPublicRes()){var e=this.commonData.GetDrawParamByID(this.drawParam);null!=e&&(""!=e.relative&&this.GetParentParam(t,e.relative),e.SetCtx(t))}}},{key:"GetParentParam",value:function(t,e){if(""!=e){var i=this.commonData.GetDrawParamByID(e);null!=i&&(""!=i.relative&&this.GetParentParam(t,i.relative),i.SetCtx(t))}}},{key:"ParseFromXml",value:function(t){if(null!=t){if(w(y(e.prototype),"ParseFromXml",this).call(this,t),null!=t.TextObject&&Array.isArray(t.TextObject))for(var i=0;i<t.TextObject.length;i++){var s=t.TextObject[i],n=new X(this.commonData);n.ParseFromXml(s),this.textObject.push(n)}else if(null!=t.TextObject){var a=new X(this.commonData);a.ParseFromXml(t.TextObject),this.textObject.push(a)}if(null!=t.PathObject&&Array.isArray(t.PathObject))for(i=0;i<t.PathObject.length;i++){s=t.PathObject[i];var r=new M(this.commonData);r.ParseFromXml(s),this.pathObject.push(r)}else if(null!=t.PathObject){var o=new M(this.commonData);o.ParseFromXml(t.PathObject),this.pathObject.push(o)}if(null!=t.ImageObject&&Array.isArray(t.ImageObject))for(i=0;i<t.ImageObject.length;i++){s=t.ImageObject[i];var l=new T(this.commonData);l.ParseFromXml(s),this.imageObject.push(l)}else if(null!=t.ImageObject){var h=new T(this.commonData);h.ParseFromXml(t.ImageObject),this.imageObject.push(h)}if(null!=t.PageBlock&&Array.isArray(t.PageBlock))for(i=0;i<t.PageBlock.length;i++){s=t.PageBlock[i];var u=new e(this.commonData);u.ParseFromXml(s.PageBlock),this.pageBlock.push(u)}else if(null!=t.PageBlock){var c=new e(this.commonData);c.ParseFromXml(t.PageBlock),this.pageBlock.push(c)}if(null!=t.CompositeObject&&Array.isArray(t.CompositeObject))for(i=0;i<t.CompositeObject.length;i++){s=t.PageBlock[i];var f=new N;f.ParseFromXml(s.CompositeObject),this.compostieObject.push(f)}else if(null!=t.CompositeObject){var m=new N;m.ParseFromXml(t.CompositeObject),this.compostieObject.push(m)}}}}]),e}(A),j=function(t){function e(t){return m(this,e),u(this,e,[t])}return v(e,t),p(e,[{key:"Draw",value:function(t){w(y(e.prototype),"Draw",this).call(this,t)}},{key:"ParseFromXml",value:function(t){w(y(e.prototype),"ParseFromXml",this).call(this,t)}}]),e}(R),G=function(){function t(e){m(this,t),this.commonData=e,this.layers=[]}return p(t,[{key:"Draw",value:function(t){for(var e=0;e<this.layers.length;e++){this.layers[e].Draw(t)}}},{key:"ParseFromXml",value:function(t){if(null!=t.Layer&&Array.isArray(t.Layer))for(var e=0;e<t.Layer.length;e++){var i=t.Layer[e],s=new j(this.commonData);s.ParseFromXml(i),this.layers.push(s)}else if(null!=t.Layer){var n=new j(this.commonData);n.ParseFromXml(t.Layer),this.layers.push(n)}}}]),t}(),E=function(){function t(){m(this,t),this.templateID="",this.zOrder=""}return p(t,[{key:"ParseFromXml",value:function(t){null!=t._TemplateID&&(this.templateID=t._TemplateID.toString()),null!=t._ZOrder&&(this.zOrder=t._ZOrder.toString())}}]),t}(),z=function(){function t(e,i){m(this,t),this.docs=e,this.commonData=i,this.id="",this.baseLoc="",this.template=[],this.area=new x(this.commonData),this.pageRes=[],this.content=[],this.actions=[]}return p(t,[{key:"Draw",value:function(t){for(var e=0;e<this.content.length;e++){this.content[e].Draw(t)}}},{key:"ParseFromXml",value:function(t){if(null!=t._ID&&(this.id=t._ID.toString()),null!=t._BaseLoc&&(this.baseLoc=t._BaseLoc.toString(),null!=this.docs&&(t=this.docs.get(this.baseLoc).Page)),null!=t.Area&&this.area.ParseFromXml(t.Area),null!=t.Content&&Array.isArray(t.Content))for(var e=0;e<t.DocBody.length;e++){var i=t.Content[e],s=new G(this.commonData);s.ParseFromXml(i),this.content.push(s)}else if(null!=t.Content){var n=new G(this.commonData);n.ParseFromXml(t.Content),this.content.push(n)}if(null!=t.Template&&Array.isArray(t.Template))for(e=0;e<t.Template.length;e++){i=t.Template[e];var a=new E(this.commonData);a.ParseFromXml(i),this.template.push(a)}else if(null!=t.Template){var r=new E(this.commonData);r.ParseFromXml(t.Template),this.template.push(r)}}}]),t}(),L=function(){function t(e,i){m(this,t),this.docs=e,this.commonData=i,this.id="",this.page=new z(this.docs,this.commonData),this.baseLoc="",this.name="",this.zOrder=""}return p(t,[{key:"Draw",value:function(t){this.page.Draw(t)}},{key:"ParseFromXml",value:function(t){if(null!=t._ID&&(this.id=t._ID.toString()),null!=t._BaseLoc){this.baseLoc=t._BaseLoc.toString();var e=this.docs.get(this.baseLoc);if(null==e)throw new Error(this.baseLoc+"模板文件找不到");this.page.ParseFromXml(e.Page)}null!=t._Name&&(this.name=t._Name.toString()),null!=t._ZOrder&&(this.zOrder=t._ZOrder.toString())}}]),t}(),V=function(){function t(){m(this,t),this.id="",this.relative="",this.lineWidth=.353,this.cap="Butt",this.dashOffset=0,this.dashPattern=[],this.join="Miter",this.miterLimit=3.528,this.fillColor=new I,this.strokeColor=new I}return p(t,[{key:"SetCtx",value:function(t){t.miterLimit=this.MM2PX(this.miterLimit),t.lineWidth=this.MM2PX(this.lineWidth),t.strokeStyle=this.strokeColor.GetCtxDrawColor(),t.fillStyle=this.fillColor.GetCtxDrawColor(),t.lineCap=this.cap.toLowerCase()}},{key:"ParseFromXml",value:function(t){if(null!=t._ID&&(this.id=t._ID.toString()),null!=t._Relative&&(this.relative=t._Relative.toString()),null!=t._LineWidth&&(this.lineWidth=parseFloat(t._LineWidth.toString())),null!=t._Cap&&(this.cap=t._Cap.toString()),null!=t._DashOffset&&(this.dashOffset=parseFloat(t._DashOffset.toString())),null!=t.DashPattern)for(var e=t.DashPattern.split(" "),i=0;i<e.length;i++)this.dashPattern.push(parseInt(e[i]));null!=t._Join&&(this.join=t._Join.toString()),null!=t._MiterLimit&&(this.miterLimit=parseFloat(t._MiterLimit.toString())),null!=t.FillColor&&this.fillColor.ParseFromXml(t.FillColor),null!=t.StrokeColor&&this.strokeColor.ParseFromXml(t.StrokeColor)}},{key:"MM2PX",value:function(t){return parseFloat(parseFloat(t)/25.4*96)}}]),t}(),U=function(){function t(){m(this,t),this.fontname="",this.familyName="",this.charset="unicode",this.italic=!1,this.bold=!1,this.serif=!1,this.fixedWidth=!1,this.fontFile=""}return p(t,[{key:"ParseFromXml",value:function(t){null!=t._ID&&(this.id=t._ID.toString()),null!=t._FontName&&(this.fontname=t._FontName.toString()),null!=t._FamilyName&&(this.familyName=t._FamilyName.toString()),null!=t._Charset&&(this.charset=t._Charset.toString()),null!=t._Italic&&(this.italic=!0),null!=t._Bold&&(this.bold=!0),null!=t._Serif&&(this.serif=!0),null!=t._FixedWidth&&(this.fixedWidth=!0),null!=t.FontFile&&(this.fontFile=t.FontFile.toString())}}]),t}(),W=function(){function t(e){m(this,t),this.docs=e,this.id="",this.type="",this.format="",this.mediaFile="",this.image=null}return p(t,[{key:"ParseFromXml",value:function(t){null!=t&&(this.id=t._ID.toString(),null!=t._Type&&(this.type=t._Type.toString()),null!=t._Format&&(this.format=t._Format),null!=t.MediaFile&&(this.mediaFile=t.MediaFile.toString().replace("/Doc_0/",""),0==this.mediaFile.indexOf("Res/")?this.image=this.docs.get(this.mediaFile):this.image=this.docs.get("Res/"+this.mediaFile)))}}]),t}(),Y=function(){function t(e){m(this,t),this.ID="",this.commonData=e,this.pageBlock=new R(e),this.width=0,this.height=0,this.boundary={x:0,y:0,h:0,w:0},this.resourceID=""}return p(t,[{key:"ParseFromXml",value:function(t){null!=t._Width&&(this.width=parseFloat(t._Width.toString())),null!=t._Height&&(this.height=parseFloat(t._Height.toString())),null!=t._ID&&(this.ID=t._ID),null!=t._ResourceID&&(this.resourceID=t._ResourceID),null!=t.Content&&this.pageBlock.ParseFromXml(t.Content)}},{key:"Draw",value:function(t,e){null!=e&&(this.boundary=e.boundary,this.pageBlock.setBoundary(this.boundary),this.pageBlock.Draw(t))}},{key:"Parse",value:function(t){try{var e=t.split(" ");return{x:parseFloat(e[0]),y:parseFloat(e[1]),w:parseFloat(e[2]),h:parseFloat(e[3])}}catch(t){return{x:0,y:0,w:0,h:0}}}}]),t}(),Z=function(){function t(e,i){m(this,t),this.commonData=i,this.docs=i.docs,this.baseLoc="",this.colorSpaces=[],this.drawParams=[],this.fonts=[],this.multiMedias=[],this.compositeGraphicUnits=[]}return p(t,[{key:"GetDrawParamByID",value:function(t){for(var e=0;e<this.drawParams.length;e++){var i=this.drawParams[e];if(i.id==t)return i}return null}},{key:"GetFontByID",value:function(t){for(var e=0;e<this.fonts.length;e++){var i=this.fonts[e];if(i.id==t)return i}return null}},{key:"GetMultiMediasByID",value:function(t){for(var e=0;e<this.multiMedias.length;e++){var i=this.multiMedias[e];if(i.id==t)return i}return null}},{key:"GetCompositeGraphicUnitByID",value:function(t){for(var e=0;e<this.compositeGraphicUnits.length;e++){var i=this.compositeGraphicUnits[e];if(i.ID==t)return i}return null}},{key:"ParseFromXml",value:function(t){if(t&&null!=t.toString()){if(null==this.docs.get(t.toString()))return;var e=this.docs.get(t.toString()).Res;if(null!=e._BaseLoc&&(this.baseLoc=e._BaseLoc.toString()),null!=e.DrawParams)if(null!=e.DrawParams.DrawParam&&Array.isArray(e.DrawParams.DrawParam))for(var i=0;i<e.DrawParams.DrawParam.length;i++){var s=e.DrawParams.DrawParam[i],n=new V;n.ParseFromXml(s),this.drawParams.push(n)}else if(null!=e.DrawParams){var a=new V;a.ParseFromXml(e.DrawParams.DrawParam),this.drawParams.push(a)}if(null!=e.Fonts)if(Array.isArray(e.Fonts))for(i=0;i<e.Fonts.length;i++){if(null!=(s=e.Fonts[i]).Font&&s.Font.length>0)for(var r=0;r<s.Font.length;r++){var o=s.Font[r],l=new U;l.ParseFromXml(o),this.fonts.push(l)}else{var h=new U;h.ParseFromXml(s),this.fonts.push(h)}}else if(null!=e.Fonts.Font&&Array.isArray(e.Fonts.Font))for(i=0;i<e.Fonts.Font.length;i++){s=e.Fonts.Font[i];var u=new U;u.ParseFromXml(s),this.fonts.push(u)}else if(null!=e.Fonts){var c=new U;c.ParseFromXml(e.Fonts.Font),this.fonts.push(c)}if(null!=e.MultiMedias)if(null!=e.MultiMedias.MultiMedia&&Array.isArray(e.MultiMedias.MultiMedia))for(i=0;i<e.MultiMedias.MultiMedia.length;i++){s=e.MultiMedias.MultiMedia[i];var f=new W(this.docs);f.ParseFromXml(s),this.multiMedias.push(f)}else if(null!=e.MultiMedias){var m=new W(this.docs);m.ParseFromXml(e.MultiMedias.MultiMedia),this.multiMedias.push(m)}if(null!=e.CompositeGraphicUnits&&null!=e.CompositeGraphicUnits.CompositeGraphicUnit){var d=e.CompositeGraphicUnits.CompositeGraphicUnit;if(null!=d&&Array.isArray(d))for(i=0;i<d.length;i++){var p=d[i],g=new Y(this.commonData);g.ParseFromXml(p.CompositeObject),this.compositeGraphicUnits.push(g)}else if(null!=d){var v=new Y(this.commonData);v.ParseFromXml(d),this.compositeGraphicUnits.push(v)}}}}},{key:"ParseFromXmlOFD",value:function(t){if(null!=t.toString()){if(null!=t._BaseLoc&&(this.baseLoc=t._BaseLoc.toString()),null!=t.DrawParams)if(null!=t.DrawParams.DrawParam&&Array.isArray(t.DrawParams.DrawParam))for(var e=0;e<t.DrawParams.DrawParam.length;e++){var i=t.DrawParams.DrawParam[e],s=new V;s.ParseFromXml(i),this.drawParams.push(s)}else if(null!=t.DrawParams){var n=new V;n.ParseFromXml(t.DrawParams.DrawParam),this.drawParams.push(n)}if(null!=t.Fonts)if(null!=t.Fonts.Font&&Array.isArray(t.Fonts.Font))for(e=0;e<t.Fonts.Font.length;e++){i=t.Fonts.Font[e];var a=new U;a.ParseFromXml(i),this.fonts.push(a)}else if(null!=t.Fonts){var r=new U;r.ParseFromXml(t.Fonts.Font),this.fonts.push(r)}if(null!=t.MultiMedias)if(null!=t.MultiMedias.MultiMedia&&Array.isArray(t.MultiMedias.MultiMedia))for(e=0;e<t.MultiMedias.MultiMedia.length;e++){i=t.MultiMedias.MultiMedia[e];var o=new W(this.docs);o.ParseFromXml(i),this.multiMedias.push(o)}else if(null!=t.MultiMedias){var l=new W(this.docs);l.ParseFromXml(t.MultiMedias.MultiMedia),this.multiMedias.push(l)}}}}]),t}(),H=function(){function t(e){m(this,t),this.docs=e,this.maxUnitID="",this.pageArea=new x,this.publicRes="",this.pubres=[],this.documentRes="",this.docres=[],this.templatePage=[],this.defaultCS=""}return p(t,[{key:"GetTemplatePageByID",value:function(t){for(var e=0;e<this.templatePage.length;e++){var i=this.templatePage[e];if(i.id==t)return i}return null}},{key:"GetPublicRes",value:function(){return this.pubres.length>0?this.pubres:null}},{key:"GetMultiMediasByID",value:function(t){if(this.pubres.length>0)for(var e=0;e<this.pubres.length;e++){var i=this.pubres[e].GetMultiMediasByID(t);if(null!=i)return i}if(this.docres.length>0)for(e=0;e<this.docres.length;e++){var s=this.docres[e].GetMultiMediasByID(t);if(null!=s)return s}return null}},{key:"GetDrawParamByID",value:function(t){if(this.pubres.length>0)for(var e=0;e<this.pubres.length;e++){var i=this.pubres[e].GetDrawParamByID(t);if(null!=i)return i}if(this.docres.length>0)for(e=0;e<this.docres.length;e++){var s=this.docres[e].GetDrawParamByID(t);if(null!=s)return s}return null}},{key:"GetCompositeGraphicUnitByID",value:function(t){if(this.pubres.length>0)for(var e=0;e<this.pubres.length;e++){var i=this.pubres[e].GetCompositeGraphicUnitByID(t);if(null!=i)return i}if(this.docres.length>0)for(e=0;e<this.docres.length;e++){var s=this.docres[e].GetCompositeGraphicUnitByID(t);if(null!=s)return s}return null}},{key:"GetFontByID",value:function(t){for(var e=0;e<this.templatePage.length;e++){var i=this.templatePage[e];if(i.id==t)return i}return null}},{key:"ParseFromXml",value:function(t){if(null!=t.MaxUnitID&&(this.maxUnitID=t.MaxUnitID.toString()),null!=t.DefaultCS&&(this.defaultCS=t.DefaultCS.toString()),null!=t.PageArea&&this.pageArea.ParseFromXml(t.PageArea),null!=t.TemplatePage&&Array.isArray(t.TemplatePage))for(var e=0;e<t.TemplatePage.length;e++){var i=t.TemplatePage[e],s=new L(this.docs,this);s.ParseFromXml(i),this.templatePage.push(s)}else if(null!=t.TemplatePage){var n=new L(this.docs,this);n.ParseFromXml(t.TemplatePage),this.templatePage.push(n)}if(null!=t.PublicRes&&Array.isArray(t.PublicRes)){this.publicRes=t.PublicRes;for(e=0;e<t.PublicRes.length;e++){i=t.PublicRes[e];var a=new Z(this.docs,this);a.ParseFromXml(i),this.pubres.push(a)}}else if(null!=t.PublicRes){this.publicRes=t.PublicRes.toString();var r=new Z(this.docs,this);r.ParseFromXml(t.PublicRes),this.pubres.push(r)}if(null!=t.DocumentRes&&Array.isArray(t.DocumentRes)){this.documentRes=t.DocumentRes;for(e=0;e<t.DocumentRes.length;e++){i=t.DocumentRes[e];var o=new Z(this.doc,this);o.ParseFromXml(i),this.docres.push(o)}}else if(null!=t.DocumentRes){this.documentRes=t.DocumentRes.toString();var l=new Z(this.docs,this);l.ParseFromXml(t.DocumentRes),this.docres.push(l)}}}]),t}(),J=function(){function t(e,i){m(this,t),this.docs=e,this.commonData=i,this.page=[]}return p(t,[{key:"ParseFromXml",value:function(t){if(null!=t.Page&&Array.isArray(t.Page))for(var e=0;e<t.Page.length;e++){var i=t.Page[e],s=new z(this.docs,this.commonData);s.ParseFromXml(i),this.page.push(s)}else if(null!=t.Page){var n=new z(this.docs,this.commonData);n.ParseFromXml(t.Page),this.page.push(n)}}}]),t}(),$=function(t){function e(t){var i;return m(this,e),(i=u(this,e,[t])).boundary={x:0,y:0,h:0,w:0},i}return v(e,t),p(e,[{key:"ParseFromXml",value:function(t){null!=t&&(null!=t._Boundary&&(this.boundary=this.Parse(t._Boundary),w(y(e.prototype),"setBoundary",this).call(this,this.boundary)),w(y(e.prototype),"ParseFromXml",this).call(this,t))}},{key:"Parse",value:function(t){try{var e=t.split(" ");return{x:parseFloat(e[0]),y:parseFloat(e[1]),w:parseFloat(e[2]),h:parseFloat(e[3])}}catch(t){return{x:0,y:0,w:0,h:0}}}},{key:"Draw",value:function(t){w(y(e.prototype),"Draw",this).call(this,t)}}]),e}(R),Q=function(){function t(e){m(this,t),this.appearance=new $(e)}return p(t,[{key:"ParseFromXml",value:function(t){null!=t&&null!=t.Appearance&&this.appearance.ParseFromXml(t.Appearance)}},{key:"Draw",value:function(t){this.appearance.Draw(t)}}]),t}(),q=function(){function t(e,i){m(this,t),this.commonData=e,this.page=i,this.annots=[]}return p(t,[{key:"ParseFromXml",value:function(t){if(null!=t&&null!=t.PageAnnot&&null!=t.PageAnnot.Annot){var e=t.PageAnnot.Annot;if(Array.isArray(e))for(var i=0;i<e.length;i++){var s=e[i],n=new Q(this.commonData);n.ParseFromXml(s),this.annots.push(n)}else{var a=new Q(this.commonData);a.ParseFromXml(e),this.annots.push(a)}}}},{key:"Draw",value:function(t){for(var e=0;e<this.annots.length;e++){this.annots[e].Draw(t)}}}]),t}(),K=function(){function t(e,i){m(this,t),null!=e&&(this.docs=e),this.commonData=i,this.Pageannots=[]}return p(t,[{key:"ParseFromXml",value:function(t){if(this.Pageannots=[],null!=this.docs){var e,i=function(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=b(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var s=0,n=function(){};return{s:n,n:function(){return s>=t.length?{done:!0}:{done:!1,value:t[s++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,o=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return r=t.done,t},e:function(t){o=!0,a=t},f:function(){try{r||null==i.return||i.return()}finally{if(o)throw a}}}}(this.docs.entries());try{for(i.s();!(e=i.n()).done;){var s=D(e.value,2),n=s[0],a=s[1],r=n.toString();if(r.indexOf("Annots/Page_")>=0){var o=r.lastIndexOf("/")-12;if(o<=0)return;var l=r.substr(12,o),h=new q(this.commonData,parseInt(l));h.ParseFromXml(a),this.Pageannots.push(h)}}}catch(t){i.e(t)}finally{i.f()}}}},{key:"Draw",value:function(t,e){for(var i=0;i<this.Pageannots.length;i++){var s=this.Pageannots[i];s.page==e&&s.Draw(t)}}}]),t}(),tt=function(){function t(e){m(this,t),this.docs=e.docs[0],this.commonData=new H(this.docs),this.pages=new J(this.docs,this.commonData),this.annots=new K(this.docs,this.commonData),this.bookmarks=[],this.annotations="Annots/Annotations.xml",this.attachments="Attachs/Attachments.xml",this.rootPath=""}return p(t,[{key:"ParseFromXml",value:function(t){if(this.commonData=new H(this.docs),this.pages=new J(this.docs,this.commonData),this.annots=new K(this.docs,this.commonData),this.bookmarks=[],this.annots.Pageannots=[],null!=this.docs){var e=this.docs.get("Document.xml").Document;null!=e.CommonData&&this.commonData.ParseFromXml(e.CommonData),null!=e.Pages&&this.pages.ParseFromXml(e.Pages),this.annots.ParseFromXml(e)}}}]),t}(),et=function(){function t(){m(this,t),this.providerName="",this.company="",this.version=""}return p(t,[{key:"ParseFromXml",value:function(t){null!=t._ProviderName&&(this.providerName=t._ProviderName.toString()),null!=t._Company&&(this.company=t._Company.toString()),null!=t._Version&&(this.version=t._Version.toString())}}]),t}(),it=function(){function t(){m(this,t),this.fileRef="",this.checkValue=""}return p(t,[{key:"ParseFromXml",value:function(t){null!=t._FileRef&&(this.fileRef=t._FileRef.toString()),null!=t.CheckValue&&(this.checkValue=t.CheckValue.toString())}}]),t}(),st=function(){function t(e){m(this,t),this.docs=e,this.checkMethod="MD5",this.reference=[]}return p(t,[{key:"ParseFromXml",value:function(t){if(null!=t._CheckMethod&&(this.checkMethod=t._CheckMethod.toString()),null!=t.Reference)if(null!=t.Reference&&Array.isArray(t.Reference))for(var e=0;e<t.Reference.length;e++){var i=t.Reference[e],s=new it(this.docs);s.ParseFromXml(i),this.reference.push(s)}else if(null!=t.StampAnnot){var n=new it(this.docs);n.ParseFromXml(t.StampAnnot),this.reference.push(n)}}}]),t}(),nt=function(){function t(){m(this,t),this.pageRef="",this.id="",this.boundary=null}return p(t,[{key:"ParseFromXml",value:function(t){null!=t._PageRef&&(this.pageRef=t._PageRef.toString()),null!=t._ID&&(this.id=t._ID.toString()),null!=t._Boundary&&(this.boundary=this.Parse(t._Boundary))}},{key:"Parse",value:function(t){try{var e=t.split(" ");return{x:parseFloat(e[0]),y:parseFloat(e[1]),w:parseFloat(e[2]),h:parseFloat(e[3])}}catch(t){return{x:0,y:0,w:0,h:0}}}}]),t}(),at=function(){function t(){m(this,t),this.ocxNumber=0,this.hexNumber=0,this.offset=0,this.lenth=0,this.value=null}return p(t,[{key:"Parse",value:function(t){var e=t[0];if(this.offset++,e!=this.ocxNumber)throw new Error("非法的"+this.elementType+"对应");var i=t[1];this.offset++;var s=0;if(i<128)s=i;else{var n=i-128;s=this.BitToInt(t.slice(this.offset,this.offset+n)),this.offset=this.offset+n}this.lenth=s}},{key:"value",value:function(){return this.value}},{key:"BitToInt",value:function(t){return 1==t.length?t[0]:2==t.length?t[0]<<8|t[1]:3==t.length?t[0]<<16|t[1]<<8|t[2]:4==t.length?t[0]<<32|t[1]<<16|t[2]<<8|t[3]:5==t.length?t[0]<<64|t[2]<<32|t[3]<<16|t[4]<<8|t[5]:void 0}}]),t}(),rt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="IA5String",t.ocxNumber=22,t.hexNumber=22,t.value="",t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t);var i=t.slice(this.offset,this.offset+this.lenth);this.value=String.fromCharCode.apply(null,i)}}]),e}(at),ot=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="INTEGER",t.ocxNumber=2,t.hexNumber=2,t.value="",t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.value=this.BitToInt(t.slice(this.offset,this.offset+this.lenth))}}]),e}(at),lt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="SEQUENCE",t.ocxNumber=48,t.hexNumber=48,t.ID=new rt("ES"),t.version=new ot(4),t.Vid=new rt,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.ID.Parse(t.slice(this.offset));var i=this.offset+this.ID.offset+this.ID.lenth;this.version.Parse(t.slice(i)),i=this.offset+this.version.offset+this.version.lenth,this.Vid.Parse(t.slice(i))}}]),e}(at),ht=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="SEQUENCE",t.ocxNumber=48,t.hexNumber=48,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t)}}]),e}(at),ut=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="OCTET STRING",t.ocxNumber=4,t.hexNumber=4,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.value=t.slice(this.offset,this.offset+this.lenth)}}]),e}(at),ct=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="SEQUENCE",t.ocxNumber=48,t.hexNumber=48,t.type=new rt,t.data=new ut,t.width=new ot,t.height=new ot,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.type.Parse(t.slice(this.offset));var i=this.offset+this.type.offset+this.type.lenth;this.data.Parse(t.slice(i)),i=i+this.data.offset+this.data.lenth,this.width.Parse(t.slice(i)),i=i+this.width.offset+this.width.lenth,this.height.Parse(t.slice(i))}}]),e}(at),ft=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="SEQUENCE",t.ocxNumber=48,t.hexNumber=48,t.header=new lt,t.esID=new rt,t.property=new ht,t.picture=new ct,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.header.Parse(t.slice(this.offset));var i=this.offset+this.header.offset+this.header.lenth;this.esID.Parse(t.slice(i)),i=i+this.esID.offset+this.esID.lenth,this.property.Parse(t.slice(i)),i=i+this.property.offset+this.property.lenth,this.picture.Parse(t.slice(i))}}]),e}(at),mt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="SEQUENCE",t.ocxNumber=48,t.hexNumber=48,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t)}}]),e}(at),dt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).version="2014",t.ocxNumber=48,t.hexNumber=48,t.eSealInof=new ft,t.signInfo=new mt,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.eSealInof.Parse(t.slice(this.offset));var i=this.offset+this.eSealInof.offset+this.eSealInof.lenth;this.signInfo.Parse(t.slice(i))}}]),e}(at),pt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="SEQUENCE",t.ocxNumber=48,t.hexNumber=48,t.ID=new rt("ES"),t.version=new ot(4),t.Vid=new rt,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.ID.Parse(t.slice(this.offset));var i=this.offset+this.ID.offset+this.ID.lenth;this.version.Parse(t.slice(i)),i=i+this.version.offset+this.version.lenth,this.Vid.Parse(t.slice(i))}}]),e}(at),gt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="GeneralizedTime",t.ocxNumber=24,t.hexNumber=24,t}return v(e,t),p(e,[{key:"Parse",value:function(t){}}]),e}(at),vt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="SEQUENCE",t.ocxNumber=48,t.hexNumber=48,t.type=new ot,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t)}}]),e}(at),yt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="SEQUENCE",t.ocxNumber=48,t.hexNumber=48,t.type=new rt,t.data=new ut,t.width=new ot,t.height=new ot,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.type.Parse(t.slice(this.offset));var i=this.offset+this.type.offset+this.type.lenth;this.data.Parse(t.slice(i)),i=i+this.data.offset+this.data.lenth,this.width.Parse(t.slice(i)),i=i+this.width.offset+this.width.lenth,this.height.Parse(t.slice(i))}}]),e}(at),Pt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="SEQUENCE",t.ocxNumber=48,t.hexNumber=48,t.header=new pt,t.esID=new rt,t.property=new vt,t.picture=new yt,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.header.Parse(t.slice(this.offset));var i=this.offset+this.header.offset+this.header.lenth;this.esID.Parse(t.slice(i)),i=i+this.esID.offset+this.esID.lenth,this.property.Parse(t.slice(i)),i=i+this.property.offset+this.property.lenth,this.picture.Parse(t.slice(i))}}]),e}(at),wt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="IDENTIFIER",t.ocxNumber=6,t.hexNumber=6,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t)}}]),e}(at),Dt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).elementType="BITSTRING",t.ocxNumber=3,t.hexNumber=3,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t)}}]),e}(at),bt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).version="2020",t.ocxNumber=48,t.hexNumber=48,t.eSealInof=new Pt,t.cert=new ut,t.signAlgID=new wt,t.signValue=new Dt,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.eSealInof.Parse(t.slice(this.offset));var i=this.offset+this.eSealInof.offset+this.eSealInof.lenth;this.cert.Parse(t.slice(i)),i=i+this.cert.offset+this.cert.lenth,this.signAlgID.Parse(t.slice(i)),i=i+this.signAlgID.offset+this.signAlgID.lenth,this.signValue.Parse(t.slice(i))}}]),e}(at),Ft=function(){function t(){m(this,t),this.seal=null,this.version="2014"}return p(t,[{key:"Parse",value:function(t){this.seal=null;try{this.seal=new dt,this.seal.Parse(t)}catch(e){try{this.version="2020",this.seal=new bt,this.seal.Parse(t)}catch(t){}}}},{key:"GetDrawData",value:function(){var t=this.seal.eSealInof.picture.data.value,e=this.seal.eSealInof.picture.type.value;return new Promise((function(i,s){"ofd"==e&&(e="png");var n=new Blob([t],{type:"image/"+e}),a=URL.createObjectURL(n),r=document.createElement("img");r.src=a,r.onload=function(){i(r)},r.onerror=function(){s(new Error("Failed to load image"))}}))}}]),t}(),St=function(){function t(e){m(this,t),this.singedInfo=e}return p(t,[{key:"Draw",value:function(t,e){try{var i=this.readImage(),s=new H(null);i.then((function(e){t.save(),s.pubres.push(e.res);for(var i=e.singedInfo.stampAnnot[0].boundary,n=e.page.content[0].layers[0],a=0;a<n.pathObject.length;a++){var r=n.pathObject[a];r.boundary.x=r.boundary.x+i.x,r.boundary.y=r.boundary.y+i.y,r.Draw(t)}for(a=0;a<n.textObject.length;a++){var o=n.textObject[a];o.boundary.x=o.boundary.x+i.x,o.boundary.y=o.boundary.y+i.y,o.commonData=s,o.Draw(t)}t.restore()}))}catch(t){throw new Error("ofd 绘制签章内容出错")}}},{key:"readImage",value:function(){var t=this,e=this;return new Promise((function(i,s){var n=t.singedInfo.signature.signature.signature.toSign.eseal.eSealInof.picture.data.value,a=new Blob([n]),l=new r.default,h=new o.default({stripWhitespaces:!1,skipEmptyTextNodesForObj:!1});l.loadAsync(a).then((function(t){var s=Object.keys(t.files),n=[];s.forEach((function(i){var s=null;(i.includes("Res")||i.includes("Page"))&&(s=t.file(i).async("string"),n.push(s)),null!=s&&s.then((function(t){if(i.includes("Res")){var s=h.xml2js(t),n=new H(null);e.res=new Z("1",n),e.res.ParseFromXmlOFD(s.Res)}else if(i.includes("Page")){var a=h.xml2js(t);e.page=new z,e.page.ParseFromXml(a.Page)}}))})),Promise.all(n).then((function(){i(e)}))}))}))}}]),t}(),xt=function(){function t(e,i){m(this,t),this.docs=e,this.signature=i,this.provider=new et(e),this.ofdImage=new St(this),this.signatureMethod="",this.signatureDateTime="",this.references=new st(e),this.stampAnnot=[],this.sealLoc=null}return p(t,[{key:"ParseFromXml",value:function(t,e){if(null!=t.Provider&&this.provider.ParseFromXml(t.Provider),null!=t.SignatureMethod&&(this.signatureMethod=t.SignatureMethod.toString()),null!=t.SignatureDateTime&&(this.signatureDateTime=t.SignatureDateTime.toString()),null!=t.References&&this.references.ParseFromXml(t.References),null!=t.StampAnnot)if(null!=t.StampAnnot&&Array.isArray(t.StampAnnot))for(var i=0;i<t.StampAnnot.length;i++){var s=t.StampAnnot[i],n=new nt(this.docs);n.ParseFromXml(s),this.stampAnnot.push(n)}else if(null!=t.StampAnnot){var a=new nt(this.docs);a.ParseFromXml(t.StampAnnot),this.stampAnnot.push(a)}null!=t.Seal&&(this.sealLoc=t.Seal.BaseLoc.toString(),this.sealdate=this.docs.get("Signs/"+e.replace("Signature.xml",this.sealLoc)),this.seal=new Ft,this.seal.Parse(this.sealdate))}},{key:"Draw",value:function(t,e){try{var i=this.GetPageBox(e);if(null==i)return;var s=this.ToPX(i.x),n=this.ToPX(i.y),a=this.ToPX(i.w),r=this.ToPX(i.h);if(null!=this.signature.signature)if("ofd"==this.signature.signature.signature.toSign.eseal.eSealInof.picture.type.value)this.ofdImage.Draw(t,e);else this.signature.signature.GetDrawData().then((function(e){null!=e&&(t.save(),t.globalAlpha=1,t.drawImage(e,s,n,a,r),t.restore())}));else if(null!=this.seal){this.seal.GetDrawData().then((function(e){t.save(),t.globalAlpha=1,t.drawImage(e,s,n,a,r),t.restore()}))}}catch(t){}}},{key:"GetPageBox",value:function(t){for(var e=0;e<this.stampAnnot.length;e++){var i=this.stampAnnot[e];if(i.pageRef==t)return i.boundary}if(this.stampAnnot.length>0)return this.stampAnnot[0].boundary}},{key:"ToPX",value:function(t){return parseFloat(parseFloat(t)/25.4*96)}}]),t}(),kt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).ocxNumber=48,t.hexNumber=48,t.version=new ot,t.eseal=new dt,t.timeInfo=new Dt,t.dadaHash=new Dt,t.propertyInfo=new rt,t.cert=new ut,t.signatureAlgorthm=new wt,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.version.Parse(t.slice(this.offset));var i=this.offset+this.version.offset+this.version.lenth;this.eseal.Parse(t.slice(i)),i=this.offset+this.eseal.offset+this.eseal.lenth,this.timeInfo.Parse(t.slice(i)),i=this.offset+this.timeInfo.offset+this.timeInfo.lenth,this.dadaHash.Parse(t.slice(i)),i=this.offset+this.dadaHash.offset+this.dadaHash.lenth,this.propertyInfo.Parse(t.slice(i)),i=this.offset+this.propertyInfo.offset+this.propertyInfo.lenth,this.cert.Parse(t.slice(i)),i=this.offset+this.cert.offset+this.cert.lenth,this.signatureAlgorthm.Parse(t.slice(i))}},{key:"GetDrawData",value:function(){}}]),e}(at),It=function(t){function e(){var t;return m(this,e),(t=u(this,e)).version="2014",t.ocxNumber=48,t.hexNumber=48,t.toSign=new kt,t.Signature=new Dt,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.toSign.Parse(t.slice(this.offset));var i=this.offset+this.toSign.offset+this.toSign.lenth;this.Signature.Parse(t.slice(i))}},{key:"GetDrawData",value:function(){}}]),e}(at),Ct=function(t){function e(){var t;return m(this,e),(t=u(this,e)).ocxNumber=48,t.hexNumber=48,t.version=new ot,t.eseal=new bt,t.timeInfo=new gt,t.dadaHash=new Dt,t.propertyInfo=new rt,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.version.Parse(t.slice(this.offset));var i=this.offset+this.version.offset+this.version.lenth;this.eseal.Parse(t.slice(i)),i=this.offset+this.eseal.offset+this.eseal.lenth,this.timeInfo.Parse(t.slice(i)),i=this.offset+this.timeInfo.offset+this.timeInfo.lenth,this.dadaHash.Parse(t.slice(i)),i=this.offset+this.dadaHash.offset+this.dadaHash.lenth,this.propertyInfo.Parse(t.slice(i))}},{key:"GetDrawData",value:function(){}}]),e}(at),Bt=function(t){function e(){var t;return m(this,e),(t=u(this,e)).version="2020",t.ocxNumber=48,t.hexNumber=48,t.toSign=new Ct,t.cert=new ut,t.signatureAlID=new wt,t.signature=new Dt,t}return v(e,t),p(e,[{key:"Parse",value:function(t){w(y(e.prototype),"Parse",this).call(this,t),this.toSign.Parse(t.slice(this.offset));var i=this.offset+this.toSign.offset+this.toSign.lenth;this.Signature.Parse(t.slice(i)),i=this.offset+this.Signature.offset+this.Signature.lenth,this.cert.Parse(t.slice(i)),i=this.offset+this.cert.offset+this.cert.lenth,this.signatureAlID.Parse(t.slice(i)),i=this.offset+this.signatureAlID.offset+this.signatureAlID.lenth,this.signature.Parse(t.slice(i))}},{key:"GetDrawData",value:function(){}}]),e}(at),_t=function(){function t(){m(this,t),this.version="2014"}return p(t,[{key:"Parse",value:function(t){this.signature=null;try{this.signature=new It,this.signature.Parse(t)}catch(e){try{this.version="2020",this.signature=new Bt,this.signature.Parse(t)}catch(t){}}}},{key:"GetDrawData",value:function(){var t=this.signature.toSign.eseal.eSealInof.picture.data.value,e=this.signature.toSign.eseal.eSealInof.picture.type.value;return new Promise(null==t?function(t,e){t(null)}:function(i,s){var n=new Blob([t],{type:"image/"+e}),a=URL.createObjectURL(n),r=document.createElement("img");r.src=a,r.onload=function(){i(r)},r.onerror=function(t){s(new Error("Failed to load image"))}})}}]),t}(),Xt=function(){function t(e){m(this,t),this.docs=e,this.id="",this.type="",this.baseLoc="",this.singedInfo=new xt(this.docs,this),this.signedValue=""}return p(t,[{key:"ParseFromXml",value:function(t){if(null!=t._ID&&(this.id=t._ID.toString()),null!=t._Type&&(this.type=t._Type.toString()),null!=t._BaseLoc){this.baseLoc=t._BaseLoc.toString(),this.baseLoc.includes("Doc")&&(this.baseLoc=this.baseLoc.replace("/Doc_0/","")),this.baseLoc.includes("Signs/")||(this.baseLoc="Signs/"+this.baseLoc);var e=this.docs.get(this.baseLoc).Signature;null!=e.SignedValue&&(this.signedValue=e.SignedValue.toString(),this.signedValue.includes("Doc")&&(this.signedValue=this.signedValue.replace("/Doc_0/",""),this.signedValue=this.signedValue.replace("Doc_0/","")),this.signedValue.includes("Signs/")?this.signedate=this.docs.get(this.signedValue):this.signedate=this.docs.get(this.baseLoc.replace("Signature.xml",this.signedValue)),this.signature=new _t,this.signature.Parse(this.signedate)),null!=e.SignedInfo&&this.singedInfo.ParseFromXml(e.SignedInfo,this.baseLoc)}}},{key:"Draw",value:function(t,e){this.singedInfo.Draw(t,e)}}]),t}(),Mt=function(){function t(e){m(this,t),this.docs=e.docs[0],this.maxSignId="",this.signature=[]}return p(t,[{key:"ParseFromXml",value:function(t){var e="";t.concat("Doc")&&(e=t.replace("Doc_0/",""));var i=this.docs.get(e).Signatures;if(null!=i.MaxSignId&&(this.maxSignId=i.MaxSignId.toString()),null!=i.Signature)if(null!=i.Signature&&Array.isArray(i.Signature))for(var s=0;s<i.Signature.length;s++){var n=i.Signature[s],a=new Xt(this.docs);a.ParseFromXml(n),this.signature.push(a)}else if(null!=i.Signature){var r=new Xt(this.docs);r.ParseFromXml(i.Signature),this.signature.push(r)}}},{key:"Draw",value:function(t,e){for(var i=0;i<this.signature.length;i++){this.signature[i].Draw(t,e);break}}}]),t}(),Ot=function(){function t(e){m(this,t),this.docInfo=new S,this.docRoot=new tt(e),this.docRootLoc="",this.signatures=new Mt(e),this.signaturesLoc=""}return p(t,[{key:"ParseFromXml",value:function(t){null!=t.DocInfo&&this.docInfo.ParseFromXml(t.DocInfo),null!=t.DocRoot&&(this.docRootLoc=t.DocRoot.toString().replace("/Doc_","Doc_")),null!=t.Signatures&&(this.signaturesLoc=t.Signatures.toString().replace("/Doc_","Doc_"),this.signatures.ParseFromXml(this.signaturesLoc)),this.docRoot.ParseFromXml(t)}}]),t}(),Tt=function(){function t(){m(this,t),this.version="1.1",this.docType="OFD",this.docBody=[]}return p(t,[{key:"ParseFromXml",value:function(t){if(this.docBody=[],null!=t.ofd.OFD){var e=t.ofd.OFD;if(null!=e.DocBody&&Array.isArray(e.DocBody))for(var i=0;i<e.DocBody.length;i++){var s=e.DocBody[i],n=new Ot(t);n.ParseFromXml(s),this.docBody.push(n)}else if(null!=e.DocBody){var a=new Ot(t);a.ParseFromXml(e.DocBody),this.docBody.push(a)}}}}]),t}(),At=function(){function t(){m(this,t),this.x2js=new o.default({stripWhitespaces:!1,skipEmptyTextNodesForObj:!1}),this.zip=new r.default}return p(t,[{key:"SetBlob",value:function(t){this.blob=t}},{key:"readImage",value:function(t,e){return e.endsWith(".jb2")?new Promise((function(i,s){t.file(e).async("blob").then((function(t){(new h.default).jb2Image(t,"png").then((function(t){i(t)}))}))})):new Promise((function(i,s){t.file(e).async("blob").then((function(t){var n=new Image;e.includes(".bmp")&&(n.alt="BMP Image"),n.onload=function(){i(n)},n.onerror=function(){s(new Error("Failed to load image"))},n.src=URL.createObjectURL(t)}))}))}},{key:"readBlobAsByteArray",value:function(t,e){var i=new FileReader;return new Promise((function(s,n){t.file(e).async("blob").then((function(t){i.onloadend=function(t){if(t.target.readyState===FileReader.DONE){var e=t.target.result,i=new Uint8Array(e);s(i)}},i.onerror=function(t){n(new Error("读取 Blob 错误"))},i.readAsArrayBuffer(t)}))}))}},{key:"loadTTFFont",value:function(t,e){var i=new FileReader,s=e.toString();return s=s.substr(s.lastIndexOf("/")+1,s.lastIndexOf(".")),new Promise((function(n,a){var r=t.file(e).async("blob");window.glyFont=new Map,r.then((function(t){i.onloadend=function(t){if(t.target.readyState===FileReader.DONE){var e=t.target.result,i=l.default.parse(e);i.fontname=s,window.glyFont.set(s,i),n(i)}},i.onerror=function(t){a(new Error("读取 Blob 错误"))},i.readAsArrayBuffer(t)}))}))}},{key:"readFile",value:function(){var t=this,e={docs:[],glyfont:new Map};return this.zip=new r.default,new Promise((function(i,s){try{var n=t,a=new Map;a.clear(),e.docs.push(a),t.zip.loadAsync(t.blob).then((function(s){var r=[],o=s.file("OFD.xml").async("string");r.push(o),o.then((function(t){e.ofd=n.x2js.xml2js(t)}));var l=/^Doc_0\/.*\.xml$/,h=/^Doc_0\/Res\/.*\.(jpg|jpeg|png|gif|bmp|jb2)$/i,u=/^Doc_0\/Res\/.*\.(ttf)$/i;s.folder("Doc_0").forEach((function(i,o){if(l.test(o.name)&&i.indexOf("Tags")<0){var c=s.file(o.name).async("string");r.push(c),c.then((function(t){a.set(i,n.x2js.xml2js(t))}))}if(h.test(o.name)){var f=t.readImage(s,o.name);r.push(f),f.then((function(t){a.set(i,t)}))}if(u.test(o.name)){var m=t.loadTTFFont(s,o.name);r.push(m),m.then((function(t){e.glyfont.set(t.fontname,t)}))}if(i.startsWith("Signs/")&&i.endsWith(".esl")|i.endsWith(".dat")){var d=t.readBlobAsByteArray(s,o.name);r.push(d),d.then((function(t){a.set(i,t)}))}})),Promise.all(r).then((function(){i(e)}))}))}catch(t){s("读取文件错误！--"+t)}}))}}]),t}(),Nt=function(){function t(){var e=this;m(this,t),g(this,"$on",(function(t,i){e.event[t]?-1==e.event[t].indexOf(i)&&e.event[t].push(i):(e.event[t]=[],e.event[t].push(i))})),g(this,"$emit",(function(t){for(var i=arguments.length,s=new Array(i>1?i-1:0),n=1;n<i;n++)s[n-1]=arguments[n];if(e.event[t])try{e.event[t].map((function(t){t.apply(void 0,s)}))}catch(t){}})),g(this,"$off",(function(t,i){if(e.event[t])if(i){var s=e.event[t].indexOf(i);s>-1&&e.event[t].splice(s,1)}else e.event[t].length=0})),this.event={},this.FinshDocRead=this.FinshDocRead.bind(this),this.zip=new r.default,this.readFromZip=new At}return p(t,[{key:"read",value:function(t){this.blob=t,this.readFromZip.SetBlob(t),this.readOfdXml()}},{key:"readOfdXml",value:function(){try{var t=this.readFromZip.readFile();this.ofd=new Tt;var e=this;t.then((function(t){e.ofd.ParseFromXml(t),e.FinshDocRead()}))}catch(t){}}},{key:"FinshDocRead",value:function(){this.$emit("DocumentChange",this)}}]),t}(),Rt=function(){function t(e){m(this,t),this.easyOFD=e,this.doc=e.doc,this.pageNow=1}return p(t,[{key:"Draw",value:function(t){this.docBody=this.doc.ofd.docBody[0];var e=this.docBody.docRoot.pages.page[this.pageNow-1];if(this.AllPageNo=this.docBody.docRoot.pages.page.length,e.template.length>0)for(var i=0;i<e.template.length;i++){var s=e.template[i].templateID,n=this.docBody.docRoot.commonData.GetTemplatePageByID(s);null!=n&&n.Draw(t)}null!=e&&e.Draw(t);var a=this.docBody.signatures;null!=a&&a.Draw(t,e.id);var r=this.docBody.docRoot.annots;null!=r&&r.Draw(t,this.pageNow-1)}},{key:"SetPage",value:function(t){this.pageNow=t}},{key:"GetAllPageNo",value:function(){AllPageNo}},{key:"GetPagePhysicalBox",value:function(){this.docBody=this.doc.ofd.docBody[0];var t=this.docBody.docRoot.pages.page[this.pageNow-1];if(null!=t&null!=t.area&null!=t.area.physicalBox)return t.area.physicalBox;if(t.template.length>0){var e=this.docBody.docRoot.commonData.GetTemplatePageByID(t.template[0].templateID);if(null!=e.page.area.physicalBox)return e.page.area.physicalBox}return this.docBody.docRoot.commonData.pageArea.physicalBox}}]),t}(),jt=function(){function t(e,i){var s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1e3,r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:550;m(this,t),this.id=e,this.x=s,this.y=n,this.width=a,this.height=r,this.parent=i,this.zoomSize=1,this.firstReadFile=!1,this.WrapperCanvas(e,a,r),this.doc=new Nt,this.view=new Rt(this),this.SelectFile.bind(this),this.OpenFile.bind(this),this.doc.$on("DocumentChange",this.DocumentChange.bind(this)),this.canvasSelectFile.addEventListener("change",this.SelectFile.bind(this))}return p(t,[{key:"WrapperCanvas",value:function(){this.rootdiv=document.createElement("div"),this.rootdiv.tabindex="0",this.rootdiv.id=this.id,this.parent.appendChild(this.rootdiv),this.wrapperToolsBar(),this.wrapperCanvas()}},{key:"wrapperCanvas",value:function(){if(this.firstReadFile){var t=this.width/(document.documentElement.clientWidth-150);t>1.3&&(this.zoomSize=(1/t).toFixed(2))}this.canvasdiv=document.createElement("div");var e=document.documentElement.clientWidth-150;this.canvasdiv.setAttribute("style","display: flex;justify-content: center ;padding: 0px 0 0px 0;background-color: gray;max-height:1000px;overflow:auto;max-width:"+e+"px;"),this.rootdiv.appendChild(this.canvasdiv),this.canvas=document.createElement("canvas"),this.canvasdiv.appendChild(this.canvas),this.canvas.id=this.id+"-ofd-canvas",this.ctx=this.canvas.getContext("2d");var i=window.devicePixelRatio,s=this.ctx.webkitBackingStorePixelRatio||this.ctx.mozBackingStorePixelRatio||this.ctx.msBackingStorePixelRatio||this.ctx.oBackingStorePixelRatio||this.ctx.backingStorePixelRatio||1;this.ratio=i/s,this.canvas.width=(parseInt(this.width)*this.zoomSize||document.documentElement.clientWidth)*this.ratio,this.canvas.height=(parseInt(this.height)*this.zoomSize||document.documentElement.clientHeight)*this.ratio,this.canvas.style.width=(parseInt(this.width)*this.zoomSize||document.documentElement.clientWidth)+"px",this.canvas.style.height=(parseInt(this.height)*this.zoomSize||document.documentElement.clientHeight)+"px",this.canvas.style.backgroundColor="white",this.ctx.scale(this.ratio*this.zoomSize,this.ratio*this.zoomSize),this.firstReadFile=!1,this.WrapperPPI(this.id),this.WrapperSelectFile(this.id,this.canvasdiv)}},{key:"wrapperToolsBar",value:function(){this.toolBar=document.createElement("div"),this.toolBar.setAttribute("style","align-items: center;display: flex;justify-content: center ;padding: 10px 0 10px 0;"),this.rootdiv.appendChild(this.toolBar),this.selectButton=document.createElement("div"),this.selectButton.id=this.id+"selectButton",this.selectButton.innerText="打开<",this.selectButton.className="OfdButton OpenFile",this.selectButton.style="display:none",this.selectButton.addEventListener("click",this.OpenFile.bind(this)),this.toolBar.appendChild(this.selectButton),this.firstPage=document.createElement("div"),this.firstPage.id=this.id+"firstPage",this.firstPage.innerText="上一页<",this.firstPage.className="OfdButton firstPage",this.firstPage.addEventListener("click",this.FirstPage.bind(this)),this.toolBar.appendChild(this.firstPage),this.prePage=document.createElement("div"),this.prePage.id=this.id+"prePage",this.prePage.innerText=" ",this.prePage.className="OfdButton prePage",this.prePage.addEventListener("click",this.PrePage.bind(this)),this.toolBar.appendChild(this.prePage),this.nowPage=document.createElement("div"),this.nowPage.id=this.id+"nowPage",this.nowPage.innerText="1/1",this.nowPage.className="OfdButton nowPage",this.toolBar.appendChild(this.nowPage),this.nextPage=document.createElement("div"),this.nextPage.id=this.id+"nextPage",this.nextPage.innerText=">",this.nextPage.className="OfdButton nextPage",this.nextPage.addEventListener("click",this.NextPage.bind(this)),this.toolBar.appendChild(this.nextPage),this.lastPage=document.createElement("div"),this.lastPage.id=this.id+"lastPage",this.lastPage.innerText="下一页",this.lastPage.className="OfdButton lastPage",this.lastPage.addEventListener("click",this.LastPage.bind(this)),this.toolBar.appendChild(this.lastPage),this.zoomIn=document.createElement("div"),this.zoomIn.id=this.id+"zoomIn",this.zoomIn.innerText="缩小",this.zoomIn.className="OfdButton zoomIn",this.zoomIn.style="display:none",this.zoomIn.addEventListener("click",this.ZoomIn.bind(this)),this.toolBar.appendChild(this.zoomIn),this.zoomValue=document.createElement("div"),this.zoomValue.id=this.id+"zoomValue",this.zoomValue.innerText="100%",this.zoomValue.className="OfdButton zoomValue",this.zoomValue.style="display:none",this.toolBar.appendChild(this.zoomValue),this.zoomOut=document.createElement("div"),this.zoomOut.id=this.id+"zoomOut",this.zoomOut.innerText="放大",this.zoomOut.className="OfdButton zoomOut",this.zoomOut.style="display:none",this.zoomOut.addEventListener("click",this.ZoomOut.bind(this)),this.toolBar.appendChild(this.zoomOut)}},{key:"FirstPage",value:function(){1!=this.view.pageNow&&(this.view.pageNow=1,this.DocumentChange())}},{key:"PrePage",value:function(){this.view.pageNow>1&&(this.view.pageNow=this.view.pageNow-1,this.DocumentChange())}},{key:"NextPage",value:function(){this.view.pageNow<this.view.AllPageNo&&(this.view.pageNow=this.view.pageNow+1,this.DocumentChange())}},{key:"LastPage",value:function(){this.view.pageNow!=this.view.AllPageNo&&(this.view.pageNow=this.view.AllPageNo,this.DocumentChange())}},{key:"ZoomIn",value:function(){this.zoomSize=this.zoomSize-.1,this.zoomValue.innerText=parseInt(100*this.zoomSize)+"%",this.scaleCanvas(this.zoomSize)}},{key:"ZoomOut",value:function(){this.zoomSize=this.zoomSize+.1,this.zoomValue.innerText=parseInt(100*this.zoomSize)+"%",this.scaleCanvas(this.zoomSize)}},{key:"scaleCanvas",value:function(t){this.zoomSize=t,this.ctx.save(),this.SetPhyViewZoom(),this.Draw(),this.ctx.restore()}},{key:"OpenFile",value:function(){this.canvasSelectFile.click()}},{key:"SetPhyViewZoom",value:function(){var t=document.getElementById(this.id);t&&t.remove();var e=this.view.GetPagePhysicalBox();this.width=this.ToPX(e.w),this.height=this.ToPX(e.h),this.WrapperCanvas(),t&&this.canvasSelectFile.addEventListener("change",this.SelectFile.bind(this))}},{key:"DocumentChange",value:function(t){this.SetPhyViewZoom(),this.Draw()}},{key:"Draw",value:function(){this.view.Draw(this.ctx),this.nowPage.innerText=this.view.pageNow+"/"+this.view.AllPageNo,this.zoomValue.innerText=parseInt(100*this.zoomSize)+"%"}},{key:"WrapperPPI",value:function(t){var e=document.createElement("div");e.id=t+"-ppi",e.style="width:1in;visible:hidden;padding:0px",this.canvasdiv.appendChild(e),this.ppi=document.getElementById(t+"-ppi").offsetWidth,this.canvas.ppi=this.ppi}},{key:"WrapperSelectFile",value:function(t,e){this.canvasSelectFile=document.createElement("input"),e.appendChild(this.canvasSelectFile),this.canvasSelectFile.id=t+"-ofd-selectfile",this.canvasSelectFile.type="file",this.canvasSelectFile.accept=".ofd",this.canvasSelectFile.style="display:none"}},{key:"SelectFile",value:function(t){try{var e=t.target.files[0];this.filename=e.name,this.firstReadFile=!0,this.doc.read(e)}catch(t){}}},{key:"loadFromBlob",value:function(t){try{this.doc.read(t)}catch(t){}}},{key:"ToPX",value:function(t){return parseFloat(parseFloat(t)/25.4*96)}}]),t}();window.EasyOFD=jt,t.default=jt,Object.defineProperty(t,"__esModule",{value:!0})}));
