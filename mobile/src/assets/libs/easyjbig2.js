/* Copyright 2023  ZhangXinPan 
*  <EMAIL>
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).JB2={})}(this,(function(e){"use strict";function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(a=r.key,i=void 0,"symbol"==typeof(i=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(a,"string"))?i:String(i)),r)}var a,i}function r(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function a(e){return function(e){if(Array.isArray(e))return s(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||i(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){if(e){if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function o(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=i(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,o=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){l=!0,s=e},f:function(){try{o||null==n.return||n.return()}finally{if(l)throw s}}}}var l=function(){function e(){t(this,e)}return r(e,[{key:"jb2Image",value:function(e,t){return new Promise((function(n,r){var a=new FileReader;a.onload=function(e){var a=e.target.result,i=new Uint8Array(a),s=new z,o=s.parse(i),l=document.createElement("canvas");l.width=s.width,l.height=s.height;for(var h=l.getContext("2d"),f=h.getImageData(0,0,l.width,l.height),u=f.data,c=0,d=0;c<u.length;c+=4,d++){255==o[d]?(u[c]=255,u[c+1]=255,u[c+2]=255):(u[c]=0,u[c+1]=0,u[c+2]=0),u[c+3]=255}h.putImageData(f,0,0);var m=l.toDataURL("image/"+t),p=new Image;p.onload=function(){n(p)},p.onerror=function(e){r(new Error("Failed to load image"))},p.src=m},a.readAsArrayBuffer(e)}))}}]),e}();function h(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return Object.defineProperty(e,t,{value:n,enumerable:!r,configurable:!0,writable:!1}),n}function f(e){return e<=0?0:Math.ceil(Math.log2(e))}function u(e,t){return e[t]<<24>>24}function c(e,t){return e[t]<<8|e[t+1]}function d(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}window.JB2=l;var m=function(){function e(){t(this,e)}return r(e,[{key:"getContexts",value:function(e){return e in this?this[e]:this[e]=new Int8Array(65536)}}]),e}(),p=function(){function e(n,r,a){t(this,e),this.data=n,this.start=r,this.end=a}return r(e,[{key:"decoder",get:function(){return h(this,"decoder",new Y(this.data,this.start,this.end))}},{key:"contextCache",get:function(){return h(this,"contextCache",new m)}}]),e}(),g=Math.pow(2,31)-1,v=-Math.pow(2,31);function y(e,t,n){var r=e.getContexts(t),a=1;function i(e){for(var t=0,i=0;i<e;i++){var s=n.readBit(r,a);a=a<256?a<<1|s:511&(a<<1|s)|256,t=t<<1|s}return t>>>0}var s,o=i(1),l=i(1)?i(1)?i(1)?i(1)?i(1)?i(32)+4436:i(12)+340:i(8)+84:i(6)+20:i(4)+4:i(2);return 0===o?s=l:l>0&&(s=-l),s>=v&&s<=g?s:null}function w(e,t,n){for(var r=e.getContexts("IAID"),a=1,i=0;i<n;i++){a=a<<1|t.readBit(r,a)}return n<31?a&(1<<n)-1:2147483647&a}var b=["SymbolDictionary",null,null,null,"IntermediateTextRegion",null,"ImmediateTextRegion","ImmediateLosslessTextRegion",null,null,null,null,null,null,null,null,"PatternDictionary",null,null,null,"IntermediateHalftoneRegion",null,"ImmediateHalftoneRegion","ImmediateLosslessHalftoneRegion",null,null,null,null,null,null,null,null,null,null,null,null,"IntermediateGenericRegion",null,"ImmediateGenericRegion","ImmediateLosslessGenericRegion","IntermediateGenericRefinementRegion",null,"ImmediateGenericRefinementRegion","ImmediateLosslessGenericRefinementRegion",null,null,null,null,"PageInformation","EndOfPage","EndOfStripe","EndOfFile","Profiles","Tables",null,null,null,null,null,null,null,null,"Extension"],x=[[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:2,y:-1},{x:-4,y:0},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}],[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:2,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:2,y:-1},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}],[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-2,y:0},{x:-1,y:0}],[{x:-3,y:-1},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-4,y:0},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}]],I=[{coding:[{x:0,y:-1},{x:1,y:-1},{x:-1,y:0}],reference:[{x:0,y:-1},{x:1,y:-1},{x:-1,y:0},{x:0,y:0},{x:1,y:0},{x:-1,y:1},{x:0,y:1},{x:1,y:1}]},{coding:[{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-1,y:0}],reference:[{x:0,y:-1},{x:-1,y:0},{x:0,y:0},{x:1,y:0},{x:0,y:1},{x:1,y:1}]}],A=[39717,1941,229,405],k=[32,8];function F(e,t,n,r,a,i,s,o){if(e)return W(new N(o.data,o.start,o.end),t,n,!1);if(0===r&&!i&&!a&&4===s.length&&3===s[0].x&&-1===s[0].y&&-3===s[1].x&&-1===s[1].y&&2===s[2].x&&-2===s[2].y&&-2===s[3].x&&-2===s[3].y)return function(e,t,n){var r,a,i,s,o,l,h,f=n.decoder,u=n.contextCache.getContexts("GB"),c=[];for(a=0;a<t;a++)for(o=c[a]=new Uint8Array(e),l=a<1?o:c[a-1],r=(h=a<2?o:c[a-2])[0]<<13|h[1]<<12|h[2]<<11|l[0]<<7|l[1]<<6|l[2]<<5|l[3]<<4,i=0;i<e;i++)o[i]=s=f.readBit(u,r),r=(31735&r)<<1|(i+3<e?h[i+3]<<11:0)|(i+4<e?l[i+4]<<4:0)|s;return c}(t,n,o);var l=!!i,h=x[r].concat(s);h.sort((function(e,t){return e.y-t.y||e.x-t.x}));var f,u,c=h.length,d=new Int8Array(c),m=new Int8Array(c),p=[],g=0,v=0,y=0,w=0;for(u=0;u<c;u++)d[u]=h[u].x,m[u]=h[u].y,v=Math.min(v,h[u].x),y=Math.max(y,h[u].x),w=Math.min(w,h[u].y),u<c-1&&h[u].y===h[u+1].y&&h[u].x===h[u+1].x-1?g|=1<<c-1-u:p.push(u);var b=p.length,I=new Int8Array(b),k=new Int8Array(b),F=new Uint16Array(b);for(f=0;f<b;f++)u=p[f],I[f]=h[u].x,k[f]=h[u].y,F[f]=1<<c-1-u;for(var S,D,q,B,R,O=-v,T=-w,C=t-y,L=A[r],P=new Uint8Array(t),E=[],U=o.decoder,H=o.contextCache.getContexts("GB"),j=0,G=0,M=0;M<n;M++){if(a)if(j^=U.readBit(H,L)){E.push(P);continue}for(P=new Uint8Array(P),E.push(P),S=0;S<t;S++)if(l&&i[M][S])P[S]=0;else{if(S>=O&&S<C&&M>=T)for(G=G<<1&g,u=0;u<b;u++)D=M+k[u],q=S+I[u],(B=E[D][q])&&(G|=B=F[u]);else for(G=0,R=c-1,u=0;u<c;u++,R--)(q=S+d[u])>=0&&q<t&&(D=M+m[u])>=0&&(B=E[D][q])&&(G|=B<<R);var z=U.readBit(H,G);P[S]=z}}return E}function S(e,t,n,r,a,i,s,o,l){var h=I[n].coding;0===n&&(h=h.concat([o[0]]));var f,u=h.length,c=new Int32Array(u),d=new Int32Array(u);for(f=0;f<u;f++)c[f]=h[f].x,d[f]=h[f].y;var m=I[n].reference;0===n&&(m=m.concat([o[1]]));var p=m.length,g=new Int32Array(p),v=new Int32Array(p);for(f=0;f<p;f++)g[f]=m[f].x,v[f]=m[f].y;for(var y=r[0].length,w=r.length,b=k[n],x=[],A=l.decoder,F=l.contextCache.getContexts("GR"),S=0;S<t;S++){if(s)A.readBit(F,b);var D=new Uint8Array(e);x.push(D);for(var q=0;q<e;q++){var B=void 0,R=void 0,O=0;for(f=0;f<u;f++)B=S+d[f],R=q+c[f],B<0||R<0||R>=e?O<<=1:O=O<<1|x[B][R];for(f=0;f<p;f++)B=S+v[f]-i,R=q+g[f]-a,B<0||B>=w||R<0||R>=y?O<<=1:O=O<<1|r[B][R];var T=A.readBit(F,O);D[q]=T}}return x}function D(e,t,n,r,a,i,s,o,l,h,f,u,c,d,m,p,g,v,b){var x,I,A=[];for(x=0;x<r;x++){if(I=new Uint8Array(n),a)for(var k=0;k<n;k++)I[k]=a;A.push(I)}var F=g.decoder,D=g.contextCache,q=e?-d.tableDeltaT.decode(b):-y(D,"IADT",F),B=0;for(x=0;x<i;){q+=e?d.tableDeltaT.decode(b):y(D,"IADT",F);for(var R=B+=e?d.tableFirstS.decode(b):y(D,"IAFS",F);;){var O=0;s>1&&(O=e?b.readBits(v):y(D,"IAIT",F));var T=s*q+O,C=e?d.symbolIDTable.decode(b):w(D,F,l),L=t&&(e?b.readBit():y(D,"IARI",F)),P=o[C],E=P[0].length,U=P.length;if(L){var H=y(D,"IARDW",F),j=y(D,"IARDH",F);P=S(E+=H,U+=j,m,P,(H>>1)+y(D,"IARDX",F),(j>>1)+y(D,"IARDY",F),!1,p,g)}var N=T-(1&u?0:U-1),G=R-(2&u?E-1:0),M=void 0,W=void 0,z=void 0;if(h){for(M=0;M<U;M++)if(I=A[G+M]){z=P[M];var V=Math.min(n-N,E);switch(c){case 0:for(W=0;W<V;W++)I[N+W]|=z[W];break;case 2:for(W=0;W<V;W++)I[N+W]^=z[W]}}R+=U-1}else{for(W=0;W<U;W++)if(I=A[N+W])switch(z=P[W],c){case 0:for(M=0;M<E;M++)I[G+M]|=z[M];break;case 2:for(M=0;M<E;M++)I[G+M]^=z[M]}R+=E-1}x++;var X=e?d.tableDeltaS.decode(b):y(D,"IADS",F);if(null===X)break;R+=X+f}}return A}function q(e,t){var n={};n.number=d(e,t);var r=e[t+4],a=63&r;b[a],n.type=a,n.typeName=b[a],n.deferredNonRetain=!!(128&r);var i=!!(64&r),s=e[t+5],o=s>>5&7,l=[31&s],h=t+6;if(7===s){o=536870911&d(e,h-1),h+=3;var f=o+7>>3;for(l[0]=e[h++];--f>0;)l.push(e[h++])}n.retainBits=l;var u=4;n.number<=256?u=1:n.number<=65536&&(u=2);var m,p,g=[];for(m=0;m<o;m++){var v=void 0;v=1===u?e[h]:2===u?c(e,h):d(e,h),g.push(v),h+=u}if(n.referredTo=g,i?(n.pageAssociation=d(e,h),h+=4):n.pageAssociation=e[h++],n.length=d(e,h),h+=4,4294967295===n.length&&38===a){var y=R(e,h),w=!!(1&e[h+O]),x=new Uint8Array(6);for(w||(x[0]=255,x[1]=172),x[2]=y.height>>>24&255,x[3]=y.height>>16&255,x[4]=y.height>>8&255,x[5]=255&y.height,m=h,p=e.length;m<p;m++){for(var I=0;I<6&&x[I]===e[m+I];)I++;if(6===I){n.length=m+6;break}}n.length}return n.headerEnd=h,n}function B(e,t,n,r){for(var a=[],i=n;i<r;){var s=q(t,i);i=s.headerEnd;var o={header:s,data:t};if(e.randomAccess||(o.start=i,i+=s.length,o.end=i),a.push(o),51===s.type)break}if(e.randomAccess)for(var l=0,h=a.length;l<h;l++)a[l].start=i,i+=a[l].header.length,a[l].end=i;return a}function R(e,t){return{width:d(e,t),height:d(e,t+4),x:d(e,t+8),y:d(e,t+12),combinationOperator:7&e[t+16]}}var O=17;function T(e,t){var n,r,a,i,s=e.header,o=e.data,l=e.end,h=e.start;switch(s.type){case 0:var f={},m=c(o,h);if(f.huffman=!!(1&m),f.refinement=!!(2&m),f.huffmanDHSelector=m>>2&3,f.huffmanDWSelector=m>>4&3,f.bitmapSizeSelector=m>>6&1,f.aggregationInstancesSelector=m>>7&1,f.bitmapCodingContextUsed=!!(256&m),f.bitmapCodingContextRetained=!!(512&m),f.template=m>>10&3,f.refinementTemplate=m>>12&1,h+=2,!f.huffman){for(i=0===f.template?4:1,r=[],a=0;a<i;a++)r.push({x:u(o,h),y:u(o,h+1)}),h+=2;f.at=r}if(f.refinement&&!f.refinementTemplate){for(r=[],a=0;a<2;a++)r.push({x:u(o,h),y:u(o,h+1)}),h+=2;f.refinementAt=r}f.numberOfExportedSymbols=d(o,h),h+=4,f.numberOfNewSymbols=d(o,h),h+=4,n=[f,s.number,s.referredTo,o,h,l];break;case 6:case 7:var p={};p.info=R(o,h);var g=c(o,h+=O);if(h+=2,p.huffman=!!(1&g),p.refinement=!!(2&g),p.logStripSize=g>>2&3,p.stripSize=1<<p.logStripSize,p.referenceCorner=g>>4&3,p.transposed=!!(64&g),p.combinationOperator=g>>7&3,p.defaultPixelValue=g>>9&1,p.dsOffset=g<<17>>27,p.refinementTemplate=g>>15&1,p.huffman){var v=c(o,h);h+=2,p.huffmanFS=3&v,p.huffmanDS=v>>2&3,p.huffmanDT=v>>4&3,p.huffmanRefinementDW=v>>6&3,p.huffmanRefinementDH=v>>8&3,p.huffmanRefinementDX=v>>10&3,p.huffmanRefinementDY=v>>12&3,p.huffmanRefinementSizeSelector=!!(16384&v)}if(p.refinement&&!p.refinementTemplate){for(r=[],a=0;a<2;a++)r.push({x:u(o,h),y:u(o,h+1)}),h+=2;p.refinementAt=r}p.numberOfSymbolInstances=d(o,h),h+=4,n=[p,s.referredTo,o,h,l];break;case 16:var y={},w=o[h++];y.mmr=!!(1&w),y.template=w>>1&3,y.patternWidth=o[h++],y.patternHeight=o[h++],y.maxPatternIndex=d(o,h),h+=4,n=[y,s.number,o,h,l];break;case 22:case 23:var b={};b.info=R(o,h),h+=O;var x=o[h++];b.mmr=!!(1&x),b.template=x>>1&3,b.enableSkip=!!(8&x),b.combinationOperator=x>>4&7,b.defaultPixelValue=x>>7&1,b.gridWidth=d(o,h),h+=4,b.gridHeight=d(o,h),h+=4,b.gridOffsetX=4294967295&d(o,h),h+=4,b.gridOffsetY=4294967295&d(o,h),h+=4,b.gridVectorX=c(o,h),h+=2,b.gridVectorY=c(o,h),h+=2,n=[b,s.referredTo,o,h,l];break;case 38:case 39:var I={};I.info=R(o,h),h+=O;var A=o[h++];if(I.mmr=!!(1&A),I.template=A>>1&3,I.prediction=!!(8&A),!I.mmr){for(i=0===I.template?4:1,r=[],a=0;a<i;a++)r.push({x:u(o,h),y:u(o,h+1)}),h+=2;I.at=r}n=[I,o,h,l];break;case 48:var k={width:d(o,h),height:d(o,h+4),resolutionX:d(o,h+8),resolutionY:d(o,h+12)};4294967295===k.height&&delete k.height;var F=o[h+16];c(o,h+17),k.lossless=!!(1&F),k.refinement=!!(2&F),k.defaultPixelValue=F>>2&1,k.combinationOperator=F>>3&3,k.requiresBuffer=!!(32&F),k.combinationOperatorOverride=!!(64&F),n=[k];break;case 49:case 50:case 51:case 62:break;case 53:n=[s.number,o,h,l]}var S="on"+s.typeName;S in t&&t[S].apply(t,n)}function C(e,t){for(var n=0,r=e.length;n<r;n++)T(e[n],t)}var L=function(){function e(){t(this,e)}return r(e,[{key:"onPageInformation",value:function(e){this.currentPageInfo=e;var t=e.width+7>>3,n=new Uint8ClampedArray(t*e.height);e.defaultPixelValue&&n.fill(255),this.buffer=n}},{key:"drawBitmap",value:function(e,t){var n,r,a,i,s=this.currentPageInfo,o=e.width,l=e.height,h=s.width+7>>3,f=s.combinationOperatorOverride?e.combinationOperator:s.combinationOperator,u=this.buffer,c=128>>(7&e.x),d=e.y*h+(e.x>>3);switch(f){case 0:for(n=0;n<l;n++){for(a=c,i=d,r=0;r<o;r++)t[n][r]&&(u[i]|=a),(a>>=1)||(a=128,i++);d+=h}break;case 2:for(n=0;n<l;n++){for(a=c,i=d,r=0;r<o;r++)t[n][r]&&(u[i]^=a),(a>>=1)||(a=128,i++);d+=h}}}},{key:"onImmediateGenericRegion",value:function(e,t,n,r){var a=e.info,i=new p(t,n,r),s=F(e.mmr,a.width,a.height,e.template,e.prediction,null,e.at,i);this.drawBitmap(a,s)}},{key:"onImmediateLosslessGenericRegion",value:function(){this.onImmediateGenericRegion.apply(this,arguments)}},{key:"onSymbolDictionary",value:function(e,t,n,r,i,s){var l,h;e.huffman&&(l=function(e,t,n){var r,a,i,s,o=0;switch(e.huffmanDHSelector){case 0:case 1:r=j(e.huffmanDHSelector+4);break;case 3:r=G(o,t,n),o++}switch(e.huffmanDWSelector){case 0:case 1:a=j(e.huffmanDWSelector+2);break;case 3:a=G(o,t,n),o++}e.bitmapSizeSelector?(i=G(o,t,n),o++):i=j(1);s=e.aggregationInstancesSelector?G(o,t,n):j(1);return{tableDeltaHeight:r,tableDeltaWidth:a,tableBitmapSize:i,tableAggregateInstances:s}}(e,n,this.customTables),h=new N(r,i,s));var u=this.symbols;u||(this.symbols=u={});var c,d=[],m=o(n);try{for(m.s();!(c=m.n()).done;){var g=u[c.value];g&&d.push.apply(d,a(g))}}catch(e){m.e(e)}finally{m.f()}var v=new p(r,i,s);u[t]=function(e,t,n,r,a,i,s,o,l,h,u,c){var d,m,p=[],g=0,v=f(n.length+r),b=u.decoder,x=u.contextCache;for(e&&(d=j(1),m=[],v=Math.max(v,1));p.length<r;){g+=e?i.tableDeltaHeight.decode(c):y(x,"IADH",b);for(var I=0,A=0,k=e?m.length:0;;){var q=e?i.tableDeltaWidth.decode(c):y(x,"IADW",b);if(null===q)break;A+=I+=q;var B=void 0;if(t){var R=y(x,"IAAI",b);if(R>1)B=D(e,t,I,g,0,R,1,n.concat(p),v,0,0,1,0,i,l,h,u,0,c);else{var O=w(x,b,v),T=y(x,"IARDX",b),C=y(x,"IARDY",b);B=S(I,g,l,O<n.length?n[O]:p[O-n.length],T,C,!1,h,u)}p.push(B)}else e?m.push(I):(B=F(!1,I,g,s,!1,null,o,u),p.push(B))}if(e&&!t){var L=i.tableBitmapSize.decode(c);c.byteAlign();var P=void 0;if(0===L)P=M(c,A,g);else{var E=c.end,U=c.position+L;c.end=U,P=W(c,A,g,!1),c.end=E,c.position=U}var H=m.length;if(k===H-1)p.push(P);else{var N=void 0,G=void 0,z=0,V=void 0,X=void 0;for(N=k;N<H;N++){for(V=z+m[N],X=[],G=0;G<g;G++)X.push(P[G].subarray(z,V));p.push(X),z=V}}}}for(var Y,J,_=[],K=[],$=!1,Q=n.length+r;K.length<Q;){for(var Z=e?d.decode(c):y(x,"IAEX",b);Z--;)K.push($);$=!$}for(Y=0,J=n.length;Y<J;Y++)K[Y]&&_.push(n[Y]);for(var ee=0;ee<r;Y++,ee++)K[Y]&&_.push(p[ee]);return _}(e.huffman,e.refinement,d,e.numberOfNewSymbols,e.numberOfExportedSymbols,l,e.template,e.at,e.refinementTemplate,e.refinementAt,v,h)}},{key:"onImmediateTextRegion",value:function(e,t,n,r,i){var s,l,h,u=e.info,c=this.symbols,d=[],m=o(t);try{for(m.s();!(h=m.n()).done;){var g=c[h.value];g&&d.push.apply(d,a(g))}}catch(e){m.e(e)}finally{m.f()}var v=f(d.length);e.huffman&&(l=new N(n,r,i),s=function(e,t,n,r,a){for(var i=[],s=0;s<=34;s++){var o=a.readBits(4);i.push(new P([s,o,0,0]))}var l=new U(i,!1);i.length=0;for(var h=0;h<r;){var f=l.decode(a);if(f>=32){var u=void 0,c=void 0,d=void 0;switch(f){case 32:c=a.readBits(2)+3,u=i[h-1].prefixLength;break;case 33:c=a.readBits(3)+3,u=0;break;case 34:c=a.readBits(7)+11,u=0}for(d=0;d<c;d++)i.push(new P([h,u,0,0])),h++}else i.push(new P([h,f,0,0])),h++}a.byteAlign();var m,p,g,v=new U(i,!1),y=0;switch(e.huffmanFS){case 0:case 1:m=j(e.huffmanFS+6);break;case 3:m=G(y,t,n),y++}switch(e.huffmanDS){case 0:case 1:case 2:p=j(e.huffmanDS+8);break;case 3:p=G(y,t,n),y++}switch(e.huffmanDT){case 0:case 1:case 2:g=j(e.huffmanDT+11);break;case 3:g=G(y,t,n),y++}e.refinement;return{symbolIDTable:v,tableFirstS:m,tableDeltaS:p,tableDeltaT:g}}(e,t,this.customTables,d.length,l));var y=new p(n,r,i),w=D(e.huffman,e.refinement,u.width,u.height,e.defaultPixelValue,e.numberOfSymbolInstances,e.stripSize,d,v,e.transposed,e.dsOffset,e.referenceCorner,e.combinationOperator,s,e.refinementTemplate,e.refinementAt,y,e.logStripSize,l);this.drawBitmap(u,w)}},{key:"onImmediateLosslessTextRegion",value:function(){this.onImmediateTextRegion.apply(this,arguments)}},{key:"onPatternDictionary",value:function(e,t,n,r,a){var i=this.patterns;i||(this.patterns=i={});var s=new p(n,r,a);i[t]=function(e,t,n,r,a,i){var s=[];e||(s.push({x:-t,y:0}),0===a&&s.push({x:-3,y:-1},{x:2,y:-2},{x:-2,y:-2}));for(var o=F(e,(r+1)*t,n,a,!1,null,s,i),l=[],h=0;h<=r;h++){for(var f=[],u=t*h,c=u+t,d=0;d<n;d++)f.push(o[d].subarray(u,c));l.push(f)}return l}(e.mmr,e.patternWidth,e.patternHeight,e.maxPatternIndex,e.template,s)}},{key:"onImmediateHalftoneRegion",value:function(e,t,n,r,a){var i=this.patterns[t[0]],s=e.info,o=new p(n,r,a),l=function(e,t,n,r,a,i,s,o,l,h,u,c,d,m,p){var g,v,y,w=[];for(g=0;g<a;g++){if(y=new Uint8Array(r),i)for(v=0;v<r;v++)y[v]=i;w.push(y)}var b=t.length,x=t[0],I=x[0].length,A=x.length,k=f(b),S=[];e||(S.push({x:n<=1?3:2,y:-1}),0===n&&S.push({x:-3,y:-1},{x:2,y:-2},{x:-2,y:-2}));var D,q,B,R,O,T,C,L,P,E,U,H=[];for(e&&(D=new N(p.data,p.start,p.end)),g=k-1;g>=0;g--)q=e?W(D,l,h,!0):F(!1,l,h,n,!1,null,S,p),H[g]=q;for(B=0;B<h;B++)for(R=0;R<l;R++){for(O=0,T=0,v=k-1;v>=0;v--)T|=(O^=H[v][B][R])<<v;if(C=t[T],P=c+B*d-R*m>>8,(L=u+B*m+R*d>>8)>=0&&L+I<=r&&P>=0&&P+A<=a)for(g=0;g<A;g++)for(U=w[P+g],E=C[g],v=0;v<I;v++)U[L+v]|=E[v];else{var j=void 0,G=void 0;for(g=0;g<A;g++)if(!((G=P+g)<0||G>=a))for(U=w[G],E=C[g],v=0;v<I;v++)(j=L+v)>=0&&j<r&&(U[j]|=E[v])}}return w}(e.mmr,i,e.template,s.width,s.height,e.defaultPixelValue,e.enableSkip,e.combinationOperator,e.gridWidth,e.gridHeight,e.gridOffsetX,e.gridOffsetY,e.gridVectorX,e.gridVectorY,o);this.drawBitmap(s,l)}},{key:"onImmediateLosslessHalftoneRegion",value:function(){this.onImmediateHalftoneRegion.apply(this,arguments)}},{key:"onTables",value:function(e,t,n,r){var a=this.customTables;a||(this.customTables=a={}),a[e]=function(e,t,n){var r,a,i=e[t],s=4294967295&d(e,t+1),o=4294967295&d(e,t+5),l=new N(e,t+9,n),h=1+(i>>1&7),f=1+(i>>4&7),u=[],c=s;do{r=l.readBits(h),a=l.readBits(f),u.push(new P([c,r,a,0])),c+=1<<a}while(c<o);r=l.readBits(h),u.push(new P([s-1,r,32,0,"lower"])),r=l.readBits(h),u.push(new P([o,r,32,0])),1&i&&(r=l.readBits(h),u.push(new P([r,0])));return new U(u,!1)}(t,n,r)}}]),e}(),P=r((function e(n){t(this,e),2===n.length?(this.isOOB=!0,this.rangeLow=0,this.prefixLength=n[0],this.rangeLength=0,this.prefixCode=n[1],this.isLowerRange=!1):(this.isOOB=!1,this.rangeLow=n[0],this.prefixLength=n[1],this.rangeLength=n[2],this.prefixCode=n[3],this.isLowerRange="lower"===n[4])})),E=function(){function e(n){t(this,e),this.children=[],n?(this.isLeaf=!0,this.rangeLength=n.rangeLength,this.rangeLow=n.rangeLow,this.isLowerRange=n.isLowerRange,this.isOOB=n.isOOB):this.isLeaf=!1}return r(e,[{key:"buildTree",value:function(t,n){var r=t.prefixCode>>n&1;if(n<=0)this.children[r]=new e(t);else{var a=this.children[r];a||(this.children[r]=a=new e(null)),a.buildTree(t,n-1)}}},{key:"decodeNode",value:function(e){if(this.isLeaf){if(this.isOOB)return null;var t=e.readBits(this.rangeLength);return this.rangeLow+(this.isLowerRange?-t:t)}var n=this.children[e.readBit()];return n.decodeNode(e)}}]),e}(),U=function(){function e(n,r){t(this,e),r||this.assignPrefixCodes(n),this.rootNode=new E(null);for(var a=0,i=n.length;a<i;a++){var s=n[a];s.prefixLength>0&&this.rootNode.buildTree(s,s.prefixLength-1)}}return r(e,[{key:"decode",value:function(e){return this.rootNode.decodeNode(e)}},{key:"assignPrefixCodes",value:function(e){for(var t=e.length,n=0,r=0;r<t;r++)n=Math.max(n,e[r].prefixLength);for(var a=new Uint32Array(n+1),i=0;i<t;i++)a[e[i].prefixLength]++;var s,o,l,h=1,f=0;for(a[0]=0;h<=n;){for(s=f=f+a[h-1]<<1,o=0;o<t;)(l=e[o]).prefixLength===h&&(l.prefixCode=s,s++),o++;h++}}}]),e}();var H={};function j(e){var t,n=H[e];if(n)return n;switch(e){case 1:t=[[0,1,4,0],[16,2,8,2],[272,3,16,6],[65808,3,32,7]];break;case 2:t=[[0,1,0,0],[1,2,0,2],[2,3,0,6],[3,4,3,14],[11,5,6,30],[75,6,32,62],[6,63]];break;case 3:t=[[-256,8,8,254],[0,1,0,0],[1,2,0,2],[2,3,0,6],[3,4,3,14],[11,5,6,30],[-257,8,32,255,"lower"],[75,7,32,126],[6,62]];break;case 4:t=[[1,1,0,0],[2,2,0,2],[3,3,0,6],[4,4,3,14],[12,5,6,30],[76,5,32,31]];break;case 5:t=[[-255,7,8,126],[1,1,0,0],[2,2,0,2],[3,3,0,6],[4,4,3,14],[12,5,6,30],[-256,7,32,127,"lower"],[76,6,32,62]];break;case 6:t=[[-2048,5,10,28],[-1024,4,9,8],[-512,4,8,9],[-256,4,7,10],[-128,5,6,29],[-64,5,5,30],[-32,4,5,11],[0,2,7,0],[128,3,7,2],[256,3,8,3],[512,4,9,12],[1024,4,10,13],[-2049,6,32,62,"lower"],[2048,6,32,63]];break;case 7:t=[[-1024,4,9,8],[-512,3,8,0],[-256,4,7,9],[-128,5,6,26],[-64,5,5,27],[-32,4,5,10],[0,4,5,11],[32,5,5,28],[64,5,6,29],[128,4,7,12],[256,3,8,1],[512,3,9,2],[1024,3,10,3],[-1025,5,32,30,"lower"],[2048,5,32,31]];break;case 8:t=[[-15,8,3,252],[-7,9,1,508],[-5,8,1,253],[-3,9,0,509],[-2,7,0,124],[-1,4,0,10],[0,2,1,0],[2,5,0,26],[3,6,0,58],[4,3,4,4],[20,6,1,59],[22,4,4,11],[38,4,5,12],[70,5,6,27],[134,5,7,28],[262,6,7,60],[390,7,8,125],[646,6,10,61],[-16,9,32,510,"lower"],[1670,9,32,511],[2,1]];break;case 9:t=[[-31,8,4,252],[-15,9,2,508],[-11,8,2,253],[-7,9,1,509],[-5,7,1,124],[-3,4,1,10],[-1,3,1,2],[1,3,1,3],[3,5,1,26],[5,6,1,58],[7,3,5,4],[39,6,2,59],[43,4,5,11],[75,4,6,12],[139,5,7,27],[267,5,8,28],[523,6,8,60],[779,7,9,125],[1291,6,11,61],[-32,9,32,510,"lower"],[3339,9,32,511],[2,0]];break;case 10:t=[[-21,7,4,122],[-5,8,0,252],[-4,7,0,123],[-3,5,0,24],[-2,2,2,0],[2,5,0,25],[3,6,0,54],[4,7,0,124],[5,8,0,253],[6,2,6,1],[70,5,5,26],[102,6,5,55],[134,6,6,56],[198,6,7,57],[326,6,8,58],[582,6,9,59],[1094,6,10,60],[2118,7,11,125],[-22,8,32,254,"lower"],[4166,8,32,255],[2,2]];break;case 11:t=[[1,1,0,0],[2,2,1,2],[4,4,0,12],[5,4,1,13],[7,5,1,28],[9,5,2,29],[13,6,2,60],[17,7,2,122],[21,7,3,123],[29,7,4,124],[45,7,5,125],[77,7,6,126],[141,7,32,127]];break;case 12:t=[[1,1,0,0],[2,2,0,2],[3,3,1,6],[5,5,0,28],[6,5,1,29],[8,6,1,60],[10,7,0,122],[11,7,1,123],[13,7,2,124],[17,7,3,125],[25,7,4,126],[41,8,5,254],[73,8,32,255]];break;case 13:t=[[1,1,0,0],[2,3,0,4],[3,4,0,12],[4,5,0,28],[5,4,1,13],[7,3,3,5],[15,6,1,58],[17,6,2,59],[21,6,3,60],[29,6,4,61],[45,6,5,62],[77,7,6,126],[141,7,32,127]];break;case 14:t=[[-2,3,0,4],[-1,3,0,5],[0,1,0,0],[1,3,0,6],[2,3,0,7]];break;case 15:t=[[-24,7,4,124],[-8,6,2,60],[-4,5,1,28],[-2,4,0,12],[-1,3,0,4],[0,1,0,0],[1,3,0,5],[2,4,0,13],[3,5,1,29],[5,6,2,61],[9,7,4,125],[-25,7,32,126,"lower"],[25,7,32,127]]}for(var r=0,a=t.length;r<a;r++)t[r]=new P(t[r]);return n=new U(t,!0),H[e]=n,n}var N=function(){function e(n,r,a){t(this,e),this.data=n,this.start=r,this.end=a,this.position=r,this.shift=-1,this.currentByte=0}return r(e,[{key:"readBit",value:function(){this.shift<0&&(this.position,this.end,this.currentByte=this.data[this.position++],this.shift=7);var e=this.currentByte>>this.shift&1;return this.shift--,e}},{key:"readBits",value:function(e){var t,n=0;for(t=e-1;t>=0;t--)n|=this.readBit()<<t;return n}},{key:"byteAlign",value:function(){this.shift=-1}},{key:"next",value:function(){return this.position>=this.end?-1:this.data[this.position++]}}]),e}();function G(e,t,n){for(var r=0,a=0,i=t.length;a<i;a++){var s=n[t[a]];if(s){if(e===r)return s;r++}}}function M(e,t,n){for(var r=[],a=0;a<n;a++){var i=new Uint8Array(t);r.push(i);for(var s=0;s<t;s++)i[s]=e.readBit();e.byteAlign()}return r}function W(e,t,n,r){for(var a,i=new CCITTFaxDecoder(e,{K:-1,Columns:t,Rows:n,BlackIs1:!0,EndOfBlock:r}),s=[],o=!1,l=0;l<n;l++){var h=new Uint8Array(t);s.push(h);for(var f=-1,u=0;u<t;u++)f<0&&(-1===(a=i.readNextChar())&&(a=0,o=!0),f=7),h[u]=a>>f&1,f--}if(r&&!o)for(var c=0;c<5&&-1!==i.readNextChar();c++);return s}var z=function(){function e(){t(this,e)}return r(e,[{key:"parseChunks",value:function(e){return function(e){for(var t=new L,n=0,r=e.length;n<r;n++){var a=e[n];C(B({},a.data,a.start,a.end),t)}return t.buffer}(e)}},{key:"parse",value:function(e){var t=function(e){var t=e.length,n=0;151!==e[n]||74!==e[n+1]||66!==e[n+2]||50!==e[n+3]||13!==e[n+4]||10!==e[n+5]||26!==e[n+6]||e[n+7];var r=Object.create(null);n+=8;var a=e[n++];r.randomAccess=!(1&a),2&a||(r.numberOfPages=d(e,n),n+=4);var i=B(r,e,n,t),s=new L;C(i,s);for(var o=s.currentPageInfo,l=o.width,h=o.height,f=s.buffer,u=new Uint8ClampedArray(l*h),c=0,m=0,p=0;p<h;p++)for(var g=0,v=void 0,y=0;y<l;y++)g||(g=128,v=f[m++]),u[c++]=v&g?0:255,g>>=1;return{imgData:u,width:l,height:h}}(e),n=t.imgData,r=t.width,a=t.height;return this.width=r,this.height=a,n}}]),e}();function V(e,t){return new Promise((function(n,r){var a=new FileReader;a.onload=function(e){var a=e.target.result,i=new Uint8Array(a),s=new z,o=s.parse(i),l=document.createElement("canvas");l.width=s.width,l.height=s.height;for(var h=l.getContext("2d"),f=h.getImageData(0,0,l.width,l.height),u=f.data,c=0,d=0;c<u.length;c+=4,d++){255==o[d]?(u[c]=255,u[c+1]=255,u[c+2]=255):(u[c]=0,u[c+1]=0,u[c+2]=0),u[c+3]=255}h.putImageData(f,0,0);var m=l.toDataURL("image/"+t),p=new Image;p.onload=function(){n(p)},p.onerror=function(e){r(new Error("Failed to load image"))},p.src=m},a.readAsArrayBuffer(e)}))}window.Jbig2Image=z,window.jb2Image=V;var X=[{qe:22017,nmps:1,nlps:1,switchFlag:1},{qe:13313,nmps:2,nlps:6,switchFlag:0},{qe:6145,nmps:3,nlps:9,switchFlag:0},{qe:2753,nmps:4,nlps:12,switchFlag:0},{qe:1313,nmps:5,nlps:29,switchFlag:0},{qe:545,nmps:38,nlps:33,switchFlag:0},{qe:22017,nmps:7,nlps:6,switchFlag:1},{qe:21505,nmps:8,nlps:14,switchFlag:0},{qe:18433,nmps:9,nlps:14,switchFlag:0},{qe:14337,nmps:10,nlps:14,switchFlag:0},{qe:12289,nmps:11,nlps:17,switchFlag:0},{qe:9217,nmps:12,nlps:18,switchFlag:0},{qe:7169,nmps:13,nlps:20,switchFlag:0},{qe:5633,nmps:29,nlps:21,switchFlag:0},{qe:22017,nmps:15,nlps:14,switchFlag:1},{qe:21505,nmps:16,nlps:14,switchFlag:0},{qe:20737,nmps:17,nlps:15,switchFlag:0},{qe:18433,nmps:18,nlps:16,switchFlag:0},{qe:14337,nmps:19,nlps:17,switchFlag:0},{qe:13313,nmps:20,nlps:18,switchFlag:0},{qe:12289,nmps:21,nlps:19,switchFlag:0},{qe:10241,nmps:22,nlps:19,switchFlag:0},{qe:9217,nmps:23,nlps:20,switchFlag:0},{qe:8705,nmps:24,nlps:21,switchFlag:0},{qe:7169,nmps:25,nlps:22,switchFlag:0},{qe:6145,nmps:26,nlps:23,switchFlag:0},{qe:5633,nmps:27,nlps:24,switchFlag:0},{qe:5121,nmps:28,nlps:25,switchFlag:0},{qe:4609,nmps:29,nlps:26,switchFlag:0},{qe:4353,nmps:30,nlps:27,switchFlag:0},{qe:2753,nmps:31,nlps:28,switchFlag:0},{qe:2497,nmps:32,nlps:29,switchFlag:0},{qe:2209,nmps:33,nlps:30,switchFlag:0},{qe:1313,nmps:34,nlps:31,switchFlag:0},{qe:1089,nmps:35,nlps:32,switchFlag:0},{qe:673,nmps:36,nlps:33,switchFlag:0},{qe:545,nmps:37,nlps:34,switchFlag:0},{qe:321,nmps:38,nlps:35,switchFlag:0},{qe:273,nmps:39,nlps:36,switchFlag:0},{qe:133,nmps:40,nlps:37,switchFlag:0},{qe:73,nmps:41,nlps:38,switchFlag:0},{qe:37,nmps:42,nlps:39,switchFlag:0},{qe:21,nmps:43,nlps:40,switchFlag:0},{qe:9,nmps:44,nlps:41,switchFlag:0},{qe:5,nmps:45,nlps:42,switchFlag:0},{qe:1,nmps:45,nlps:43,switchFlag:0},{qe:22017,nmps:46,nlps:46,switchFlag:0}],Y=function(){function e(n,r,a){t(this,e),this.data=n,this.bp=r,this.dataEnd=a,this.chigh=n[r],this.clow=0,this.byteIn(),this.chigh=this.chigh<<7&65535|this.clow>>9&127,this.clow=this.clow<<7&65535,this.ct-=7,this.a=32768}return r(e,[{key:"byteIn",value:function(){var e=this.data,t=this.bp;255===e[t]?e[t+1]>143?(this.clow+=65280,this.ct=8):(t++,this.clow+=e[t]<<9,this.ct=7,this.bp=t):(t++,this.clow+=t<this.dataEnd?e[t]<<8:65280,this.ct=8,this.bp=t),this.clow>65535&&(this.chigh+=this.clow>>16,this.clow&=65535)}},{key:"readBit",value:function(e,t){var n,r=e[t]>>1,a=1&e[t],i=X[r],s=i.qe,o=this.a-s;if(this.chigh<s)o<s?(o=s,n=a,r=i.nmps):(o=s,n=1^a,1===i.switchFlag&&(a=n),r=i.nlps);else{if(this.chigh-=s,0!=(32768&o))return this.a=o,a;o<s?(n=1^a,1===i.switchFlag&&(a=n),r=i.nlps):(n=a,r=i.nmps)}do{0===this.ct&&this.byteIn(),o<<=1,this.chigh=this.chigh<<1&65535|this.clow>>15&1,this.clow=this.clow<<1&65535,this.ct--}while(0==(32768&o));return this.a=o,e[t]=r<<1|a,n}}]),e}();e.default=l,e.jb2Image=V,Object.defineProperty(e,"__esModule",{value:!0})}));
