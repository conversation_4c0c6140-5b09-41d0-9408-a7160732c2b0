<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT预览器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            overflow-x: hidden;
        }

        .container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: #fff;
            padding: 12px 16px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1000;
        }

        .header h1 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            text-align: center;
            max-width: 70%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .content {
            flex: 1;
            position: relative;
            overflow: auto;
            background: #f5f5f5;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #666;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #f44336;
            padding: 20px;
        }

        .error-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .error-message {
            font-size: 16px;
            margin-bottom: 8px;
        }

        .error-detail {
            font-size: 12px;
            color: #999;
        }

        /* PPT内容样式 */
        #pptx-content {
            width: 100%;
            min-height: 100%;
            background: #f5f5f5;
            padding: 10px;
        }

        /* 移动端优化 */
        #pptx-content .slide {
            margin: 10px auto;
            max-width: 100%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }

        /* 确保内容适应屏幕宽度 */
        #pptx-content * {
            max-width: 100% !important;
            box-sizing: border-box;
        }

        /* 图片自适应 */
        #pptx-content img {
            max-width: 100% !important;
            height: auto !important;
        }

        /* 文本自适应 */
        #pptx-content div, #pptx-content p, #pptx-content span {
            word-wrap: break-word;
            word-break: break-word;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 id="fileName">PPT预览</h1>
        </div>
        <div class="content">
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <div>正在加载PPT文档...</div>
            </div>
            <div id="error" class="error" style="display: none;">
                <div class="error-icon">⚠️</div>
                <div class="error-message">PPT加载失败</div>
                <div class="error-detail">请检查文件格式是否正确</div>
            </div>
            <div id="pptx-content" style="display: none;"></div>
        </div>
    </div>

    <!-- 库文件将由React Native动态注入 -->
    <script src="./libs/jquery.min.js"></script>
    <script src="./libs/jszip.min.js"></script>
    <script src="./libs/jszip-utils.min.js"></script>
    <script src="./libs/d3.min.js"></script>
    <script src="./libs/nv.d3.min.js"></script>
    <script src="./libs/divs2slides.js"></script>
    <script src="./libs/pptxjs.js"></script>

    <script>
        let currentFileData = null;
        let currentFileName = '';

        // 标准消息监听，接收来自React Native的数据
        window.addEventListener("message", function(event) {
            try {
                const message = JSON.parse(event.data);
                if (message.type === 'file') {
                    console.log('[PPTX Viewer] 接收到文件数据');
                    currentFileData = message.data;
                    currentFileName = message.name || 'presentation.pptx';
                    document.getElementById('fileName').textContent = currentFileName;
                    renderPPTX(currentFileData);
                }
            } catch (error) {
                console.error('[PPTX Viewer] 解析消息失败:', error);
                showError('无法解析文件数据: ' + error.message);
            }
        });

        // 页面加载完成后，通知React Native就绪
        window.addEventListener('load', function() {
            console.log('[PPTX Viewer] 页面加载完成，通知React Native就绪');
            if (window.ReactNativeWebView) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'ready'
                }));
            }
        });

        // 向React Native发送消息
        function sendMessage(type, data = {}) {
            if (window.ReactNativeWebView) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: type,
                    ...data
                }));
            }
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            document.getElementById('pptx-content').style.display = 'none';
        }

        // 显示错误
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('pptx-content').style.display = 'none';

            const errorDetail = document.querySelector('.error-detail');
            if (errorDetail) {
                errorDetail.textContent = message;
            }

            sendMessage('ERROR', { message: message });
        }

        // 显示PPT内容
        function showContent() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'none';
            document.getElementById('pptx-content').style.display = 'block';
        }

        // 渲染PPTX文件
        function renderPPTX(base64Data) {
            try {
                showLoading();
                console.log('[PPTX Viewer] 开始渲染PPTX文件:', currentFileName);

                if (!base64Data) {
                    throw new Error('PPT数据为空');
                }

                $("#pptx-content").pptxToHtml({
                    pptxFileUrl: "data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64," + base64Data,
                    slideMode: false,
                    slidesScale: "100%",
                    onend: function (e) {
                        console.log('[PPTX Viewer] 渲染完成');
                        showContent();
                        sendMessage('LOADED');
                    },
                    onerror: function (e) {
                        console.error('[PPTX Viewer] 渲染失败:', e);
                        showError('PPTX渲染失败: ' + e.message);
                    }
                });

            } catch (e) {
                console.error('[PPTX Viewer] 渲染过程中发生异常:', e);
                showError('渲染异常: ' + e.message);
            }
        }

        // 移动端优化
        function optimizeForMobile() {
            console.log('[PPTX Viewer] 开始移动端优化');

            const content = document.getElementById('pptx-content');
            if (!content) return;

            // 确保所有元素适应屏幕宽度
            const allElements = content.querySelectorAll('*');
            allElements.forEach(element => {
                const style = element.style;

                // 移除固定宽度，使用相对宽度
                if (style.width && style.width.includes('px')) {
                    style.maxWidth = '100%';
                    style.width = 'auto';
                }

                // 确保文本不会溢出
                if (element.tagName === 'DIV' || element.tagName === 'P' || element.tagName === 'SPAN') {
                    style.wordWrap = 'break-word';
                    style.wordBreak = 'break-word';
                }
            });

            console.log('[PPTX Viewer] 移动端优化完成');
        }
    </script>
</body>
</html>
