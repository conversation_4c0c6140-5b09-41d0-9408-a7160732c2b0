<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=3.0, user-scalable=yes">
    <title>DOCX文档预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
            width: 100% !important;
            max-width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* 🔧 强制所有容器元素使用全宽 */
        html, body, #app, #docx-container {
            width: 100% !important;
            max-width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* 全局重置 - 确保所有元素都能正确适应移动端 */
        * {
            box-sizing: border-box;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e3e3e3;
            border-top: 4px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 16px;
            color: #666;
            text-align: center;
        }

        .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            text-align: center;
        }

        .error-icon {
            font-size: 48px;
            color: #f44336;
            margin-bottom: 20px;
        }

        .error-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .error-message {
            font-size: 14px;
            color: #666;
            margin-bottom: 30px;
            max-width: 300px;
            line-height: 1.5;
        }

        .retry-button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 12px;
        }

        .retry-button:hover {
            background-color: #1976D2;
        }

        .external-button {
            background-color: transparent;
            color: #2196F3;
            border: 1px solid #2196F3;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
        }

        .external-button:hover {
            background-color: #f0f8ff;
        }

        /* DOCX预览容器样式 - 移动端优化 */
        .docx-container {
            width: 100vw !important; /* 使用视口宽度 */
            max-width: 100vw !important;
            margin: 0 !important;
            padding: 0 !important; /* 完全移除padding */
            background-color: #fff;
            min-height: 100vh;
            box-sizing: border-box;
            overflow-x: hidden; /* 防止水平滚动，强制内容适应宽度 */
            position: relative;
        }

        /* docx-preview库的默认样式覆盖 - 移动端优化 */
        .docx {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.5;
            color: #333;
            font-size: 14px;
            word-wrap: break-word; /* 确保长单词换行 */
            overflow-wrap: break-word;
            width: 100vw !important; /* 使用视口宽度 */
            max-width: 100vw !important;
            margin: 0 !important;
            padding: 8px !important; /* 最小内边距 */
            box-sizing: border-box;
        }

        .docx-wrapper {
            background-color: #fff;
            box-shadow: none; /* 移除阴影避免占用空间 */
            border-radius: 0; /* 移除圆角 */
            overflow: hidden;
            width: 100vw !important;
            max-width: 100vw !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box;
        }

        /* 确保中文字符正确显示 - 移动端优化 */
        .docx p, .docx span, .docx div {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            font-size: 14px;
            line-height: 1.5;
            width: 100% !important;
            max-width: 100% !important;
            margin: 0 !important;
            padding: 4px 8px !important; /* 适当的内边距 */
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        /* 表格样式优化 - 移动端适配 */
        .docx table {
            border-collapse: collapse;
            width: 100%;
            margin: 8px 0;
            font-size: 12px; /* 表格使用更小的字体 */
            display: block; /* 使表格可以水平滚动 */
            overflow-x: auto;
            white-space: nowrap;
        }

        .docx table td, .docx table th {
            border: 1px solid #ddd;
            padding: 4px 6px; /* 减少内边距以适应手机屏幕 */
            text-align: left;
            min-width: 60px; /* 设置最小宽度 */
        }

        .docx table th {
            background-color: #f2f2f2;
            font-weight: bold;
            font-size: 12px;
        }

        /* 图片样式优化 */
        .docx img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 10px auto;
        }

        /* 标题样式优化 - 移动端适配 */
        .docx h1, .docx h2, .docx h3, .docx h4, .docx h5, .docx h6 {
            margin: 12px 0 8px 0; /* 减少标题间距 */
            font-weight: bold;
            color: #333;
            line-height: 1.3; /* 紧凑的行高 */
        }

        /* 适合手机屏幕的标题字体大小 */
        .docx h1 { font-size: 20px; }
        .docx h2 { font-size: 18px; }
        .docx h3 { font-size: 16px; }
        .docx h4 { font-size: 15px; }
        .docx h5 { font-size: 14px; }
        .docx h6 { font-size: 13px; }

        /* 段落样式优化 - 移动端适配 */
        .docx p {
            margin: 6px 0; /* 减少段落间距 */
            text-align: justify;
            font-size: 14px;
            line-height: 1.5;
        }

        /* 列表样式优化 - 移动端适配 */
        .docx ul, .docx ol {
            margin: 6px 0; /* 减少列表间距 */
            padding-left: 20px; /* 减少缩进以节省空间 */
        }

        .docx li {
            margin: 3px 0; /* 减少列表项间距 */
            font-size: 14px;
            line-height: 1.4;
        }

        /* 🔧 强制所有docx元素适应屏幕宽度 */
        .docx * {
            max-width: 100vw !important;
            box-sizing: border-box !important;
        }

        /* 🔧 特别处理可能导致宽度问题的元素 */
        .docx section, .docx article, .docx div[style*="width"], .docx div {
            width: 100% !important;
            max-width: 100vw !important;
            margin: 0 !important;
        }

        /* 🔧 强制移除任何可能限制宽度的样式 */
        .docx [style*="width"], .docx [style*="max-width"] {
            width: 100% !important;
            max-width: 100vw !important;
        }

        /* 移动端特定样式 */
        @media screen and (max-width: 768px) {
            .docx-container {
                padding: 0 !important; /* 完全移除padding */
                width: 100vw !important;
            }

            .docx {
                font-size: 13px; /* 在小屏幕上使用更小的字体 */
                padding: 4px !important;
                width: 100vw !important;
            }

            .docx p, .docx span, .docx div {
                font-size: 13px;
                padding: 2px 4px !important;
            }

            .docx table {
                font-size: 11px; /* 表格字体更小 */
                width: 100% !important;
            }

            .docx h1 { font-size: 18px; padding: 4px !important; }
            .docx h2 { font-size: 16px; padding: 4px !important; }
            .docx h3 { font-size: 15px; padding: 4px !important; }
            .docx h4 { font-size: 14px; padding: 4px !important; }
            .docx h5 { font-size: 13px; padding: 4px !important; }
            .docx h6 { font-size: 12px; padding: 4px !important; }
        }

        /* 隐藏元素 */
        .hidden {
            display: none !important;
        }



        /* 触摸优化 */
        .docx-container {
            -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
            touch-action: manipulation; /* 优化触摸响应 */
        }
    </style>
</head>
<body>
    <!-- 加载状态 -->
    <div id="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载DOCX文档...</div>
    </div>

    <!-- 错误状态 -->
    <div id="error" class="error-container hidden">
        <div class="error-icon">⚠️</div>
        <div class="error-title">文档加载失败</div>
        <div class="error-message" id="error-message">加载文档时发生错误，请稍后重试。</div>
        <button class="retry-button" onclick="retryLoad()">重新加载</button>
        <button class="external-button" onclick="openExternal()">使用外部应用打开</button>
    </div>

    <!-- DOCX文档容器 -->
    <div id="docx-container" class="docx-container hidden"></div>



    <!-- 引入JSZip库 - 使用本地版本 -->
    <script src="./libs/jszip.min.js"></script>

    <!-- 引入docx-preview库 - 使用本地版本 -->
    <script src="./libs/docx-preview.min.js"></script>

    <script>
        let currentFileData = null;
        let currentFileName = '';


        // 🔧 采用OFD成功模式：监听'ready'事件和自定义数据事件
        window.addEventListener('load', function() {
            console.log('[DOCX Viewer] 页面加载完成，通知React Native就绪');
            if (window.ReactNativeWebView) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'ready'
                }));
            }
        });

        // 监听Word数据就绪事件
        window.addEventListener('wordDataReady', function() {
            console.log('[DOCX Viewer] Word数据就绪，开始渲染');
            if (window.WORD_DATA) {
                currentFileData = window.WORD_DATA;
                currentFileName = window.WORD_FILENAME || 'document.docx';
                renderDOCX(window.WORD_DATA);
            } else {
                console.error('[DOCX Viewer] Word数据未找到');
                showError('Word数据未找到');
            }
        });

        // 渲染DOCX文档
        async function renderDOCX(base64Data) {
            try {
                console.log('[DOCX Viewer] 开始渲染DOCX文档, base64长度:', base64Data.length);
                
                showLoading();

                // 将base64转换为ArrayBuffer
                const binaryString = atob(base64Data);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }

                console.log('[DOCX Viewer] ArrayBuffer创建成功, 大小:', bytes.length);

                // 获取容器元素
                const container = document.getElementById('docx-container');
                container.innerHTML = ''; // 清空容器

                // 使用docx-preview渲染文档 - 移动端优化配置
                const options = {
                    className: 'docx',
                    inWrapper: false, // 🔧 不使用wrapper，避免额外的容器影响宽度
                    hideWrapperOnPrint: false,
                    ignoreWidth: true, // 🔧 忽略原始宽度，让内容自适应容器
                    ignoreHeight: true, // 🔧 忽略原始高度，适应移动端显示
                    ignoreFonts: false,
                    breakPages: false, // 🔧 在移动端不分页，连续显示
                    ignoreLastRenderedPageBreak: true,
                    experimental: false,
                    trimXmlDeclaration: true,
                    useBase64URL: true, // 使用base64 URL处理图片
                    renderChanges: false,
                    renderHeaders: true,
                    renderFooters: true,
                    renderFootnotes: true,
                    renderEndnotes: true,
                    renderComments: false,
                    renderAltChunks: true,
                    debug: false // 🔧 生产环境关闭调试模式
                };

                console.log('[DOCX Viewer] 开始调用docx.renderAsync');
                
                await docx.renderAsync(bytes.buffer, container, null, options);

                console.log('[DOCX Viewer] DOCX文档渲染成功');

                // 🔧 渲染后处理：确保所有内容适应屏幕宽度
                optimizeContentForMobile(container);

                showDocument();

                // 通知React Native渲染完成
                if (window.ReactNativeWebView) {
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'RENDER_SUCCESS',
                        fileName: currentFileName
                    }));
                }

            } catch (error) {
                console.error('[DOCX Viewer] DOCX渲染失败:', error);
                showError('文档渲染失败: ' + error.message);
                
                // 通知React Native渲染失败
                if (window.ReactNativeWebView) {
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'RENDER_ERROR',
                        error: error.message
                    }));
                }
            }
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').classList.remove('hidden');
            document.getElementById('error').classList.add('hidden');
            document.getElementById('docx-container').classList.add('hidden');
        }

        // 显示错误状态
        function showError(message) {
            document.getElementById('error-message').textContent = message;
            document.getElementById('loading').classList.add('hidden');
            document.getElementById('error').classList.remove('hidden');
            document.getElementById('docx-container').classList.add('hidden');
        }

        // 显示文档
        function showDocument() {
            document.getElementById('loading').classList.add('hidden');
            document.getElementById('error').classList.add('hidden');
            document.getElementById('docx-container').classList.remove('hidden');

        }

        // 重试加载
        function retryLoad() {
            if (currentFileData) {
                renderDOCX(currentFileData);
            }
        }

        // 使用外部应用打开
        function openExternal() {
            if (window.ReactNativeWebView) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'OPEN_EXTERNAL'
                }));
            }
        }

        // 🔧 优化渲染后的内容以适应移动端
        function optimizeContentForMobile(container) {
            console.log('[DOCX Viewer] 开始优化移动端显示');

            // 首先确保容器本身使用全宽
            container.style.width = '100vw';
            container.style.maxWidth = '100vw';
            container.style.margin = '0';
            container.style.padding = '4px';
            container.style.boxSizing = 'border-box';

            // 强制所有元素适应容器宽度
            const allElements = container.querySelectorAll('*');
            allElements.forEach(element => {
                const style = element.style;

                // 移除所有固定宽度设置
                if (style.width) {
                    style.width = '100%';
                }
                if (style.maxWidth) {
                    style.maxWidth = '100%';
                }
                if (style.minWidth) {
                    style.minWidth = 'auto';
                }

                // 移除可能导致宽度问题的margin
                style.marginLeft = '0';
                style.marginRight = '0';

                // 确保box-sizing
                style.boxSizing = 'border-box';

                // 特别处理表格
                if (element.tagName === 'TABLE') {
                    style.width = '100%';
                    style.maxWidth = '100%';
                    style.tableLayout = 'auto';
                    style.wordWrap = 'break-word';
                }

                // 特别处理段落和文本容器
                if (['P', 'DIV', 'SPAN', 'SECTION', 'ARTICLE'].includes(element.tagName)) {
                    style.wordWrap = 'break-word';
                    style.overflowWrap = 'break-word';
                    style.width = '100%';
                    style.maxWidth = '100%';
                    style.padding = '2px 4px';
                }

                // 移除任何可能的绝对定位或浮动
                if (style.position === 'absolute' || style.position === 'fixed') {
                    style.position = 'relative';
                }
                if (style.float && style.float !== 'none') {
                    style.float = 'none';
                }
            });

            console.log('[DOCX Viewer] 移动端显示优化完成');
        }



        // 错误处理
        window.addEventListener('error', function(event) {
            console.error('[DOCX Viewer] 全局错误:', event.error);
            showError('页面发生错误: ' + event.error.message);
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('[DOCX Viewer] 未处理的Promise拒绝:', event.reason);
            showError('异步操作失败: ' + event.reason);
        });
    </script>
</body>
</html>
