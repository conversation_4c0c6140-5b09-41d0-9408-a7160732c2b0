# 公职猫OCR测试图片生成器

## 概述

这个工具基于您提供的真实政务文档和通知内容，生成符合OCR测试需求的图片。生成的图片包括微信截图、政务文档、会议安排等多种场景，并支持不同质量级别的测试。

## 特性

✅ **基于真实数据**：分析您提供的制度类文档、培训通知、会议安排等真实内容  
✅ **多种场景**：微信截图、政务文档、会议安排表格等  
✅ **质量变化**：高、中、低三种质量级别测试  
✅ **自动映射**：生成测试数据映射文件，包含预期文本和关键词  
✅ **容错处理**：即使无法读取某些文档，也会生成合理的模拟内容  

## 安装依赖

```bash
pip install Pillow python-docx pandas openpyxl
```

## 使用方法

### 方法一：直接运行（推荐）

```bash
cd mobile/src/assets/test-images/scripts
python run.py
```

### 方法二：使用完整安装脚本

```bash
cd mobile/src/assets/test-images/scripts
python setup.py
```

### 方法三：直接运行生成器

```bash
cd mobile/src/assets/test-images/scripts
python generate_test_images.py
```

## 输出结果

### 生成的图片文件

生成的图片将保存在 `mobile/src/assets/test-images/generated/` 目录下：

#### 微信截图类
- `wechat_meeting.png` - 会议通知截图
- `wechat_task.png` - 工作任务截图  
- `wechat_notice.png` - 通知公告截图

#### 政务文档类
- `official_notice.png` - 公文通知
- `meeting_minutes.png` - 会议纪要
- `work_plan.png` - 工作计划

#### 会议安排类
- `meeting_schedule.png` - 会议活动安排表

#### 质量测试类
- `high_quality.png` - 高质量图片
- `medium_quality.png` - 中等质量图片
- `low_quality.png` - 低质量图片

### 测试数据映射

生成的 `test_data_mapping.json` 文件包含每张图片的：
- `expectedText` - 预期识别的文本内容
- `expectedKeywords` - 关键词列表
- `minConfidence` - 最低置信度要求
- `scenario` - 测试场景类型
- `realSource` - 数据来源（真实文档或模拟数据）

## 数据来源

生成器会自动分析以下真实文档：

1. **制度类文档** (`mobile/src/assets/制度类/`)
   - 代表委员联络站工作制度.docx

2. **学习培训类通知** (`mobile/src/assets/学习培训类通知/`)
   - 各种培训通知.doc/.docx文件

3. **领导会议活动预安排** (`mobile/src/assets/领导会议活动预安排/`)
   - 会议活动安排.xls/.xlsx文件

## 技术实现

### 图片生成技术
- **PIL/Pillow**：核心图像生成和处理
- **ImageDraw**：文本绘制和图形绘制
- **ImageFont**：字体处理（支持中文）
- **ImageFilter**：质量效果处理

### 文档解析技术
- **python-docx**：Word文档内容提取
- **pandas + openpyxl**：Excel表格数据解析
- **容错机制**：文档读取失败时自动生成合理的模拟内容

### 质量控制
- **高质量**：原始清晰图片
- **中等质量**：轻微模糊和压缩
- **低质量**：重度模糊、噪声、低分辨率

## 自定义配置

如需修改生成参数，可以编辑 `generate_test_images.py` 中的相关配置：

```python
# 图片尺寸
width, height = 375, 200  # 微信截图尺寸
width, height = 595, 842  # A4文档尺寸

# 字体大小
title_font_size = 24
content_font_size = 16

# 质量参数
blur_radius = 0.5  # 中等质量模糊半径
low_quality_blur = 1.5  # 低质量模糊半径
```

## 故障排除

### 常见问题

1. **依赖包安装失败**
   ```bash
   # 使用国内镜像源
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple Pillow python-docx pandas openpyxl
   ```

2. **字体显示问题**
   - 生成器会自动检测系统字体
   - 支持 macOS、Windows、Linux 系统字体
   - 如果没有合适字体，会使用默认字体

3. **文档读取失败**
   - 生成器具有容错机制
   - 即使无法读取某些文档，也会生成合理的模拟内容
   - 检查文档格式是否正确（.docx, .doc, .xls, .xlsx）

4. **权限问题**
   ```bash
   # 确保有写入权限
   chmod +w mobile/src/assets/test-images/
   ```

### 调试模式

如需查看详细的执行过程，可以在代码中添加更多调试信息：

```python
# 在 generate_test_images.py 开头添加
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展功能

### 添加新的测试场景

1. 在 `generate_test_images.py` 中添加新的生成方法
2. 在 `generate_all_test_images()` 中调用新方法
3. 更新测试数据映射

### 支持新的文档格式

1. 在 `extract_*_content()` 方法中添加新格式支持
2. 更新依赖包列表
3. 添加相应的容错处理

## 联系支持

如有问题或建议，请联系开发团队。 