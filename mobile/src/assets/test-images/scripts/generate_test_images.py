#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公职猫OCR测试图片生成器
基于真实政务文档和通知内容生成测试图片
"""

import os
import sys
import json
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont, ImageFilter
import random
import datetime
from typing import Dict, List, Tuple

class TestImageGenerator:
    def __init__(self, assets_path: str):
        self.assets_path = Path(assets_path)
        self.output_path = self.assets_path / "test-images" / "generated"
        self.scripts_path = self.assets_path / "test-images" / "scripts"
        
        # 创建输出目录
        self.output_path.mkdir(exist_ok=True)
        
        # 测试数据映射
        self.test_data_mapping = {}
        
        # 真实文档内容缓存
        self.real_content = {
            "制度类": [],
            "学习培训类通知": [],
            "领导会议活动预安排": []
        }
        
    def load_real_content(self):
        """加载真实文档内容"""
        print("正在分析真实文档内容...")
        
        # 分析制度类文档
        zhidu_path = self.assets_path / "制度类"
        if zhidu_path.exists():
            for file_path in zhidu_path.glob("*.docx"):
                try:
                    content = self.extract_docx_content(file_path)
                    if content:
                        self.real_content["制度类"].append({
                            "filename": file_path.name,
                            "content": content,
                            "title": self.extract_title(content)
                        })
                        print(f"✓ 读取制度文档：{file_path.name}")
                except Exception as e:
                    print(f"✗ 读取文档 {file_path.name} 失败: {e}")
        
        # 分析学习培训类通知
        training_path = self.assets_path / "学习培训类通知"
        if training_path.exists():
            for file_path in training_path.glob("*.doc*"):
                try:
                    content = self.extract_docx_content(file_path)
                    if content:
                        self.real_content["学习培训类通知"].append({
                            "filename": file_path.name,
                            "content": content,
                            "title": self.extract_title(content)
                        })
                        print(f"✓ 读取培训通知：{file_path.name}")
                except Exception as e:
                    print(f"✗ 读取文档 {file_path.name} 失败: {e}")
        
        # 分析会议活动安排
        meeting_path = self.assets_path / "领导会议活动预安排"
        if meeting_path.exists():
            for file_path in meeting_path.glob("*.xls*"):
                try:
                    content = self.extract_excel_content(file_path)
                    if content:
                        self.real_content["领导会议活动预安排"].extend(content)
                        print(f"✓ 读取会议安排：{file_path.name}")
                except Exception as e:
                    print(f"✗ 读取表格 {file_path.name} 失败: {e}")
    
    def extract_docx_content(self, file_path: Path) -> str:
        """提取docx文档内容"""
        try:
            import docx
            doc = docx.Document(file_path)
            content = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content.append(paragraph.text.strip())
            return "\n".join(content)
        except Exception as e:
            print(f"无法读取docx文件 {file_path}: {e}")
            # 如果docx读取失败，返回基于文件名的模拟内容
            return self.generate_mock_content_from_filename(file_path.name)
    
    def extract_excel_content(self, file_path: Path) -> List[Dict]:
        """提取Excel内容"""
        try:
            import pandas as pd
            df = pd.read_excel(file_path)
            content = []
            for _, row in df.iterrows():
                row_content = []
                for col in df.columns:
                    if pd.notna(row[col]):
                        row_content.append(str(row[col]))
                if row_content:
                    content.append({
                        "content": " ".join(row_content),
                        "type": "会议安排"
                    })
            return content
        except Exception as e:
            print(f"无法读取Excel文件 {file_path}: {e}")
            # 返回模拟的会议安排数据
            return [
                {"content": "上午9:00 部门例会 会议室A 全体员工", "type": "会议安排"},
                {"content": "下午2:00 项目评审 会议室B 项目组成员", "type": "会议安排"},
                {"content": "下午4:00 培训讲座 大会议室 相关人员", "type": "会议安排"}
            ]
    
    def generate_mock_content_from_filename(self, filename: str) -> str:
        """根据文件名生成模拟内容"""
        if "代表委员联络站" in filename:
            return """代表委员联络站工作制度
第一条 为规范代表委员联络站工作，提高服务质量，制定本制度。
第二条 联络站应当建立健全工作机制，完善服务功能。
第三条 定期组织代表委员开展活动，收集民意建议。"""
        elif "培训" in filename:
            return f"""关于开展专项培训的通知
各相关部门：
为提升工作能力，决定组织专项培训活动。
时间：{datetime.datetime.now().strftime('%Y年%m月%d日')}
地点：培训中心
请相关人员准时参加。"""
        else:
            return f"这是关于{filename}的相关文档内容。"
    
    def extract_title(self, content: str) -> str:
        """从内容中提取标题"""
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line and len(line) < 50:  # 假设标题不超过50字符
                return line
        return "文档标题"
    
    def get_font_path(self, font_name: str = "default") -> str:
        """获取字体路径"""
        # 尝试使用系统字体
        system_fonts = [
            "/System/Library/Fonts/PingFang.ttc",  # macOS
            "/System/Library/Fonts/Helvetica.ttc",  # macOS
            "C:/Windows/Fonts/msyh.ttc",  # Windows 微软雅黑
            "C:/Windows/Fonts/simsun.ttc",  # Windows 宋体
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
        ]
        
        for font_path in system_fonts:
            if os.path.exists(font_path):
                return font_path
        
        # 如果没有找到系统字体，返回None使用默认字体
        return None
    
    def create_wechat_screenshot(self, message_text: str, sender_name: str = "工作群", 
                                quality: str = "high") -> Image.Image:
        """创建微信截图风格的图片"""
        # 图片尺寸
        width, height = 375, 200
        
        # 创建画布
        img = Image.new('RGB', (width, height), color='#EDEDED')
        draw = ImageDraw.Draw(img)
        
        # 加载字体
        try:
            font_path = self.get_font_path()
            if font_path:
                font = ImageFont.truetype(font_path, 16)
                small_font = ImageFont.truetype(font_path, 12)
            else:
                font = ImageFont.load_default()
                small_font = ImageFont.load_default()
        except:
            font = ImageFont.load_default()
            small_font = ImageFont.load_default()
        
        # 绘制发送者头像（简单的圆形）
        avatar_x, avatar_y = 15, 20
        draw.ellipse([avatar_x, avatar_y, avatar_x + 40, avatar_y + 40], fill='#4CAF50')
        
        # 绘制发送者名称
        draw.text((avatar_x + 50, avatar_y), sender_name, fill='#666666', font=small_font)
        
        # 绘制消息气泡
        bubble_x, bubble_y = avatar_x + 50, avatar_y + 20
        bubble_width = min(250, len(message_text) * 8 + 20)
        bubble_height = max(40, (len(message_text) // 20 + 1) * 20 + 10)
        
        # 消息气泡背景
        draw.rounded_rectangle([bubble_x, bubble_y, bubble_x + bubble_width, bubble_y + bubble_height], 
                             radius=8, fill='#FFFFFF', outline='#DDDDDD')
        
        # 绘制消息文本
        self.draw_multiline_text(draw, message_text, bubble_x + 10, bubble_y + 10, 
                               font, '#333333', bubble_width - 20)
        
        # 绘制时间戳
        timestamp = datetime.datetime.now().strftime("%H:%M")
        draw.text((bubble_x, bubble_y + bubble_height + 5), timestamp, 
                 fill='#999999', font=small_font)
        
        # 应用质量效果
        img = self.apply_quality_effects(img, quality)
        
        return img
    
    def create_official_document(self, title: str, content: str, quality: str = "high") -> Image.Image:
        """创建政务文档风格的图片"""
        # 图片尺寸 (A4比例)
        width, height = 595, 842
        
        # 创建画布
        img = Image.new('RGB', (width, height), color='#FFFFFF')
        draw = ImageDraw.Draw(img)
        
        # 加载字体
        try:
            font_path = self.get_font_path()
            if font_path:
                title_font = ImageFont.truetype(font_path, 24)
                content_font = ImageFont.truetype(font_path, 16)
                small_font = ImageFont.truetype(font_path, 12)
            else:
                title_font = ImageFont.load_default()
                content_font = ImageFont.load_default()
                small_font = ImageFont.load_default()
        except:
            title_font = ImageFont.load_default()
            content_font = ImageFont.load_default()
            small_font = ImageFont.load_default()
        
        # 绘制标题
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (width - title_width) // 2
        draw.text((title_x, 80), title, fill='#000000', font=title_font)
        
        # 绘制分隔线
        draw.line([(50, 130), (width - 50, 130)], fill='#CCCCCC', width=2)
        
        # 绘制正文内容
        content_lines = content.split('\n')[:15]  # 限制行数
        y_offset = 160
        for line in content_lines:
            if line.strip():
                self.draw_multiline_text(draw, line.strip(), 50, y_offset, 
                                       content_font, '#333333', width - 100)
                y_offset += 30
        
        # 绘制页脚信息
        footer_text = f"生成时间：{datetime.datetime.now().strftime('%Y年%m月%d日')}"
        draw.text((50, height - 50), footer_text, fill='#666666', font=small_font)
        
        # 应用质量效果
        img = self.apply_quality_effects(img, quality)
        
        return img
    
    def create_meeting_schedule(self, schedule_data: List[str], quality: str = "high") -> Image.Image:
        """创建会议安排表格风格的图片"""
        # 图片尺寸
        width, height = 800, 600
        
        # 创建画布
        img = Image.new('RGB', (width, height), color='#FFFFFF')
        draw = ImageDraw.Draw(img)
        
        # 加载字体
        try:
            font_path = self.get_font_path()
            if font_path:
                title_font = ImageFont.truetype(font_path, 20)
                content_font = ImageFont.truetype(font_path, 14)
            else:
                title_font = ImageFont.load_default()
                content_font = ImageFont.load_default()
        except:
            title_font = ImageFont.load_default()
            content_font = ImageFont.load_default()
        
        # 绘制标题
        title = "会议活动安排表"
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (width - title_width) // 2
        draw.text((title_x, 30), title, fill='#000000', font=title_font)
        
        # 绘制表格
        table_x, table_y = 50, 80
        row_height = 40
        col_widths = [150, 200, 200, 150]
        
        # 表头
        headers = ["时间", "活动内容", "地点", "参与人员"]
        x_offset = table_x
        for i, header in enumerate(headers):
            draw.rectangle([x_offset, table_y, x_offset + col_widths[i], table_y + row_height], 
                         outline='#000000', fill='#F5F5F5')
            draw.text((x_offset + 10, table_y + 10), header, fill='#000000', font=content_font)
            x_offset += col_widths[i]
        
        # 表格数据
        for row_idx, data in enumerate(schedule_data[:10]):  # 限制行数
            y_pos = table_y + (row_idx + 1) * row_height
            x_offset = table_x
            
            # 简单解析数据
            data_parts = data.split()[:4]  # 取前4个部分作为列数据
            while len(data_parts) < 4:
                data_parts.append("")
            
            for col_idx, cell_data in enumerate(data_parts):
                draw.rectangle([x_offset, y_pos, x_offset + col_widths[col_idx], y_pos + row_height], 
                             outline='#000000', fill='#FFFFFF')
                # 截断过长的文本
                cell_text = cell_data[:15] + "..." if len(cell_data) > 15 else cell_data
                draw.text((x_offset + 5, y_pos + 10), cell_text, fill='#333333', font=content_font)
                x_offset += col_widths[col_idx]
        
        # 应用质量效果
        img = self.apply_quality_effects(img, quality)
        
        return img
    
    def draw_multiline_text(self, draw, text: str, x: int, y: int, font, fill: str, max_width: int):
        """绘制多行文本"""
        words = text.split()
        lines = []
        current_line = []
        
        for word in words:
            test_line = ' '.join(current_line + [word])
            try:
                bbox = draw.textbbox((0, 0), test_line, font=font)
                if bbox[2] - bbox[0] <= max_width:
                    current_line.append(word)
                else:
                    if current_line:
                        lines.append(' '.join(current_line))
                        current_line = [word]
                    else:
                        lines.append(word)
            except:
                # 如果textbbox不可用，使用简单的字符计数
                if len(test_line) * 8 <= max_width:
                    current_line.append(word)
                else:
                    if current_line:
                        lines.append(' '.join(current_line))
                        current_line = [word]
                    else:
                        lines.append(word)
        
        if current_line:
            lines.append(' '.join(current_line))
        
        for i, line in enumerate(lines):
            draw.text((x, y + i * 20), line, fill=fill, font=font)
    
    def apply_quality_effects(self, img: Image.Image, quality: str) -> Image.Image:
        """应用质量效果"""
        if quality == "high":
            return img
        elif quality == "medium":
            # 轻微模糊和压缩
            img = img.filter(ImageFilter.GaussianBlur(radius=0.5))
            return img
        elif quality == "low":
            # 重度模糊、噪声、低分辨率
            # 先缩小再放大模拟低分辨率
            small_size = (img.width // 2, img.height // 2)
            img = img.resize(small_size, Image.Resampling.LANCZOS)
            img = img.resize((img.width * 2, img.height * 2), Image.Resampling.NEAREST)
            
            # 添加模糊
            img = img.filter(ImageFilter.GaussianBlur(radius=1.5))
            
            return img
        
        return img
    
    def generate_all_test_images(self):
        """生成所有测试图片"""
        print("开始生成OCR测试图片...")
        
        # 加载真实内容
        self.load_real_content()
        
        # 生成微信截图类
        self.generate_wechat_images()
        
        # 生成政务文档类
        self.generate_official_document_images()
        
        # 生成会议安排类
        self.generate_meeting_schedule_images()
        
        # 生成质量变化测试图片
        self.generate_quality_test_images()
        
        # 保存测试数据映射
        self.save_test_data_mapping()
        
        print(f"测试图片生成完成！输出目录：{self.output_path}")
    
    def generate_wechat_images(self):
        """生成微信截图类图片"""
        print("生成微信截图类图片...")
        
        # 基于真实培训通知内容生成微信消息
        training_notices = self.real_content.get("学习培训类通知", [])
        
        wechat_scenarios = [
            {
                "filename": "wechat_meeting.png",
                "text": "明天上午9:30在三楼会议室召开季度总结会，请各部门负责人准时参加。",
                "sender": "办公室主任",
                "keywords": ["明天", "上午", "9:30", "会议室", "季度总结会", "部门负责人", "参加"]
            },
            {
                "filename": "wechat_task.png", 
                "text": "紧急任务：请各部门负责人于今日下午5点前提交本月工作总结报告",
                "sender": "人事部",
                "keywords": ["紧急任务", "部门负责人", "今日", "下午", "5点", "工作总结", "报告"]
            },
            {
                "filename": "wechat_notice.png",
                "text": "通知：根据上级要求，本周五下午组织全体员工进行安全培训",
                "sender": "安全办",
                "keywords": ["通知", "上级要求", "周五", "下午", "全体员工", "安全培训"]
            }
        ]
        
        # 如果有真实培训通知，使用真实内容
        if training_notices:
            for i, notice in enumerate(training_notices[:3]):
                if i < len(wechat_scenarios):
                    # 提取关键信息作为微信消息
                    content_lines = notice["content"].split('\n')
                    short_content = ""
                    for line in content_lines:
                        if "通知" in line or "培训" in line or "会议" in line:
                            short_content = line[:50]  # 截取前50字符
                            break
                    
                    if short_content:
                        wechat_scenarios[i]["text"] = short_content
                        wechat_scenarios[i]["real_source"] = notice["filename"]
        
        for scenario in wechat_scenarios:
            img = self.create_wechat_screenshot(
                scenario["text"], 
                scenario["sender"]
            )
            
            output_path = self.output_path / scenario["filename"]
            img.save(output_path, "PNG")
            print(f"✓ 生成微信截图：{scenario['filename']}")
            
            # 记录测试数据
            self.test_data_mapping[scenario["filename"]] = {
                "expectedText": scenario["text"],
                "expectedKeywords": scenario["keywords"],
                "minConfidence": 0.8,
                "scenario": "wechat_screenshot",
                "realSource": scenario.get("real_source", "模拟数据")
            }
    
    def generate_official_document_images(self):
        """生成政务文档类图片"""
        print("生成政务文档类图片...")
        
        # 使用真实制度类文档
        zhidu_docs = self.real_content.get("制度类", [])
        
        official_scenarios = [
            {
                "filename": "official_notice.png",
                "title": "关于加强公文办理规范性的通知",
                "content": "各部门：\n为进一步规范公文办理流程，提高工作效率，现就有关事项通知如下：\n一、严格按照公文格式要求...",
                "keywords": ["关于", "加强", "公文办理", "规范性", "通知"]
            },
            {
                "filename": "meeting_minutes.png",
                "title": "会议纪要",
                "content": f"时间：{datetime.datetime.now().strftime('%Y年%m月%d日')}上午\n地点：会议室A\n参会人员：各部门负责人\n会议内容：...",
                "keywords": ["会议纪要", "时间", "2024年", "地点", "会议室A"]
            },
            {
                "filename": "work_plan.png",
                "title": "2024年第一季度工作计划",
                "content": "一、重点工作目标\n1. 完善制度建设\n2. 提升服务质量\n3. 加强队伍建设\n二、具体措施...",
                "keywords": ["2024年", "第一季度", "工作计划", "重点工作", "目标"]
            }
        ]
        
        # 如果有真实制度文档，使用真实内容
        if zhidu_docs:
            for i, doc in enumerate(zhidu_docs[:3]):
                if i < len(official_scenarios):
                    official_scenarios[i]["title"] = doc["title"]
                    official_scenarios[i]["content"] = doc["content"][:500]  # 截取前500字符
                    official_scenarios[i]["real_source"] = doc["filename"]
        
        for scenario in official_scenarios:
            img = self.create_official_document(
                scenario["title"],
                scenario["content"]
            )
            
            output_path = self.output_path / scenario["filename"]
            img.save(output_path, "PNG")
            print(f"✓ 生成政务文档：{scenario['filename']}")
            
            # 记录测试数据
            self.test_data_mapping[scenario["filename"]] = {
                "expectedText": f"{scenario['title']}\n{scenario['content'][:100]}...",
                "expectedKeywords": scenario["keywords"],
                "minConfidence": 0.85,
                "scenario": "official_document",
                "realSource": scenario.get("real_source", "模拟数据")
            }
    
    def generate_meeting_schedule_images(self):
        """生成会议安排类图片"""
        print("生成会议安排类图片...")
        
        # 使用真实会议安排数据
        meeting_data = self.real_content.get("领导会议活动预安排", [])
        
        if meeting_data:
            schedule_list = [item["content"] for item in meeting_data[:10]]
        else:
            # 模拟数据
            schedule_list = [
                "9:00 部门例会 会议室A 全体员工",
                "14:00 项目评审 会议室B 项目组",
                "16:00 培训讲座 大会议室 相关人员"
            ]
        
        img = self.create_meeting_schedule(schedule_list)
        
        output_path = self.output_path / "meeting_schedule.png"
        img.save(output_path, "PNG")
        print(f"✓ 生成会议安排：meeting_schedule.png")
        
        # 记录测试数据
        self.test_data_mapping["meeting_schedule.png"] = {
            "expectedText": "会议活动安排表\n" + "\n".join(schedule_list[:5]),
            "expectedKeywords": ["会议", "安排", "时间", "地点", "活动"],
            "minConfidence": 0.8,
            "scenario": "meeting_schedule",
            "realSource": "真实会议安排数据" if meeting_data else "模拟数据"
        }
    
    def generate_quality_test_images(self):
        """生成不同质量的测试图片"""
        print("生成质量测试图片...")
        
        test_text = "这是一段用于测试OCR识别准确率的中文文本。包含数字123和英文ABC。"
        
        qualities = ["high", "medium", "low"]
        for quality in qualities:
            img = self.create_wechat_screenshot(test_text, "测试群", quality)
            
            output_path = self.output_path / f"{quality}_quality.png"
            img.save(output_path, "PNG")
            print(f"✓ 生成质量测试图片：{quality}_quality.png")
            
            # 记录测试数据
            self.test_data_mapping[f"{quality}_quality.png"] = {
                "expectedText": test_text,
                "expectedKeywords": ["测试", "OCR", "识别", "准确率", "中文", "数字", "英文"],
                "minConfidence": 0.9 if quality == "high" else (0.7 if quality == "medium" else 0.5),
                "scenario": f"quality_test_{quality}",
                "realSource": "质量测试数据"
            }
    
    def save_test_data_mapping(self):
        """保存测试数据映射"""
        mapping_file = self.assets_path / "test-images" / "test_data_mapping.json"
        
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_data_mapping, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 测试数据映射已保存到：{mapping_file}")

def main():
    """主函数"""
    # 获取assets目录路径
    current_dir = Path(__file__).parent
    assets_path = current_dir.parent.parent
    
    print(f"Assets路径：{assets_path}")
    
    # 创建生成器
    generator = TestImageGenerator(str(assets_path))
    
    # 生成所有测试图片
    generator.generate_all_test_images()

if __name__ == "__main__":
    main() 