#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查生成的OCR测试图片
"""

import os
from pathlib import Path
from PIL import Image
import json

def check_images():
    """检查生成的图片"""
    current_dir = Path(__file__).parent
    generated_dir = current_dir.parent / "generated"
    mapping_file = current_dir.parent / "enhanced_test_data_mapping.json"
    
    print("🔍 检查增强版OCR测试图片")
    print("=" * 50)
    
    # 读取测试数据映射
    if mapping_file.exists():
        with open(mapping_file, 'r', encoding='utf-8') as f:
            mapping = json.load(f)
        print(f"✓ 测试数据映射文件：{len(mapping)} 个条目")
    else:
        print("❌ 未找到测试数据映射文件")
        return
    
    # 检查生成的图片
    enhanced_images = []
    for file_path in generated_dir.glob("enhanced_*.png"):
        enhanced_images.append(file_path)
    
    enhanced_images.extend(generated_dir.glob("wechat_*.png"))
    enhanced_images.extend(generated_dir.glob("official_*.png"))
    enhanced_images.extend(generated_dir.glob("real_*.png"))
    
    print(f"\n📷 发现 {len(enhanced_images)} 个增强版图片：")
    
    for img_path in sorted(enhanced_images):
        try:
            img = Image.open(img_path)
            file_size = img_path.stat().st_size
            
            # 获取测试数据信息
            img_info = mapping.get(img_path.name, {})
            scenario = img_info.get("scenario", "未知")
            real_source = img_info.get("realSource", "未知")
            
            print(f"\n📄 {img_path.name}")
            print(f"   尺寸: {img.width}x{img.height}")
            print(f"   大小: {file_size/1024:.1f}KB")
            print(f"   场景: {scenario}")
            print(f"   数据源: {real_source}")
            
            # 检查是否为增强版
            if "enhanced" in img_path.name or "wechat" in img_path.name or "official" in img_path.name:
                print(f"   ✓ 增强版图片（支持中文字体和格式化）")
            elif "real" in img_path.name:
                print(f"   ✓ 真实图片（已整合到测试集）")
            else:
                print(f"   ⚠️  旧版图片")
                
        except Exception as e:
            print(f"❌ 无法读取图片 {img_path.name}: {e}")
    
    print(f"\n📊 总结：")
    print(f"   - 总图片数量: {len(enhanced_images)}")
    print(f"   - 增强版图片: {len([p for p in enhanced_images if 'enhanced' in p.name or 'wechat' in p.name or 'official' in p.name])}")
    print(f"   - 真实图片: {len([p for p in enhanced_images if 'real' in p.name])}")
    print(f"   - 测试数据映射: {len(mapping)} 个条目")
    
    print(f"\n✅ 图片检查完成！")
    print(f"📁 图片目录: {generated_dir}")
    print(f"📄 数据映射: {mapping_file}")

if __name__ == "__main__":
    check_images() 