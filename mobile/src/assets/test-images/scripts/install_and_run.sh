#!/bin/bash

# 公职猫OCR测试图片生成器 - 自动安装和运行脚本

echo "=== 公职猫OCR测试图片生成器 ==="
echo "正在准备运行环境..."

# 检查Python3是否可用
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误：未找到python3，请先安装Python 3"
    exit 1
fi

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 安装依赖包
echo "📥 安装依赖包..."
pip install --quiet Pillow python-docx pandas openpyxl xlrd

# 运行生成器
echo "🚀 运行测试图片生成器..."
python run.py

echo ""
echo "✅ 完成！生成的文件位置："
echo "📁 测试图片：mobile/src/assets/test-images/generated/"
echo "📄 数据映射：mobile/src/assets/test-images/test_data_mapping.json"
echo ""
echo "💡 提示：如需重新生成，直接运行此脚本即可" 