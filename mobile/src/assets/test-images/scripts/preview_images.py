#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR测试图片预览工具
检查文本布局、换行、表格等问题
"""

import os
from pathlib import Path
from PIL import Image
import json

def analyze_image_layout(img_path: Path):
    """分析图片布局"""
    try:
        img = Image.open(img_path)
        width, height = img.size
        file_size = img_path.stat().st_size
        
        # 基本信息
        info = {
            "filename": img_path.name,
            "size": f"{width}x{height}",
            "file_size": f"{file_size/1024:.1f}KB",
            "aspect_ratio": f"{width/height:.2f}"
        }
        
        # 布局检查
        layout_issues = []
        
        # 检查图片尺寸合理性
        if width > 1000 or height > 1000:
            layout_issues.append("图片尺寸较大，可能影响加载速度")
        
        if width < 300 or height < 200:
            layout_issues.append("图片尺寸较小，可能影响文字清晰度")
        
        # 检查宽高比
        if width / height > 3:
            layout_issues.append("图片过宽，可能存在文字排版问题")
        elif height / width > 3:
            layout_issues.append("图片过高，可能存在文字排版问题")
        
        info["layout_issues"] = layout_issues
        return info
        
    except Exception as e:
        return {"filename": img_path.name, "error": str(e)}

def preview_enhanced_images():
    """预览增强版图片"""
    current_dir = Path(__file__).parent
    generated_dir = current_dir.parent / "generated"
    mapping_file = current_dir.parent / "enhanced_test_data_mapping.json"
    
    print("🖼️  OCR测试图片预览")
    print("=" * 60)
    
    # 读取测试数据映射
    mapping = {}
    if mapping_file.exists():
        with open(mapping_file, 'r', encoding='utf-8') as f:
            mapping = json.load(f)
    
    # 按类型分组
    image_groups = {
        "微信截图类": [],
        "政务文档类": [],
        "会议安排类": [],
        "质量测试类": [],
        "真实图片类": []
    }
    
    # 分析所有图片
    for img_path in generated_dir.glob("*.png"):
        info = analyze_image_layout(img_path)
        
        # 获取测试数据
        test_data = mapping.get(img_path.name, {})
        info["scenario"] = test_data.get("scenario", "未知")
        info["real_source"] = test_data.get("realSource", "未知")
        
        # 分类
        if "wechat" in img_path.name:
            image_groups["微信截图类"].append(info)
        elif "official" in img_path.name:
            image_groups["政务文档类"].append(info)
        elif "meeting" in img_path.name:
            image_groups["会议安排类"].append(info)
        elif "enhanced" in img_path.name and "quality" in img_path.name:
            image_groups["质量测试类"].append(info)
        elif "real" in img_path.name:
            image_groups["真实图片类"].append(info)
    
    # 显示分组结果
    total_images = 0
    total_issues = 0
    
    for group_name, images in image_groups.items():
        if not images:
            continue
            
        print(f"\n📁 {group_name} ({len(images)}张)")
        print("-" * 40)
        
        for info in images:
            total_images += 1
            
            print(f"📄 {info['filename']}")
            print(f"   尺寸: {info['size']} | 大小: {info['file_size']} | 宽高比: {info['aspect_ratio']}")
            print(f"   场景: {info['scenario']}")
            print(f"   数据源: {info['real_source']}")
            
            if info.get("layout_issues"):
                total_issues += len(info["layout_issues"])
                print(f"   ⚠️  布局问题:")
                for issue in info["layout_issues"]:
                    print(f"      - {issue}")
            else:
                print(f"   ✅ 布局正常")
            
            if info.get("error"):
                print(f"   ❌ 错误: {info['error']}")
            
            print()
    
    # 总结
    print("📊 预览总结")
    print("=" * 60)
    print(f"总图片数量: {total_images}")
    print(f"布局问题数: {total_issues}")
    
    if total_issues == 0:
        print("🎉 所有图片布局正常！")
    else:
        print(f"⚠️  发现 {total_issues} 个布局问题，建议检查")
    
    print(f"\n📁 图片目录: {generated_dir}")
    print(f"📄 数据映射: {mapping_file}")
    
    # 推荐的测试图片
    print(f"\n🎯 推荐OCR测试图片:")
    recommendations = [
        ("wechat_meeting_notice.png", "微信截图测试"),
        ("official_system_notice.png", "政务文档测试"),
        ("enhanced_meeting_schedule.png", "表格数据测试"),
        ("enhanced_high_quality.png", "高质量文本测试"),
        ("enhanced_low_quality.png", "低质量适应性测试"),
        ("real_screenshot_1.png", "真实场景验证")
    ]
    
    for filename, description in recommendations:
        if (generated_dir / filename).exists():
            print(f"   ✓ {filename} - {description}")
        else:
            print(f"   ✗ {filename} - {description} (未找到)")

if __name__ == "__main__":
    preview_enhanced_images() 