#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公职猫OCR测试图片生成器 - 简化运行脚本
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """主函数"""
    print("=== 公职猫OCR测试图片生成器 ===")
    print()
    
    try:
        # 导入生成器
        from generate_test_images import TestImageGenerator
        
        # 获取assets目录路径
        assets_path = current_dir.parent.parent
        print(f"Assets路径：{assets_path}")
        
        # 创建生成器
        generator = TestImageGenerator(str(assets_path))
        
        # 生成所有测试图片
        generator.generate_all_test_images()
        
        print()
        print("✅ 测试图片生成完成！")
        print(f"📁 输出目录：{generator.output_path}")
        print("📄 测试数据映射：mobile/src/assets/test-images/test_data_mapping.json")
        
    except ImportError as e:
        print(f"❌ 导入错误：{e}")
        print("请确保已安装必要的依赖包：")
        print("pip install Pillow python-docx pandas openpyxl")
        
    except Exception as e:
        print(f"❌ 生成失败：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 