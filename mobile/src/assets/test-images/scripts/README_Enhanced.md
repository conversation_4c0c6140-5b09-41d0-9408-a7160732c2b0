# 公职猫OCR测试图片生成器 - 增强版

## 🎯 功能特点

### ✅ 已解决的问题
- **字体乱码问题**：自动检测并使用系统中文字体（苹方、黑体、微软雅黑等）
- **现有图片整合**：自动处理test-images目录下的现有图片，重命名并加入测试集
- **文本格式化**：支持换行、加粗、颜色标记、符号等真实格式
- **真实数据利用**：充分利用assets目录下的真实政务文档内容

### 🆕 增强功能
- **智能字体检测**：优先使用系统中文字体，确保中文显示正常
- **多层次格式化**：支持标题、正文、重点标记、列表等多种格式
- **真实内容融合**：基于真实的制度文档、培训通知、会议安排生成测试图片
- **质量分级测试**：高、中、低三种质量级别，模拟不同拍摄条件
- **现有资源整合**：自动处理并标准化命名现有测试图片

## 📁 文件结构

```
mobile/src/assets/test-images/
├── scripts/
│   ├── generate_test_images_v2.py    # 增强版生成器
│   ├── requirements_v2.txt           # 增强版依赖
│   ├── run_enhanced.py              # 增强版运行脚本
│   ├── check_images.py              # 图片检查工具
│   ├── venv_enhanced/               # 虚拟环境
│   └── README_Enhanced.md           # 本文档
├── generated/                       # 生成的测试图片
│   ├── enhanced_*.png              # 增强版新生成图片
│   ├── wechat_*.png               # 微信截图类
│   ├── official_*.png             # 政务文档类
│   └── real_*.png                 # 整合的真实图片
└── enhanced_test_data_mapping.json # 增强版测试数据映射
```

## 🚀 快速开始

### 方法1：一键运行（推荐）
```bash
cd mobile/src/assets/test-images/scripts
python3 run_enhanced.py
```

### 方法2：手动运行
```bash
cd mobile/src/assets/test-images/scripts

# 创建虚拟环境
python3 -m venv venv_enhanced
source venv_enhanced/bin/activate

# 安装依赖
pip install -r requirements_v2.txt

# 运行生成器
python generate_test_images_v2.py

# 检查结果
python check_images.py
```

## 📷 生成的图片类型

### 1. 微信截图类（3张）
- `wechat_meeting_notice.png` - 会议通知截图
- `wechat_urgent_task.png` - 紧急任务截图  
- `wechat_training_notice.png` - 培训通知截图

**特点**：
- 真实微信界面样式
- 支持消息气泡、时间戳
- 基于真实培训通知内容
- 支持文本格式化（加粗、颜色标记）

### 2. 政务文档类（1张）
- `official_system_notice.png` - 制度通知文档

**特点**：
- 标准公文格式
- 页眉页脚、标题居中
- 条款编号、段落缩进
- 基于真实制度文档内容

### 3. 会议安排类（1张）
- `enhanced_meeting_schedule.png` - 会议活动安排表

**特点**：
- 标准表格格式
- 时间、内容、地点、人员四列
- 基于真实会议安排数据
- 交替行颜色、重点标记

### 4. 质量测试类（3张）
- `enhanced_high_quality.png` - 高质量测试
- `enhanced_medium_quality.png` - 中等质量测试
- `enhanced_low_quality.png` - 低质量测试

**特点**：
- 不同模糊程度和压缩质量
- 包含数字、英文、特殊符号
- 用于测试OCR在不同质量下的表现

### 5. 真实图片类（5张）
- `real_screenshot_*.png` - 真实截图（3张）
- `real_document_*.png` - 真实文档（2张）

**特点**：
- 来源于您提供的真实测试图片
- 标准化命名和分类
- 保留原始质量和内容

## 📊 测试数据映射

`enhanced_test_data_mapping.json` 包含每张图片的详细信息：

```json
{
  "图片文件名": {
    "expectedText": "预期识别的文本内容",
    "expectedKeywords": ["关键词1", "关键词2"],
    "minConfidence": 0.8,
    "scenario": "测试场景类型",
    "realSource": "真实数据来源"
  }
}
```

## 🔧 技术特点

### 字体处理
- 自动检测系统中文字体
- 支持 macOS（苹方、黑体）、Windows（微软雅黑、宋体）、Linux（文泉驿）
- 字体加载失败时优雅降级

### 文本格式化
- **标题**：居中、加粗、下划线
- **重点内容**：红色标记、加粗显示
- **列表项**：缩进、编号
- **表格**：边框、交替行颜色
- **换行处理**：自动文本换行

### 质量控制
- **高质量**：原始清晰度
- **中等质量**：轻微模糊、对比度降低
- **低质量**：重度模糊、分辨率降低、亮度调整

### 真实数据集成
- 读取 `.docx` 制度文档
- 处理 `.doc` 培训通知（容错机制）
- 解析 `.xls/.xlsx` 会议安排
- 自动提取标题和关键内容

## 🎯 OCR测试建议

### 测试场景覆盖
1. **微信截图识别**：测试移动端截图OCR能力
2. **政务文档识别**：测试正式文档格式识别
3. **表格数据识别**：测试结构化数据提取
4. **质量适应性**：测试不同图片质量下的识别率
5. **真实场景验证**：使用真实用户数据验证

### 评估指标
- **文本准确率**：与expectedText的匹配度
- **关键词识别**：expectedKeywords的识别率
- **置信度阈值**：达到minConfidence的比例
- **场景适应性**：不同scenario下的表现

## 🔍 检查和验证

运行检查脚本查看生成结果：
```bash
python check_images.py
```

输出信息包括：
- 图片尺寸和文件大小
- 测试场景分类
- 数据来源标识
- 增强版特性确认

## 📝 更新日志

### V2.0 增强版
- ✅ 解决字体乱码问题
- ✅ 整合现有测试图片
- ✅ 支持文本格式化
- ✅ 基于真实数据生成
- ✅ 改进质量控制
- ✅ 完善测试数据映射

### V1.0 基础版
- ✅ 基本图片生成功能
- ✅ 简单文本渲染
- ✅ 基础测试场景

## 🤝 使用建议

1. **首次使用**：运行 `python3 run_enhanced.py` 一键生成
2. **定期更新**：当有新的真实文档时重新生成
3. **质量检查**：使用 `check_images.py` 验证生成结果
4. **OCR测试**：使用 `enhanced_test_data_mapping.json` 进行自动化测试
5. **问题反馈**：如遇字体或格式问题，检查系统字体安装情况

## 📞 技术支持

如果遇到问题：
1. 检查Python版本（需要3.7+）
2. 确认虚拟环境正确激活
3. 验证依赖包安装完整
4. 检查系统中文字体可用性
5. 查看错误日志定位问题 