#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版OCR测试图片生成器运行脚本
解决字体问题，整合现有图片，支持更好的文本格式化
"""

import subprocess
import sys
import os
from pathlib import Path

def install_dependencies():
    """安装依赖包"""
    print("📦 安装依赖包...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "-r", "requirements_v2.txt"
        ])
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def run_generator():
    """运行增强版生成器"""
    print("🚀 启动增强版OCR测试图片生成器...")
    try:
        subprocess.check_call([sys.executable, "generate_test_images_v2.py"])
        print("✅ 增强版测试图片生成完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 生成器运行失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 增强版OCR测试图片生成器")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 无法安装依赖包，请手动安装")
        sys.exit(1)
    
    # 运行生成器
    if not run_generator():
        print("❌ 生成器运行失败")
        sys.exit(1)
    
    print("\n🎉 增强版OCR测试图片生成完成！")
    print("📁 输出目录：mobile/src/assets/test-images/generated/")
    print("📄 测试数据映射：mobile/src/assets/test-images/enhanced_test_data_mapping.json")

if __name__ == "__main__":
    main() 