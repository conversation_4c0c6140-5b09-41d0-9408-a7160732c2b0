#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公职猫OCR测试图片生成器 - 安装和运行脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("依赖包安装完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"依赖包安装失败：{e}")
        return False

def check_dependencies():
    """检查依赖包是否已安装"""
    required_packages = [
        "PIL",
        "docx", 
        "pandas",
        "openpyxl"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == "PIL":
                import PIL
            elif package == "docx":
                import docx
            elif package == "pandas":
                import pandas
            elif package == "openpyxl":
                import openpyxl
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def main():
    """主函数"""
    print("=== 公职猫OCR测试图片生成器 ===")
    print()
    
    # 检查依赖
    missing = check_dependencies()
    if missing:
        print(f"缺少依赖包：{', '.join(missing)}")
        print("正在自动安装...")
        if not install_dependencies():
            print("请手动安装依赖包后重试")
            return
    else:
        print("依赖包检查通过！")
    
    # 运行生成器
    print()
    print("开始生成测试图片...")
    
    try:
        from generate_test_images import main as generate_main
        generate_main()
        print()
        print("✅ 测试图片生成完成！")
        print("📁 输出目录：mobile/src/assets/test-images/generated/")
        print("📄 测试数据映射：mobile/src/assets/test-images/test_data_mapping.json")
    except Exception as e:
        print(f"❌ 生成失败：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 