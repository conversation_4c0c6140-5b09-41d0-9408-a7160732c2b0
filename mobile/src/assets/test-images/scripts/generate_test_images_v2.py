#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公职猫OCR测试图片生成器 V2.0
改进版：解决字体问题，整合现有图片，支持更好的文本格式化
"""

import os
import sys
import json
import shutil
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
import random
import datetime
from typing import Dict, List, Tuple
import urllib.request
import tempfile

class TestImageGeneratorV2:
    def __init__(self, assets_path: str):
        self.assets_path = Path(assets_path)
        self.test_images_path = self.assets_path / "test-images"
        self.output_path = self.test_images_path / "generated"
        self.scripts_path = self.test_images_path / "scripts"
        self.fonts_path = self.scripts_path / "fonts"
        
        # 创建必要目录
        self.output_path.mkdir(exist_ok=True)
        self.fonts_path.mkdir(exist_ok=True)
        
        # 测试数据映射
        self.test_data_mapping = {}
        
        # 真实文档内容缓存
        self.real_content = {
            "制度类": [],
            "学习培训类通知": [],
            "领导会议活动预安排": []
        }
        
        # 现有测试图片
        self.existing_images = []
        
        # 字体缓存
        self.fonts = {}
        
    def setup_fonts(self):
        """设置和下载中文字体"""
        print("🔤 设置中文字体...")
        
        # 尝试系统字体
        system_fonts = [
            "/System/Library/Fonts/PingFang.ttc",  # macOS 苹方
            "/System/Library/Fonts/STHeiti Light.ttc",  # macOS 黑体
            "/System/Library/Fonts/Hiragino Sans GB.ttc",  # macOS 冬青黑体
            "C:/Windows/Fonts/msyh.ttc",  # Windows 微软雅黑
            "C:/Windows/Fonts/simsun.ttc",  # Windows 宋体
            "C:/Windows/Fonts/simhei.ttf",  # Windows 黑体
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",  # Linux 文泉驿
        ]
        
        found_font = None
        for font_path in system_fonts:
            if os.path.exists(font_path):
                found_font = font_path
                print(f"✓ 找到系统字体：{font_path}")
                break
        
        if found_font:
            try:
                # 加载不同大小的字体
                self.fonts = {
                    'title': ImageFont.truetype(found_font, 24),
                    'subtitle': ImageFont.truetype(found_font, 20),
                    'content': ImageFont.truetype(found_font, 16),
                    'small': ImageFont.truetype(found_font, 12),
                    'large': ImageFont.truetype(found_font, 28)
                }
                print("✓ 字体加载成功")
                return True
            except Exception as e:
                print(f"✗ 字体加载失败：{e}")
        
        # 如果没有找到合适字体，使用默认字体
        print("⚠️  使用默认字体（可能不支持中文）")
        default_font = ImageFont.load_default()
        self.fonts = {
            'title': default_font,
            'subtitle': default_font,
            'content': default_font,
            'small': default_font,
            'large': default_font
        }
        return False
    
    def load_existing_images(self):
        """加载现有的测试图片"""
        print("📷 分析现有测试图片...")
        
        image_extensions = ['.png', '.jpg', '.jpeg']
        for file_path in self.test_images_path.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                # 跳过generated目录中的文件
                if 'generated' not in str(file_path):
                    self.existing_images.append({
                        'path': file_path,
                        'name': file_path.name,
                        'size': file_path.stat().st_size
                    })
                    print(f"✓ 发现测试图片：{file_path.name}")
    
    def process_existing_images(self):
        """处理现有图片，重命名并添加到测试数据"""
        print("🔄 处理现有测试图片...")
        
        for i, img_info in enumerate(self.existing_images):
            original_path = img_info['path']
            
            # 生成新的标准化文件名
            if 'image' in img_info['name'].lower():
                new_name = f"real_document_{i+1}.png"
                scenario = "real_document"
                keywords = ["真实", "文档", "扫描", "图片"]
            elif any(char in img_info['name'] for char in ['B4CD', '9AF1', '75BD']):
                new_name = f"real_screenshot_{i+1}.png"
                scenario = "real_screenshot"
                keywords = ["真实", "截图", "手机", "应用"]
            else:
                new_name = f"real_mixed_{i+1}.png"
                scenario = "real_mixed"
                keywords = ["真实", "混合", "内容"]
            
            # 复制到generated目录
            new_path = self.output_path / new_name
            try:
                shutil.copy2(original_path, new_path)
                print(f"✓ 处理图片：{img_info['name']} -> {new_name}")
                
                # 添加到测试数据映射
                self.test_data_mapping[new_name] = {
                    "expectedText": "真实图片内容，需要OCR识别",
                    "expectedKeywords": keywords,
                    "minConfidence": 0.6,  # 真实图片可能质量不一
                    "scenario": scenario,
                    "realSource": f"原始文件：{img_info['name']}",
                    "originalSize": img_info['size']
                }
                
            except Exception as e:
                print(f"✗ 处理图片失败 {img_info['name']}: {e}")
    
    def load_real_content(self):
        """加载真实文档内容"""
        print("📄 正在分析真实文档内容...")
        
        # 分析制度类文档
        zhidu_path = self.assets_path / "制度类"
        if zhidu_path.exists():
            for file_path in zhidu_path.glob("*.docx"):
                try:
                    content = self.extract_docx_content(file_path)
                    if content:
                        self.real_content["制度类"].append({
                            "filename": file_path.name,
                            "content": content,
                            "title": self.extract_title(content)
                        })
                        print(f"✓ 读取制度文档：{file_path.name}")
                except Exception as e:
                    print(f"✗ 读取文档 {file_path.name} 失败: {e}")
        
        # 分析学习培训类通知
        training_path = self.assets_path / "学习培训类通知"
        if training_path.exists():
            for file_path in training_path.glob("*.doc*"):
                try:
                    content = self.extract_docx_content(file_path)
                    if content:
                        self.real_content["学习培训类通知"].append({
                            "filename": file_path.name,
                            "content": content,
                            "title": self.extract_title(content)
                        })
                        print(f"✓ 读取培训通知：{file_path.name}")
                except Exception as e:
                    print(f"✗ 读取文档 {file_path.name} 失败: {e}")
        
        # 分析会议活动安排
        meeting_path = self.assets_path / "领导会议活动预安排"
        if meeting_path.exists():
            for file_path in meeting_path.glob("*.xls*"):
                try:
                    content = self.extract_excel_content(file_path)
                    if content:
                        self.real_content["领导会议活动预安排"].extend(content)
                        print(f"✓ 读取会议安排：{file_path.name}")
                except Exception as e:
                    print(f"✗ 读取表格 {file_path.name} 失败: {e}")
    
    def extract_docx_content(self, file_path: Path) -> str:
        """提取docx文档内容"""
        try:
            import docx
            doc = docx.Document(file_path)
            content = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content.append(paragraph.text.strip())
            return "\n".join(content)
        except Exception as e:
            print(f"无法读取docx文件 {file_path}: {e}")
            return self.generate_mock_content_from_filename(file_path.name)
    
    def extract_excel_content(self, file_path: Path) -> List[Dict]:
        """提取Excel内容"""
        try:
            import pandas as pd
            df = pd.read_excel(file_path)
            content = []
            for _, row in df.iterrows():
                row_content = []
                for col in df.columns:
                    if pd.notna(row[col]):
                        row_content.append(str(row[col]))
                if row_content:
                    content.append({
                        "content": " ".join(row_content),
                        "type": "会议安排"
                    })
            return content
        except Exception as e:
            print(f"无法读取Excel文件 {file_path}: {e}")
            return [
                {"content": "上午9:00 部门例会 会议室A 全体员工", "type": "会议安排"},
                {"content": "下午2:00 项目评审 会议室B 项目组成员", "type": "会议安排"},
                {"content": "下午4:00 培训讲座 大会议室 相关人员", "type": "会议安排"}
            ]
    
    def generate_mock_content_from_filename(self, filename: str) -> str:
        """根据文件名生成模拟内容"""
        if "代表委员联络站" in filename:
            return """代表委员联络站工作制度

第一条 为规范代表委员联络站工作，提高服务质量，制定本制度。

第二条 联络站应当建立健全工作机制，完善服务功能。

第三条 定期组织代表委员开展活动，收集民意建议。

第四条 加强与群众的联系，及时反映社情民意。"""
        elif "培训" in filename:
            return f"""关于开展专项培训的通知

各相关部门：

为提升工作能力，决定组织专项培训活动。

时间：{datetime.datetime.now().strftime('%Y年%m月%d日')}
地点：培训中心
参加人员：相关工作人员

请各部门高度重视，确保相关人员准时参加。

特此通知。"""
        else:
            return f"这是关于{filename}的相关文档内容。"
    
    def extract_title(self, content: str) -> str:
        """从内容中提取标题"""
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line and len(line) < 50:
                return line
        return "文档标题"
    
    def add_emoji_to_text(self, text: str) -> str:
        """为文本添加emoji表情"""
        emoji_map = {
            "会议": "📅",
            "通知": "📢", 
            "培训": "📚",
            "学习": "📖",
            "重要": "⚠️",
            "紧急": "🚨",
            "时间": "⏰",
            "地点": "📍",
            "参加": "👥",
            "完成": "✅",
            "注意": "⚠️",
            "提醒": "🔔",
            "工作": "💼",
            "任务": "📋",
            "安排": "📝",
            "活动": "🎯"
        }
        
        # 随机添加emoji
        import random
        for keyword, emoji in emoji_map.items():
            if keyword in text and random.random() < 0.3:  # 30%概率添加emoji
                text = text.replace(keyword, f"{emoji}{keyword}", 1)
        
        return text

    def create_enhanced_wechat_screenshot(self, message_text: str, sender_name: str = "工作群", 
                                        quality: str = "high") -> Image.Image:
        """创建增强版微信截图，支持更好的文本格式化和emoji"""
        width, height = 375, 250
        
        # 为消息文本添加emoji
        message_text = self.add_emoji_to_text(message_text)
        
        # 创建画布
        img = Image.new('RGB', (width, height), color='#EDEDED')
        draw = ImageDraw.Draw(img)
        
        # 绘制状态栏
        draw.rectangle([0, 0, width, 25], fill='#000000')
        draw.text((10, 5), "中国移动", fill='#FFFFFF', font=self.fonts['small'])
        draw.text((width-60, 5), "100%", fill='#FFFFFF', font=self.fonts['small'])
        draw.text((width-30, 5), "🔋", fill='#FFFFFF', font=self.fonts['small'])
        
        # 绘制微信顶部栏
        draw.rectangle([0, 25, width, 70], fill='#393A3E')
        draw.text((15, 35), sender_name, fill='#FFFFFF', font=self.fonts['content'])
        draw.text((width-50, 35), "···", fill='#FFFFFF', font=self.fonts['content'])
        
        # 绘制发送者头像
        avatar_x, avatar_y = 15, 85
        draw.ellipse([avatar_x, avatar_y, avatar_x + 40, avatar_y + 40], fill='#4CAF50')
        draw.text((avatar_x + 15, avatar_y + 12), "办", fill='#FFFFFF', font=self.fonts['content'])
        
        # 绘制发送者名称
        draw.text((avatar_x + 50, avatar_y - 5), sender_name, fill='#666666', font=self.fonts['small'])
        
        # 绘制消息气泡
        bubble_x, bubble_y = avatar_x + 50, avatar_y + 15
        
        # 计算文本尺寸
        max_text_width = 200  # 限制最大文本宽度
        lines = self.wrap_text(message_text, self.fonts['content'], max_text_width)
        line_height = 20
        text_height = len([line for line in lines if line.strip()]) * line_height
        bubble_width = min(250, max_text_width + 20)
        bubble_height = text_height + 20
        
        # 绘制消息气泡背景
        self.draw_rounded_rectangle(draw, bubble_x, bubble_y, bubble_width, bubble_height, 
                                  radius=8, fill='#FFFFFF', outline='#DDDDDD')
        
        # 绘制消息文本（支持格式化）
        y_offset = bubble_y + 10
        line_height = 20  # 固定行高
        
        for line in lines:
            if not line.strip():  # 空行
                y_offset += line_height // 2
                continue
                
            # 检查是否包含特殊格式
            if '【' in line and '】' in line:
                # 加粗标题
                draw.text((bubble_x + 10, y_offset), line, fill='#000000', font=self.fonts['subtitle'])
            elif line.startswith('时间：') or line.startswith('地点：'):
                # 重要信息
                draw.text((bubble_x + 10, y_offset), line, fill='#E74C3C', font=self.fonts['content'])
            else:
                # 普通文本
                draw.text((bubble_x + 10, y_offset), line, fill='#333333', font=self.fonts['content'])
            y_offset += line_height
        
        # 绘制时间戳
        timestamp = datetime.datetime.now().strftime("%H:%M")
        draw.text((bubble_x, bubble_y + bubble_height + 5), timestamp, 
                 fill='#999999', font=self.fonts['small'])
        
        # 应用质量效果
        img = self.apply_quality_effects(img, quality)
        
        return img
    
    def create_enhanced_official_document(self, title: str, content: str, quality: str = "high") -> Image.Image:
        """创建增强版政务文档，支持更好的格式化"""
        width, height = 595, 842
        
        # 创建画布
        img = Image.new('RGB', (width, height), color='#FFFFFF')
        draw = ImageDraw.Draw(img)
        
        # 绘制页眉
        draw.rectangle([0, 0, width, 50], fill='#F8F9FA')
        draw.line([(0, 50), (width, 50)], fill='#DEE2E6', width=2)
        
        # 绘制标题
        title_y = 80
        title_width = self.get_text_width(title, self.fonts['title'])
        title_x = (width - title_width) // 2
        draw.text((title_x, title_y), title, fill='#000000', font=self.fonts['title'])
        
        # 绘制标题下划线
        draw.line([(title_x, title_y + 35), (title_x + title_width, title_y + 35)], 
                 fill='#007BFF', width=3)
        
        # 绘制正文内容（修复重叠问题）
        content_lines = content.split('\n')
        y_offset = 130  # 减少顶部间距
        max_content_width = width - 120  # 左右各留60像素边距
        line_height = 30  # 增加行高避免重叠
        
        for line in content_lines[:18]:  # 减少行数避免超出
            line = line.strip()
            if not line:
                y_offset += line_height // 2
                continue
            
            # 检查是否会超出页面
            if y_offset > height - 150:  # 增加底部边距
                break
                
            # 根据内容类型选择格式
            if line.startswith('第') and ('条' in line or '章' in line):
                # 条款标题
                wrapped_lines = self.wrap_text(line, self.fonts['subtitle'], max_content_width - 20)
                for wrapped_line in wrapped_lines:
                    draw.text((80, y_offset), wrapped_line, fill='#DC3545', font=self.fonts['subtitle'])
                    y_offset += line_height
                y_offset += 10  # 增加额外间距
            elif line.startswith('一、') or line.startswith('二、') or line.startswith('三、'):
                # 大标题
                wrapped_lines = self.wrap_text(line, self.fonts['subtitle'], max_content_width)
                for wrapped_line in wrapped_lines:
                    draw.text((60, y_offset), wrapped_line, fill='#28A745', font=self.fonts['subtitle'])
                    y_offset += line_height
                y_offset += 10  # 增加额外间距
            elif line.startswith('1.') or line.startswith('2.') or line.startswith('3.'):
                # 列表项
                wrapped_lines = self.wrap_text(line, self.fonts['content'], max_content_width - 40)
                for wrapped_line in wrapped_lines:
                    draw.text((100, y_offset), wrapped_line, fill='#6C757D', font=self.fonts['content'])
                    y_offset += line_height
                y_offset += 5  # 列表项间距
            elif '：' in line:
                # 包含冒号的重要信息
                parts = line.split('：', 1)
                if len(parts) == 2:
                    label = parts[0] + '：'
                    content_part = parts[1]
                    
                    # 绘制标签部分
                    draw.text((60, y_offset), label, fill='#495057', font=self.fonts['subtitle'])
                    label_width = self.get_text_width(label, self.fonts['subtitle'])
                    
                    # 绘制内容部分，考虑换行
                    remaining_width = max_content_width - label_width
                    if remaining_width > 150:  # 提高剩余宽度要求
                        content_lines_wrapped = self.wrap_text(content_part, self.fonts['content'], remaining_width)
                        for i, content_line in enumerate(content_lines_wrapped):
                            if i == 0:
                                draw.text((60 + label_width, y_offset), content_line, fill='#333333', font=self.fonts['content'])
                            else:
                                y_offset += line_height
                                draw.text((80, y_offset), content_line, fill='#333333', font=self.fonts['content'])  # 缩进对齐
                    else:  # 换行显示
                        y_offset += line_height
                        content_lines_wrapped = self.wrap_text(content_part, self.fonts['content'], max_content_width - 20)
                        for content_line in content_lines_wrapped:
                            draw.text((80, y_offset), content_line, fill='#333333', font=self.fonts['content'])
                            y_offset += line_height
                        y_offset -= line_height  # 调整，因为下面还会加一次
                else:
                    wrapped_lines = self.wrap_text(line, self.fonts['content'], max_content_width)
                    for wrapped_line in wrapped_lines:
                        draw.text((60, y_offset), wrapped_line, fill='#333333', font=self.fonts['content'])
                        y_offset += line_height
                y_offset += line_height + 8  # 增加段落间距
            else:
                # 普通段落
                wrapped_lines = self.wrap_text(line, self.fonts['content'], max_content_width)
                for wrapped_line in wrapped_lines:
                    draw.text((60, y_offset), wrapped_line, fill='#333333', font=self.fonts['content'])
                    y_offset += line_height
                y_offset += 10  # 增加段落间距
        
        # 绘制页脚
        footer_y = height - 80
        draw.line([(50, footer_y), (width - 50, footer_y)], fill='#DEE2E6', width=1)
        
        footer_text = f"生成时间：{datetime.datetime.now().strftime('%Y年%m月%d日')}"
        draw.text((60, footer_y + 10), footer_text, fill='#6C757D', font=self.fonts['small'])
        
        # 页码
        page_text = "第 1 页"
        page_width = self.get_text_width(page_text, self.fonts['small'])
        draw.text((width - 60 - page_width, footer_y + 10), page_text, fill='#6C757D', font=self.fonts['small'])
        
        # 应用质量效果
        img = self.apply_quality_effects(img, quality)
        
        return img
    
    def wrap_text(self, text: str, font, max_width: int) -> List[str]:
        """改进的文本换行处理"""
        if not text.strip():
            return ['']
        
        lines = []
        # 先按换行符分割
        paragraphs = text.split('\n')
        
        for paragraph in paragraphs:
            if not paragraph.strip():
                lines.append('')
                continue
                
            # 对于中文文本，按字符处理
            if any('\u4e00' <= char <= '\u9fff' for char in paragraph):
                current_line = ''
                for char in paragraph:
                    test_line = current_line + char
                    if self.get_text_width(test_line, font) <= max_width:
                        current_line = test_line
                    else:
                        if current_line:
                            lines.append(current_line)
                            current_line = char
                        else:
                            lines.append(char)
                if current_line:
                    lines.append(current_line)
            else:
                # 英文按单词处理
                words = paragraph.split()
                current_line = []
                
                for word in words:
                    test_line = ' '.join(current_line + [word])
                    if self.get_text_width(test_line, font) <= max_width:
                        current_line.append(word)
                    else:
                        if current_line:
                            lines.append(' '.join(current_line))
                            current_line = [word]
                        else:
                            # 单词太长，强制换行
                            lines.append(word)
                
                if current_line:
                    lines.append(' '.join(current_line))
        
        return lines
    
    def get_text_width(self, text: str, font) -> int:
        """获取文本宽度"""
        try:
            bbox = font.getbbox(text)
            return bbox[2] - bbox[0]
        except:
            # 如果getbbox不可用，使用更精确的估算
            # 中文字符宽度约为字体大小，英文约为字体大小的0.6倍
            chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
            other_chars = len(text) - chinese_chars
            font_size = getattr(font, 'size', 16)
            return chinese_chars * font_size + other_chars * int(font_size * 0.6)
    
    def draw_rounded_rectangle(self, draw, x: int, y: int, width: int, height: int, 
                             radius: int = 5, fill: str = None, outline: str = None):
        """绘制圆角矩形"""
        # 简化版圆角矩形
        draw.rectangle([x + radius, y, x + width - radius, y + height], fill=fill, outline=outline)
        draw.rectangle([x, y + radius, x + width, y + height - radius], fill=fill, outline=outline)
        
        # 绘制四个角的圆
        draw.ellipse([x, y, x + 2*radius, y + 2*radius], fill=fill, outline=outline)
        draw.ellipse([x + width - 2*radius, y, x + width, y + 2*radius], fill=fill, outline=outline)
        draw.ellipse([x, y + height - 2*radius, x + 2*radius, y + height], fill=fill, outline=outline)
        draw.ellipse([x + width - 2*radius, y + height - 2*radius, x + width, y + height], fill=fill, outline=outline)
    
    def apply_quality_effects(self, img: Image.Image, quality: str) -> Image.Image:
        """应用质量效果"""
        if quality == "high":
            return img
        elif quality == "medium":
            # 轻微模糊和压缩
            img = img.filter(ImageFilter.GaussianBlur(radius=0.5))
            # 轻微降低对比度
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(0.9)
            return img
        elif quality == "low":
            # 重度模糊、噪声、低分辨率
            # 先缩小再放大模拟低分辨率
            small_size = (img.width // 2, img.height // 2)
            img = img.resize(small_size, Image.Resampling.LANCZOS)
            img = img.resize((img.width * 2, img.height * 2), Image.Resampling.NEAREST)
            
            # 添加模糊
            img = img.filter(ImageFilter.GaussianBlur(radius=1.5))
            
            # 降低对比度和亮度
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(0.7)
            enhancer = ImageEnhance.Brightness(img)
            img = enhancer.enhance(0.8)
            
            return img
        
        return img
    
    def generate_all_test_images(self):
        """生成所有测试图片"""
        print("🚀 开始生成OCR测试图片...")
        
        # 设置字体
        self.setup_fonts()
        
        # 加载现有图片
        self.load_existing_images()
        
        # 处理现有图片
        self.process_existing_images()
        
        # 加载真实内容
        self.load_real_content()
        
        # 生成新的测试图片
        self.generate_enhanced_wechat_images()
        self.generate_enhanced_official_document_images()
        self.generate_enhanced_meeting_schedule_images()
        self.generate_enhanced_quality_test_images()
        
        # 保存测试数据映射
        self.save_test_data_mapping()
        
        print(f"✅ 测试图片生成完成！输出目录：{self.output_path}")
    
    def generate_enhanced_wechat_images(self):
        """生成增强版微信截图类图片"""
        print("💬 生成增强版微信截图...")
        
        training_notices = self.real_content.get("学习培训类通知", [])
        
        wechat_scenarios = [
            {
                "filename": "wechat_meeting_notice.png",
                "text": "【重要通知】\n明天上午9:30在三楼会议室召开季度总结会\n时间：2024年12月14日 09:30\n地点：三楼大会议室\n参会人员：各部门负责人\n请准时参加，谢谢！",
                "sender": "办公室主任",
                "keywords": ["重要通知", "明天", "上午", "9:30", "会议室", "季度总结会", "部门负责人"]
            },
            {
                "filename": "wechat_urgent_task.png", 
                "text": "【紧急任务】\n请各部门负责人于今日下午5点前提交本月工作总结报告\n\n要求：\n1. 包含工作完成情况\n2. 存在问题及改进措施\n3. 下月工作计划\n\n请发送至邮箱：<EMAIL>",
                "sender": "人事部",
                "keywords": ["紧急任务", "部门负责人", "今日", "下午", "5点", "工作总结", "报告"]
            },
            {
                "filename": "wechat_training_notice.png",
                "text": "【培训通知】\n根据上级要求，本周五下午组织全体员工进行安全培训\n\n培训内容：消防安全知识\n培训时间：本周五 14:00-17:00\n培训地点：一楼大厅\n\n请各部门统计参训人数，周四前报送。",
                "sender": "安全办",
                "keywords": ["培训通知", "上级要求", "周五", "下午", "全体员工", "安全培训", "消防"]
            }
        ]
        
        # 如果有真实培训通知，使用真实内容
        if training_notices:
            for i, notice in enumerate(training_notices[:3]):
                if i < len(wechat_scenarios):
                    content_lines = notice["content"].split('\n')
                    # 提取并格式化关键信息
                    formatted_content = f"【{notice['title']}】\n"
                    for line in content_lines[:5]:
                        if line.strip():
                            formatted_content += line.strip() + "\n"
                    
                    wechat_scenarios[i]["text"] = formatted_content[:100] + "..."
                    wechat_scenarios[i]["real_source"] = notice["filename"]
        
        for scenario in wechat_scenarios:
            img = self.create_enhanced_wechat_screenshot(
                scenario["text"], 
                scenario["sender"]
            )
            
            output_path = self.output_path / scenario["filename"]
            img.save(output_path, "PNG", quality=95)
            print(f"✓ 生成微信截图：{scenario['filename']}")
            
            # 记录测试数据
            self.test_data_mapping[scenario["filename"]] = {
                "expectedText": scenario["text"],
                "expectedKeywords": scenario["keywords"],
                "minConfidence": 0.8,
                "scenario": "wechat_screenshot_enhanced",
                "realSource": scenario.get("real_source", "模拟数据")
            }
    
    def generate_enhanced_official_document_images(self):
        """生成增强版政务文档类图片"""
        print("📋 生成增强版政务文档...")
        
        zhidu_docs = self.real_content.get("制度类", [])
        
        official_scenarios = [
            {
                "filename": "official_system_notice.png",
                "title": "关于加强公文办理规范性的通知",
                "content": """各部门：

为进一步规范公文办理流程，提高工作效率，现就有关事项通知如下：

一、严格按照公文格式要求
1. 标题居中，字体为方正小标宋体
2. 正文使用仿宋体，字号为三号
3. 落款右对齐，包含单位名称和日期

二、规范办理流程
1. 收文登记要及时准确
2. 批办意见要明确具体
3. 办理结果要及时反馈

三、加强监督检查
定期对公文办理情况进行检查，确保各项要求落实到位。

特此通知。""",
                "keywords": ["关于", "加强", "公文办理", "规范性", "通知", "各部门"]
            }
        ]
        
        # 如果有真实制度文档，使用真实内容
        if zhidu_docs:
            for i, doc in enumerate(zhidu_docs[:1]):
                if i < len(official_scenarios):
                    official_scenarios[i]["title"] = doc["title"]
                    official_scenarios[i]["content"] = doc["content"][:800]
                    official_scenarios[i]["real_source"] = doc["filename"]
        
        for scenario in official_scenarios:
            img = self.create_enhanced_official_document(
                scenario["title"],
                scenario["content"]
            )
            
            output_path = self.output_path / scenario["filename"]
            img.save(output_path, "PNG", quality=95)
            print(f"✓ 生成政务文档：{scenario['filename']}")
            
            # 记录测试数据
            self.test_data_mapping[scenario["filename"]] = {
                "expectedText": f"{scenario['title']}\n{scenario['content'][:200]}...",
                "expectedKeywords": scenario["keywords"],
                "minConfidence": 0.85,
                "scenario": "official_document_enhanced",
                "realSource": scenario.get("real_source", "模拟数据")
            }
    
    def generate_enhanced_meeting_schedule_images(self):
        """生成增强版会议安排类图片"""
        print("📅 生成增强版会议安排...")
        
        meeting_data = self.real_content.get("领导会议活动预安排", [])
        
        if meeting_data:
            schedule_list = [item["content"] for item in meeting_data[:8]]
        else:
            schedule_list = [
                "09:00-10:00 部门例会 会议室A 全体员工",
                "10:30-11:30 项目评审 会议室B 项目组成员", 
                "14:00-15:00 培训讲座 大会议室 相关人员",
                "15:30-16:30 工作汇报 小会议室 部门负责人"
            ]
        
        img = self.create_enhanced_meeting_schedule(schedule_list)
        
        output_path = self.output_path / "enhanced_meeting_schedule.png"
        img.save(output_path, "PNG", quality=95)
        print(f"✓ 生成会议安排：enhanced_meeting_schedule.png")
        
        # 记录测试数据
        self.test_data_mapping["enhanced_meeting_schedule.png"] = {
            "expectedText": "会议活动安排表\n" + "\n".join(schedule_list[:5]),
            "expectedKeywords": ["会议", "安排", "时间", "地点", "活动", "部门"],
            "minConfidence": 0.8,
            "scenario": "meeting_schedule_enhanced",
            "realSource": "真实会议安排数据" if meeting_data else "模拟数据"
        }
    
    def create_enhanced_meeting_schedule(self, schedule_data: List[str], quality: str = "high") -> Image.Image:
        """创建增强版会议安排表格"""
        width, height = 800, 600
        
        img = Image.new('RGB', (width, height), color='#FFFFFF')
        draw = ImageDraw.Draw(img)
        
        # 绘制标题
        title = "会议活动安排表"
        title_width = self.get_text_width(title, self.fonts['title'])
        title_x = (width - title_width) // 2
        draw.text((title_x, 30), title, fill='#000000', font=self.fonts['title'])
        
        # 绘制日期
        date_text = f"日期：{datetime.datetime.now().strftime('%Y年%m月%d日')}"
        draw.text((50, 70), date_text, fill='#666666', font=self.fonts['content'])
        
        # 表格参数
        table_x, table_y = 50, 110
        row_height = 50  # 增加行高
        col_widths = [100, 220, 120, 160]  # 调整列宽，确保总宽度不超过750
        total_width = sum(col_widths)
        
        # 确保表格不超出画布
        if table_x + total_width > width - 50:
            scale_factor = (width - 100) / total_width
            col_widths = [int(w * scale_factor) for w in col_widths]
        
        # 表头
        headers = ["时间", "活动内容", "地点", "参与人员"]
        x_offset = table_x
        for i, header in enumerate(headers):
            # 表头背景
            draw.rectangle([x_offset, table_y, x_offset + col_widths[i], table_y + row_height], 
                         outline='#000000', fill='#E3F2FD', width=2)
            # 表头文字 - 居中显示
            header_width = self.get_text_width(header, self.fonts['subtitle'])
            header_x = x_offset + (col_widths[i] - header_width) // 2
            header_y = table_y + (row_height - 20) // 2  # 垂直居中
            draw.text((header_x, header_y), header, fill='#1976D2', font=self.fonts['subtitle'])
            x_offset += col_widths[i]
        
        # 表格数据
        for row_idx, data in enumerate(schedule_data[:6]):  # 减少行数避免超出
            y_pos = table_y + (row_idx + 1) * row_height
            
            # 检查是否超出画布
            if y_pos + row_height > height - 50:
                break
                
            x_offset = table_x
            
            # 解析数据
            parts = data.split()
            time_part = parts[0] if parts else ""
            content_part = " ".join(parts[1:3]) if len(parts) > 2 else ""
            location_part = parts[3] if len(parts) > 3 else ""
            people_part = " ".join(parts[4:]) if len(parts) > 4 else ""
            
            data_parts = [time_part, content_part, location_part, people_part]
            
            for col_idx, cell_data in enumerate(data_parts):
                # 交替行颜色
                bg_color = '#F8F9FA' if row_idx % 2 == 0 else '#FFFFFF'
                
                # 绘制单元格背景
                draw.rectangle([x_offset, y_pos, x_offset + col_widths[col_idx], y_pos + row_height], 
                             outline='#DDDDDD', fill=bg_color, width=1)
                
                # 文本处理
                if col_idx == 0:  # 时间列，使用红色
                    text_color = '#DC3545'
                    font = self.fonts['content']
                elif col_idx == 1:  # 内容列，使用加粗
                    text_color = '#000000'
                    font = self.fonts['content']  # 改为普通字体避免过宽
                else:
                    text_color = '#333333'
                    font = self.fonts['content']
                
                # 文本换行和绘制
                cell_padding = 8
                max_cell_width = col_widths[col_idx] - cell_padding * 2
                wrapped_text = self.wrap_text(cell_data, font, max_cell_width)
                
                # 计算文本起始位置（垂直居中）
                text_lines = len(wrapped_text[:3])  # 最多显示3行
                text_height = text_lines * 16
                text_start_y = y_pos + (row_height - text_height) // 2
                
                for line_idx, line in enumerate(wrapped_text[:3]):  # 最多显示3行
                    if line.strip():  # 只绘制非空行
                        text_x = x_offset + cell_padding
                        text_y = text_start_y + line_idx * 16
                        draw.text((text_x, text_y), line, fill=text_color, font=font)
                
                x_offset += col_widths[col_idx]
        
        # 应用质量效果
        img = self.apply_quality_effects(img, quality)
        
        return img
    
    def generate_enhanced_quality_test_images(self):
        """生成增强版质量测试图片"""
        print("🔍 生成增强版质量测试图片...")
        
        test_texts = [
            "这是一段用于测试OCR识别准确率的中文文本。包含数字123、英文ABC和特殊符号：@#￥%。",
            "【测试文档】\n姓名：张三\n工号：2024001\n部门：技术部\n联系方式：138-0000-0000",
            "会议纪要\n时间：2024年12月13日\n地点：会议室A\n主持人：李主任\n记录人：王秘书"
        ]
        
        qualities = ["high", "medium", "low"]
        
        for i, quality in enumerate(qualities):
            test_text = test_texts[i % len(test_texts)]
            
            if i == 0:  # 微信格式
                img = self.create_enhanced_wechat_screenshot(test_text, "测试群", quality)
            else:  # 文档格式
                img = self.create_enhanced_official_document(f"质量测试文档-{quality.upper()}", test_text, quality)
            
            output_path = self.output_path / f"enhanced_{quality}_quality.png"
            img.save(output_path, "PNG", quality=95 if quality == "high" else (75 if quality == "medium" else 50))
            print(f"✓ 生成质量测试图片：enhanced_{quality}_quality.png")
            
            # 记录测试数据
            self.test_data_mapping[f"enhanced_{quality}_quality.png"] = {
                "expectedText": test_text,
                "expectedKeywords": ["测试", "OCR", "识别", "准确率", "中文", "数字", "英文", "符号"],
                "minConfidence": 0.9 if quality == "high" else (0.7 if quality == "medium" else 0.5),
                "scenario": f"quality_test_enhanced_{quality}",
                "realSource": "增强版质量测试数据"
            }
    
    def save_test_data_mapping(self):
        """保存测试数据映射"""
        mapping_file = self.test_images_path / "enhanced_test_data_mapping.json"
        
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_data_mapping, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 增强版测试数据映射已保存到：{mapping_file}")

def main():
    """主函数"""
    current_dir = Path(__file__).parent
    assets_path = current_dir.parent.parent
    
    print(f"Assets路径：{assets_path}")
    
    # 创建增强版生成器
    generator = TestImageGeneratorV2(str(assets_path))
    
    # 生成所有测试图片
    generator.generate_all_test_images()

if __name__ == "__main__":
    main() 