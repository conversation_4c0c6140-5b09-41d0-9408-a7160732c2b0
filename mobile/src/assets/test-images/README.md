# OCR测试图片集

## 📋 测试图片要求

为了进行真正的OCR准确率测试，我们需要创建包含已知文本内容的真实图片。

### 🎯 测试场景分类

#### 1. 微信截图类 (WeChat Screenshots)
- **wechat_meeting.png** - 会议通知截图
  - 预期文本：明天上午9:30在三楼会议室召开季度总结会，请各部门负责人准时参加。
  - 关键词：明天、上午、9:30、会议室、季度总结会、部门负责人、参加

- **wechat_task.png** - 工作任务截图  
  - 预期文本：紧急任务：请各部门负责人于今日下午5点前提交本月工作总结报告
  - 关键词：紧急任务、部门负责人、今日、下午、5点、工作总结、报告

- **wechat_notice.png** - 通知公告截图
  - 预期文本：通知：根据上级要求，本周五下午组织全体员工进行安全培训
  - 关键词：通知、上级要求、周五、下午、全体员工、安全培训

#### 2. 政务文档类 (Official Documents)
- **official_notice.png** - 公文通知
  - 预期文本：关于加强公文办理规范性的通知
  - 关键词：关于、加强、公文办理、规范性、通知

- **meeting_minutes.png** - 会议纪要
  - 预期文本：会议纪要 时间：2024年1月10日上午 地点：会议室A
  - 关键词：会议纪要、时间、2024年、1月10日、地点、会议室A

- **work_plan.png** - 工作计划
  - 预期文本：2024年第一季度工作计划 一、重点工作目标
  - 关键词：2024年、第一季度、工作计划、重点工作、目标

#### 3. 不同质量图片类 (Quality Variations)
- **high_quality.png** - 高质量图片（清晰、高分辨率）
- **medium_quality.png** - 中等质量图片（轻微模糊）
- **low_quality.png** - 低质量图片（模糊、低分辨率）

## 🔧 图片创建方法

### 方法1：手动创建（推荐）
1. 使用截图工具创建真实的微信截图
2. 使用文档编辑器创建政务文档，然后截图
3. 调整图片质量（压缩、模糊处理）

### 方法2：程序生成
1. 使用Canvas API生成包含文字的图片
2. 模拟不同的字体、大小、背景
3. 添加噪声和模糊效果

## 📊 测试数据映射

每个图片都应该有对应的预期结果：

```json
{
  "wechat_meeting.png": {
    "expectedText": "明天上午9:30在三楼会议室召开季度总结会，请各部门负责人准时参加。",
    "expectedKeywords": ["明天", "上午", "9:30", "会议室", "季度总结会", "部门负责人", "参加"],
    "minConfidence": 0.8,
    "scenario": "wechat_screenshot"
  }
}
```

## ⚠️ 重要说明

**当前状态**：这些图片需要手动创建或通过程序生成。在真实图片创建完成之前，我们的OCR测试只能验证服务架构和流程，无法验证真实的识别准确率。

**下一步**：
1. 创建真实的测试图片
2. 在真机环境中运行真实OCR测试
3. 基于真实结果优化OCR算法 