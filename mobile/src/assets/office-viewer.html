<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Office文档预览</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #2196F3;
            color: white;
            padding: 15px 20px;
            font-size: 16px;
            font-weight: 500;
        }
        .content {
            padding: 20px;
            min-height: 400px;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .error {
            text-align: center;
            padding: 50px;
            color: #f44336;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #2196F3;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .document-content {
            line-height: 1.6;
            font-size: 14px;
        }
        .document-content h1, .document-content h2, .document-content h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .document-content p {
            margin-bottom: 10px;
        }
        .document-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .document-content th, .document-content td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .document-content th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <span id="fileName">Office文档预览</span>
        </div>
        <div class="content">
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <div>正在加载文档...</div>
            </div>
            <div id="error" class="error" style="display: none;">
                <div>文档加载失败</div>
                <div style="font-size: 12px; margin-top: 10px;">请检查文件格式是否正确</div>
            </div>
            <div id="documentContent" class="document-content" style="display: none;"></div>
        </div>
    </div>

    <!-- 暂时移除外部JavaScript库，使用简化预览 -->

    <script>
        let fileData = null;
        let fileName = '';

        // 接收来自React Native的消息
        window.addEventListener('message', function(event) {
            const data = JSON.parse(event.data);
            if (data.type === 'LOAD_DOCUMENT') {
                fileData = data.fileData;
                fileName = data.fileName;
                document.getElementById('fileName').textContent = fileName;
                loadDocument();
            }
        });

        // 向React Native发送消息
        function sendMessage(message) {
            if (window.ReactNativeWebView) {
                window.ReactNativeWebView.postMessage(JSON.stringify(message));
            }
        }

        async function loadDocument() {
            try {
                const loadingEl = document.getElementById('loading');
                const errorEl = document.getElementById('error');
                const contentEl = document.getElementById('documentContent');

                loadingEl.style.display = 'block';
                errorEl.style.display = 'none';
                contentEl.style.display = 'none';

                // 根据文件类型选择解析方式
                const extension = fileName.toLowerCase().split('.').pop();
                
                if (extension === 'docx') {
                    await loadWordDocument();
                } else if (extension === 'xlsx' || extension === 'xls') {
                    await loadExcelDocument();
                } else if (extension === 'pptx') {
                    await loadPowerPointDocument();
                } else {
                    throw new Error('不支持的文件格式: ' + extension);
                }

                loadingEl.style.display = 'none';
                contentEl.style.display = 'block';
                
                sendMessage({ type: 'DOCUMENT_LOADED', success: true });
            } catch (error) {
                console.error('文档加载失败:', error);
                showError(error.message);
                sendMessage({ type: 'DOCUMENT_LOADED', success: false, error: error.message });
            }
        }

        async function loadWordDocument() {
            // 简化版Word文档预览
            document.getElementById('documentContent').innerHTML =
                '<div style="text-align: center; padding: 50px;">' +
                '<div style="font-size: 48px; margin-bottom: 20px;">📄</div>' +
                '<h3>Word文档</h3>' +
                '<p style="color: #666; margin-bottom: 30px;">文件名: ' + fileName + '</p>' +
                '<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">' +
                '<p style="margin: 0; color: #333;">此文档包含文字内容，建议使用专业的Office应用打开以获得最佳体验。</p>' +
                '</div>' +
                '<button onclick="openWithExternalApp()" style="background: #2196F3; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-size: 16px; cursor: pointer;">使用外部应用打开</button>' +
                '</div>';
        }

        async function loadExcelDocument() {
            // 简化版Excel文档预览
            document.getElementById('documentContent').innerHTML =
                '<div style="text-align: center; padding: 50px;">' +
                '<div style="font-size: 48px; margin-bottom: 20px;">📊</div>' +
                '<h3>Excel表格</h3>' +
                '<p style="color: #666; margin-bottom: 30px;">文件名: ' + fileName + '</p>' +
                '<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">' +
                '<p style="margin: 0; color: #333;">此文档包含表格数据，建议使用专业的表格应用打开以获得最佳体验。</p>' +
                '</div>' +
                '<button onclick="openWithExternalApp()" style="background: #2196F3; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-size: 16px; cursor: pointer;">使用外部应用打开</button>' +
                '</div>';
        }

        async function loadPowerPointDocument() {
            // 简化版PowerPoint文档预览
            document.getElementById('documentContent').innerHTML =
                '<div style="text-align: center; padding: 50px;">' +
                '<div style="font-size: 48px; margin-bottom: 20px;">📽️</div>' +
                '<h3>PowerPoint演示文稿</h3>' +
                '<p style="color: #666; margin-bottom: 30px;">文件名: ' + fileName + '</p>' +
                '<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">' +
                '<p style="margin: 0; color: #333;">此文档包含演示内容，建议使用专业的演示应用打开以获得最佳体验。</p>' +
                '</div>' +
                '<button onclick="openWithExternalApp()" style="background: #2196F3; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-size: 16px; cursor: pointer;">使用外部应用打开</button>' +
                '</div>';
        }

        function openWithExternalApp() {
            sendMessage({ type: 'OPEN_EXTERNAL', fileUri: fileData });
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('documentContent').style.display = 'none';
            
            const errorEl = document.getElementById('error');
            errorEl.innerHTML = 
                '<div>文档加载失败</div>' +
                '<div style="font-size: 12px; margin-top: 10px;">' + message + '</div>';
        }

        // 页面加载完成后通知React Native
        window.addEventListener('load', function() {
            sendMessage({ type: 'PAGE_LOADED' });
        });
    </script>
</body>
</html>
