# 公职猫项目 - Assets资源文件夹

## 📁 文件夹结构

```
mobile/src/assets/
├── README.md                    # 本文档
├── documents/                   # 📄 政务文档资源
│   ├── regulations/            # 制度类文档
│   ├── training-notices/       # 学习培训类通知
│   └── meeting-schedules/      # 领导会议活动预安排
├── test-images/                # 🖼️ OCR测试图片
│   ├── README.md              # 测试图片说明
│   ├── generated/             # 生成的测试图片
│   ├── scripts/               # 图片生成脚本
│   └── original/              # 原始测试图片
└── archive/                    # 📦 归档文件
    └── legacy/                # 历史遗留文件
```

## 📄 文档资源 (documents/)

### 制度类文档 (regulations/)
- 代表委员联络站工作制度.docx

### 学习培训类通知 (training-notices/)
- 内部明电（关于召开代表联络站运行绩效评价指引（试行）解读培训会的通知.doc
- 关于开展人工智能领域知识专项学习培训的通知.doc
- 关于赴杭州开展路桥区人大系统干部综合能力提升培训的通知.doc
- 关于开展人代会前代表议案建议办理系统业务培训的通知.doc

### 会议安排 (meeting-schedules/)
- 市人大常委会领导主要会议活动预安排.xls
- 副本1-王康_潘建华书记一周工作安排（11月15日-11月18日）.xls

## 🖼️ OCR测试图片 (test-images/)

详细说明请查看 `test-images/README.md`

### 生成的测试图片 (generated/)
- 19张OCR测试图片，包括微信截图、政务文档、会议安排等

### 图片生成脚本 (scripts/)
- 完整的OCR测试图片生成器
- 支持中文字体、emoji、文本格式化
- 基于真实政务文档内容生成

### 原始测试图片 (original/)
- 用户提供的真实截图和文档图片
- 已整合到测试集中

## 📦 归档文件 (archive/)

### 历史遗留文件 (legacy/)
- 重复的会议安排文件
- 临时文件和备份

## 🎯 使用说明

### OCR测试
1. 使用 `test-images/generated/` 中的图片进行OCR功能测试
2. 参考 `test-images/enhanced_test_data_mapping.json` 获取预期识别结果

### 文档资源
1. `documents/` 中的文件为真实政务文档，可用于内容生成
2. 支持.docx、.doc、.xls等格式

### 脚本工具
1. 运行 `test-images/scripts/generate_test_images_v2.py` 生成新的测试图片
2. 运行 `test-images/scripts/preview_images.py` 预览图片质量

## 📊 统计信息

- **政务文档**: 5个文件
- **会议安排**: 4个文件（含重复）
- **测试图片**: 19张（生成）+ 5张（原始）
- **脚本工具**: 完整的生成器和预览工具

## 🔄 维护说明

1. **添加新文档**: 放入对应的 `documents/` 子文件夹
2. **更新测试图片**: 运行生成脚本重新生成
3. **清理重复文件**: 定期检查并移动到 `archive/legacy/`
4. **版本控制**: 重要变更请更新本README文档

---
*最后更新: 2025年1月13日*
*维护者: AI助手* 