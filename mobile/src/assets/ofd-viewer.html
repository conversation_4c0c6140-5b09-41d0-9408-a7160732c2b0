<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=3.0, minimum-scale=0.5, user-scalable=yes"
    />
    <title>OFD Viewer</title>
    <style>
      /* 🔧 全局重置 - 确保所有元素都能正确适应移动端 */
      * {
        box-sizing: border-box;
      }

      /* 🔧 强制所有容器元素使用全宽 */
      html, body, #ofd-container {
        width: 100vw !important;
        max-width: 100vw !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      html,
      body {
        height: 100%;
        background-color: #f5f5f5;
        overflow: auto;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      }

      #ofd-container {
        height: 100%;
        min-height: 100vh;
        background-color: #ffffff;
        overflow: auto;
        position: relative;
      }

      /* 🔧 强制所有OFD内容适应屏幕宽度 */
      #ofd-container * {
        max-width: 100vw !important;
        box-sizing: border-box !important;
      }

      /* 🔧 确保OFD内容能够充分利用宽度 */
      #ofd-container > div {
        width: 100vw !important;
        max-width: 100vw !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      /* 🔧 优化OFD页面显示 - 移动端适配 */
      .ofd-page {
        margin: 4px auto !important;
        max-width: calc(100vw - 8px) !important;
        width: calc(100vw - 8px) !important;
        box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        box-sizing: border-box !important;
      }

      /* 🔧 强制移除任何可能限制宽度的样式 */
      [style*="width"], [style*="max-width"] {
        width: 100% !important;
        max-width: 100vw !important;
      }

      /* 🔧 移动端特定样式 */
      @media screen and (max-width: 768px) {
        #ofd-container {
          padding: 0 !important;
        }

        .ofd-page {
          margin: 0px auto !important;
          max-width: calc(100vw - 4px) !important;
          width: calc(100vw - 4px) !important;
        }
      }
    </style>
</head>
<body>
    <div id="ofd-container"></div>

    <!-- The browser-specific controls are now removed for the final version -->

    <script src="./libs/jszip.min.js"></script>
    <script src="./libs/opentype.min.js"></script>
    <script src="./libs/x2js.js"></script>
    <script src="./libs/easyjbig2.js"></script>
    <script src="./libs/easyofd.js"></script>

    <script>
      (function () {
        // Log as soon as the script block starts executing
        if (window.ReactNativeWebView) {
            window.ReactNativeWebView.postMessage(JSON.stringify({ type: 'diag', message: 'WebView script block initiated.' }));
        } else {
            console.log('[WebView Diag] WebView script block initiated.');
                    }

        let ofdViewerInstance;

        // Function to post messages back to React Native
        function postMessageToRN(type, message) {
          if (window.ReactNativeWebView) {
            const msg = JSON.stringify({ type, message });
            window.ReactNativeWebView.postMessage(msg);
                } else {
            console.log(`[WebView - ${type}]`, message);
                }
        }

        // 🔧 优化OFD渲染后的内容以适应移动端
        function optimizeOFDContentForMobile(container) {
          console.log('[OFD Viewer] 开始优化移动端显示');

          // 确保容器本身使用全宽
          container.style.width = '100vw';
          container.style.maxWidth = '100vw';
          container.style.margin = '0';
          container.style.padding = '0';
          container.style.boxSizing = 'border-box';

          // 强制所有元素适应容器宽度
          const allElements = container.querySelectorAll('*');
          allElements.forEach(element => {
            const style = element.style;

            // 移除所有固定宽度设置
            if (style.width) {
              style.width = '100%';
            }
            if (style.maxWidth) {
              style.maxWidth = '100%';
            }
            if (style.minWidth) {
              style.minWidth = 'auto';
            }

            // 移除可能导致宽度问题的margin
            style.marginLeft = '0';
            style.marginRight = '0';

            // 确保box-sizing
            style.boxSizing = 'border-box';

            // 特别处理OFD页面元素
            if (element.classList.contains('ofd-page') || element.tagName === 'CANVAS') {
              style.width = 'calc(100vw - 8px)';
              style.maxWidth = 'calc(100vw - 8px)';
              style.margin = '4px auto';
            }

            // 移除任何可能的绝对定位或浮动
            if (style.position === 'absolute' || style.position === 'fixed') {
              style.position = 'relative';
            }
            if (style.float && style.float !== 'none') {
              style.float = 'none';
            }
          });

          console.log('[OFD Viewer] 移动端显示优化完成');
        }

        // 1. Listen for the custom event dispatched by React Native.
        window.addEventListener('ofdDataReady', () => {
          postMessageToRN('diag', 'ofdDataReady event received.');
          // 2. Retrieve the data from the global variable.
          const base64Data = window.OFD_DATA;

          if (!base64Data) {
            postMessageToRN('error', 'ofdDataReady event fired, but window.OFD_DATA is missing.');
                        return;
                    }

          if (!ofdViewerInstance) {
            postMessageToRN('error', 'OFD viewer instance is not ready when data arrived.');
                        return;
                    }

          try {
            // 3. Process the data.
            const byteCharacters = atob(base64Data);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'application/ofd' });

            ofdViewerInstance.loadFromBlob(blob);
            postMessageToRN('diag', 'Successfully called loadFromBlob.');

            // 🔧 延迟执行优化，确保OFD内容已经渲染
            setTimeout(() => {
              const container = document.getElementById('ofd-container');
              if (container) {
                optimizeOFDContentForMobile(container);
                postMessageToRN('diag', 'OFD content optimized for mobile.');
              }
            }, 1000); // 给OFD渲染一些时间
                    } catch (e) {
            postMessageToRN('error', `Failed to load blob: ${e.message}`);
          } finally {
            // 4. Clean up the global variable to free memory.
            window.OFD_DATA = null;
                }
        });

        // Wait for the DOM to be ready.
        document.addEventListener('DOMContentLoaded', () => {
          postMessageToRN('diag', 'DOMContentLoaded event fired.');
          const container = document.getElementById('ofd-container');
          if (!container) {
            postMessageToRN('error', 'Container element #ofd-container not found.');
            return;
          }
          
          try {
            // 3. Initialize the OFD library.
            postMessageToRN('diag', 'Attempting to initialize EasyOFD...');
            ofdViewerInstance = new EasyOFD('react-native-ofd-viewer', container);
            postMessageToRN('diag', 'EasyOFD initialized successfully.');

            // 🔧 设置MutationObserver来监听内容变化
            const observer = new MutationObserver((mutations) => {
              let shouldOptimize = false;
              mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                  shouldOptimize = true;
                }
              });

              if (shouldOptimize) {
                setTimeout(() => {
                  optimizeOFDContentForMobile(container);
                  postMessageToRN('diag', 'OFD content re-optimized after DOM changes.');
                }, 500);
              }
            });

            observer.observe(container, {
              childList: true,
              subtree: true
            });

            // 4. CRITICAL: Only after successful initialization,
            //    signal to React Native that the WebView is ready to receive data.
            postMessageToRN('ready', 'WebView is fully initialized and ready for data.');
          } catch(e) {
            postMessageToRN('error', `EasyOFD initialization failed: ${e.message}`);
            }
        });
      })();
    </script>
</body>
</html>