import type { AppStateStatus, AppStateEvent } from 'react-native';

const RN = jest.requireActual('react-native');

// Mock the native bridge for WatermelonDB
RN.NativeModules.WMDatabaseBridge = {
  initialize: jest.fn(),
  setUpWithSchema: jest.fn(),
  setUpWithMigrations: jest.fn(),
  find: jest.fn(),
  query: jest.fn(),
  queryIds: jest.fn(),
  count: jest.fn(),
  batch: jest.fn(),
  getDeletedRecords: jest.fn(),
  destroyDeletedRecords: jest.fn(),
  unsafeResetDatabase: jest.fn(),
  getLocal: jest.fn(),
  setLocal: jest.fn(),
  removeLocal: jest.fn(),
};

// Mock AppState
type AppStateListener = (status: AppStateStatus) => void;
const appStateListeners = new Set<AppStateListener>();

RN.AppState = {
  ...RN.AppState,
  currentState: 'active' as AppStateStatus,
  addEventListener: (event: AppStateEvent, listener: AppStateListener) => {
    appStateListeners.add(listener);
    return { remove: () => appStateListeners.delete(listener) };
  },
  removeEventListener: (event: AppStateEvent, listener: AppStateListener) => {
    appStateListeners.delete(listener);
  },
};

export default RN;
