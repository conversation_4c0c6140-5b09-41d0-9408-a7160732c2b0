import { create } from 'zustand';

// 定义文件对象的基本结构
export interface SharedFileData {
  uri: string;
  name: string;
  type: string;
  size: number;
  originalUri?: string;
  text?: string; // 兼容 share-menu 文本分享
}

// 定义Store的状态和方法
interface GlobalFileShareState {
  file: SharedFileData | null;
  setSharedFile: (file: SharedFileData | null) => void;
  clearSharedFile: () => void;
}

export const useGlobalFileShareStore = create<GlobalFileShareState>((set) => ({
  file: null,
  setSharedFile: (file) => set({ file }),
  clearSharedFile: () => set({ file: null }),
}));
