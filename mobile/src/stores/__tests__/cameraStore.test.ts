/**
 * @jest-environment node
 */

import { Database } from '@nozbe/watermelondb';
import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite';
import { Q } from '@nozbe/watermelondb';

import { createCameraStore, CameraState } from '../cameraStore';
import schema from '../../db/schema';
import CaptureSession from '../../db/models/CaptureSession';
import Media from '../../db/models/Media';
import { PhotoFile } from 'react-native-vision-camera';
import { UseBoundStore, StoreApi } from 'zustand';

describe('cameraStore', () => {
  let database: Database;
  let useTestStore: UseBoundStore<StoreApi<CameraState>>;

  beforeEach(async () => {
    const adapter = new SQLiteAdapter({
      schema,
      jsi: false, // JSI is not available in Node.js
      dbName: ':memory:', // Use in-memory database for tests
    });

    database = new Database({
      adapter,
      modelClasses: [CaptureSession, Media],
    });

    // Reset the database before each test
    await database.write(async () => {
      await database.unsafeResetDatabase();
    });

    useTestStore = createCameraStore(database);
  });

  it('should start a new session if no paused session exists', async () => {
    const { getState } = useTestStore;

    await getState().showCamera();

    expect(getState().isCameraVisible).toBe(true);
    expect(getState().activeSession).not.toBeNull();
    expect(getState().activeSession?.status).toBe('active');

    const sessionsCount = await database.get<CaptureSession>('capture_sessions').query().fetchCount();
    expect(sessionsCount).toBe(1);
  });

  it('should resume a paused session if one exists', async () => {
    const { getState } = useTestStore;
    let pausedSession: CaptureSession;

    // 1. Create and pause a session
    await database.write(async () => {
      pausedSession = await database.get<CaptureSession>('capture_sessions').create(s => {
        s.status = 'paused';
      });
      await database.get<Media>('media').create(m => {
        if (pausedSession) { // Satisfy TypeScript null check
          m.sessionId = pausedSession.id;
        }
        m.uri = 'test_uri';
        m.type = 'photo';
      });
    });

    // 2. Call showCamera to resume it
    await getState().showCamera();

    // 3. Assertions
    expect(getState().isCameraVisible).toBe(true);
    expect(getState().activeSession?.id).toBe(pausedSession!.id);
    expect(getState().activeSession?.status).toBe('active');
    expect(getState().capturedMedia.length).toBe(1);
    expect(getState().capturedMedia[0].uri).toBe('test_uri');
  });

  it('should set session status to paused on hideCamera', async () => {
    const { getState } = useTestStore;

    // Start a session first
    await getState().showCamera();
    const session = getState().activeSession;
    expect(session).not.toBeNull();

    // Hide the camera
    await getState().hideCamera();

    // Assertions
    expect(getState().isCameraVisible).toBe(false);

    const updatedSession = await database.get<CaptureSession>('capture_sessions').find(session!.id);
    expect(updatedSession.status).toBe('paused');
  });

  it('should add media to the active session and persist to DB', async () => {
    const { getState } = useTestStore;

    // Start a session
    await getState().showCamera();
    const session = getState().activeSession;
    expect(session).not.toBeNull();

    const photo: PhotoFile = {
      path: '/test/photo.jpg',
      width: 1920,
      height: 1080,
      isRawPhoto: false,
      orientation: 'portrait',
      isMirrored: false,
    };

    // Add media
    await getState().addMedia(photo);

    // Assertions
    expect(getState().capturedMedia.length).toBe(1);
    expect(getState().capturedMedia[0].uri).toBe(photo.path);

    const db = database;
    const mediaInDb = await db.get<Media>('media').query(Q.where('session_id', session!.id)).fetch();
    expect(mediaInDb.length).toBe(1);
    expect(mediaInDb[0].uri).toBe(photo.path);
  });
});
