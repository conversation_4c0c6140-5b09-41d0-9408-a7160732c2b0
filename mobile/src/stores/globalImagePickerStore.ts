import { create } from 'zustand';

type PickerMode = 'single' | 'multiple';

interface GlobalPickerOptions {
  mode: PickerMode;
  selectionLimit: number;
  onSelect: (uris: string[]) => void;
  onClose: () => void;
}

interface GlobalImagePickerState {
  isVisible: boolean;
  options: Partial<GlobalPickerOptions>;
  openPicker: (pickerOptions: Partial<GlobalPickerOptions>) => void;
  closePicker: () => void;
}

export const useGlobalImagePickerStore = create<GlobalImagePickerState>((set) => ({
  isVisible: false,
  options: {},
  openPicker: (pickerOptions) => set({ isVisible: true, options: pickerOptions }),
  closePicker: () => set({ isVisible: false, options: {} }),
}));
