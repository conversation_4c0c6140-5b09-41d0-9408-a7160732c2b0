import { create } from 'zustand';
import ocrService, { OCRResult } from '../services/OCRService';

interface OCRState {
  ocrResult: OCRResult | null;
  isOcrLoading: boolean;
  ocrError: string | null;
  runOcrForImage: (imageUri: string) => Promise<void>;
  clearOcrState: () => void;
}

export const useOcrStore = create<OCRState>()((set, get) => ({
  ocrResult: null,
  isOcrLoading: false,
  ocrError: null,

  runOcrForImage: async (imageUri: string) => {
    const state = get();
    if (state.isOcrLoading) {
      return; // 防止重复调用
    }

    try {
      set({ isOcrLoading: true, ocrError: null });

      // const ocrService = new OCRService(); // Use the imported instance instead
      const result = await ocrService.recognizeFromImageUri(imageUri);

      // 检查组件是否还在挂载状态
      const currentState = get();
      if (currentState.isOcrLoading) {
        set({
          ocrResult: result,
          isOcrLoading: false,
          ocrError: null,
        });
      }
    } catch (error) {
      console.error('OCR识别失败:', error);
      // 检查组件是否还在挂载状态
      const currentState = get();
      if (currentState.isOcrLoading) {
        set({
          ocrError: error instanceof Error ? error.message : '识别失败',
          isOcrLoading: false,
          ocrResult: null,
        });
      }
    }
  },

  clearOcrState: () => {
    set({
      ocrResult: null,
      isOcrLoading: false,
      ocrError: null,
    });
  },
}));
