import { useState } from 'react';

export interface ImageViewerState {
  visible: boolean;
  uris: { id: string; uri: string }[];
  index: number;
}

export function useImageViewer() {
  const [imageViewerState, setImageViewerState] = useState<ImageViewerState>({
    visible: false,
    uris: [],
    index: 0,
  });

  const openImageViewer = (uris: string[], index: number) => {
    setImageViewerState({
      visible: true,
      uris: uris.map((uri, i) => ({ id: `${i}`, uri })),
      index,
    });
  };

  const closeImageViewer = () => {
    setImageViewerState((prev) => ({ ...prev, visible: false }));
  };

  const setImageIndex = (index: number) => {
    setImageViewerState((prev) => ({ ...prev, index }));
  };

  return {
    imageViewerState,
    openImageViewer,
    closeImageViewer,
    setImageIndex,
  };
}
