import { useState } from 'react';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';

export function useAudioPlayer(audioRecorderPlayer: AudioRecorderPlayer) {
  const [playingAudioId, setPlayingAudioId] = useState<string | null>(null);
  const [audioProgress, setAudioProgress] = useState(0);

  // 播放/暂停音频
  const handleAudioPlayPause = async (msg: any) => {
    if (playingAudioId === msg.id?.toString()) {
      // 正在播放，点击则暂停
      await audioRecorderPlayer.pausePlayer();
      setPlayingAudioId(null);
      return;
    }
    // 播放新音频，先停止上一个
    await audioRecorderPlayer.stopPlayer();
    setPlayingAudioId(msg.id?.toString());
    setAudioProgress(0);
    await audioRecorderPlayer.startPlayer(msg.audioData?.uri || msg.audio?.uri);
    audioRecorderPlayer.addPlayBackListener((e) => {
      setAudioProgress(Math.floor(e.currentPosition / 1000));
      if (e.currentPosition >= e.duration) {
        setPlayingAudioId(null);
        setAudioProgress(0);
        audioRecorderPlayer.stopPlayer();
      }
      return;
    });
  };

  return { playingAudioId, audioProgress, handleAudioPlayPause };
}
