/**
 * 数据一致性服务
 * 解决聊天消息与文件存储不一致的问题
 */

import { FilePathManager } from '../utils/filePathManager';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface FileMessageData {
  uri: string;
  name: string;
  size: number;
  type: string;
}

export interface ChatMessage {
  id: string;
  type: string;
  ext?: string;
  fileData?: FileMessageData;
}

export interface ConsistencyCheckResult {
  totalMessages: number;
  fileMessages: number;
  validFiles: number;
  invalidFiles: number;
  migratedFiles: number;
  unrepairedFiles: number;
  details: {
    messageId: string;
    fileName: string;
    status: 'valid' | 'migrated' | 'missing';
    originalPath?: string;
    newPath?: string;
    error?: string;
  }[];
}

/**
 * 数据一致性服务类
 */
export class DataConsistencyService {
  private static readonly STORAGE_KEY = 'chat_messages';
  private static readonly LAST_CHECK_KEY = 'last_consistency_check';
  private static readonly CHECK_INTERVAL = 24 * 60 * 60 * 1000; // 24小时

  /**
   * 从AsyncStorage加载聊天消息
   */
  private static async loadChatMessages(): Promise<ChatMessage[]> {
    try {
      const messagesJson = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (!messagesJson) {
        return [];
      }

      const messages = JSON.parse(messagesJson);
      return Array.isArray(messages) ? messages : [];
    } catch (error) {
      console.error('[DataConsistencyService] 加载聊天消息失败:', error);
      return [];
    }
  }

  /**
   * 保存聊天消息到AsyncStorage
   */
  private static async saveChatMessages(messages: ChatMessage[]): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(messages));
    } catch (error) {
      console.error('[DataConsistencyService] 保存聊天消息失败:', error);
      throw error;
    }
  }

  /**
   * 提取消息中的文件数据
   */
  private static extractFileData(message: ChatMessage): FileMessageData | null {
    try {
      if (message.type !== 'file' || !message.ext) {
        return null;
      }

      const extData = JSON.parse(message.ext);
      return extData.fileData || null;
    } catch (error) {
      console.warn('[DataConsistencyService] 解析文件数据失败:', error);
      return null;
    }
  }

  /**
   * 更新消息中的文件路径
   */
  private static updateMessageFilePath(message: ChatMessage, newPath: string): ChatMessage {
    try {
      if (message.type !== 'file' || !message.ext) {
        return message;
      }

      const extData = JSON.parse(message.ext);
      if (extData.fileData) {
        extData.fileData.uri = newPath;
        message.ext = JSON.stringify(extData);
      }

      return message;
    } catch (error) {
      console.error('[DataConsistencyService] 更新文件路径失败:', error);
      return message;
    }
  }

  /**
   * 检查并修复数据一致性
   */
  static async checkAndRepairConsistency(): Promise<ConsistencyCheckResult> {
    console.log('[DataConsistencyService] 开始数据一致性检查...');

    const result: ConsistencyCheckResult = {
      totalMessages: 0,
      fileMessages: 0,
      validFiles: 0,
      invalidFiles: 0,
      migratedFiles: 0,
      unrepairedFiles: 0,
      details: [],
    };

    try {
      // 加载聊天消息
      const messages = await this.loadChatMessages();
      result.totalMessages = messages.length;

      // 筛选文件消息
      const fileMessages = messages.filter(msg => msg.type === 'file');
      result.fileMessages = fileMessages.length;

      console.log(`[DataConsistencyService] 找到${result.fileMessages}个文件消息`);

      // 检查每个文件消息
      const updatedMessages = [...messages];
      let hasUpdates = false;

      for (const message of fileMessages) {
        const fileData = this.extractFileData(message);
        if (!fileData) {
          continue;
        }

        const detail: {
          messageId: string;
          fileName: string;
          status: 'valid' | 'migrated' | 'missing';
          originalPath: string;
          newPath?: string;
          error?: string;
        } = {
          messageId: message.id,
          fileName: fileData.name,
          status: 'missing',
          originalPath: fileData.uri,
        };

        // 尝试迁移文件路径
        const migrationResult = await FilePathManager.migrateFilePath(fileData.uri);

        if (migrationResult.success && migrationResult.newPath) {
          if (migrationResult.newPath === fileData.uri) {
            // 路径未变化，文件有效
            detail.status = 'valid';
            result.validFiles++;
          } else {
            // 路径已迁移
            detail.status = 'migrated';
            detail.newPath = migrationResult.newPath;
            result.migratedFiles++;

            // 更新消息中的路径
            const messageIndex = updatedMessages.findIndex(m => m.id === message.id);
            if (messageIndex !== -1) {
              updatedMessages[messageIndex] = this.updateMessageFilePath(
                updatedMessages[messageIndex],
                migrationResult.newPath
              );
              hasUpdates = true;
            }
          }
        } else {
          // 迁移失败，文件丢失
          detail.status = 'missing';
          detail.error = migrationResult.error;
          result.unrepairedFiles++;
        }

        result.details.push(detail);
      }

      // 计算无效文件数
      result.invalidFiles = result.unrepairedFiles;

      // 如果有更新，保存消息
      if (hasUpdates) {
        await this.saveChatMessages(updatedMessages);
        console.log(`[DataConsistencyService] 已更新${result.migratedFiles}个文件路径`);
      }

      // 记录检查时间
      await AsyncStorage.setItem(this.LAST_CHECK_KEY, Date.now().toString());

      console.log('[DataConsistencyService] 数据一致性检查完成:', {
        总消息: result.totalMessages,
        文件消息: result.fileMessages,
        有效文件: result.validFiles,
        迁移文件: result.migratedFiles,
        丢失文件: result.unrepairedFiles,
      });

    } catch (error) {
      console.error('[DataConsistencyService] 数据一致性检查失败:', error);
      throw error;
    }

    return result;
  }

  /**
   * 检查是否需要进行一致性检查
   */
  static async shouldPerformCheck(): Promise<boolean> {
    try {
      const lastCheckStr = await AsyncStorage.getItem(this.LAST_CHECK_KEY);
      if (!lastCheckStr) {
        return true; // 从未检查过
      }

      const lastCheck = parseInt(lastCheckStr, 10);
      const now = Date.now();

      return (now - lastCheck) > this.CHECK_INTERVAL;
    } catch (error) {
      console.error('[DataConsistencyService] 检查时间判断失败:', error);
      return true; // 出错时默认需要检查
    }
  }

  /**
   * 应用启动时的自动检查
   */
  static async performStartupCheck(): Promise<void> {
    try {
      const needsCheck = await this.shouldPerformCheck();
      if (!needsCheck) {
        console.log('[DataConsistencyService] 跳过一致性检查（距离上次检查不足24小时）');
        return;
      }

      console.log('[DataConsistencyService] 执行启动时一致性检查...');
      const result = await this.checkAndRepairConsistency();

      // 如果有文件需要修复，记录详细信息
      if (result.migratedFiles > 0 || result.unrepairedFiles > 0) {
        console.log('[DataConsistencyService] 检测到文件路径问题:', {
          迁移成功: result.migratedFiles,
          无法修复: result.unrepairedFiles,
        });
      }

    } catch (error) {
      console.error('[DataConsistencyService] 启动时检查失败:', error);
      // 不抛出错误，避免影响应用启动
    }
  }

  /**
   * 手动触发完整检查
   */
  static async performManualCheck(): Promise<ConsistencyCheckResult> {
    console.log('[DataConsistencyService] 执行手动一致性检查...');
    return await this.checkAndRepairConsistency();
  }

  /**
   * 清理无效的文件消息
   * 注意：这是一个危险操作，会永久删除无法修复的文件消息
   */
  static async cleanupInvalidMessages(): Promise<{
    removedCount: number;
    removedMessages: string[];
  }> {
    console.log('[DataConsistencyService] 开始清理无效文件消息...');

    const result = {
      removedCount: 0,
      removedMessages: [] as string[],
    };

    try {
      const messages = await this.loadChatMessages();
      const validMessages: ChatMessage[] = [];

      for (const message of messages) {
        if (message.type !== 'file') {
          validMessages.push(message);
          continue;
        }

        const fileData = this.extractFileData(message);
        if (!fileData) {
          validMessages.push(message);
          continue;
        }

        // 检查文件是否存在
        const migrationResult = await FilePathManager.migrateFilePath(fileData.uri);
        if (migrationResult.success) {
          validMessages.push(message);
        } else {
          // 文件不存在，标记为删除
          result.removedCount++;
          result.removedMessages.push(fileData.name);
          console.log(`[DataConsistencyService] 删除无效文件消息: ${fileData.name}`);
        }
      }

      // 保存清理后的消息
      if (result.removedCount > 0) {
        await this.saveChatMessages(validMessages);
        console.log(`[DataConsistencyService] 已清理${result.removedCount}个无效文件消息`);
      }

    } catch (error) {
      console.error('[DataConsistencyService] 清理无效消息失败:', error);
      throw error;
    }

    return result;
  }

  /**
   * 生成一致性检查报告
   */
  static async generateConsistencyReport(): Promise<string> {
    const report = [];
    report.push('# 数据一致性检查报告');
    report.push('');

    try {
      const result = await this.checkAndRepairConsistency();

      report.push('## 检查结果概览');
      report.push(`- 总消息数: ${result.totalMessages}`);
      report.push(`- 文件消息数: ${result.fileMessages}`);
      report.push(`- 有效文件: ${result.validFiles}`);
      report.push(`- 迁移文件: ${result.migratedFiles}`);
      report.push(`- 丢失文件: ${result.unrepairedFiles}`);
      report.push('');

      if (result.details.length > 0) {
        report.push('## 详细信息');
        result.details.forEach(detail => {
          const status = detail.status === 'valid' ? '✅' :
                        detail.status === 'migrated' ? '🔄' : '❌';
          report.push(`- ${status} ${detail.fileName}`);
          if (detail.status === 'migrated' && detail.newPath) {
            report.push(`  - 路径已更新: ${detail.originalPath} → ${detail.newPath}`);
          }
          if (detail.status === 'missing' && detail.error) {
            report.push(`  - 错误: ${detail.error}`);
          }
        });
        report.push('');
      }

      report.push('## 建议');
      if (result.migratedFiles > 0) {
        report.push('- ✅ 已自动修复文件路径问题');
      }
      if (result.unrepairedFiles > 0) {
        report.push('- ⚠️ 部分文件无法找到，建议用户重新上传');
      }
      if (result.validFiles === result.fileMessages) {
        report.push('- ✅ 所有文件消息状态正常');
      }

    } catch (error: any) {
      report.push('## 检查失败');
      report.push(`错误信息: ${error.message}`);
    }

    return report.join('\n');
  }
}
