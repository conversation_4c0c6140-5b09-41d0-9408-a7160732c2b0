/**
 * 设备注册服务
 * 管理设备信息、推送令牌和设备注册状态
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import WeChatAPIService from './WeChatAPIService';
import { userDeviceManager } from '../utils/UserDeviceManager';

export interface DeviceInfo {
  deviceId: string;
  deviceName: string;
  platform: string;
  appVersion: string;
  pushToken?: string;
  registeredAt?: string;
  lastSyncedId?: number;
}

export interface DeviceRegistrationResult {
  success: boolean;
  deviceInfo?: DeviceInfo;
  error?: string;
}

export class DeviceRegistrationService {
  private static instance: DeviceRegistrationService; // Add static instance property
  private static readonly DEVICE_INFO_KEY = 'device_registration_info';
  private static readonly PUSH_TOKEN_KEY = 'jpush_registration_id';
  private static readonly LAST_SYNCED_ID_KEY = 'last_synced_message_id';

  private constructor() {} // Private constructor for singleton pattern

  public static getInstance(): DeviceRegistrationService {
    if (!DeviceRegistrationService.instance) {
      DeviceRegistrationService.instance = new DeviceRegistrationService();
    }
    return DeviceRegistrationService.instance;
  }

  /**
   * 获取设备信息
   */
  async getDeviceInfo(): Promise<DeviceInfo | null> {
    try {
      const deviceInfoStr = await AsyncStorage.getItem(DeviceRegistrationService.DEVICE_INFO_KEY);
      if (!deviceInfoStr) {
        return null;
      }

      const deviceInfo: DeviceInfo = JSON.parse(deviceInfoStr);

      // 获取最新的推送令牌和同步ID
      const pushToken = await this.getPushToken();
      const lastSyncedId = await this.getLastSyncedId();

      return {
        ...deviceInfo,
        pushToken: pushToken || deviceInfo.pushToken,
        lastSyncedId: lastSyncedId || deviceInfo.lastSyncedId,
      };
    } catch (error) {
      console.error('[DeviceRegistration] 获取设备信息失败:', error);
      return null;
    }
  }

  /**
   * 保存设备信息
   */
  async saveDeviceInfo(deviceInfo: DeviceInfo): Promise<void> {
    try {
      await AsyncStorage.setItem(
        DeviceRegistrationService.DEVICE_INFO_KEY,
        JSON.stringify(deviceInfo)
      );
      console.log('[DeviceRegistration] 设备信息已保存:', deviceInfo.deviceId);
    } catch (error) {
      console.error('[DeviceRegistration] 保存设备信息失败:', error);
    }
  }

  /**
   * 获取推送令牌
   */
  async getPushToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(DeviceRegistrationService.PUSH_TOKEN_KEY);
    } catch (error) {
      console.error('[DeviceRegistration] 获取推送令牌失败:', error);
      return null;
    }
  }

  /**
   * 设置推送令牌
   */
  async setPushToken(pushToken: string): Promise<void> {
    try {
      await AsyncStorage.setItem(DeviceRegistrationService.PUSH_TOKEN_KEY, pushToken);
      console.log('[DeviceRegistration] 推送令牌已更新:', pushToken.substring(0, 16) + '...');

      // 如果设备已注册，更新推送令牌
      const deviceInfo = await this.getDeviceInfo();
      if (deviceInfo) {
        await this.registerDevice({
          ...deviceInfo,
          pushToken,
        });
      }
    } catch (error) {
      console.error('[DeviceRegistration] 设置推送令牌失败:', error);
    }
  }

  /**
   * 获取最后同步的消息ID
   */
  async getLastSyncedId(): Promise<number | null> {
    try {
      const lastSyncedIdStr = await AsyncStorage.getItem(DeviceRegistrationService.LAST_SYNCED_ID_KEY);
      return lastSyncedIdStr ? parseInt(lastSyncedIdStr, 10) : null;
    } catch (error) {
      console.error('[DeviceRegistration] 获取最后同步ID失败:', error);
      return null;
    }
  }

  /**
   * 设置最后同步的消息ID
   */
  static async setLastSyncedId(id: number): Promise<void> {
    try {
      await AsyncStorage.setItem(this.LAST_SYNCED_ID_KEY, id.toString());
      console.log(`[DeviceRegistrationService] 最后同步ID已更新为: ${id}`);
    } catch (error) {
      console.error('[DeviceRegistrationService] 保存最后同步ID失败:', error);
    }
  }

  static async getLastSyncedId(): Promise<number | null> {
    try {
      const id = await AsyncStorage.getItem(this.LAST_SYNCED_ID_KEY);
      return id ? parseInt(id, 10) : null;
    } catch (error) {
      console.error('[DeviceRegistrationService] 获取最后同步ID失败:', error);
      return null;
    }
  }

  /**
   * 生成设备名称
   */
  private generateDeviceName(): string {
    const platform = userDeviceManager.getCurrentPlatform() === 'ios' ? 'iPhone' : 'Android';
    const timestamp = new Date().toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
    return `${platform} ${timestamp}`;
  }

  /**
   * 获取应用版本（使用统一管理器）
   */
  private async getAppVersion(): Promise<string> {
    const deviceInfo = await userDeviceManager.getDeviceInfo();
    return deviceInfo.appVersion;
  }

  /**
   * 注册设备
   */
  async registerDevice(customDeviceInfo?: Partial<DeviceInfo>): Promise<DeviceRegistrationResult> {
    try {
      console.log('[DeviceRegistration] 开始注册设备...');

      // 获取当前设备信息
      const currentDeviceInfo = await this.getDeviceInfo();
      const userUuid = await WeChatAPIService.getUserUuid();
      const deviceId = await WeChatAPIService.getDeviceId();
      const pushToken = await this.getPushToken();

      // 构建设备信息
      const deviceInfo: DeviceInfo = {
        deviceId,
        deviceName: customDeviceInfo?.deviceName || currentDeviceInfo?.deviceName || this.generateDeviceName(),
        platform: customDeviceInfo?.platform || currentDeviceInfo?.platform || userDeviceManager.getCurrentPlatform(),
        appVersion: customDeviceInfo?.appVersion || currentDeviceInfo?.appVersion || await this.getAppVersion(),
        pushToken: customDeviceInfo?.pushToken || pushToken || currentDeviceInfo?.pushToken,
        lastSyncedId: customDeviceInfo?.lastSyncedId || currentDeviceInfo?.lastSyncedId || 0,
        ...customDeviceInfo,
      };

      // 调用API注册设备
      const response = await WeChatAPIService.registerDevice({
        userUuid,
        deviceId: deviceInfo.deviceId,
        deviceName: deviceInfo.deviceName,
        platform: deviceInfo.platform,
        appVersion: deviceInfo.appVersion,
        pushToken: deviceInfo.pushToken,
      });

      if (!response.success) {
        console.error('[DeviceRegistration] 设备注册失败:', response.error);
        return {
          success: false,
          error: response.error || '设备注册失败',
        };
      }

      // 更新设备信息
      const updatedDeviceInfo: DeviceInfo = {
        ...deviceInfo,
        registeredAt: response.data?.registered_at || new Date().toISOString(),
      };

      // 保存设备信息
      await this.saveDeviceInfo(updatedDeviceInfo);

      console.log('[DeviceRegistration] 设备注册成功:', updatedDeviceInfo.deviceId);
      return {
        success: true,
        deviceInfo: updatedDeviceInfo,
      };
    } catch (error) {
      console.error('[DeviceRegistration] 设备注册异常:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '设备注册异常',
      };
    }
  }

  /**
   * 检查设备是否已注册
   */
  async isDeviceRegistered(): Promise<boolean> {
    try {
      const deviceInfo = await this.getDeviceInfo();
      return deviceInfo !== null && !!deviceInfo.registeredAt;
    } catch (error) {
      console.error('[DeviceRegistration] 检查设备注册状态失败:', error);
      return false;
    }
  }

  /**
   * 重新注册设备（用于更新设备信息）
   */
  async reregisterDevice(): Promise<DeviceRegistrationResult> {
    console.log('[DeviceRegistration] 重新注册设备...');
    return this.registerDevice();
  }

  /**
   * 清除设备注册信息
   */
  async clearDeviceRegistration(): Promise<void> {
    try {
      await AsyncStorage.removeItem(DeviceRegistrationService.DEVICE_INFO_KEY);
      await AsyncStorage.removeItem(DeviceRegistrationService.LAST_SYNCED_ID_KEY);
      console.log('[DeviceRegistration] 设备注册信息已清除');
    } catch (error) {
      console.error('[DeviceRegistration] 清除设备注册信息失败:', error);
    }
  }

  /**
   * 获取设备状态摘要
   */
  async getDeviceStatusSummary(): Promise<{
    isRegistered: boolean;
    deviceId?: string;
    deviceName?: string;
    platform?: string;
    registeredAt?: string;
    hasPushToken: boolean;
    lastSyncedId?: number;
  }> {
    try {
      const deviceInfo = await this.getDeviceInfo();
      const isRegistered = await this.isDeviceRegistered();

      return {
        isRegistered,
        deviceId: deviceInfo?.deviceId,
        deviceName: deviceInfo?.deviceName,
        platform: deviceInfo?.platform,
        registeredAt: deviceInfo?.registeredAt,
        hasPushToken: !!deviceInfo?.pushToken,
        lastSyncedId: deviceInfo?.lastSyncedId,
      };
    } catch (error) {
      console.error('[DeviceRegistration] 获取设备状态摘要失败:', error);
      return {
        isRegistered: false,
        hasPushToken: false,
      };
    }
  }

  /**
   * 初始化设备注册（应用启动时调用）
   */
  async initializeDeviceRegistration(): Promise<DeviceRegistrationResult> {
    try {
      console.log('[DeviceRegistration] 初始化设备注册...');

      // 检查是否已注册
      const isRegistered = await this.isDeviceRegistered();

      if (isRegistered) {
        console.log('[DeviceRegistration] 设备已注册，跳过初始化');
        const deviceInfo = await this.getDeviceInfo();
        return {
          success: true,
          deviceInfo: deviceInfo || undefined,
        };
      }

      // 首次注册
      return this.registerDevice();
    } catch (error) {
      console.error('[DeviceRegistration] 初始化设备注册失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '初始化失败',
      };
    }
  }
}

