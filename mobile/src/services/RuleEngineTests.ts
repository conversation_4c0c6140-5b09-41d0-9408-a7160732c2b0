// 公职猫 - 规则引擎测试用例
// 用于验证政务文档识别的准确性和功能

import { analyzeText, AnalysisResult } from './AIBridge';

/**
 * 政务文档测试样例
 */
export const GOVERNMENT_TEST_CASES = [
  // P0: 角色识别测试
  {
    name: '主办角色识别',
    text: '请办公室主办此次会议，各部门协办配合。',
    expectedEntities: {
      role: '主办',
      department: '办公室',
    },
    expectedIntent: '任务分配',
  },
  {
    name: '协办角色识别',
    text: '由财政局牵头，人社局、教育局协办开展专项检查。',
    expectedEntities: {
      role: '协办',
      department: '财政局',
    },
  },

  // P0: 时间识别测试
  {
    name: '具体时间识别',
    text: '定于2024年3月15日上午9点召开党委会。',
    expectedEntities: {
      date: '2024年3月15日',
      time: '上午9点',
    },
    expectedIntent: '会议通知',
  },
  {
    name: '相对时间识别',
    text: '请明天下午3点前完成材料准备。',
    expectedEntities: {
      date: '明天',
      time: '下午3点',
    },
  },

  // P1: 紧急程度识别测试
  {
    name: '紧急程度识别',
    text: '紧急通知：请各单位立即上报疫情防控情况。',
    expectedEntities: {
      urgency: '紧急',
    },
    expectedIntent: '信息同步',
  },

  // 综合场景测试
  {
    name: '会议通知综合场景',
    text: '关于召开全市脱贫攻坚专题会的通知\n\n各区县政府，市直各部门：\n\n定于明天（3月20日）上午9:00在市政府三楼会议室召开全市脱贫攻坚专题会，请各单位主要负责人务必参加。联系人：张主任，电话：13812345678。\n\n请各部门做好汇报准备。',
    expectedEntities: {
      date: '明天',
      time: '9:00',
      location: '三楼',
      contact: '13812345678',
      role: '参与',
    },
    expectedIntent: '会议通知',
  },

  {
    name: '任务分配综合场景',
    text: '经研究决定，由发改委牵头负责制定"十四五"规划实施方案，财政局、统计局协办，各区县配合，请于本周五前报送初稿。此项工作列为重点任务，请抓紧时间完成。',
    expectedEntities: {
      role: '牵头',
      department: '发改委',
      urgency: '重点',
      date: '本周五',
    },
    expectedIntent: '任务分配',
  },
];

/**
 * 运行规则引擎测试
 */
export const runRuleEngineTests = async (): Promise<void> => {
  console.log('🧪 开始规则引擎测试...\n');

  let passedTests = 0;
  let totalTests = GOVERNMENT_TEST_CASES.length;

  for (const testCase of GOVERNMENT_TEST_CASES) {
    console.log(`📋 测试案例: ${testCase.name}`);
    console.log(`📝 输入文本: ${testCase.text.substring(0, 50)}...`);

    try {
      const result: AnalysisResult = await analyzeText(testCase.text);

      console.log('🔍 识别结果:');
      console.log(`   意图: ${result.intent} (置信度: ${result.confidence.toFixed(2)})`);
      console.log(`   实体: ${JSON.stringify(result.entities, null, 6)}`);
      console.log(`   匹配规则: ${result.matchedRules?.join(', ') || '无'}`);

      // 验证期望结果
      let testPassed = true;
      const issues: string[] = [];

      if (testCase.expectedIntent && result.intent !== testCase.expectedIntent) {
        testPassed = false;
        issues.push(`意图不匹配: 期望 ${testCase.expectedIntent}, 实际 ${result.intent}`);
      }

      if (testCase.expectedEntities) {
        for (const [key, expectedValue] of Object.entries(testCase.expectedEntities)) {
          if (result.entities[key] !== expectedValue) {
            testPassed = false;
            issues.push(`实体 ${key} 不匹配: 期望 ${expectedValue}, 实际 ${result.entities[key] || '未识别'}`);
          }
        }
      }

      if (testPassed) {
        console.log('✅ 测试通过\n');
        passedTests++;
      } else {
        console.log('❌ 测试失败:');
        issues.forEach(issue => console.log(`   - ${issue}`));
        console.log('');
      }

    } catch (error) {
      console.log(`💥 测试异常: ${error}\n`);
    }
  }

  console.log(`🎯 测试总结: ${passedTests}/${totalTests} 通过 (${((passedTests / totalTests) * 100).toFixed(1)}%)`);

  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！规则引擎工作正常。');
  } else {
    console.log('⚠️  部分测试失败，需要优化规则引擎。');
  }
};

/**
 * 交互式测试函数，用于真机调试
 */
export const testTextAnalysis = async (text: string): Promise<void> => {
  console.log(`🔍 分析文本: "${text}"`);

  try {
    const result = await analyzeText(text);

    console.log('📊 分析结果:');
    console.log(`意图: ${result.intent}`);
    console.log(`置信度: ${result.confidence.toFixed(2)}`);
    console.log(`处理方式: ${result.processedBy}`);
    console.log('实体信息:');

    Object.entries(result.entities).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });

    if (result.matchedRules && result.matchedRules.length > 0) {
      console.log('匹配规则:');
      result.matchedRules.forEach(rule => console.log(`  - ${rule}`));
    }

  } catch (error) {
    console.error('分析失败:', error);
  }
};
