/**
 * 语音命令解析服务
 * 用于解析用户的语音输入，提取任务信息、时间安排等
 */

export interface VoiceCommand {
  type: 'task' | 'meeting' | 'reminder' | 'query' | 'unknown';
  text: string;
  confidence: number;
  entities: {
    time?: string;
    date?: string;
    location?: string;
    person?: string[];
    action?: string;
    priority?: 'high' | 'medium' | 'low';
  };
  rawText: string;
}

export class VoiceCommandParser {
  private taskKeywords = [
    '任务', '工作', '安排', '办理', '处理', '完成', '做', '搞定',
    '准备', '整理', '撰写', '起草', '修改', '审核', '汇报',
  ];

  private meetingKeywords = [
    '会议', '开会', '座谈', '讨论', '交流', '汇报', '例会',
    '专题会', '工作会', '碰头会', '视频会议',
  ];

  private timePatterns = [
    /(\d{1,2})[点|时]/g,                    // 几点
    /(\d{1,2})[：|:](\d{2})/g,              // 几点几分
    /(上午|下午|晚上|早上|中午)/g,            // 时间段
    /(今天|明天|后天|大后天)/g,              // 相对日期
    /(\d{1,2})月(\d{1,2})[日|号]/g,         // 绝对日期
    /(周|星期)([一二三四五六日天])/g,        // 星期
    /(下周|本周|这周|上周)/g,                // 相对星期
  ];

  private priorityKeywords = {
    high: ['紧急', '急', '重要', '优先', '马上', '立即', '尽快'],
    medium: ['一般', '正常', '常规'],
    low: ['不急', '有时间', '方便时'],
  };

  private personPatterns = [
    /([张王李赵刘陈杨黄吴周徐孙马朱胡林郭何高罗郑梁谢宋唐许邓冯韩曹曾彭肖蔡潘田董袁于余叶蒋杜苏魏程吕丁沈任姚卢傅钟姜崔谭廖范汪陆金石戴贾韦夏邱方侯邹熊孟秦白江阎薛尹段雷黎史龙陶贺顾毛郝龚邵万钱严赖覃洪武莫孔汤向常温康施文牛樊葛邢安齐易乔伍庞颜倪庄聂章鲁岑薄翟殷詹申欧耿关兰焦俞左柳甘祝包宁尚符茅封芮羊储汲邴糜松井段富巫乌焉历戈终居衡步都耿満弘匡国文寇广禄阙东欧殳解薄雍辛韶简施邸富弟顾咸欧慎缇彤霍皮卞孔端木巫马公西漆雕乐正壤驷公良拓跋夹谷宇文长孙慕容鲜于宗政濮阳尉迟羊舌锺离梁丘左丘东门西门南门][\u4e00-\u9fa5]{0,2})(主任|局长|部长|科长|处长|司长|秘书|领导|老师|同志|院长|校长|书记|副|董事长|总经理|经理|主管|组长|队长)/g,
  ];

  /**
   * 解析语音文本，提取命令信息
   */
  public parseVoiceText(text: string): VoiceCommand {
    const normalizedText = text.trim().toLowerCase();

    // 判断命令类型
    const commandType = this.identifyCommandType(normalizedText);

    // 提取实体信息
    const entities = this.extractEntities(text, normalizedText);

    // 计算置信度
    const confidence = this.calculateConfidence(commandType, entities, normalizedText);

    return {
      type: commandType,
      text: this.cleanText(text),
      confidence,
      entities,
      rawText: text,
    };
  }

  /**
   * 识别命令类型 - 按优先级排序，避免关键词冲突
   */
  private identifyCommandType(text: string): VoiceCommand['type'] {
    // 优先检查提醒关键词（最具体）
    if (text.includes('提醒') || text.includes('记住') || text.includes('别忘')) {
      return 'reminder';
    }

    // 检查查询关键词
    if (text.includes('什么') || text.includes('怎么') || text.includes('查') || text.includes('看')) {
      return 'query';
    }

    // 检查会议关键词（优先于任务，因为会议是特殊类型的任务）
    if (this.meetingKeywords.some(keyword => text.includes(keyword))) {
      return 'meeting';
    }

    // 最后检查任务关键词（最宽泛）
    if (this.taskKeywords.some(keyword => text.includes(keyword))) {
      return 'task';
    }

    return 'unknown';
  }

  /**
   * 提取实体信息
   */
  private extractEntities(originalText: string, normalizedText: string): VoiceCommand['entities'] {
    const entities: VoiceCommand['entities'] = {};

    // 提取时间信息
    const timeInfo = this.extractTimeInfo(originalText);
    if (timeInfo.time) {entities.time = timeInfo.time;}
    if (timeInfo.date) {entities.date = timeInfo.date;}

    // 提取地点信息
    const location = this.extractLocation(originalText);
    if (location) {entities.location = location;}

    // 提取人员信息
    const persons = this.extractPersons(originalText);
    if (persons.length > 0) {entities.person = persons;}

    // 提取动作信息
    const action = this.extractAction(normalizedText);
    if (action) {entities.action = action;}

    // 提取优先级信息
    const priority = this.extractPriority(normalizedText);
    if (priority) {entities.priority = priority;}

    return entities;
  }

  /**
   * 提取时间信息
   */
  private extractTimeInfo(text: string): { time?: string; date?: string } {
    const result: { time?: string; date?: string } = {};

    // 提取具体时间
    const timeMatch = text.match(/(\d{1,2})[点时](\d{1,2}分?)?|(\d{1,2})[：:](\d{2})/);
    if (timeMatch) {
      if (timeMatch[1]) {
        result.time = timeMatch[2] ? `${timeMatch[1]}:${timeMatch[2].replace('分', '')}` : `${timeMatch[1]}:00`;
      } else if (timeMatch[3]) {
        result.time = `${timeMatch[3]}:${timeMatch[4]}`;
      }
    }

    // 提取时间段（如果没有具体时间）
    const periodMatch = text.match(/(上午|下午|晚上|早上|中午)/);
    if (periodMatch) {
      if (result.time) {
        // 如果已有具体时间，将时间段作为前缀
        result.time = `${periodMatch[1]} ${result.time}`;
      } else {
        // 如果没有具体时间，使用时间段
        result.time = periodMatch[1];
      }
    }

    // 提取日期
    const dateMatch = text.match(/(今天|明天|后天|大后天)|(\d{1,2})月(\d{1,2})[日号]|(周|星期)([一二三四五六日天])|(下周|本周|这周|上周)/);
    if (dateMatch) {
      result.date = dateMatch[0];
    }

    return result;
  }

  /**
   * 提取地点信息
   */
  private extractLocation(text: string): string | undefined {
    // 常见的地点关键词
    const locationPatterns = [
      /在(.{0,10})(会议室|办公室|大厅|楼|室|厅|所|局|部|处|科)(\d+|[一二三四五六七八九十]+)?/,
      /(会议室|办公室|大厅)(\d+|[一二三四五六七八九十]+)/,
      /去(.{1,8})(那里|那边|现场)/,
    ];

    for (const pattern of locationPatterns) {
      const match = text.match(pattern);
      if (match) {
        if (match[3]) {
          // 情况1：在...会议室205
          return match[1] + match[2] + match[3];
        } else if (match[2] && !match[3]) {
          // 情况2：会议室205
          return match[1] + match[2];
        } else {
          // 情况3：去...那里
          return match[1] + (match[2] || '');
        }
      }
    }

    return undefined;
  }

  /**
   * 提取人员信息
   */
  private extractPersons(text: string): string[] {
    const persons: string[] = [];
    const matches = text.matchAll(this.personPatterns[0]);

    for (const match of matches) {
      if (match[1]) {
        persons.push(match[1] + (match[2] || ''));
      }
    }

    return [...new Set(persons)]; // 去重
  }

  /**
   * 提取动作信息
   */
  private extractAction(text: string): string | undefined {
    const actionPatterns = [
      '准备', '整理', '撰写', '起草', '修改', '审核', '汇报',
      '检查', '督办', '跟进', '协调', '安排', '通知', '联系',
    ];

    for (const action of actionPatterns) {
      if (text.includes(action)) {
        return action;
      }
    }

    return undefined;
  }

  /**
   * 提取优先级信息
   */
  private extractPriority(text: string): VoiceCommand['entities']['priority'] {
    for (const [priority, keywords] of Object.entries(this.priorityKeywords)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        return priority as VoiceCommand['entities']['priority'];
      }
    }

    return undefined;
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(
    type: VoiceCommand['type'],
    entities: VoiceCommand['entities'],
    text: string
  ): number {
    let confidence = 0.3; // 基础置信度

    // 根据命令类型调整
    if (type !== 'unknown') {
      confidence += 0.3;
    }

    // 根据实体数量调整
    const entityCount = Object.keys(entities).length;
    confidence += Math.min(entityCount * 0.1, 0.3);

    // 根据文本长度调整（适中的长度更有意义）
    const textLength = text.length;
    if (textLength >= 10 && textLength <= 50) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * 清理文本
   */
  private cleanText(text: string): string {
    return text
      .replace(/[，。！？；：]/g, '') // 移除标点符号
      .replace(/\s+/g, ' ')          // 合并空格
      .trim();
  }

  /**
   * 格式化解析结果为可读文本
   */
  public formatResult(command: VoiceCommand): string {
    const parts: string[] = [];

    parts.push(`类型: ${this.getTypeLabel(command.type)}`);
    parts.push(`置信度: ${(command.confidence * 100).toFixed(0)}%`);

    if (command.entities.action) {
      parts.push(`动作: ${command.entities.action}`);
    }

    if (command.entities.time || command.entities.date) {
      const timeStr = [command.entities.date, command.entities.time].filter(Boolean).join(' ');
      parts.push(`时间: ${timeStr}`);
    }

    if (command.entities.location) {
      parts.push(`地点: ${command.entities.location}`);
    }

    if (command.entities.person && command.entities.person.length > 0) {
      parts.push(`涉及人员: ${command.entities.person.join(', ')}`);
    }

    if (command.entities.priority) {
      parts.push(`优先级: ${this.getPriorityLabel(command.entities.priority)}`);
    }

    return parts.join('\n');
  }

  private getTypeLabel(type: VoiceCommand['type']): string {
    const labels = {
      task: '任务',
      meeting: '会议',
      reminder: '提醒',
      query: '查询',
      unknown: '未知',
    };
    return labels[type];
  }

  private getPriorityLabel(priority: string): string {
    const labels: Record<string, string> = {
      high: '高',
      medium: '中',
      low: '低',
    };
    return labels[priority] || priority;
  }
}

export default new VoiceCommandParser();
