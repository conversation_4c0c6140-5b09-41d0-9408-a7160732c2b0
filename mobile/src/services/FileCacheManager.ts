import RNFS from 'react-native-fs';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 缓存项接口
interface CacheItem {
  filePath: string;
  fileName: string;
  fileSize: number;
  lastAccessed: number;
  accessCount: number;
  isProtected: boolean; // 是否受保护（不会被自动清理）
  fileType: string;
  createdAt: number;
}

// 缓存统计信息
interface CacheStats {
  totalSize: number;
  totalFiles: number;
  protectedFiles: number;
  oldestFile: number;
  newestFile: number;
}

// 清理策略配置
interface CleanupConfig {
  maxCacheSize: number; // 最大缓存大小（字节）
  maxFiles: number; // 最大文件数量
  maxAge: number; // 最大文件年龄（毫秒）
  protectedFileTypes: string[]; // 受保护的文件类型
}

class FileCacheManager {
  private static readonly CACHE_KEY = 'file_cache_metadata';
  private static readonly DEFAULT_CONFIG: CleanupConfig = {
    maxCacheSize: 500 * 1024 * 1024, // 500MB
    maxFiles: 1000,
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
    protectedFileTypes: ['pdf', 'doc', 'docx', 'ofd'], // 政务文档类型
  };

  private cacheItems: Map<string, CacheItem> = new Map();
  private config: CleanupConfig = FileCacheManager.DEFAULT_CONFIG;

  /**
   * 初始化缓存管理器
   */
  async initialize(): Promise<void> {
    try {
      await this.loadCacheMetadata();
      await this.validateCacheItems();
    } catch (error) {
      console.error('[FileCacheManager] 初始化失败:', error);
    }
  }

  /**
   * 添加文件到缓存
   */
  async addToCache(filePath: string, fileName: string, isProtected: boolean = false): Promise<void> {
    try {
      const stat = await RNFS.stat(filePath);
      const fileType = fileName.split('.').pop()?.toLowerCase() || '';

      const cacheItem: CacheItem = {
        filePath,
        fileName,
        fileSize: stat.size,
        lastAccessed: Date.now(),
        accessCount: 1,
        isProtected: isProtected || this.config.protectedFileTypes.includes(fileType),
        fileType,
        createdAt: Date.now(),
      };

      this.cacheItems.set(filePath, cacheItem);
      await this.saveCacheMetadata();

      // 检查是否需要清理
      await this.checkAndCleanup();
    } catch (error) {
      console.error('[FileCacheManager] 添加缓存失败:', error);
    }
  }

  /**
   * 访问文件（更新访问统计）
   */
  async accessFile(filePath: string): Promise<void> {
    const item = this.cacheItems.get(filePath);
    if (item) {
      item.lastAccessed = Date.now();
      item.accessCount++;
      await this.saveCacheMetadata();
    }
  }

  /**
   * 移除文件从缓存
   */
  async removeFromCache(filePath: string): Promise<void> {
    try {
      this.cacheItems.delete(filePath);
      await this.saveCacheMetadata();

      // 删除实际文件
      const exists = await RNFS.exists(filePath);
      if (exists) {
        await RNFS.unlink(filePath);
      }
    } catch (error) {
      console.error('[FileCacheManager] 移除缓存失败:', error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): CacheStats {
    let totalSize = 0;
    let protectedFiles = 0;
    let oldestFile = Date.now();
    let newestFile = 0;

    for (const item of this.cacheItems.values()) {
      totalSize += item.fileSize;
      if (item.isProtected) {protectedFiles++;}
      if (item.createdAt < oldestFile) {oldestFile = item.createdAt;}
      if (item.createdAt > newestFile) {newestFile = item.createdAt;}
    }

    return {
      totalSize,
      totalFiles: this.cacheItems.size,
      protectedFiles,
      oldestFile,
      newestFile,
    };
  }

  /**
   * 智能清理缓存
   */
  async smartCleanup(targetSize?: number): Promise<number> {
    const stats = this.getCacheStats();
    const maxSize = targetSize || this.config.maxCacheSize;

    if (stats.totalSize <= maxSize && stats.totalFiles <= this.config.maxFiles) {
      return 0; // 不需要清理
    }

    // 获取可清理的文件列表（排除受保护的文件）
    const cleanableItems = Array.from(this.cacheItems.values())
      .filter(item => !item.isProtected)
      .sort((a, b) => this.calculateCleanupPriority(a) - this.calculateCleanupPriority(b));

    let cleanedSize = 0;
    let cleanedCount = 0;

    for (const item of cleanableItems) {
      if (stats.totalSize - cleanedSize <= maxSize &&
          stats.totalFiles - cleanedCount <= this.config.maxFiles) {
        break;
      }

      try {
        await this.removeFromCache(item.filePath);
        cleanedSize += item.fileSize;
        cleanedCount++;
      } catch (error) {
        console.error('[FileCacheManager] 清理文件失败:', item.filePath, error);
      }
    }

    console.log(`[FileCacheManager] 智能清理完成: 清理了 ${cleanedCount} 个文件，释放 ${this.formatFileSize(cleanedSize)} 空间`);
    return cleanedSize;
  }

  /**
   * 计算清理优先级（数值越小优先级越高，越先被清理）
   */
  private calculateCleanupPriority(item: CacheItem): number {
    const now = Date.now();
    const ageWeight = 0.4;
    const sizeWeight = 0.3;
    const accessWeight = 0.3;

    // 年龄分数（越老分数越低）
    const ageScore = (now - item.lastAccessed) / this.config.maxAge;

    // 大小分数（越大分数越低）
    const sizeScore = item.fileSize / (10 * 1024 * 1024); // 以10MB为基准

    // 访问频率分数（访问越少分数越低）
    const accessScore = 1 / Math.max(item.accessCount, 1);

    return ageWeight * ageScore + sizeWeight * sizeScore + accessWeight * accessScore;
  }

  /**
   * 检查并执行清理
   */
  private async checkAndCleanup(): Promise<void> {
    const stats = this.getCacheStats();

    if (stats.totalSize > this.config.maxCacheSize ||
        stats.totalFiles > this.config.maxFiles) {
      await this.smartCleanup();
    }
  }

  /**
   * 加载缓存元数据
   */
  private async loadCacheMetadata(): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(FileCacheManager.CACHE_KEY);
      if (data) {
        const items = JSON.parse(data) as CacheItem[];
        this.cacheItems = new Map(items.map(item => [item.filePath, item]));
      }
    } catch (error) {
      console.error('[FileCacheManager] 加载缓存元数据失败:', error);
    }
  }

  /**
   * 保存缓存元数据
   */
  private async saveCacheMetadata(): Promise<void> {
    try {
      const items = Array.from(this.cacheItems.values());
      await AsyncStorage.setItem(FileCacheManager.CACHE_KEY, JSON.stringify(items));
    } catch (error) {
      console.error('[FileCacheManager] 保存缓存元数据失败:', error);
    }
  }

  /**
   * 验证缓存项（移除不存在的文件）
   */
  private async validateCacheItems(): Promise<void> {
    const invalidItems: string[] = [];

    for (const [filePath, _item] of this.cacheItems.entries()) {
      try {
        const exists = await RNFS.exists(filePath);
        if (!exists) {
          invalidItems.push(filePath);
        }
      } catch (error) {
        invalidItems.push(filePath);
      }
    }

    // 移除无效项
    for (const filePath of invalidItems) {
      this.cacheItems.delete(filePath);
    }

    if (invalidItems.length > 0) {
      await this.saveCacheMetadata();
      console.log(`[FileCacheManager] 清理了 ${invalidItems.length} 个无效缓存项`);
    }
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes < 1024) {return `${bytes} B`;}
    if (bytes < 1024 * 1024) {return `${(bytes / 1024).toFixed(1)} KB`;}
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<CleanupConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  getConfig(): CleanupConfig {
    return { ...this.config };
  }

  /**
   * 强制清理所有非保护文件
   */
  async forceCleanup(): Promise<number> {
    return await this.smartCleanup(0);
  }
}

export default new FileCacheManager();
