/**
 * 简化的微信绑定服务
 * APP自动跳转到微信服务号并发送绑定确认消息，用户无需手动操作
 *
 * 重构版本：
 * - 使用配置管理器消除硬编码
 * - 集成通用的用户设备管理工具
 * - 适配新的API端点和签名机制
 * - 提供生产级别的错误处理和重试机制
 */

import { AppState } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { weChatBindingConfig } from '../config/wechatBindingConfig';
import { userDeviceManager } from '../utils/UserDeviceManager';
import {
  safeRegisterApp,
  safeIsWechatInstalled,
  safeOpenCustomerService,
  getSDKStatus,
} from '../utils/SafeWeChatSDK';

import { SignatureService } from './SignatureService';

// 绑定状态接口
export interface SimpleWeChatBinding {
  isBinding: boolean;
  bindingTime?: number;
  bindingToken?: string;
  userId?: string;
  wechatId?: string;
  verified?: boolean; // 是否经过服务器验证（true: 服务器确认, false: 本地缓存）
}

// 绑定结果接口
export interface BindingResult {
  success: boolean;
  message: string;
  binding?: SimpleWeChatBinding | null;
  error?: string;
}



// 绑定状态检查响应接口
interface BindingStatusResponse {
  success: boolean;
  status?: 'pending' | 'success' | 'failed';
  bound?: boolean;
  message?: string;
  error?: string;
}

/**
 * 简化版微信绑定服务
 * 核心功能：一键跳转到微信客服完成绑定
 */
export class SimpleWeChatBindingService {
  private static instance: SimpleWeChatBindingService;
  private jpushRegistrationId: string | null = null;
  private pollingTimer: number | null = null;
  private bindingStatusListeners: Array<(binding: SimpleWeChatBinding | null) => void> = [];

  private constructor() {
    console.log('[SimpleWeChatBindingService] 初始化微信绑定服务...');
    // 异步注册微信应用，不阻塞构造函数
    this.registerWeChatApp().catch(error => {
      console.error('[SimpleWeChatBindingService] 构造函数中注册微信应用失败:', error);
    });
    AppState.addEventListener('change', this._handleAppStateChange.bind(this));
  }



  private async _handleAppStateChange(nextAppState: string) {
    if (nextAppState === 'active') {
      console.log('[SimpleWeChatBindingService] App has come to the foreground!');
      const isBinding = await AsyncStorage.getItem('binding_in_progress');
      if (isBinding === 'true') {
        console.log('[SimpleWeChatBindingService] Binding in progress, checking status...');

        // 开始轮询检查绑定状态，最多检查10次
        this.startBindingStatusPolling();
      }
    }
  }

  /**
   * 注册微信应用
   */
  private async registerWeChatApp(): Promise<void> {
    try {
      const config = weChatBindingConfig.getConfig();

      console.log('[SimpleWeChatBindingService] 开始注册微信应用...');

      const success = await safeRegisterApp({
        appid: config.wechat.openPlatform.appId,
        universalLink: config.wechat.openPlatform.universalLink,
        log: __DEV__, // 开发模式下启用日志
      });

      if (success) {
        console.log('[SimpleWeChatBindingService] 微信应用注册成功');
      } else {
        console.warn('[SimpleWeChatBindingService] 微信应用注册失败，但应用可继续运行');
        console.log('[SimpleWeChatBindingService] SDK状态:', getSDKStatus());
      }
    } catch (error) {
      console.error('[SimpleWeChatBindingService] 注册微信应用失败:', error);
      // 不抛出错误，允许应用继续运行
    }
  }

  public static getInstance(): SimpleWeChatBindingService {
    if (!SimpleWeChatBindingService.instance) {
      SimpleWeChatBindingService.instance = new SimpleWeChatBindingService();
    }
    return SimpleWeChatBindingService.instance;
  }

  /**
   * 设置JPush注册ID
   */
  public setJPushRegistrationId(registrationId: string): void {
    this.jpushRegistrationId = registrationId;
    console.log('[SimpleWeChatBindingService] JPush注册ID已设置:', registrationId);
  }



  /**
   * 检查微信安装状态
   */
  public async checkWeChatInstallation(): Promise<boolean> {
    try {
      const installed = await safeIsWechatInstalled();
      console.log('[SimpleWeChatBindingService] 微信安装状态:', installed);
      return installed;
    } catch (error) {
      console.error('[SimpleWeChatBindingService] 检查微信安装状态失败:', error);
      return false;
    }
  }



  /**
   * 获取本地绑定信息（不调用API）
   */
  private async getLocalBindingInfo(): Promise<SimpleWeChatBinding | null> {
    try {
      const config = weChatBindingConfig.getConfig();
      const bindingTimeStr = await AsyncStorage.getItem(config.storage.keys.bindingTime);

      if (bindingTimeStr) {
        const bindingTime = parseInt(bindingTimeStr, 10);
        const userUuid = await userDeviceManager.getUserUuid();

        return {
          isBinding: true,
          bindingTime,
          userId: userUuid,
        };
      }
      return null;
    } catch (error) {
      console.error('[SimpleWeChatBindingService] 获取本地绑定信息失败:', error);
      return null;
    }
  }

  /**
   * 保存本地绑定信息
   */
  private async saveLocalBindingInfo(bindingInfo: SimpleWeChatBinding): Promise<void> {
    try {
      const config = weChatBindingConfig.getConfig();

      if (bindingInfo.bindingTime) {
        await AsyncStorage.setItem(config.storage.keys.bindingTime, bindingInfo.bindingTime.toString());
      }

      console.log('[SimpleWeChatBindingService] 本地绑定信息已保存');
    } catch (error) {
      console.error('[SimpleWeChatBindingService] 保存本地绑定信息失败:', error);
    }
  }

  /**
   * 获取当前绑定状态
   * 策略：本地优先，减少不必要的API调用
   */
  public async getCurrentBinding(): Promise<SimpleWeChatBinding | null> {
    try {
      // 1. 首先检查本地绑定状态
      const localBinding = await this.getLocalBindingInfo();

      if (localBinding) {
        // 本地有绑定信息，直接返回（标记为未验证，表示基于本地数据）
        console.log('[SimpleWeChatBindingService] 使用本地绑定状态，避免API调用');
        return { ...localBinding, verified: false };
      } else {
        // 本地没有绑定信息，返回null（表示未绑定）
        console.log('[SimpleWeChatBindingService] 本地无绑定信息，用户未绑定');
        return null;
      }
    } catch (error) {
      console.error('[SimpleWeChatBindingService] 获取本地绑定状态失败:', error);
      return null;
    }
  }

  /**
   * 获取当前绑定状态（带网络验证）
   * 仅在需要验证绑定状态时调用，如手动刷新、绑定操作后验证等
   */
  public async getCurrentBindingWithVerification(): Promise<SimpleWeChatBinding | null> {
    try {
      // 调用API验证绑定状态
      const statusResult = await this.checkBindingStatus();

      if (statusResult.success) {
        if (statusResult.bound) {
          // 服务器确认已绑定，保存到本地状态并标记为已验证
          const bindingInfo: SimpleWeChatBinding = {
            isBinding: true,
            verified: true,
            userId: await userDeviceManager.getUserUuid(),
            bindingTime: Date.now(),
          };

          // 保存到本地状态
          await this.saveLocalBindingInfo(bindingInfo);
          console.log('[SimpleWeChatBindingService] 服务器确认已绑定，已保存到本地状态');

          // 通知状态变更
          this.notifyBindingStatusChange(bindingInfo);
          return bindingInfo;
        } else {
          // 服务器确认未绑定，清除本地状态
          await this.clearBindingStatus();

          // 通知状态变更
          this.notifyBindingStatusChange(null);
          return null;
        }
      } else {
        // API调用失败，回退到本地状态
        console.log('[SimpleWeChatBindingService] API调用失败，使用本地状态');
        const localBinding = await this.getLocalBindingInfo();
        return localBinding ? { ...localBinding, verified: false } : null;
      }
    } catch (error) {
      console.error('[SimpleWeChatBindingService] 获取绑定状态失败:', error);
      // 异常情况下也检查本地状态
      const localBinding = await this.getLocalBindingInfo();
      return localBinding ? { ...localBinding, verified: false } : null;
    }
  }

  /**
   * 检查绑定状态（仅在必要时调用API）
   */
  public async checkBindingStatus(): Promise<{
    success: boolean;
    bound?: boolean;
    message?: string;
    error?: string;
  }> {
    try {
      console.log('[SimpleWeChatBindingService] 调用API检查绑定状态');

      const userDeviceInfo = await userDeviceManager.getUserDeviceInfo();
      const baseUrl = weChatBindingConfig.getApiUrl('status');
      const endpoint = `${baseUrl}?user_uuid=${userDeviceInfo.userUuid}`;

      const response = await SignatureService.signedFetch(endpoint, 'GET');

      const data: BindingStatusResponse = await response.json();

      if (response.ok) {
        return {
          success: true,
          bound: data.status === 'success' || data.bound === true,
          message: data.message,
        };
      } else {
        // 404 也表示未绑定
        if (response.status === 404) {
          return { success: true, bound: false, message: '用户未绑定' };
        }
        return {
          success: false,
          error: data.error || '检查绑定状态失败',
        };
      }
    } catch (error) {
      const errorMessage = this.formatError(error, '网络错误');
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 清除绑定状态
   */
  public async clearBindingStatus(): Promise<void> {
    const config = weChatBindingConfig.getConfig();

    try {
      await Promise.all([
        AsyncStorage.removeItem(config.storage.keys.bindingToken),
        AsyncStorage.removeItem(config.storage.keys.bindingTime),
      ]);

      // 停止轮询
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
      }

      console.log('[SimpleWeChatBindingService] 绑定状态已清除');
    } catch (error) {
      console.error('[SimpleWeChatBindingService] 清除绑定状态失败:', error);
    }
  }

  /**
   * 刷新绑定状态（强制网络验证）
   */
  public async refreshBindingStatus(): Promise<BindingResult> {
    console.log('[SimpleWeChatBindingService] 手动刷新绑定状态，进行网络验证');

    // 使用带网络验证的方法
    const binding = await this.getCurrentBindingWithVerification();

    return {
      success: true,
      message: binding ? '绑定状态正常' : '未绑定',
      binding,
    };
  }

  /**
   * 开始绑定（一键绑定方式）
   */
  public async startBinding(): Promise<BindingResult> {
    try {
      console.log('[SimpleWeChatBindingService] 开始一键绑定流程...');

      // 1. 检查微信是否安装
      const isWeChatInstalled = await this.checkWeChatInstallation();
      if (!isWeChatInstalled) {
        return {
          success: false,
          message: '请先安装微信客户端',
        };
      }

      // 2. 获取用户UUID
      const userDeviceInfo = await userDeviceManager.getUserDeviceInfo();
      if (!userDeviceInfo.userUuid) {
        return {
          success: false,
          message: '无法获取用户UUID，请重试',
        };
      }

      // 3. 调用函数获取一键绑定链接
      const apiUrl = weChatBindingConfig.getApiUrl('oneClickBinding');

      const response = await SignatureService.signedFetch(
        apiUrl,
        'POST',
        { user_uuid: userDeviceInfo.userUuid }
      );

      const data = await response.json();

      if (!response.ok || !data.success) {
        if (data.already_bound) {
          return {
            success: false,
            message: '您已经绑定过微信账号了',
          };
        }
        return {
          success: false,
          message: data.error || '获取绑定链接失败',
        };
      }

      // 4. 记录绑定链接信息用于调试
      console.log('[SimpleWeChatBindingService] 绑定链接详情:');
      console.log('- 完整URL:', data.binding_url);
      console.log('- 企业ID:', weChatBindingConfig.getConfig().wechat.corpId);
      console.log('- 用户UUID:', userDeviceInfo.userUuid);

      // 检查链接格式
      if (!data.binding_url.includes('work.weixin.qq.com')) {
        console.warn('[SimpleWeChatBindingService] 警告：链接不是企业微信域名');
      }

      if (!data.binding_url.includes('scene_param')) {
        console.warn('[SimpleWeChatBindingService] 警告：链接缺少scene_param参数');
      }

      // 5. 使用企业微信SDK跳转到客服
      const customerServiceResult = await safeOpenCustomerService({
        corpid: weChatBindingConfig.getConfig().wechat.corpId,
        url: data.binding_url,
      });

      if (!customerServiceResult) {
        console.warn('[SimpleWeChatBindingService] 微信客服跳转失败');
        return {
          success: false,
          message: '跳转微信客服失败，请手动打开微信',
        };
      }

      console.log('[SimpleWeChatBindingService] 一键绑定跳转成功');

      // 5. 标记绑定流程进行中
      await AsyncStorage.setItem('binding_in_progress', 'true');

      return {
        success: true,
        message: '已跳转到微信客服，进入对话即可完成绑定',
      };

    } catch (error) {
      console.error('[SimpleWeChatBindingService] 一键绑定失败:', error);
      return {
        success: false,
        message: this.formatError(error, '一键绑定失败'),
      };
    }
  }



  /**
   * 解除绑定
   */
  public async unbind(): Promise<BindingResult> {
    try {
      console.log('[SimpleWeChatBindingService] 开始解除绑定...');

      // 1. 调用服务器端解绑API
      const userUuid = await userDeviceManager.getUserUuid();
      console.log('[SimpleWeChatBindingService] 用户UUID:', userUuid);

      const apiUrl = weChatBindingConfig.getApiUrl('unbind');
      console.log('[SimpleWeChatBindingService] 解绑API URL:', apiUrl);

      const response = await SignatureService.signedFetch(
        apiUrl,
        'DELETE',
        { user_uuid: userUuid }
      );

      console.log('[SimpleWeChatBindingService] 解绑API响应状态:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[SimpleWeChatBindingService] 解绑API HTTP错误:', response.status, errorText);
        return {
          success: false,
          message: `服务器错误: ${response.status}`,
        };
      }

      const apiResult = await response.json();
      console.log('[SimpleWeChatBindingService] 解绑API响应:', apiResult);

      if (!apiResult.success) {
        console.error('[SimpleWeChatBindingService] 服务器端解绑失败:', apiResult.message);
        return {
          success: false,
          message: apiResult.message || '服务器端解绑失败',
        };
      }

      // 2. 清除本地状态
      await this.clearBindingStatus();
      console.log('[SimpleWeChatBindingService] 本地状态已清除');

      // 3. 通知状态变更
      this.notifyBindingStatusChange(null);

      return {
        success: true,
        message: '解除绑定成功',
      };
    } catch (error) {
      console.error('[SimpleWeChatBindingService] 解除绑定失败:', error);
      return {
        success: false,
        message: '解除绑定失败',
      };
    }
  }

  /**
   * 格式化绑定时间
   */


  /**
   * 检查是否可以同步消息（基于本地状态，无API调用）
   */
  public async canSync(): Promise<boolean> {
    try {
      const binding = await this.getCurrentBinding();
      return binding !== null && binding.isBinding;
    } catch (error) {
      console.error('[SimpleWeChatBindingService] 检查同步状态失败:', error);
      return false;
    }
  }

  /**
   * 开始绑定状态轮询（从微信返回后）
   * 优化：减少API调用频率，降低云函数成本
   */
  private async startBindingStatusPolling(): Promise<void> {
    let pollCount = 0;
    const maxPolls = 6; // 6次（60秒总时长）

    console.log('[SimpleWeChatBindingService] 开始轮询绑定状态...');

    // 优化：使用递增延迟策略，减少总调用次数
    const checkWithDelay = async (delay: number) => {
      await new Promise(resolve => setTimeout(resolve, delay));

      pollCount++;
      console.log(`[SimpleWeChatBindingService] 第${pollCount}次检查绑定状态`);

      try {
        const status = await this.checkBindingStatus();

        if (status.success && status.bound) {
          // 绑定成功，保存到本地状态
          const bindingInfo: SimpleWeChatBinding = {
            isBinding: true,
            verified: true,
            userId: await userDeviceManager.getUserUuid(),
            bindingTime: Date.now(),
          };

          await this.saveLocalBindingInfo(bindingInfo);
          await AsyncStorage.removeItem('binding_in_progress');
          console.log('[SimpleWeChatBindingService] 绑定成功！已保存到本地状态');
          this.notifyBindingSuccess();

          // 通知状态变更
          this.notifyBindingStatusChange(bindingInfo);

          // 立即停止轮询
          this.stopPolling();
          return true;
        }

        if (pollCount >= maxPolls) {
          // 轮询超时
          await AsyncStorage.removeItem('binding_in_progress');
          console.log('[SimpleWeChatBindingService] 绑定检查超时');
          this.notifyBindingTimeout();
          return false;
        }

        console.log(`[SimpleWeChatBindingService] 第${pollCount}次检查：尚未绑定，继续等待...`);

        // 递增延迟：3s, 5s, 8s, 12s, 18s, 25s (总计71秒，6次调用)
        const nextDelay = Math.min(3000 + pollCount * 2000, 25000);
        return await checkWithDelay(nextDelay);

      } catch (error) {
        console.error(`[SimpleWeChatBindingService] 第${pollCount}次检查失败:`, error);

        if (pollCount >= maxPolls) {
          await AsyncStorage.removeItem('binding_in_progress');
          this.notifyBindingError();
          return false;
        }

        // 错误时也使用递增延迟
        const nextDelay = Math.min(3000 + pollCount * 2000, 25000);
        return await checkWithDelay(nextDelay);
      }
    };

    // 开始第一次检查（延迟2秒，给绑定处理一些时间）
    checkWithDelay(2000);
  }

  /**
   * 通知绑定成功
   */
  private notifyBindingSuccess(): void {
    // 这里可以发送通知或触发回调
    console.log('[SimpleWeChatBindingService] 🎉 微信绑定成功！');
    // 可以在这里添加用户通知逻辑
  }

  /**
   * 通知绑定超时
   */
  private notifyBindingTimeout(): void {
    console.log('[SimpleWeChatBindingService] ⏰ 绑定检查超时，请手动刷新状态');
    // 可以在这里添加用户通知逻辑
  }

  /**
   * 通知绑定错误
   */
  private notifyBindingError(): void {
    console.log('[SimpleWeChatBindingService] ❌ 绑定状态检查出错');
    // 可以在这里添加用户通知逻辑
  }



  /**
   * 格式化错误信息
   */
  private formatError(error: unknown, defaultMessage: string): string {
    if (error && typeof error === 'object') {
      if ('message' in error && typeof error.message === 'string') {
        return `${defaultMessage}: ${error.message}`;
      } else {
        try {
          return `${defaultMessage}: ${JSON.stringify(error)}`;
        } catch {
          return `${defaultMessage}: 未知错误`;
        }
      }
    } else if (typeof error === 'string') {
      return `${defaultMessage}: ${error}`;
    }

    return defaultMessage;
  }

  /**
   * 获取服务状态摘要（用于调试）
   */
  public async getServiceSummary(): Promise<{
    config: object;
    userDevice: object;
    binding: SimpleWeChatBinding | null;
    isPolling: boolean;
  }> {
    const [binding, userDeviceInfo] = await Promise.all([
      this.getCurrentBinding(),
      userDeviceManager.getDetailedInfo(),
    ]);

    return {
      config: weChatBindingConfig.getConfigSummary(),
      userDevice: userDeviceInfo,
      binding,
      isPolling: this.pollingTimer !== null,
    };
  }

  /**
   * 添加绑定状态监听器
   */
  public addBindingStatusListener(listener: (binding: SimpleWeChatBinding | null) => void): void {
    this.bindingStatusListeners.push(listener);
    console.log('[SimpleWeChatBindingService] 添加绑定状态监听器，当前监听器数量:', this.bindingStatusListeners.length);
  }

  /**
   * 移除绑定状态监听器
   */
  public removeBindingStatusListener(listener: (binding: SimpleWeChatBinding | null) => void): void {
    const index = this.bindingStatusListeners.indexOf(listener);
    if (index > -1) {
      this.bindingStatusListeners.splice(index, 1);
      console.log('[SimpleWeChatBindingService] 移除绑定状态监听器，当前监听器数量:', this.bindingStatusListeners.length);
    }
  }

  /**
   * 通知所有监听器绑定状态变更
   */
  private notifyBindingStatusChange(binding: SimpleWeChatBinding | null): void {
    console.log('[SimpleWeChatBindingService] 通知绑定状态变更，监听器数量:', this.bindingStatusListeners.length);
    this.bindingStatusListeners.forEach(listener => {
      try {
        listener(binding);
      } catch (error) {
        console.error('[SimpleWeChatBindingService] 绑定状态监听器执行失败:', error);
      }
    });
  }

  /**
   * 停止轮询
   */
  private stopPolling(): void {
    if (this.pollingTimer) {
      clearTimeout(this.pollingTimer);
      this.pollingTimer = null;
      console.log('[SimpleWeChatBindingService] 轮询已停止');
    }
  }
}

// 创建单例实例并导出
const serviceInstance = SimpleWeChatBindingService.getInstance();
export default serviceInstance;
