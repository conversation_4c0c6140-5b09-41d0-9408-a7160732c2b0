import { Platform } from 'react-native';
import * as chrono from 'chrono-node';

/**
 * AI分析结果的统一接口
 */
export interface AnalysisResult {
  /**
   * 识别出的意图或任务类型
   * e.g., '会议通知', '任务分配', '信息同步'
   */
  intent: string;
  /**
   * 提取出的关键实体
   */
  entities: {
    [key: string]: any;
    date?: string;
    time?: string;
    location?: string;
    participants?: string[];
    role?: string;
    urgency?: string;
    department?: string;
    contact?: string;
  };
  /**
   * AI处理的置信度
   */
  confidence: number;
  /**
   * 使用的AI处理层级
   */
  processedBy: 'rule_engine' | 'onnx' | 'apple_intelligence' | 'cloud_ai';
  /**
   * 调试信息 - 匹配到的规则
   */
  matchedRules?: string[];
}

/**
 * 政务文档专业词典 - 基于真实政府工作场景
 */
const GOVERNMENT_DICTIONARY = {
  // 角色识别关键词 - P0优先级（基于真实公文用词）
  roles: {
    主办: ['主办', '牵头', '负责', '主持', '承办', '组织', '统筹', '牵头负责', '主要负责'],
    协办: ['协办', '配合', '协助', '支持', '参与配合', '协同', '配合实施', '协助办理'],
    参与: ['参与', '列席', '参加', '到场', '出席', '旁听', '参会', '与会'],
    报告: ['汇报', '报告', '通报', '反馈', '上报', '呈报', '专题汇报'],
    督办: ['督办', '督查', '监督', '检查', '巡查', '跟踪督办', '专项督查'],
  },

  // 紧急程度识别 - P1优先级（基于政府公文标准）
  urgency: {
    特急: ['特急', '十万火急', '特别紧急', '火急'],
    加急: ['加急', '急件', '紧急', '急办'],
    从速: ['从速', '尽快', '抓紧', '迅速', '及时', '立即', '马上', '即刻'],
    重要: ['重要', '重点', '关键', '核心', '首要', '优先'],
    一般: ['一般', '常规', '正常', '普通', '例行'],
  },

  // 会议类型（基于政府会议体系）
  meetingTypes: [
    '党委会', '常委会', '主任办公会', '专题会', '调度会', '推进会',
    '座谈会', '汇报会', '部署会', '总结会', '动员会', '表彰会',
    '民主生活会', '组织生活会', '警示教育会', '述职会', '评议会',
    '联席会议', '协调会', '现场会', '观摩会', '交流会',
  ],

  // 地点关键词 - P1优先级（基于政府办公场所）
  locations: [
    '会议室', '办公室', '大厅', '礼堂', '报告厅', '会堂', '接待室',
    '政府大楼', '办公大厦', '行政中心', '党校', '培训中心',
    '一楼', '二楼', '三楼', '四楼', '五楼', '六楼', '七楼', '八楼',
    '大会议室', '小会议室', '视频会议室', '多功能厅',
  ],

  // 人员职位 - P1优先级（基于政府职务体系）
  positions: [
    '主任', '副主任', '局长', '副局长', '处长', '副处长',
    '科长', '副科长', '主席', '副主席', '书记', '副书记',
    '部长', '副部长', '司长', '副司长', '厅长', '副厅长',
    '县长', '副县长', '市长', '副市长', '省长', '副省长',
    '同志', '领导', '负责人', '分管领导', '主管领导',
    '主任科员', '副主任科员', '一级主任科员', '二级主任科员',
  ],

  // 部门机构（基于标准政府部门设置）
  departments: [
    '办公室', '组织部', '宣传部', '统战部', '政法委', '纪委监委',
    '发改委', '财政局', '人社局', '住建局', '交通局', '水利局',
    '教育局', '卫健委', '民政局', '农业局', '商务局', '文旅局',
    '应急管理局', '市场监管局', '生态环境局', '自然资源局',
    '人大办', '政协办', '法院', '检察院', '公安局', '司法局',
  ],
};

/**
 * 政务意图识别模式（基于真实公文场景）
 */
const INTENT_PATTERNS = {
  会议通知: [
    /会议.*通知/, /召开.*会/, /定于.*开会/, /会议安排/, /会议议程/,
    /请.*参加/, /会议时间/, /会议地点/, /参会人员/, /会议要求/,
  ],
  任务分配: [
    /请.*负责/, /安排.*办理/, /交办/, /督办/, /责成/,
    /请.*牵头/, /请.*协办/, /分工负责/, /任务分工/, /工作安排/,
  ],
  信息同步: [
    /通报/, /告知/, /转发/, /抄送/, /知悉/, /周知/,
    /情况说明/, /工作简报/, /信息报送/, /情况通报/,
  ],
  请示报告: [
    /请示/, /申请/, /报告/, /汇报/, /请批/, /请审/,
    /请批示/, /请审定/, /请指示/, /专题报告/, /工作报告/,
  ],
  督查督办: [
    /督查/, /督办/, /检查/, /巡查/, /监督/, /核查/,
    /落实情况/, /整改/, /问责/, /追责/, /督导检查/,
  ],
};

/**
 * 简单的中文分词实现（基于词典匹配）
 * 替代@node-rs/jieba，确保React Native兼容性
 */
const simpleChineseTokenizer = (text: string): string[] => {
  const tokens: string[] = [];
  const allWords = [
    ...Object.values(GOVERNMENT_DICTIONARY.roles).flat(),
    ...Object.values(GOVERNMENT_DICTIONARY.urgency).flat(),
    ...GOVERNMENT_DICTIONARY.meetingTypes,
    ...GOVERNMENT_DICTIONARY.locations,
    ...GOVERNMENT_DICTIONARY.positions,
    ...GOVERNMENT_DICTIONARY.departments,
  ];

  // 按长度排序，优先匹配长词
  const sortedWords = allWords.sort((a, b) => b.length - a.length);

  let remainingText = text;
  let position = 0;

  while (position < text.length) {
    let matched = false;

    // 尝试匹配词典中的词
    for (const word of sortedWords) {
      if (remainingText.startsWith(word)) {
        tokens.push(word);
        remainingText = remainingText.slice(word.length);
        position += word.length;
        matched = true;
        break;
      }
    }

    // 如果没有匹配到词典词，按字符分割
    if (!matched) {
      const char = text[position];
      if (char && char.trim()) {
        tokens.push(char);
      }
      remainingText = remainingText.slice(1);
      position++;
    }
  }

  return tokens.filter(token => token.trim().length > 0);
};

/**
 * 配置chrono中文时间识别
 */
const configureChronoForChinese = () => {
  // 创建自定义的chrono实例，支持中文
  const customChrono = chrono.casual.clone();

  // 添加中文时间表达式支持
  const chineseTimePatterns = [
    { pattern: /今天/, replacement: 'today' },
    { pattern: /明天/, replacement: 'tomorrow' },
    { pattern: /后天/, replacement: 'day after tomorrow' },
    { pattern: /昨天/, replacement: 'yesterday' },
    { pattern: /前天/, replacement: 'day before yesterday' },
    { pattern: /下周/, replacement: 'next week' },
    { pattern: /上周/, replacement: 'last week' },
    { pattern: /本周/, replacement: 'this week' },
    { pattern: /下月/, replacement: 'next month' },
    { pattern: /上月/, replacement: 'last month' },
    { pattern: /本月/, replacement: 'this month' },
  ];

  return { chineseTimePatterns, customChrono };
};

/**
 * 基于开源库的增强规则引擎
 * @param text - 输入文本
 * @returns 分析结果
 */
const enhancedRuleBasedEngine = async (text: string): Promise<AnalysisResult> => {
  const result: AnalysisResult = {
    intent: '未知',
    entities: {},
    confidence: 0.1,
    processedBy: 'rule_engine',
    matchedRules: [],
  };

  const originalText = text;

  try {
    // 1. 增强的时间识别（chrono + 基础正则）
    const { chineseTimePatterns, customChrono } = configureChronoForChinese();

    // 预处理中文时间表达式
    let preprocessedText = originalText;
    chineseTimePatterns.forEach(({ pattern, replacement }) => {
      preprocessedText = preprocessedText.replace(pattern, replacement);
    });

    // 使用chrono解析时间
    try {
      const chronoResults = customChrono.parse(preprocessedText);
      if (chronoResults.length > 0) {
        const timeResult = chronoResults[0];
        if (timeResult.start.get('year')) {
          result.entities.date = timeResult.start.date().toISOString().split('T')[0];
        }
        if (timeResult.start.get('hour') !== undefined) {
          result.entities.time = timeResult.start.date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
          });
        }
        result.matchedRules?.push(`chrono时间识别: ${timeResult.text} -> ${result.entities.date || ''} ${result.entities.time || ''}`);
        result.confidence += 0.3; // chrono识别给予更高置信度
      }
    } catch (chronoError) {
      console.warn('Chrono解析失败:', chronoError);
    }

    // 补充基础时间识别（作为兜底）
    if (!result.entities.date) {
      const basicTimePatterns = [
        /(\d{4}年\d{1,2}月\d{1,2}日)/,
        /(\d{1,2}月\d{1,2}日)/,
        /(明天)/,
        /(后天)/,
        /(昨天)/,
        /(今天)/,
        /(下周\w*)/,
        /(上周\w*)/,
        /(本周\w*)/,
      ];

      for (const pattern of basicTimePatterns) {
        const match = originalText.match(pattern);
        if (match) {
          result.entities.date = match[1];
          result.matchedRules?.push(`基础时间识别: ${match[1]}`);
          result.confidence += 0.2;
          break;
        }
      }
    }

    // 2. 使用简单中文分词，提高关键词匹配准确性
    const words = simpleChineseTokenizer(originalText);
    const wordSet = new Set(words);

    // 3. 基于分词结果进行角色识别 - P0优先级
    for (const [roleType, keywords] of Object.entries(GOVERNMENT_DICTIONARY.roles)) {
      for (const keyword of keywords) {
        if (wordSet.has(keyword) || originalText.includes(keyword)) {
          result.entities.role = roleType;
          result.matchedRules?.push(`jieba角色识别: ${keyword} -> ${roleType}`);
          result.confidence += 0.25;
          break;
        }
      }
      if (result.entities.role) {break;}
    }

    // 4. 紧急程度识别 - P1优先级
    for (const [urgencyLevel, keywords] of Object.entries(GOVERNMENT_DICTIONARY.urgency)) {
      for (const keyword of keywords) {
        if (wordSet.has(keyword) || originalText.includes(keyword)) {
          result.entities.urgency = urgencyLevel;
          result.matchedRules?.push(`紧急程度识别: ${keyword} -> ${urgencyLevel}`);
          result.confidence += 0.2;
          break;
        }
      }
      if (result.entities.urgency) {break;}
    }

    // 5. 意图识别（基于正则模式） - 调整优先级，督查督办优先识别
    const intentPriority = ['督查督办', '请示报告', '会议通知', '任务分配', '信息同步'];
    for (const intentType of intentPriority) {
      const patterns = INTENT_PATTERNS[intentType as keyof typeof INTENT_PATTERNS];
      for (const pattern of patterns) {
        if (pattern.test(originalText)) {
          result.intent = intentType;
          result.matchedRules?.push(`意图识别: ${pattern.source} -> ${intentType}`);
          result.confidence += 0.3;

          // 特殊处理：为督查督办和请示报告设置对应的角色（覆盖之前的角色识别）
          if (intentType === '督查督办') {
            result.entities.role = '督办';
            result.matchedRules?.push('意图派生角色: 督查督办 -> 督办 (覆盖之前的角色)');
          }
          if (intentType === '请示报告') {
            result.entities.role = '报告';
            result.matchedRules?.push('意图派生角色: 请示报告 -> 报告 (覆盖之前的角色)');
          }
          break;
        }
      }
      if (result.intent !== '未知') {break;}
    }

    // 6. 地点识别 - P1优先级（优化：先识别具体楼层，再识别一般地点）
    // 优先识别楼层信息
    const floorRegex = /(一|二|三|四|五|六|七|八|九|十|\d+)楼/;
    const floorMatch = originalText.match(floorRegex);
    if (floorMatch) {
      result.entities.location = floorMatch[0];
      result.matchedRules?.push(`楼层识别: ${floorMatch[0]}`);
      result.confidence += 0.15;
    } else {
      // 回退到一般地点识别
      for (const location of GOVERNMENT_DICTIONARY.locations) {
        if (wordSet.has(location) || originalText.includes(location)) {
          result.entities.location = location;
          result.matchedRules?.push(`地点识别: ${location}`);
          result.confidence += 0.15;
          break;
        }
      }
    }

    // 7. 人员和职位识别 - P1优先级（利用分词结果）
    const participants: string[] = [];
    for (const position of GOVERNMENT_DICTIONARY.positions) {
      if (wordSet.has(position)) {
        // 在分词结果中查找职位前的可能人名
        const wordArray = Array.from(words);
        const positionIndex = wordArray.findIndex(w => w === position);
        if (positionIndex > 0) {
          const potentialName = wordArray[positionIndex - 1] as string;
          if (potentialName && potentialName.length >= 2 && potentialName.length <= 4) {
            participants.push(`${potentialName}${position}`);
            result.matchedRules?.push(`jieba人员识别: ${potentialName}${position}`);
            result.confidence += 0.15;
          }
        }
      }
    }
    if (participants.length > 0) {
      result.entities.participants = participants;
    }

    // 8. 部门识别（基于分词结果）
    for (const department of GOVERNMENT_DICTIONARY.departments) {
      if (wordSet.has(department) || originalText.includes(department)) {
        result.entities.department = department;
        result.matchedRules?.push(`部门识别: ${department}`);
        result.confidence += 0.15;
        break;
      }
    }

    // 9. 联系方式识别（优化正则表达式）
    const phoneRegex = /(?:手机|电话|联系方式|联系电话|联系)[:：\s]*([1][3-9]\d{9})/;
    const simplePhoneRegex = /([1][3-9]\d{9})/; // 简化版，直接匹配11位手机号
    const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;

    let phoneMatch = originalText.match(phoneRegex);
    if (!phoneMatch) {
      phoneMatch = originalText.match(simplePhoneRegex);
    }

    if (phoneMatch) {
      result.entities.contact = phoneMatch[1];
      result.matchedRules?.push(`联系方式识别: ${phoneMatch[1]}`);
      result.confidence += 0.1;
    }

    const emailMatch = originalText.match(emailRegex);
    if (emailMatch && !result.entities.contact) {
      result.entities.contact = emailMatch[1];
      result.matchedRules?.push(`邮箱识别: ${emailMatch[1]}`);
      result.confidence += 0.1;
    }

  } catch (error) {
    console.warn('开源库处理失败，回退到基础规则:', error);
    result.matchedRules?.push('警告: 开源库处理异常，使用基础规则');
  }

  // 最终置信度限制在0.1-0.8之间（规则引擎保持合理自信度）
  result.confidence = Math.min(Math.max(result.confidence, 0.1), 0.8);

  return result;
};

/**
 * 统一AI调用接口，根据平台选择不同的处理方式
 * @param text - 需要分析的文本
 * @returns Promise<AnalysisResult>
 */
export const analyzeText = async (text: string): Promise<AnalysisResult> => {
  if (Platform.OS === 'ios') {
    // TODO: 调用Apple Intelligence原生模块
    console.log('使用Apple Intelligence进行分析 (待实现)');
    // 当前回退到增强规则引擎
    return await enhancedRuleBasedEngine(text);
  } else if (Platform.OS === 'android') {
    // TODO: 调用ONNX Runtime原生模块
    console.log('使用ONNX Runtime进行分析 (待实现)');
    // 当前回退到增强规则引擎
    return await enhancedRuleBasedEngine(text);
  } else {
    // 其他平台（如web，以防万一）
    console.log('未知平台，使用增强规则引擎');
    return await enhancedRuleBasedEngine(text);
  }
};
