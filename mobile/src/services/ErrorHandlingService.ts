/**
 * 错误处理服务
 * 提供统一的错误处理机制，包括错误分类、重试、用户友好提示等
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

// 错误类型定义
export enum ErrorType {
  NETWORK_TIMEOUT = 'NETWORK_TIMEOUT',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  USER_NOT_BOUND = 'USER_NOT_BOUND',
  INVALID_BINDING_TOKEN = 'INVALID_BINDING_TOKEN',
  SERVER_ERROR = 'SERVER_ERROR',
  MEDIA_DOWNLOAD_FAILED = 'MEDIA_DOWNLOAD_FAILED',
  PUSH_SERVICE_UNAVAILABLE = 'PUSH_SERVICE_UNAVAILABLE',
  BINDING_FAILED = 'BINDING_FAILED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 错误严重程度
export enum ErrorSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 错误分类
export enum ErrorCategory {
  NETWORK = 'network',
  AUTH = 'auth',
  BINDING = 'binding',
  SERVER = 'server',
  MEDIA = 'media',
  PUSH = 'push',
  UNKNOWN = 'unknown'
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType;
  message: string;
  context?: any;
  timestamp: string;
  severity: ErrorSeverity;
  category: ErrorCategory;
  shouldRetry: boolean;
  maxRetries: number;
  userMessage: string;
}

// 重试配置
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

// 错误处理结果
export interface ErrorHandlingResult {
  success: boolean;
  error?: ErrorInfo;
  retryCount?: number;
  recoveryAction?: string;
}

class ErrorHandlingService {
  private static instance: ErrorHandlingService;
  private errorLog: ErrorInfo[] = [];
  private retryAttempts: Map<string, number> = new Map();
  private errorCallbacks: Map<string, ((error: ErrorInfo) => void)[]> = new Map();

  private constructor() {}

  static getInstance(): ErrorHandlingService {
    if (!ErrorHandlingService.instance) {
      ErrorHandlingService.instance = new ErrorHandlingService();
    }
    return ErrorHandlingService.instance;
  }

  /**
   * 处理错误
   */
  async handleError(error: any, context?: any): Promise<ErrorHandlingResult> {
    const errorInfo = this.classifyError(error, context);

    // 记录错误日志
    await this.logError(errorInfo);

    // 显示用户友好提示
    this.showUserFriendlyMessage(errorInfo);

    // 触发错误回调
    this.triggerErrorCallbacks(errorInfo);

    // 判断是否需要重试
    if (errorInfo.shouldRetry) {
      const retryResult = await this.handleRetry(errorInfo);
      return retryResult;
    }

    // 执行恢复策略
    const recoveryAction = this.getRecoveryAction(errorInfo);

    return {
      success: false,
      error: errorInfo,
      recoveryAction,
    };
  }

  /**
   * 错误分类
   */
  private classifyError(error: any, context?: any): ErrorInfo {
    let errorType: ErrorType;
    let severity: ErrorSeverity;
    let category: ErrorCategory;
    let shouldRetry: boolean;
    let maxRetries: number;
    let userMessage: string;

    // 根据错误信息分类
    if (error.message?.includes('timeout') || error.code === 'TIMEOUT') {
      errorType = ErrorType.NETWORK_TIMEOUT;
      severity = ErrorSeverity.WARNING;
      category = ErrorCategory.NETWORK;
      shouldRetry = true;
      maxRetries = 3;
      userMessage = '网络连接超时，请检查网络状态后重试';
    } else if (error.message?.includes('Network') || error.code === 'NETWORK_ERROR') {
      errorType = ErrorType.NETWORK_ERROR;
      severity = ErrorSeverity.ERROR;
      category = ErrorCategory.NETWORK;
      shouldRetry = true;
      maxRetries = 2;
      userMessage = '网络连接失败，请检查网络设置';
    } else if (error.message?.includes('token') && error.message?.includes('expired')) {
      errorType = ErrorType.TOKEN_EXPIRED;
      severity = ErrorSeverity.WARNING;
      category = ErrorCategory.AUTH;
      shouldRetry = true;
      maxRetries = 1;
      userMessage = '访问令牌已过期，正在自动刷新...';
    } else if (error.message?.includes('not bound') || error.code === 'USER_NOT_BOUND') {
      errorType = ErrorType.USER_NOT_BOUND;
      severity = ErrorSeverity.INFO;
      category = ErrorCategory.BINDING;
      shouldRetry = false;
      maxRetries = 0;
      userMessage = '您还未绑定微信，请先完成绑定操作';
    } else if (error.message?.includes('invalid') && error.message?.includes('token')) {
      errorType = ErrorType.INVALID_BINDING_TOKEN;
      severity = ErrorSeverity.ERROR;
      category = ErrorCategory.BINDING;
      shouldRetry = false;
      maxRetries = 0;
      userMessage = '绑定令牌无效或已过期，请重新获取绑定令牌';
    } else if (error.status >= 500 || error.message?.includes('server error')) {
      errorType = ErrorType.SERVER_ERROR;
      severity = ErrorSeverity.ERROR;
      category = ErrorCategory.SERVER;
      shouldRetry = true;
      maxRetries = 2;
      userMessage = '服务器暂时不可用，请稍后重试';
    } else if (error.message?.includes('download') && error.message?.includes('failed')) {
      errorType = ErrorType.MEDIA_DOWNLOAD_FAILED;
      severity = ErrorSeverity.WARNING;
      category = ErrorCategory.MEDIA;
      shouldRetry = true;
      maxRetries = 3;
      userMessage = '文件下载失败，正在重试...';
    } else if (error.message?.includes('push') && error.message?.includes('unavailable')) {
      errorType = ErrorType.PUSH_SERVICE_UNAVAILABLE;
      severity = ErrorSeverity.WARNING;
      category = ErrorCategory.PUSH;
      shouldRetry = true;
      maxRetries = 2;
      userMessage = '推送服务暂时不可用，消息同步可能延迟';
    } else {
      errorType = ErrorType.UNKNOWN_ERROR;
      severity = ErrorSeverity.ERROR;
      category = ErrorCategory.UNKNOWN;
      shouldRetry = false;
      maxRetries = 0;
      userMessage = '操作失败，请稍后重试';
    }

    return {
      type: errorType,
      message: error.message || error.toString(),
      context,
      timestamp: new Date().toISOString(),
      severity,
      category,
      shouldRetry,
      maxRetries,
      userMessage,
    };
  }

  /**
   * 记录错误日志
   */
  private async logError(errorInfo: ErrorInfo): Promise<void> {
    try {
      // 添加到内存日志
      this.errorLog.push(errorInfo);

      // 保持日志大小在合理范围内
      if (this.errorLog.length > 100) {
        this.errorLog = this.errorLog.slice(-50);
      }

      // 保存到本地存储
      await AsyncStorage.setItem('error_log', JSON.stringify(this.errorLog));

      // 如果是严重错误，立即上报
      if (errorInfo.severity === ErrorSeverity.CRITICAL) {
        await this.reportCriticalError(errorInfo);
      }

      console.error('[ErrorHandling]', errorInfo);
    } catch (error) {
      console.error('[ErrorHandling] 记录错误日志失败:', error);
    }
  }

  /**
   * 显示用户友好提示
   */
  private showUserFriendlyMessage(errorInfo: ErrorInfo): void {
    const { userMessage, severity } = errorInfo;

    switch (severity) {
      case ErrorSeverity.INFO:
        // 可以使用Toast或其他轻量级提示
        console.info(userMessage);
        break;
      case ErrorSeverity.WARNING:
        Alert.alert('提示', userMessage, [{ text: '确定' }]);
        break;
      case ErrorSeverity.ERROR:
      case ErrorSeverity.CRITICAL:
        Alert.alert('错误', userMessage, [
          { text: '确定' },
          { text: '重试', onPress: () => this.handleRetryRequest(errorInfo) },
        ]);
        break;
    }
  }

  /**
   * 处理重试
   */
  private async handleRetry(errorInfo: ErrorInfo): Promise<ErrorHandlingResult> {
    const retryKey = `${errorInfo.type}_${errorInfo.timestamp}`;
    const currentRetries = this.retryAttempts.get(retryKey) || 0;

    if (currentRetries >= errorInfo.maxRetries) {
      this.retryAttempts.delete(retryKey);
      return {
        success: false,
        error: errorInfo,
        retryCount: currentRetries,
        recoveryAction: this.getRecoveryAction(errorInfo),
      };
    }

    // 计算延迟时间（指数退避）
    const delay = this.calculateRetryDelay(currentRetries, errorInfo.type);

    // 更新重试次数
    this.retryAttempts.set(retryKey, currentRetries + 1);

    // 等待后重试
    await new Promise(resolve => setTimeout(resolve, delay));

    return {
      success: true,
      retryCount: currentRetries + 1,
    };
  }

  /**
   * 计算重试延迟
   */
  private calculateRetryDelay(retryCount: number, errorType: ErrorType): number {
    const baseDelay = 1000; // 1秒
    const maxDelay = 30000; // 30秒
    const backoffFactor = 2;

    // 根据错误类型调整延迟
    const typeMultiplier = this.getDelayMultiplier(errorType);

    const delay = Math.min(
      baseDelay * Math.pow(backoffFactor, retryCount) * typeMultiplier,
      maxDelay
    );

    return delay;
  }

  /**
   * 获取延迟倍数
   */
  private getDelayMultiplier(errorType: ErrorType): number {
    switch (errorType) {
      case ErrorType.NETWORK_TIMEOUT:
        return 1.5;
      case ErrorType.NETWORK_ERROR:
        return 2;
      case ErrorType.TOKEN_EXPIRED:
        return 0.5;
      case ErrorType.SERVER_ERROR:
        return 3;
      case ErrorType.MEDIA_DOWNLOAD_FAILED:
        return 1;
      default:
        return 1;
    }
  }

  /**
   * 获取恢复策略
   */
  private getRecoveryAction(errorInfo: ErrorInfo): string {
    switch (errorInfo.type) {
      case ErrorType.NETWORK_TIMEOUT:
      case ErrorType.NETWORK_ERROR:
        return 'check_network_settings';
      case ErrorType.TOKEN_EXPIRED:
        return 'refresh_token';
      case ErrorType.USER_NOT_BOUND:
        return 'redirect_to_binding';
      case ErrorType.INVALID_BINDING_TOKEN:
        return 'regenerate_binding_token';
      case ErrorType.SERVER_ERROR:
        return 'contact_support';
      case ErrorType.MEDIA_DOWNLOAD_FAILED:
        return 'retry_download';
      case ErrorType.PUSH_SERVICE_UNAVAILABLE:
        return 'enable_manual_sync';
      default:
        return 'contact_support';
    }
  }

  /**
   * 处理用户重试请求
   */
  private async handleRetryRequest(errorInfo: ErrorInfo): Promise<void> {
    // 这里可以触发相应的重试逻辑
    console.log(`用户请求重试: ${errorInfo.type}`);
  }

  /**
   * 上报严重错误
   */
  private async reportCriticalError(errorInfo: ErrorInfo): Promise<void> {
    try {
      // 这里可以实现向服务器上报严重错误的逻辑
      console.error('[CRITICAL ERROR]', errorInfo);
    } catch (error) {
      console.error('上报严重错误失败:', error);
    }
  }

  /**
   * 触发错误回调
   */
  private triggerErrorCallbacks(errorInfo: ErrorInfo): void {
    const callbacks = this.errorCallbacks.get(errorInfo.type.toString()) || [];
    callbacks.forEach(callback => {
      try {
        callback(errorInfo);
      } catch (error) {
        console.error('错误回调执行失败:', error);
      }
    });
  }

  /**
   * 注册错误回调
   */
  registerErrorCallback(errorType: ErrorType, callback: (error: ErrorInfo) => void): void {
    const key = errorType.toString();
    if (!this.errorCallbacks.has(key)) {
      this.errorCallbacks.set(key, []);
    }
    this.errorCallbacks.get(key)!.push(callback);
  }

  /**
   * 移除错误回调
   */
  removeErrorCallback(errorType: ErrorType, callback: (error: ErrorInfo) => void): void {
    const key = errorType.toString();
    const callbacks = this.errorCallbacks.get(key);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * 获取错误日志
   */
  async getErrorLog(): Promise<ErrorInfo[]> {
    try {
      const logData = await AsyncStorage.getItem('error_log');
      return logData ? JSON.parse(logData) : [];
    } catch (error) {
      console.error('获取错误日志失败:', error);
      return [];
    }
  }

  /**
   * 清除错误日志
   */
  async clearErrorLog(): Promise<void> {
    try {
      this.errorLog = [];
      await AsyncStorage.removeItem('error_log');
    } catch (error) {
      console.error('清除错误日志失败:', error);
    }
  }

  /**
   * 获取错误统计
   */
  getErrorStatistics(): { [key: string]: number } {
    const stats: { [key: string]: number } = {};

    this.errorLog.forEach(error => {
      const key = error.type.toString();
      stats[key] = (stats[key] || 0) + 1;
    });

    return stats;
  }

  /**
   * 检查是否应该显示错误提示
   */
  shouldShowErrorMessage(errorType: ErrorType): boolean {
    // 避免相同错误类型的重复提示
    const recentErrors = this.errorLog
      .filter(error => error.type === errorType)
      .filter(error => Date.now() - new Date(error.timestamp).getTime() < 60000); // 1分钟内

    return recentErrors.length <= 3; // 1分钟内最多显示3次相同错误
  }
}

export default ErrorHandlingService.getInstance();
