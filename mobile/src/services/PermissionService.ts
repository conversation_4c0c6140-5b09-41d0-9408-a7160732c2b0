import { Platform, Alert, Linking } from 'react-native';
import {
  check,
  request,
  PERMISSIONS,
  Permission,
  PermissionStatus,
  openPhotoPicker,
} from 'react-native-permissions';

// 权限类型定义
export type AppPermissionType =
  | 'camera'
  | 'microphone'
  | 'photoLibrary'
  | 'location'
  | 'storage'
  | 'network';

// 权限映射
function getPermissionConstant(type: AppPermissionType): Permission | null {
  switch (type) {
    case 'camera':
      return Platform.select({
        ios: PERMISSIONS.IOS.CAMERA,
        android: PERMISSIONS.ANDROID.CAMERA,
        default: null,
      });
    case 'microphone':
      return Platform.select({
        ios: PERMISSIONS.IOS.MICROPHONE,
        android: PERMISSIONS.ANDROID.RECORD_AUDIO,
        default: null,
      });
    case 'photoLibrary':
      return Platform.select({
        ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
        android: PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
        default: null,
      });
    case 'location':
      return Platform.select({
        ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
        android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
        default: null,
      });
    case 'storage':
      return Platform.select({
        ios: null, // iOS无需单独存储权限
        android: PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
        default: null,
      });
    case 'network':
      // 网络权限通常无需动态申请，Android 6.0+自动授予
      return null;
    default:
      return null;
  }
}

// 检查权限状态
export async function checkPermission(type: AppPermissionType): Promise<PermissionStatus | 'unavailable'> {
  const perm = getPermissionConstant(type);
  if (!perm) {
    // 网络权限或iOS存储权限等无需动态申请
    return 'unavailable';
  }
  try {
    const status = await check(perm);
    // iOS下photoLibrary的limited直接返回'limited'，不再转为'granted'
    if (
      type === 'photoLibrary' &&
      Platform.OS === 'ios' &&
      status === 'limited'
    ) {
      return 'limited';
    }
    return status;
  } catch (e) {
    return 'unavailable';
  }
}

// 请求权限
export async function requestPermission(type: AppPermissionType): Promise<PermissionStatus | 'unavailable'> {
  const perm = getPermissionConstant(type);
  if (!perm) {
    // 网络权限或iOS存储权限等无需动态申请
    return 'unavailable';
  }
  try {
    return await request(perm);
  } catch (e) {
    return 'unavailable';
  }
}

// 定位权限可选原生API（如需更高精度，可后续扩展）
// 示例：navigator.geolocation.getCurrentPosition(...)

// 用法示例：
// const status = await checkPermission('camera');
// const result = await requestPermission('camera');

// 统一的权限被拒绝弹窗
export async function showPermissionDeniedAlert(
  type: AppPermissionType,
  onCancel?: () => void
) {
  console.log('[PermissionService] showPermissionDeniedAlert called, type:', type);
  let title = '';
  let message = '';
  switch (type) {
    case 'camera':
      title = '相机权限被拒绝';
      message = '请在系统设置中开启相机权限，否则无法使用拍照功能';
      break;
    case 'microphone':
      title = '麦克风权限被拒绝';
      message = '请在系统设置中开启麦克风权限，否则无法使用语音输入';
      break;
    case 'photoLibrary':
      title = '相册权限被拒绝';
      message = '请在系统设置中开启相册权限，否则无法选择图片';
      break;
    case 'location':
      title = '定位权限被拒绝';
      message = '请在系统设置中开启定位权限，否则无法获取位置信息';
      break;
    case 'storage':
      title = '存储权限被拒绝';
      message = '请在系统设置中开启存储权限，否则无法保存或读取文件';
      break;
    case 'network':
      title = '网络权限被拒绝';
      message = '请在系统设置中开启网络权限，否则部分功能无法使用';
      break;
    default:
      title = '权限被拒绝';
      message = '请在系统设置中开启相关权限';
  }

  // 特殊处理：iOS相册权限为limited时，弹窗增加"添加更多照片"按钮
  if (
    type === 'photoLibrary' &&
    Platform.OS === 'ios'
  ) {
    const perm = getPermissionConstant('photoLibrary');
    if (perm) {
      try {
        const status = await check(perm);
        console.log('[PermissionService] iOS photoLibrary check result:', status);
        if (status === 'limited') {
          console.log('[PermissionService] 弹出受限访问Alert');
          Alert.alert(
            title,
            '当前为受限访问，仅可访问部分照片。您可以添加更多照片或前往设置修改权限。',
            [
              {
                text: '添加更多照片',
                onPress: openLimitedPhotoLibraryPicker,
              },
              {
                text: '去设置',
                onPress: () => Linking.openSettings(),
              },
              {
                text: '取消',
                style: 'cancel',
                onPress: onCancel,
              },
            ],
            { cancelable: true }
          );
          return;
        }
      } catch (e) {
        console.log('[PermissionService] check(perm) error:', e);
      }
    }
  }

  // 其他情况维持原有弹窗
  console.log('[PermissionService] 弹出普通权限Alert');
  Alert.alert(
    title,
    message,
    [
      {
        text: '去设置',
        onPress: () => Linking.openSettings(),
      },
      {
        text: '取消',
        style: 'cancel',
        onPress: onCancel,
      },
    ],
    { cancelable: true }
  );
}

// 入口权限确保工具 - 检查+请求+弹窗一体化
export async function ensurePermission(type: AppPermissionType): Promise<boolean> {
  const status = await checkPermission(type);
  if (status === 'granted') {
    return true;
  }
  // iOS下photoLibrary limited也算可用
  if (type === 'photoLibrary' && Platform.OS === 'ios' && status === 'limited') {
    return true;
  }
  const reqStatus = await requestPermission(type);
  if (reqStatus === 'granted') {
    return true;
  }
  // iOS下photoLibrary limited也算可用
  if (type === 'photoLibrary' && Platform.OS === 'ios' && reqStatus === 'limited') {
    return true;
  }
  // 权限被拒绝，弹窗提示
  showPermissionDeniedAlert(type);
  return false;
}

// 判断是否应显示"添加更多照片"按钮（仅iOS且photoLibrary为limited时）
export async function shouldShowAddMorePhotosButton(): Promise<boolean> {
  if (Platform.OS !== 'ios') {return false;}
  const perm = getPermissionConstant('photoLibrary');
  if (!perm) {return false;}
  try {
    const status = await check(perm);
    return status === 'limited';
  } catch {
    return false;
  }
}

// 打开iOS受限相册扩展选择器（如API不可用则降级为openSettings）
export async function openLimitedPhotoLibraryPicker(): Promise<void> {
  if (Platform.OS !== 'ios') {return;}
  try {
    if (typeof openPhotoPicker === 'function') {
      await openPhotoPicker();
    } else {
      // 低版本降级为打开设置
      await Linking.openSettings();
    }
  } catch (e) {
    // 兜底：打开设置
    await Linking.openSettings();
  }
}
