import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import RNFS from 'react-native-fs';
import { Alert } from 'react-native';

export interface ExportOptions {
  includeMetadata?: boolean;
  compressionQuality?: number;
  exportFormat?: 'original' | 'pdf' | 'zip';
}

export interface ExportResult {
  success: boolean;
  exportedCount: number;
  failedCount: number;
  totalSize: number;
  exportPath?: string;
  errors?: string[];
}

export interface StorageReport {
  totalFiles: number;
  totalSize: number;
  importantFiles: number;
  importantSize: number;
  tempFiles: number;
  tempSize: number;
  oldFiles: number;
  oldSize: number;
  lastCleanup: Date;
}

/**
 * 数据导出服务 - 替代iOS沙盒内备份的实用数据保护方案
 * 支持用户主动导出重要数据到iOS相册或文件App
 */
export class DataExportService {
  private static instance: DataExportService;

  public static getInstance(): DataExportService {
    if (!DataExportService.instance) {
      DataExportService.instance = new DataExportService();
    }
    return DataExportService.instance;
  }

  /**
   * 导出图片和视频到相册
   */
  async exportMediaToPhotos(filePaths: string[], options: ExportOptions = {}): Promise<ExportResult> {
    const result: ExportResult = {
      success: false,
      exportedCount: 0,
      failedCount: 0,
      totalSize: 0,
      errors: [],
    };

    try {
      for (const filePath of filePaths) {
        try {
          const fileInfo = await RNFS.stat(filePath);
          const isImage = /\.(jpg|jpeg|png|gif|bmp)$/i.test(filePath);
          const isVideo = /\.(mp4|mov|avi|mkv)$/i.test(filePath);

          if (isImage || isVideo) {
            await CameraRoll.save(filePath, { type: isVideo ? 'video' : 'photo' });
            result.exportedCount++;
            result.totalSize += fileInfo.size;
          } else {
            result.failedCount++;
            result.errors?.push(`不支持的媒体格式: ${filePath}`);
          }
        } catch (error) {
          result.failedCount++;
          result.errors?.push(`导出失败: ${filePath} - ${error}`);
        }
      }

      result.success = result.exportedCount > 0;
      return result;
    } catch (error) {
      result.errors?.push(`导出过程出错: ${error}`);
      return result;
    }
  }

  /**
   * 导出文档到文件App
   */
  async exportDocumentsToFiles(filePaths: string[], options: ExportOptions = {}): Promise<ExportResult> {
    const result: ExportResult = {
      success: false,
      exportedCount: 0,
      failedCount: 0,
      totalSize: 0,
      errors: [],
    };

    try {
      // 创建导出目录
      const exportDir = `${RNFS.DocumentDirectoryPath}/Export_${Date.now()}`;
      await RNFS.mkdir(exportDir);

      for (const filePath of filePaths) {
        try {
          const fileInfo = await RNFS.stat(filePath);
          const fileName = filePath.split('/').pop() || 'unknown';
          const exportPath = `${exportDir}/${fileName}`;

          await RNFS.copyFile(filePath, exportPath);
          result.exportedCount++;
          result.totalSize += fileInfo.size;
        } catch (error) {
          result.failedCount++;
          result.errors?.push(`复制失败: ${filePath} - ${error}`);
        }
      }

      if (result.exportedCount > 0) {
        // 使用系统分享功能
        await this.shareDirectory(exportDir);
        result.exportPath = exportDir;
        result.success = true;
      }

      return result;
    } catch (error) {
      result.errors?.push(`导出过程出错: ${error}`);
      return result;
    }
  }

  /**
   * 导出聊天记录为PDF
   */
  async exportChatHistoryToPDF(chatId: string, options: ExportOptions = {}): Promise<ExportResult> {
    // TODO: 实现聊天记录PDF导出
    // 这里需要集成PDF生成库，如react-native-html-to-pdf
    throw new Error('PDF导出功能待实现');
  }

  /**
   * 获取存储使用报告
   */
  async getStorageReport(): Promise<StorageReport> {
    const chatFilesDir = `${RNFS.DocumentDirectoryPath}/ChatFiles`;
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    let totalFiles = 0;
    let totalSize = 0;
    let importantFiles = 0;
    let importantSize = 0;
    let tempFiles = 0;
    let tempSize = 0;
    let oldFiles = 0;
    let oldSize = 0;

    try {
      const files = await this.getAllFiles(chatFilesDir);

      for (const file of files) {
        const fileInfo = await RNFS.stat(file);
        totalFiles++;
        totalSize += fileInfo.size;

        // 判断文件类型
        if (this.isImportantFile(file)) {
          importantFiles++;
          importantSize += fileInfo.size;
        } else if (this.isTempFile(file)) {
          tempFiles++;
          tempSize += fileInfo.size;
        }

        // 判断是否为旧文件
        if (new Date(fileInfo.mtime) < thirtyDaysAgo) {
          oldFiles++;
          oldSize += fileInfo.size;
        }
      }
    } catch (error) {
      console.error('获取存储报告失败:', error);
    }

    return {
      totalFiles,
      totalSize,
      importantFiles,
      importantSize,
      tempFiles,
      tempSize,
      oldFiles,
      oldSize,
      lastCleanup: new Date(), // TODO: 从配置中读取上次清理时间
    };
  }

  /**
   * 清理临时文件
   */
  async cleanupTempFiles(): Promise<{ deletedFiles: number; freedSpace: number }> {
    const chatFilesDir = `${RNFS.DocumentDirectoryPath}/ChatFiles`;
    let deletedFiles = 0;
    let freedSpace = 0;

    try {
      const files = await this.getAllFiles(chatFilesDir);

      for (const file of files) {
        if (this.isTempFile(file)) {
          try {
            const fileInfo = await RNFS.stat(file);
            await RNFS.unlink(file);
            deletedFiles++;
            freedSpace += fileInfo.size;
          } catch (error) {
            console.error(`删除临时文件失败: ${file}`, error);
          }
        }
      }
    } catch (error) {
      console.error('清理临时文件失败:', error);
    }

    return { deletedFiles, freedSpace };
  }

  /**
   * 识别重要文件
   */
  async identifyImportantFiles(): Promise<string[]> {
    const chatFilesDir = `${RNFS.DocumentDirectoryPath}/ChatFiles`;
    const importantFiles: string[] = [];

    try {
      const files = await this.getAllFiles(chatFilesDir);

      for (const file of files) {
        if (this.isImportantFile(file)) {
          importantFiles.push(file);
        }
      }
    } catch (error) {
      console.error('识别重要文件失败:', error);
    }

    return importantFiles;
  }

  /**
   * 显示数据风险警告
   */
  showDataRiskWarning(): void {
    Alert.alert(
      '数据安全提醒',
      'iOS系统限制：应用卸载时会清空所有数据。建议您定期将重要文件导出到相册或文件App中保存。',
      [
        { text: '了解', style: 'default' },
        { text: '立即导出', style: 'default', onPress: () => this.remindUserToBackup() },
      ]
    );
  }

  /**
   * 提醒用户备份
   */
  remindUserToBackup(): void {
    Alert.alert(
      '建议导出重要数据',
      '检测到您有重要文件，建议导出保存：\n• 工作文档导出到文件App\n• 照片视频导出到相册',
      [
        { text: '稍后提醒', style: 'cancel' },
        { text: '导出文档', style: 'default', onPress: () => this.exportImportantDocuments() },
        { text: '导出媒体', style: 'default', onPress: () => this.exportImportantMedia() },
      ]
    );
  }

  // 私有方法

  private async shareDirectory(dirPath: string): Promise<void> {
    // TODO: 实现目录分享功能
    // 可以使用react-native-share或react-native-fs的分享功能
    console.log('分享目录:', dirPath);
  }

  private async getAllFiles(dirPath: string): Promise<string[]> {
    const files: string[] = [];

    try {
      const items = await RNFS.readDir(dirPath);

      for (const item of items) {
        if (item.isFile()) {
          files.push(item.path);
        } else if (item.isDirectory()) {
          const subFiles = await this.getAllFiles(item.path);
          files.push(...subFiles);
        }
      }
    } catch (error) {
      // 目录不存在或无法访问
    }

    return files;
  }

  private isImportantFile(filePath: string): boolean {
    const fileName = filePath.toLowerCase();

    // 重要文件类型
    const importantExtensions = [
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.txt', '.md', '.json', '.xml', '.csv',
    ];

    // 重要关键词
    const importantKeywords = [
      '会议', '报告', '计划', '总结', '方案', '通知', '公文',
      'meeting', 'report', 'plan', 'summary', 'document',
    ];

    return importantExtensions.some(ext => fileName.endsWith(ext)) ||
           importantKeywords.some(keyword => fileName.includes(keyword));
  }

  private isTempFile(filePath: string): boolean {
    const fileName = filePath.toLowerCase();

    // 临时文件特征
    const tempPatterns = [
      /\.tmp$/,
      /\.cache$/,
      /\.temp$/,
      /^temp_/,
      /^cache_/,
      /thumbnail/,
      /preview/,
    ];

    return tempPatterns.some(pattern => pattern.test(fileName));
  }

  private async exportImportantDocuments(): Promise<void> {
    try {
      const importantFiles = await this.identifyImportantFiles();
      const documents = importantFiles.filter(file =>
        !/\.(jpg|jpeg|png|gif|bmp|mp4|mov|avi|mkv)$/i.test(file)
      );

      if (documents.length > 0) {
        await this.exportDocumentsToFiles(documents);
      } else {
        Alert.alert('提示', '未发现重要文档文件');
      }
    } catch (error) {
      Alert.alert('导出失败', `导出重要文档时出错: ${error}`);
    }
  }

  private async exportImportantMedia(): Promise<void> {
    try {
      const importantFiles = await this.identifyImportantFiles();
      const media = importantFiles.filter(file =>
        /\.(jpg|jpeg|png|gif|bmp|mp4|mov|avi|mkv)$/i.test(file)
      );

      if (media.length > 0) {
        await this.exportMediaToPhotos(media);
      } else {
        Alert.alert('提示', '未发现重要媒体文件');
      }
    } catch (error) {
      Alert.alert('导出失败', `导出重要媒体时出错: ${error}`);
    }
  }
}

export default DataExportService;
