import { pick, errorCodes, isErrorWithCode } from '@react-native-documents/picker';
import { Platform } from 'react-native';
import { FileErrorHandler, FileErrorType } from '../utils/fileErrorHandler';
import {

  getFileIcon,
  getFileTypeDescription,
  isSupportedFileType,
} from '../utils/fileTypes';
import { formatFileSize, decodeIOSFileNameAndUri } from '../utils/file';

// 文件选择配置接口
export interface FilePickerOptions {
  allowMultiSelection?: boolean;
  maxFiles?: number;
  maxSizeBytes?: number;
}

// 文件选择结果接口
export interface FilePickerResult {
  uri: string;
  name: string;
  size: number;
  type: string;
  lastModified?: number;
}

class FilePickerService {
  /**
   * 选择文件
   */
  async pickFile(options: FilePickerOptions = {}): Promise<FilePickerResult[]> {
    const {
      allowMultiSelection = true,
      maxFiles = 5,
      maxSizeBytes = 100 * 1024 * 1024, // 100MB - 适应政务文档
    } = options;

    try {
      console.log('[FilePickerService] 开始文件选择，平台:', Platform.OS);
      console.log('[FilePickerService] 选择选项:', { allowMultiSelection, maxFiles, maxSizeBytes });

      // 🔧 修复iOS文件重复显示问题 - 明确设置文件复制策略
      const pickerConfig: any = {
        allowMultiSelection,
        // 🚨 关键修复：设置copyTo为false，避免文件被复制到用户可见位置
        copyTo: 'cachesDirectory', // 复制到缓存目录，避免污染用户文件系统
        ...(Platform.OS === 'ios' && {
          presentationStyle: 'pageSheet',
          transitionStyle: 'coverVertical',
        }),
        ...(Platform.OS === 'android' && {
          allowVirtualFiles: true,
        }),
      };

      // 🔧 不指定type参数，让系统处理所有文件类型
      // 这样可以避免iOS和Android的类型限制问题
      console.log('[FilePickerService] 启动文件选择器，配置:', pickerConfig);

      const result = await pick(pickerConfig);

      console.log('[FilePickerService] 文件选择器返回结果数量:', result.length);
      result.forEach((file, index) => {
        console.log(`[FilePickerService] 文件${index + 1}:`, {
          name: file.name,
          type: file.type,
          size: file.size,
          uri: file.uri?.substring(0, 50) + '...',
        });
      });

      if (result.length > maxFiles) {
        FileErrorHandler.showError(
          FileErrorType.UNKNOWN_ERROR,
          `最多只能选择${maxFiles}个文件，请重新选择。`
        );
        return [];
      }

      const validFiles: FilePickerResult[] = [];

      for (const file of result) {
        if (file.size && file.size > maxSizeBytes) {
          console.warn('[FilePickerService] 文件过大:', file.name, file.size);
          FileErrorHandler.showErrorWithSuggestion(
            FileErrorType.FILE_TOO_LARGE,
            `${file.name} 文件过大（${formatFileSize(file.size)}），超出100MB限制。`
          );
          continue;
        }

        // 🔧 修复图片文件检测逻辑
        // 检查文件的实际MIME类型是否为图片类型
        const isImageFile = (file.type || '').startsWith('image/') ||
                           /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i.test(file.name || '');
        if (isImageFile) {
          console.log('[FilePickerService] 跳过图片文件:', file.name, file.type);
          continue;
        }

        // 允许所有非图片文件类型上传，只记录预览能力
        const canPreview = isSupportedFileType(file.type || '', file.name || '');
        if (!canPreview) {
          console.log('[FilePickerService] 文件可上传但不支持预览:', file.name, file.type);
        }

        let processedUri = file.uri;
        let processedName = file.name || '未知文件';

        if (Platform.OS === 'ios') {
          [processedName, processedUri] = decodeIOSFileNameAndUri(processedName, processedUri);
        }

        console.log('[FilePickerService] 处理文件:', {
          originalName: file.name,
          processedName,
          originalUri: file.uri,
          processedUri,
          type: file.type,
          size: file.size,
          canPreview,
          platform: Platform.OS,
        });

        validFiles.push({
          uri: processedUri,
          name: processedName,
          size: file.size || 0,
          type: file.type || 'application/octet-stream',
          lastModified: Date.now(),
        });
      }

      return validFiles;
    } catch (error: any) {
      if (isErrorWithCode(error) && error.code === errorCodes.OPERATION_CANCELED) {
        console.log('[FilePickerService] 用户取消文件选择');
        return [];
      }

      console.error('[FilePickerService] 文件选择失败:', {
        error,
        message: error?.message,
        stack: error?.stack,
        code: error?.code,
        name: error?.name,
      });

      if (error?.message?.includes('permission') || error?.message?.includes('权限')) {
        FileErrorHandler.showError(FileErrorType.PERMISSION_DENIED);
      } else if (isErrorWithCode(error) && error.code === errorCodes.UNABLE_TO_OPEN_FILE_TYPE) {
        console.log('[FilePickerService] 文件类型限制错误，尝试重新选择');
        FileErrorHandler.showError(
          FileErrorType.UNKNOWN_ERROR,
          '该文件类型暂时无法选择，请尝试选择其他文件或联系技术支持。'
        );
      } else if (isErrorWithCode(error) && error.code === errorCodes.IN_PROGRESS) {
        FileErrorHandler.showError(
          FileErrorType.UNKNOWN_ERROR,
          '文件选择器正在使用中，请稍后重试。'
        );
      } else if (error?.message?.includes('No such file') || error?.message?.includes('file not found')) {
        FileErrorHandler.showError(
          FileErrorType.UNKNOWN_ERROR,
          '文件不存在或已被移动，请重新选择。'
        );
      } else if (error?.message?.includes('access') || error?.message?.includes('denied')) {
        FileErrorHandler.showError(
          FileErrorType.PERMISSION_DENIED,
          '无法访问该文件，请检查文件权限或选择其他文件。'
        );
      } else {
        FileErrorHandler.showError(
          FileErrorType.UNKNOWN_ERROR,
          '文件选择失败，请重试。如问题持续存在，请检查应用权限设置。'
        );
      }

      return [];
    }
  }

  /**
   * 格式化文件大小
   */
  public formatFileSize(bytes: number): string {
    return formatFileSize(bytes);
  }



  /**
   * 获取文件类型描述
   */
  getFileTypeDescription(mimeType: string, fileName: string): string {
    return getFileTypeDescription(mimeType, fileName);
  }

  /**
   * 获取文件icon
   */
  getFileIcon(mimeType: string, fileName: string): string {
    return getFileIcon(mimeType, fileName);
  }
}

export default new FilePickerService();
