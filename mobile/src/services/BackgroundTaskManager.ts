import { AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import RNFS from 'react-native-fs';
import FileCacheManager from './FileCacheManager';

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

// 任务接口
export interface BackgroundTask {
  id: string;
  type: 'file_upload' | 'file_copy' | 'cache_cleanup';
  status: TaskStatus;
  progress: number;
  data: any;
  createdAt: number;
  startedAt?: number;
  completedAt?: number;
  error?: string;
  onProgress?: (progress: number) => void;
  onComplete?: (result: any) => void;
  onError?: (error: Error) => void;
}

// 任务执行器接口
export interface TaskExecutor {
  execute(task: BackgroundTask): Promise<any>;
  cancel?(taskId: string): Promise<void>;
}

class BackgroundTaskManager {
  private tasks: Map<string, BackgroundTask> = new Map();
  private executors: Map<string, TaskExecutor> = new Map();
  private isProcessing = false;
  private maxConcurrentTasks = 3;
  private runningTasks = 0;

  constructor() {
    this.setupAppStateListener();
  }

  /**
   * 注册任务执行器
   */
  registerExecutor(taskType: string, executor: TaskExecutor): void {
    this.executors.set(taskType, executor);
  }

  /**
   * 添加后台任务
   */
  addTask(task: Omit<BackgroundTask, 'id' | 'status' | 'progress' | 'createdAt'>): string {
    const taskId = this.generateTaskId();
    const fullTask: BackgroundTask = {
      ...task,
      id: taskId,
      status: TaskStatus.PENDING,
      progress: 0,
      createdAt: Date.now(),
    };

    this.tasks.set(taskId, fullTask);
    console.log(`[BackgroundTaskManager] 添加任务: ${taskId} (${task.type})`);

    // 立即尝试处理任务
    this.processNextTask();

    return taskId;
  }

  /**
   * 获取任务状态
   */
  getTask(taskId: string): BackgroundTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): BackgroundTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 取消任务
   */
  async cancelTask(taskId: string): Promise<boolean> {
    const task = this.tasks.get(taskId);
    if (!task) {
      return false;
    }

    if (task.status === TaskStatus.RUNNING) {
      const executor = this.executors.get(task.type);
      if (executor && executor.cancel) {
        try {
          await executor.cancel(taskId);
        } catch (error) {
          console.error(`[BackgroundTaskManager] 取消任务失败: ${taskId}`, error);
        }
      }
      this.runningTasks--;
    }

    task.status = TaskStatus.CANCELLED;
    task.completedAt = Date.now();

    console.log(`[BackgroundTaskManager] 任务已取消: ${taskId}`);
    this.processNextTask();

    return true;
  }

  /**
   * 清理已完成的任务
   */
  cleanupCompletedTasks(olderThanHours: number = 24): number {
    const cutoffTime = Date.now() - (olderThanHours * 60 * 60 * 1000);
    let cleanedCount = 0;

    for (const [taskId, task] of this.tasks.entries()) {
      if (
        (task.status === TaskStatus.COMPLETED ||
         task.status === TaskStatus.FAILED ||
         task.status === TaskStatus.CANCELLED) &&
        task.createdAt < cutoffTime
      ) {
        this.tasks.delete(taskId);
        cleanedCount++;
      }
    }

    console.log(`[BackgroundTaskManager] 清理了 ${cleanedCount} 个已完成的任务`);
    return cleanedCount;
  }

  /**
   * 处理下一个任务
   */
  private async processNextTask(): Promise<void> {
    if (this.isProcessing || this.runningTasks >= this.maxConcurrentTasks) {
      return;
    }

    // 查找下一个待处理的任务
    const pendingTask = Array.from(this.tasks.values())
      .find(task => task.status === TaskStatus.PENDING);

    if (!pendingTask) {
      return;
    }

    this.isProcessing = true;
    this.runningTasks++;

    try {
      await this.executeTask(pendingTask);
    } catch (error) {
      console.error(`[BackgroundTaskManager] 任务执行异常: ${pendingTask.id}`, error);
    } finally {
      this.runningTasks--;
      this.isProcessing = false;

      // 继续处理下一个任务
      setTimeout(() => this.processNextTask(), 100);
    }
  }

  /**
   * 执行任务
   */
  private async executeTask(task: BackgroundTask): Promise<void> {
    const executor = this.executors.get(task.type);
    if (!executor) {
      throw new Error(`未找到任务执行器: ${task.type}`);
    }

    task.status = TaskStatus.RUNNING;
    task.startedAt = Date.now();

    console.log(`[BackgroundTaskManager] 开始执行任务: ${task.id} (${task.type})`);

    try {
      // 设置进度回调
      const originalOnProgress = task.onProgress;
      task.onProgress = (progress: number) => {
        task.progress = progress;
        if (originalOnProgress) {
          originalOnProgress(progress);
        }
      };

      const result = await executor.execute(task);

      task.status = TaskStatus.COMPLETED;
      task.completedAt = Date.now();
      task.progress = 100;

      console.log(`[BackgroundTaskManager] 任务完成: ${task.id}`);

      if (task.onComplete) {
        task.onComplete(result);
      }
    } catch (error) {
      task.status = TaskStatus.FAILED;
      task.completedAt = Date.now();
      task.error = error instanceof Error ? error.message : String(error);

      console.error(`[BackgroundTaskManager] 任务失败: ${task.id}`, error);

      if (task.onError) {
        task.onError(error instanceof Error ? error : new Error(String(error)));
      }
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 9);
    return `task_${timestamp}_${random}`;
  }

  /**
   * 设置应用状态监听器
   */
  private setupAppStateListener(): void {
    AppState.addEventListener('change', this.handleAppStateChange.bind(this));
  }

  /**
   * 处理应用状态变化
   */
  private handleAppStateChange(nextAppState: AppStateStatus): void {
    if (nextAppState === 'background') {
      console.log('[BackgroundTaskManager] 应用进入后台，保存任务状态');
      this.saveTasksToStorage();
    } else if (nextAppState === 'active') {
      console.log('[BackgroundTaskManager] 应用回到前台，恢复任务处理');
      this.loadTasksFromStorage();
      this.processNextTask();
    }
  }

  /**
   * 保存任务到本地存储
   */
  private async saveTasksToStorage(): Promise<void> {
    try {
      const tasksData = Array.from(this.tasks.entries());
      await AsyncStorage.setItem('background_tasks', JSON.stringify(tasksData));
    } catch (error) {
      console.error('[BackgroundTaskManager] 保存任务状态失败:', error);
    }
  }

  /**
   * 从本地存储加载任务
   */
  private async loadTasksFromStorage(): Promise<void> {
    try {
      const tasksData = await AsyncStorage.getItem('background_tasks');

      if (tasksData) {
        const tasks = JSON.parse(tasksData) as Array<[string, BackgroundTask]>;
        this.tasks = new Map(tasks);

        // 重置运行中的任务状态为待处理
        for (const task of this.tasks.values()) {
          if (task.status === TaskStatus.RUNNING) {
            task.status = TaskStatus.PENDING;
          }
        }
      }
    } catch (error) {
      console.error('[BackgroundTaskManager] 加载任务状态失败:', error);
    }
  }

  /**
   * 获取任务统计信息
   */
  getTaskStats(): {
    total: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
    cancelled: number;
  } {
    const stats = {
      total: this.tasks.size,
      pending: 0,
      running: 0,
      completed: 0,
      failed: 0,
      cancelled: 0,
    };

    for (const task of this.tasks.values()) {
      switch (task.status) {
        case TaskStatus.PENDING:
          stats.pending++;
          break;
        case TaskStatus.RUNNING:
          stats.running++;
          break;
        case TaskStatus.COMPLETED:
          stats.completed++;
          break;
        case TaskStatus.FAILED:
          stats.failed++;
          break;
        case TaskStatus.CANCELLED:
          stats.cancelled++;
          break;
      }
    }

    return stats;
  }
}

// 文件操作任务执行器
export class FileOperationExecutor implements TaskExecutor {
  async execute(task: BackgroundTask): Promise<any> {
    const { type } = task;

    switch (type) {
      case 'file_copy':
        return await this.executeFileCopy(task);
      case 'cache_cleanup':
        return await this.executeCacheCleanup(task);
      default:
        throw new Error(`不支持的任务类型: ${type}`);
    }
  }

  private async executeFileCopy(task: BackgroundTask): Promise<any> {
    const { sourcePath, targetPath, totalSize } = task.data;

    // 对于小文件，直接复制
    if (totalSize < 1024 * 1024) {
      await RNFS.copyFile(sourcePath, targetPath);
      if (task.onProgress) {
        task.onProgress(100);
      }
      return { success: true, targetPath };
    }

    // 大文件分块复制
    const chunkSize = 256 * 1024; // 256KB
    let bytesWritten = 0;

    await RNFS.writeFile(targetPath, '', 'base64');

    while (bytesWritten < totalSize) {
      const remainingBytes = totalSize - bytesWritten;
      const currentChunkSize = Math.min(chunkSize, remainingBytes);

      const chunk = await RNFS.read(sourcePath, currentChunkSize, bytesWritten, 'base64');
      await RNFS.appendFile(targetPath, chunk, 'base64');

      bytesWritten += currentChunkSize;

      if (task.onProgress) {
        task.onProgress((bytesWritten / totalSize) * 100);
      }

      // 让出控制权
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    return { success: true, targetPath, bytesWritten };
  }

  private async executeCacheCleanup(task: BackgroundTask): Promise<any> {
    const { targetSize } = task.data;

    const cleanedSize = await FileCacheManager.smartCleanup(targetSize);

    if (task.onProgress) {
      task.onProgress(100);
    }

    return { success: true, cleanedSize };
  }
}

export default new BackgroundTaskManager();
