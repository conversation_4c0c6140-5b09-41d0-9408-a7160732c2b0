/**
 * 极光推送服务
 * 处理推送通知的接收和处理
 */

// 修复：使用正确的JPush导入方式
import JPush from 'jpush-react-native';
import { Platform, Alert, PermissionsAndroid } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SimpleWeChatBindingService } from './SimpleWeChatBindingService';
import WeChatMessageSyncService from './WeChatMessageSyncService';
import { DeviceRegistrationService } from './DeviceRegistrationService';
import { jpushConfig, configValidator } from '../config/appConfig';
import { userDeviceManager } from '../utils/UserDeviceManager';

class JPushService {
  private isInitialized = false;
  private registrationId: string | null = null;

  // 事件监听器引用，用于清理
  private connectListener: any = null;
  private notificationListener: any = null;
  private localNotificationListener: any = null;
  private tagAliasListener: any = null;
  private mobileNumberListener: any = null;

  /**
   * 检查推送权限
   */
  private async checkPushPermission(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        // Android 13+ 需要显式请求通知权限
        if (Platform.Version >= 33) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
            {
              title: '通知权限',
              message: '公职猫需要通知权限来接收微信转发消息',
              buttonNeutral: '稍后询问',
              buttonNegative: '拒绝',
              buttonPositive: '允许',
            }
          );

          if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
            console.warn('[JPush] 通知权限未授权');
            Alert.alert(
              '权限提醒',
              '为了及时接收微信转发消息，请在设置中开启通知权限',
              [
                { text: '稍后设置', style: 'cancel' },
                { text: '去设置', onPress: () => {
                  // 可以引导用户到设置页面
                  console.log('[JPush] 引导用户到设置页面');
                }},
              ]
            );
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      console.error('[JPush] 权限检查失败:', error);
      return false;
    }
  }

  /**
   * 初始化极光推送 (修复：使用官方推荐的初始化方式)
   */
  async initialize(): Promise<boolean> {
    try {
      // 防止重复初始化
      if (this.isInitialized) {
        console.log('[JPush] 服务已初始化，跳过重复初始化');
        return true;
      }

      console.log('[JPush] 开始初始化极光推送服务');

      // 检查推送权限
      const hasPermission = await this.checkPushPermission();
      if (!hasPermission) {
        console.warn('[JPush] 推送权限检查失败，继续初始化但功能可能受限');
      }

      // 验证配置
      if (!configValidator.validateJPushConfig()) {
        console.error('[JPush] 配置验证失败，无法初始化');
        return false;
      }

      // 修复：使用JPush官方文档中的正确属性名
      const initConfig = {
        appKey: jpushConfig.appKey,
        channel: jpushConfig.channel,
        production: jpushConfig.production,
      };

      console.log('[JPush] 初始化配置:', initConfig);
      // @ts-ignore - 本地类型定义可能存在拼写错误 (titchannelle)，但实际应为 channel
      JPush.init(initConfig);

      // 设置调试模式
      if (__DEV__) {
        JPush.setLoggerEnable(true);
      }

      // 监听推送事件 - 必须在init之后设置
      this.setupEventListeners();

      // 获取注册ID - 延迟执行，确保初始化完成
      setTimeout(async () => {
        await this.getRegistrationId();
      }, 1000);

      // 设置用户别名
      await this.setUserAlias();

      this.isInitialized = true;
      console.log('[JPush] 极光推送初始化成功');
      return true;

    } catch (error) {
      console.error('[JPush] 极光推送初始化失败:', error);
      return false;
    }
  }

  /**
   * 设置事件监听器 (修复：使用官方推荐的API方式)
   */
  private setupEventListeners(): void {
    try {
      console.log('[JPush] 开始设置事件监听器');

      // 连接状态监听
      this.connectListener = (result: any) => {
        console.log('[JPush] 连接状态:', result);
      };
      JPush.addConnectEventListener(this.connectListener);

      // 通知接收监听
      this.notificationListener = (notification: any) => {
        console.log('[JPush] 收到通知:', notification);
        this.handleNotification(notification);
      };
      JPush.addNotificationListener(this.notificationListener);

      // 本地通知监听
      this.localNotificationListener = (notification: any) => {
        console.log('[JPush] 收到本地通知:', notification);
        this.handleNotificationOpened(notification);
      };
      JPush.addLocalNotificationListener(this.localNotificationListener);

      // 标签别名监听
      this.tagAliasListener = (result: any) => {
        console.log('[JPush] 标签别名设置结果:', result);
      };
      JPush.addTagAliasListener(this.tagAliasListener);

      // 手机号码监听（如果支持）
      if (JPush.addMobileNumberListener && typeof JPush.addMobileNumberListener === 'function') {
        this.mobileNumberListener = (result: any) => {
          console.log('[JPush] 手机号码设置结果:', result);
        };
        JPush.addMobileNumberListener(this.mobileNumberListener);
      }

      console.log('[JPush] 事件监听器设置完成');
    } catch (error) {
      console.error('[JPush] 设置事件监听器失败:', error);
    }
  }

  /**
   * 获取注册ID (修复：使用官方推荐的回调方式)
   */
  private getRegistrationId(): Promise<string | null> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log('[JPush] 开始获取注册ID');
        console.log('[JPush] 当前初始化状态:', this.isInitialized);

        // 使用官方推荐的回调方式
        JPush.getRegistrationID((result) => {
          console.log('[JPush] getRegistrationID 回调结果:', result);
          console.log('[JPush] 回调结果类型:', typeof result);
          console.log('[JPush] 回调结果详细信息:', JSON.stringify(result, null, 2));

          // 修复：处理回调返回的对象格式（根据TypeScript定义）
          let registerID: string | null = null;
          if (typeof result === 'object' && result !== null) {
            // 对象格式：{registerID: 'xxx'} - 根据JPush TypeScript定义
            registerID = (result as any).registerID || null;
            console.log('[JPush] 从对象中提取的注册ID:', registerID);
          } else if (typeof result === 'string') {
            // 字符串格式
            registerID = result;
            console.log('[JPush] 字符串格式的注册ID:', registerID);
          }

          if (registerID && registerID.trim() !== '' && registerID !== 'null' && registerID !== 'undefined') {
            this.registrationId = registerID;
            console.log('[JPush] 注册ID获取成功:', registerID);

            // 保存到本地存储 - 修复：确保保存字符串
            AsyncStorage.setItem('jpush_registration_id', registerID).then(async () => {
              console.log('[JPush] 注册ID已保存到本地存储');

              // 重要：通知设备注册服务更新push_token
              try {
                const deviceService = DeviceRegistrationService.getInstance();
                await deviceService.setPushToken(registerID);
                console.log('[JPush] 已通知设备注册服务更新push_token');
              } catch (error) {
                console.error('[JPush] 通知设备注册服务失败:', error);
              }
            }).catch(error => {
              console.warn('[JPush] 保存注册ID到本地存储失败:', error);
            });

            resolve(registerID);
          } else {
            console.warn('[JPush] 获取注册ID返回空值，尝试从本地存储恢复');
            console.warn('[JPush] 空值详情 - registerID:', registerID, 'type:', typeof registerID);

            // 尝试从本地存储恢复
            AsyncStorage.getItem('jpush_registration_id').then(async (savedId) => {
              if (savedId && savedId !== 'null' && savedId !== 'undefined') {
                this.registrationId = savedId;
                console.log('[JPush] 从本地存储恢复注册ID:', savedId);

                // 确保设备注册服务也有这个token
                try {
                  const deviceService = DeviceRegistrationService.getInstance();
                  const currentToken = await deviceService.getPushToken();
                  if (!currentToken || currentToken !== savedId) {
                    await deviceService.setPushToken(savedId);
                    console.log('[JPush] 已同步push_token到设备注册服务');
                  }
                } catch (error) {
                  console.error('[JPush] 同步push_token到设备注册服务失败:', error);
                }

                resolve(savedId);
              } else {
                console.warn('[JPush] 本地存储中也没有注册ID');
                console.warn('[JPush] 本地存储值:', savedId);
                resolve(null);
              }
            }).catch(error => {
              console.error('[JPush] 从本地存储读取注册ID失败:', error);
              resolve(null);
            });
          }
        });

        // 添加超时处理
        setTimeout(() => {
          if (!this.registrationId) {
            console.warn('[JPush] 获取注册ID超时（5秒），可能需要更长时间初始化');
          }
        }, 5000);

      } catch (error) {
        console.error('[JPush] 获取注册ID异常:', error);
        reject(error);
      }
    });
  }

  /**
   * 设置用户别名 (修复：使用官方推荐的参数格式)
   */
  private async setUserAlias(): Promise<void> {
    try {
      // 获取用户UUID作为别名
      const userUuid = await userDeviceManager.getUserUuid();

      if (userUuid) {
        console.log('[JPush] 开始设置用户别名:', userUuid);

        // 使用官方推荐的参数格式
        JPush.setAlias({
          sequence: Date.now(),
          alias: userUuid,
        });

        console.log('[JPush] 用户别名设置请求已发送');
        await AsyncStorage.setItem('jpush_user_alias', userUuid);
      } else {
        console.warn('[JPush] 无法获取用户UUID，跳过别名设置');
      }
    } catch (error) {
      console.error('[JPush] 设置用户别名失败:', error);
    }
  }

  /**
   * 处理收到的通知
   */
  private handleNotification(notification: any): void {
    try {
      console.log('[JPush] 处理通知:', notification);

      const { extras } = notification;
      if (extras && extras.type) {
        switch (extras.type) {
          case 'wechat_message_sync':
            // 微信消息同步通知
            console.log('[JPush] 收到微信消息同步通知，触发同步');
            this.triggerWeChatSync(extras);
            break;
          case 'wechat_binding_success':
            // 微信绑定成功通知
            console.log('[JPush] 收到微信绑定成功通知，更新绑定状态');
            this.handleBindingSuccess(extras);
            break;
          case 'wechat_binding_failed':
            // 微信绑定失败通知
            console.log('[JPush] 收到微信绑定失败通知');
            this.handleBindingFailed(extras);
            break;
          default:
            console.log('[JPush] 收到未知类型的通知:', extras.type);
        }
      }
    } catch (error) {
      console.error('[JPush] 处理通知失败:', error);
    }
  }

  /**
   * 处理通知点击事件
   */
  private handleNotificationOpened(notification: any): void {
    try {
      console.log('[JPush] 处理通知点击:', notification);

      const { extras } = notification;
      if (extras && extras.type) {
        switch (extras.type) {
          case 'wechat_message_sync':
            // 用户点击了微信消息通知，触发同步并可能跳转到聊天界面
            this.triggerWeChatSync(extras);
            // 这里可以添加导航逻辑，跳转到聊天界面
            // NavigationService.navigate('Chat');
            break;
          case 'wechat_binding_success':
            // 用户点击了绑定成功通知，处理绑定状态并可能跳转到绑定页面
            this.handleBindingSuccess(extras);
            // NavigationService.navigate('SimpleWeChatBinding');
            break;
          default:
            console.log('[JPush] 点击了未知类型的通知:', extras.type);
        }
      }
    } catch (error) {
      console.error('[JPush] 处理通知点击失败:', error);
    }
  }

  /**
   * 处理自定义消息
   */
  private handleCustomMessage(message: any): void {
    try {
      console.log('[JPush] 处理自定义消息:', message);

      const { extras } = message;
      if (extras && extras.type) {
        switch (extras.type) {
          case 'wechat_message_sync':
            // 静默同步，不显示通知
            this.triggerWeChatSync(extras);
            break;
          case 'wechat_binding_success':
            // 静默处理绑定成功
            this.handleBindingSuccess(extras);
            break;
          case 'wechat_binding_failed':
            // 静默处理绑定失败
            this.handleBindingFailed(extras);
            break;
          default:
            console.log('[JPush] 收到未知类型的自定义消息:', extras.type);
        }
      }
    } catch (error) {
      console.error('[JPush] 处理自定义消息失败:', error);
    }
  }

  /**
   * 处理微信绑定成功通知
   */
  private async handleBindingSuccess(extras: any): Promise<void> {
    try {
      console.log('[JPush] 处理微信绑定成功通知:', extras);

      // 刷新绑定状态（这会从服务器获取最新状态并更新本地）
      const bindingService = SimpleWeChatBindingService.getInstance();
      const refreshResult = await bindingService.refreshBindingStatus();

      if (refreshResult.success && refreshResult.binding) {
        console.log('[JPush] 绑定状态刷新成功，启动消息同步服务');

        // 启动消息同步服务
        const syncStarted = await WeChatMessageSyncService.startSync();
        if (syncStarted) {
          console.log('[JPush] 消息同步服务启动成功');

          // 立即执行一次同步，获取可能已有的消息
          const syncResult = await WeChatMessageSyncService.manualSync();
          if (syncResult.success) {
            console.log('[JPush] 初始消息同步成功');
          }
        } else {
          console.warn('[JPush] 消息同步服务启动失败');
        }
      } else {
        console.warn('[JPush] 绑定状态刷新失败:', refreshResult.message);
      }
    } catch (error) {
      console.error('[JPush] 处理绑定成功通知失败:', error);
    }
  }

  /**
   * 处理微信绑定失败通知
   */
  private async handleBindingFailed(extras: any): Promise<void> {
    try {
      console.log('[JPush] 处理微信绑定失败通知:', extras);

      // 清除本地绑定状态
      const bindingService = SimpleWeChatBindingService.getInstance();
      await bindingService.clearBindingStatus();

      console.log('[JPush] 已清除本地绑定状态');
    } catch (error) {
      console.error('[JPush] 处理绑定失败通知失败:', error);
    }
  }

  /**
   * 触发微信消息同步
   */
  private async triggerWeChatSync(extras: any): Promise<void> {
    try {
      console.log('[JPush] 触发微信消息同步:', extras);

      // 检查绑定状态
      const canSync = await SimpleWeChatBindingService.getInstance().canSync();
      if (!canSync) {
        console.log('[JPush] 微信未绑定，跳过同步');
        return;
      }

      // 触发消息同步
      const result = await WeChatMessageSyncService.manualSync();
      if (result.success) {
        console.log('[JPush] 微信消息同步成功');
      } else {
        console.error('[JPush] 微信消息同步失败:', result.message);
      }
    } catch (error) {
      console.error('[JPush] 触发微信消息同步失败:', error);
    }
  }

  /**
   * 请求通知权限
   */
  async requestPermission(): Promise<boolean> {
    try {
      // iOS和Android在新版中通常通过原生配置处理，但可以保留此API以确保兼容性
      JPush.requestPermission();
      return true;
    } catch (error) {
      console.error('[JPush] 请求通知权限失败:', error);
      return false;
    }
  }

  /**
   * 获取当前注册ID
   */
  getRegistrationID(): string | null {
    return this.registrationId;
  }

  /**
   * 检查初始化状态
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 清理资源 (修复：使用正确的清理方法)
   */
  cleanup(): void {
    try {
      console.log('[JPush] 开始清理极光推送服务');

      // 使用新的清理方法
      this.cleanupListeners();

      // 重置状态
      this.isInitialized = false;
      this.registrationId = null;

      console.log('[JPush] 极光推送服务清理完成');
    } catch (error) {
      console.error('[JPush] 清理极光推送服务失败:', error);
    }
  }

  /**
   * 清理事件监听器
   */
  private cleanupListeners(): void {
    try {
      // 修复：使用官方API移除监听器。本地类型定义可能过时，缺少 remove 方法。
      if (this.connectListener) {
        // @ts-ignore
        JPush.removeConnectEventListener(this.connectListener);
        this.connectListener = null;
      }
      if (this.notificationListener) {
        // @ts-ignore
        JPush.removeNotificationListener(this.notificationListener);
        this.notificationListener = null;
      }
      if (this.localNotificationListener) {
        // @ts-ignore
        JPush.removeLocalNotificationListener(this.localNotificationListener);
        this.localNotificationListener = null;
      }
      if (this.tagAliasListener) {
        // @ts-ignore
        JPush.removeTagAliasListener(this.tagAliasListener);
        this.tagAliasListener = null;
      }
      if (this.mobileNumberListener) {
        // @ts-ignore
        JPush.removeMobileNumberListener(this.mobileNumberListener);
        this.mobileNumberListener = null;
      }
      console.log('[JPush] 事件监听器清理完成');
    } catch (error) {
      console.error('[JPush] 清理事件监听器失败:', error);
    }
  }

  /**
   * 发送测试通知（开发调试用）
   */
  async sendTestNotification(): Promise<void> {
    if (__DEV__) {
      Alert.alert('测试通知', '这是一条测试推送通知');
    }
  }
}

export default new JPushService();
