import RNFS from 'react-native-fs';
import { Platform } from 'react-native';
import { FilePickerResult } from './FilePickerService';
import { FileErrorHandler, FileErrorType } from '../utils/fileErrorHandler';
import FileCacheManager from './FileCacheManager';
import BackgroundTaskManager, { FileOperationExecutor } from './BackgroundTaskManager';
import { validateFileStorageSecurity, getSecureStoragePath, cleanupTempFiles } from '../utils/fileStorageValidator';
import { FilePathManager } from '../utils/filePathManager';

// 文件存储结果接口
export interface StoredFileInfo {
  localPath: string;          // 绝对路径（用于立即访问）
  relativePath: string;       // 相对路径（用于数据库存储，解决iOS路径一致性问题）
  originalName: string;
  size: number;
  type: string;
  storedAt: number;
  fileId: string;
}

// 文件上传进度回调接口
export interface FileUploadProgress {
  bytesWritten: number;
  totalBytes: number;
  progress: number; // 0-1之间的进度值
}

// 文件存储配置
const STORAGE_CONFIG = {
  // 文件存储目录名
  FILES_DIR: 'ChatFiles',
  // 最大存储大小 (500MB)
  MAX_STORAGE_SIZE: 500 * 1024 * 1024,
  // 单个文件最大大小 (100MB) - 适应政务文档需求
  MAX_FILE_SIZE: 100 * 1024 * 1024,
  // 文件保留天数 (30天)
  FILE_RETENTION_DAYS: 30,
};

class FileStorageService {
  private filesDir: string;

  constructor() {
    // 根据平台设置文件存储目录
    this.filesDir = Platform.select({
      ios: `${RNFS.DocumentDirectoryPath}/${STORAGE_CONFIG.FILES_DIR}`,
      android: `${RNFS.DocumentDirectoryPath}/${STORAGE_CONFIG.FILES_DIR}`,
      default: `${RNFS.DocumentDirectoryPath}/${STORAGE_CONFIG.FILES_DIR}`,
    });
  }

  /**
   * 初始化文件存储目录
   */
  async initialize(): Promise<void> {
    try {
      const exists = await RNFS.exists(this.filesDir);
      if (!exists) {
        await RNFS.mkdir(this.filesDir);
        console.log('[FileStorageService] 文件存储目录创建成功:', this.filesDir);
      }

      // 🔧 定期清理临时文件，防止存储空间浪费
      await cleanupTempFiles();

      // 初始化缓存管理器
      await FileCacheManager.initialize();

      // 注册后台任务执行器
      BackgroundTaskManager.registerExecutor('file_copy', new FileOperationExecutor());
      BackgroundTaskManager.registerExecutor('cache_cleanup', new FileOperationExecutor());
    } catch (error) {
      console.error('[FileStorageService] 初始化失败:', error);
      throw new Error('文件存储初始化失败');
    }
  }

  /**
   * 保存文件到本地存储
   */
  async saveFile(
    fileInfo: FilePickerResult,
    onProgress?: (progress: FileUploadProgress) => void
  ): Promise<StoredFileInfo> {
    try {
      await this.initialize();

      // 生成唯一的文件ID
      const fileId = this.generateFileId();

      // 获取文件扩展名
      const extension = this.getFileExtension(fileInfo.name);

      // 生成本地文件路径
      const localFileName = `${fileId}${extension}`;
      let localPath = `${this.filesDir}/${localFileName}`;

      // 检查存储空间
      await this.checkStorageSpace(fileInfo.size);

      // 开始上传进度
      if (onProgress) {
        onProgress({
          bytesWritten: 0,
          totalBytes: fileInfo.size,
          progress: 0,
        });
      }

      // 🔧 安全验证：确保目标路径安全，防止文件重复显示
      const securityCheck = await validateFileStorageSecurity(localPath);
      if (!securityCheck.isSecure) {
        console.warn('[FileStorageService] 存储路径安全检查失败:', securityCheck.issues);
        // 使用推荐的安全路径
        const securePath = getSecureStoragePath(localFileName);
        // 🔧 使用字符串操作替代path.dirname，兼容React Native
        const secureDir = securePath.substring(0, securePath.lastIndexOf('/'));
        await this.ensureDirectoryExists(secureDir);
        // 更新localPath为安全路径
        localPath = securePath;
      }

      // 复制文件到本地存储（保持原始质量）
      if (onProgress && fileInfo.size > 1024 * 1024) { // 大于1MB的文件显示进度
        await this.copyFileWithProgress(fileInfo.uri, localPath, fileInfo.size, onProgress);
      } else {
        await RNFS.copyFile(fileInfo.uri, localPath);
      }

      // 完成进度
      if (onProgress) {
        onProgress({
          bytesWritten: fileInfo.size,
          totalBytes: fileInfo.size,
          progress: 1,
        });
      }

      // 验证文件是否复制成功
      const exists = await RNFS.exists(localPath);
      if (!exists) {
        throw new Error('文件保存失败');
      }

      // 🔧 生成相对路径，解决iOS覆盖安装路径一致性问题
      const relativePath = FilePathManager.extractRelativePath(localPath) || localFileName;

      const storedInfo: StoredFileInfo = {
        localPath,
        relativePath,
        originalName: fileInfo.name,
        size: fileInfo.size,
        type: fileInfo.type,
        storedAt: Date.now(),
        fileId,
      };

      // 添加到缓存管理器
      await FileCacheManager.addToCache(localPath, fileInfo.name, false);

      console.log('[FileStorageService] 文件保存成功:', storedInfo);
      return storedInfo;

    } catch (error) {
      console.error('[FileStorageService] 文件保存失败:', error);
      // 不在这里显示错误提示，让调用方处理
      throw error;
    }
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(localPath: string): Promise<StoredFileInfo | null> {
    try {
      const exists = await RNFS.exists(localPath);
      if (!exists) {
        return null;
      }

      const stat = await RNFS.stat(localPath);
      const fileName = localPath.split('/').pop() || '';
      const fileId = fileName.split('.')[0];

      // 更新访问统计
      await FileCacheManager.accessFile(localPath);

      // 🔧 生成相对路径，解决iOS覆盖安装路径一致性问题
      const relativePath = FilePathManager.extractRelativePath(localPath) || fileName;

      return {
        localPath,
        relativePath,
        originalName: fileName,
        size: stat.size,
        type: 'application/octet-stream', // 默认类型，实际应从数据库获取
        storedAt: stat.mtime ? new Date(stat.mtime).getTime() : Date.now(),
        fileId,
      };
    } catch (error) {
      console.error('[FileStorageService] 获取文件信息失败:', error);
      return null;
    }
  }

  /**
   * 删除文件
   */
  async deleteFile(localPath: string): Promise<boolean> {
    try {
      const exists = await RNFS.exists(localPath);
      if (exists) {
        await RNFS.unlink(localPath);
        console.log('[FileStorageService] 文件删除成功:', localPath);
      }
      return true;
    } catch (error) {
      console.error('[FileStorageService] 文件删除失败:', error);
      return false;
    }
  }

  /**
   * 清理过期文件
   */
  async cleanupExpiredFiles(): Promise<void> {
    try {
      await this.initialize();

      const files = await RNFS.readDir(this.filesDir);
      const now = Date.now();
      const retentionTime = STORAGE_CONFIG.FILE_RETENTION_DAYS * 24 * 60 * 60 * 1000;

      for (const file of files) {
        const fileAge = now - new Date(file.mtime || 0).getTime();
        if (fileAge > retentionTime) {
          await this.deleteFile(file.path);
          console.log('[FileStorageService] 清理过期文件:', file.name);
        }
      }
    } catch (error) {
      console.error('[FileStorageService] 清理过期文件失败:', error);
    }
  }

  /**
   * 获取存储使用情况
   */
  async getStorageUsage(): Promise<{
    totalSize: number;
    fileCount: number;
    availableSpace: number;
  }> {
    try {
      await this.initialize();

      const files = await RNFS.readDir(this.filesDir);
      let totalSize = 0;

      for (const file of files) {
        if (file.isFile()) {
          totalSize += file.size;
        }
      }

      return {
        totalSize,
        fileCount: files.filter(f => f.isFile()).length,
        availableSpace: STORAGE_CONFIG.MAX_STORAGE_SIZE - totalSize,
      };
    } catch (error) {
      console.error('[FileStorageService] 获取存储使用情况失败:', error);
      return {
        totalSize: 0,
        fileCount: 0,
        availableSpace: STORAGE_CONFIG.MAX_STORAGE_SIZE,
      };
    }
  }

  /**
   * 检查存储空间是否足够
   */
  private async checkStorageSpace(fileSize: number): Promise<void> {
    const usage = await this.getStorageUsage();

    if (fileSize > STORAGE_CONFIG.MAX_FILE_SIZE) {
      FileErrorHandler.showErrorWithSuggestion(FileErrorType.FILE_TOO_LARGE);
      throw new Error('文件太大，无法保存');
    }

    if (fileSize > usage.availableSpace) {
      // 尝试清理过期文件
      await this.cleanupExpiredFiles();

      // 重新检查空间
      const newUsage = await this.getStorageUsage();
      if (fileSize > newUsage.availableSpace) {
        FileErrorHandler.showErrorWithSuggestion(FileErrorType.STORAGE_FULL);
        throw new Error('存储空间不足，请清理一些文件');
      }
    }
  }

  /**
   * 简化的文件复制（带进度）
   * 移除复杂的fallback逻辑，提高可靠性
   */
  private async copyFileWithProgress(
    sourcePath: string,
    targetPath: string,
    totalSize: number,
    onProgress: (progress: FileUploadProgress) => void
  ): Promise<void> {
    try {
      console.log('[FileStorageService] 开始文件复制:', {
        sourcePath: sourcePath.substring(0, 50) + '...',
        targetPath: targetPath.substring(0, 50) + '...',
        totalSize: `${(totalSize / 1024 / 1024).toFixed(2)}MB`,
        platform: Platform.OS,
      });

      // 验证源文件存在
      const sourceExists = await RNFS.exists(sourcePath);
      if (!sourceExists) {
        throw new Error('源文件不存在');
      }

      // 对于超大文件（>50MB），使用后台任务
      if (totalSize > 50 * 1024 * 1024) {
        return this.copyFileInBackground(sourcePath, targetPath, totalSize, onProgress);
      }

      // 开始进度报告
      onProgress({
        bytesWritten: 0,
        totalBytes: totalSize,
        progress: 0,
      });

      // 使用标准的文件复制方法
      await RNFS.copyFile(sourcePath, targetPath);

      // 验证复制结果
      const targetExists = await RNFS.exists(targetPath);
      if (!targetExists) {
        throw new Error('文件复制后验证失败');
      }

      // 完成进度报告
      onProgress({
        bytesWritten: totalSize,
        totalBytes: totalSize,
        progress: 1,
      });

      console.log('[FileStorageService] 文件复制成功');

    } catch (error: any) {
      console.error('[FileStorageService] 文件复制失败:', error?.message);
      throw new Error(`文件复制失败: ${error?.message || '未知错误'}`);
    }
  }

  /**
   * 后台文件复制（用于超大文件）
   */
  private async copyFileInBackground(
    sourcePath: string,
    targetPath: string,
    totalSize: number,
    onProgress: (progress: FileUploadProgress) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const taskId = BackgroundTaskManager.addTask({
        type: 'file_copy',
        data: { sourcePath, targetPath, totalSize },
        onProgress: (progress: number) => {
          onProgress({
            bytesWritten: Math.round((progress / 100) * totalSize),
            totalBytes: totalSize,
            progress: progress / 100,
          });
        },
        onComplete: (result) => {
          console.log('[FileStorageService] 后台文件复制完成:', result);
          resolve();
        },
        onError: (error) => {
          console.error('[FileStorageService] 后台文件复制失败:', error);
          reject(error);
        },
      });

      console.log(`[FileStorageService] 启动后台文件复制任务: ${taskId}`);
    });
  }

  /**
   * 生成唯一文件ID
   */
  private generateFileId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 9);
    return `${timestamp}_${random}`;
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot > 0 ? fileName.substring(lastDot) : '';
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number): string {
    if (bytes < 1024) {return `${bytes} B`;}
    if (bytes < 1024 * 1024) {return `${(bytes / 1024).toFixed(1)} KB`;}
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }

  /**
   * 获取文件存储目录路径
   */
  getStorageDirectory(): string {
    return this.filesDir;
  }

  /**
   * 智能检查文件是否存在
   * 支持路径迁移和修复
   */
  async fileExists(localPath: string): Promise<boolean> {
    try {
      // 首先尝试直接检查
      const directExists = await RNFS.exists(localPath);
      if (directExists) {
        return true;
      }

      // 如果直接检查失败，尝试路径迁移
      console.log('[FileStorageService] 文件不存在，尝试路径迁移:', localPath);
      const migrationResult = await FilePathManager.migrateFilePath(localPath);

      return migrationResult.success;
    } catch (error) {
      console.error('[FileStorageService] 检查文件存在性失败:', error);
      return false;
    }
  }

  /**
   * 智能获取文件路径
   * 自动处理路径迁移，支持相对路径和绝对路径
   */
  async getValidFilePath(originalPath: string): Promise<string | null> {
    try {
      // 🔧 使用FilePathManager进行智能路径解析
      const pathInfo = await FilePathManager.resolvePath(originalPath);

      if (pathInfo.isValid) {
        // 如果需要迁移，记录日志
        if (pathInfo.migrationNeeded) {
          console.log('[FileStorageService] 路径迁移成功:', originalPath, '->', pathInfo.absolutePath);
        }
        return pathInfo.absolutePath;
      }

      // 如果智能解析失败，尝试传统的路径迁移
      const migrationResult = await FilePathManager.migrateFilePath(originalPath);
      if (migrationResult.success && migrationResult.newPath) {
        console.log('[FileStorageService] 传统路径迁移成功:', originalPath, '->', migrationResult.newPath);
        return migrationResult.newPath;
      }

      console.warn('[FileStorageService] 无法找到有效文件路径:', originalPath);
      return null;
    } catch (error) {
      console.error('[FileStorageService] 获取有效文件路径失败:', error);
      return null;
    }
  }

  /**
   * 静态方法：删除文件
   */
  static async deleteFile(filePath: string): Promise<void> {
    try {
      const exists = await RNFS.exists(filePath);
      if (exists) {
        await RNFS.unlink(filePath);
        console.log('[FileStorageService] 文件删除成功:', filePath);
      } else {
        console.warn('[FileStorageService] 文件不存在，无需删除:', filePath);
      }

      // 从缓存管理器中移除
      await FileCacheManager.removeFromCache(filePath);
    } catch (error) {
      console.error('[FileStorageService] 删除文件失败:', error);
      throw error;
    }
  }

  /**
   * 静态方法：重命名文件
   */
  static async renameFile(oldPath: string, newName: string): Promise<string> {
    try {
      // 获取文件目录
      const directory = oldPath.substring(0, oldPath.lastIndexOf('/'));
      const newPath = `${directory}/${newName}`;

      // 检查新文件名是否已存在
      const newExists = await RNFS.exists(newPath);
      if (newExists) {
        throw new Error('目标文件名已存在');
      }

      // 重命名文件
      await RNFS.moveFile(oldPath, newPath);
      console.log('[FileStorageService] 文件重命名成功:', oldPath, '->', newPath);

      return newPath;
    } catch (error) {
      console.error('[FileStorageService] 重命名文件失败:', error);
      throw error;
    }
  }

  /**
   * 静态方法：分享文件
   */
  static async shareFile(filePath: string, fileName: string): Promise<void> {
    try {
      const Share = require('react-native-share').default;

      const shareOptions = {
        title: '分享文件',
        message: `分享文件: ${fileName}`,
        url: `file://${filePath}`,
        type: 'application/octet-stream',
      };

      await Share.open(shareOptions);
      console.log('[FileStorageService] 文件分享成功:', fileName);
    } catch (error: any) {
      console.error('[FileStorageService] 分享文件失败:', error);
      // 如果用户取消分享，不抛出错误
      if (error?.message !== 'User did not share') {
        throw error;
      }
    }
  }

  /**
   * 获取所有存储的文件列表
   */
  async getAllStoredFiles(): Promise<StoredFileInfo[]> {
    try {
      await this.initialize();

      const files = await RNFS.readDir(this.filesDir);
      const storedFiles: StoredFileInfo[] = [];

      for (const file of files) {
        if (file.isFile()) {
          const fileInfo = await this.getFileInfo(file.path);
          if (fileInfo) {
            storedFiles.push(fileInfo);
          }
        }
      }

      return storedFiles.sort((a, b) => b.storedAt - a.storedAt);
    } catch (error) {
      console.error('[FileStorageService] 获取文件列表失败:', error);
      return [];
    }
  }

  /**
   * 智能缓存清理（后台执行）
   */
  static async performSmartCacheCleanup(targetSize?: number): Promise<string> {
    return new Promise((resolve, reject) => {
      const taskId = BackgroundTaskManager.addTask({
        type: 'cache_cleanup',
        data: { targetSize },
        onComplete: (result) => {
          const { cleanedSize } = result;
          const message = `缓存清理完成，释放了 ${FileStorageService.formatFileSize(cleanedSize)} 空间`;
          console.log('[FileStorageService] ' + message);
          resolve(taskId);
        },
        onError: (error) => {
          console.error('[FileStorageService] 缓存清理失败:', error);
          reject(error);
        },
      });

      console.log(`[FileStorageService] 启动智能缓存清理任务: ${taskId}`);
    });
  }

  /**
   * 获取缓存统计信息
   */
  static getCacheStats() {
    return FileCacheManager.getCacheStats();
  }

  /**
   * 获取后台任务统计信息
   */
  static getTaskStats() {
    return BackgroundTaskManager.getTaskStats();
  }

  /**
   * 确保目录存在，如果不存在则创建
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      const exists = await RNFS.exists(dirPath);
      if (!exists) {
        await RNFS.mkdir(dirPath);
        console.log('[FileStorageService] 创建目录:', dirPath);
      }
    } catch (error) {
      console.error('[FileStorageService] 创建目录失败:', dirPath, error);
      throw error;
    }
  }

  /**
   * 格式化文件大小
   */
  private static formatFileSize(bytes: number): string {
    if (bytes < 1024) {return `${bytes} B`;}
    if (bytes < 1024 * 1024) {return `${(bytes / 1024).toFixed(1)} KB`;}
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
}

// 导出类和实例
export { FileStorageService };
export default new FileStorageService();
