/**
 * iOS Vision框架集成测试
 * 用于验证原生模块是否正确集成
 */

import { NativeModules, Platform } from 'react-native';

const { RNVisionOCR } = NativeModules;

export interface VisionOCRTestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export class VisionOCRTest {
  /**
   * 测试原生模块是否可用
   */
  static async testNativeModuleAvailable(): Promise<VisionOCRTestResult> {
    try {
      if (Platform.OS !== 'ios') {
        return {
          success: false,
          message: '当前平台不是iOS，跳过Vision框架测试',
          error: 'PLATFORM_NOT_SUPPORTED',
        };
      }

      if (!RNVisionOCR) {
        return {
          success: false,
          message: 'RNVisionOCR原生模块未找到',
          error: 'NATIVE_MODULE_NOT_FOUND',
        };
      }

      return {
        success: true,
        message: 'RNVisionOCR原生模块已成功加载',
        data: { platform: Platform.OS, moduleFound: true },
      };
    } catch (error) {
      return {
        success: false,
        message: '测试原生模块时发生错误',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 测试Vision框架能力检查
   */
  static async testOCRCapability(): Promise<VisionOCRTestResult> {
    try {
      if (!RNVisionOCR) {
        return {
          success: false,
          message: 'RNVisionOCR原生模块不可用',
          error: 'NATIVE_MODULE_NOT_AVAILABLE',
        };
      }

      const capability = await RNVisionOCR.checkOCRCapability();

      return {
        success: true,
        message: 'OCR能力检查成功',
        data: capability,
      };
    } catch (error) {
      return {
        success: false,
        message: 'OCR能力检查失败',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 测试Vision框架初始化
   */
  static async testVisionFrameworkInit(): Promise<VisionOCRTestResult> {
    try {
      if (!RNVisionOCR) {
        return {
          success: false,
          message: 'RNVisionOCR原生模块不可用',
          error: 'NATIVE_MODULE_NOT_AVAILABLE',
        };
      }

      const isInitialized = await RNVisionOCR.initializeVisionFramework();

      return {
        success: isInitialized,
        message: isInitialized ? 'Vision框架初始化成功' : 'Vision框架初始化失败',
        data: { initialized: isInitialized },
      };
    } catch (error) {
      return {
        success: false,
        message: 'Vision框架初始化测试失败',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 测试基础OCR识别
   */
  static async testBasicOCR(): Promise<VisionOCRTestResult> {
    try {
      if (!RNVisionOCR) {
        return {
          success: false,
          message: 'RNVisionOCR原生模块不可用',
          error: 'NATIVE_MODULE_NOT_AVAILABLE',
        };
      }

      const result = await RNVisionOCR.recognizeText('test.jpg');

      return {
        success: true,
        message: '基础OCR测试成功',
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: '基础OCR测试失败',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 运行完整的集成测试套件
   */
  static async runFullTestSuite(): Promise<{
    overall: boolean;
    results: { [key: string]: VisionOCRTestResult };
  }> {
    const results: { [key: string]: VisionOCRTestResult } = {};

    console.log('🧪 开始iOS Vision框架集成测试...');

    // 测试1: 原生模块可用性
    console.log('📱 测试原生模块可用性...');
    results.nativeModule = await this.testNativeModuleAvailable();
    console.log(`   ${results.nativeModule.success ? '✅' : '❌'} ${results.nativeModule.message}`);

    // 测试2: OCR能力检查
    console.log('🔍 测试OCR能力检查...');
    results.ocrCapability = await this.testOCRCapability();
    console.log(`   ${results.ocrCapability.success ? '✅' : '❌'} ${results.ocrCapability.message}`);

    // 测试3: Vision框架初始化
    console.log('🚀 测试Vision框架初始化...');
    results.visionInit = await this.testVisionFrameworkInit();
    console.log(`   ${results.visionInit.success ? '✅' : '❌'} ${results.visionInit.message}`);

    // 测试4: 基础OCR识别
    console.log('📝 测试基础OCR识别...');
    results.basicOCR = await this.testBasicOCR();
    console.log(`   ${results.basicOCR.success ? '✅' : '❌'} ${results.basicOCR.message}`);

    // 计算总体结果
    const allTests = Object.values(results);
    const passedTests = allTests.filter(test => test.success).length;
    const overall = passedTests === allTests.length;

    console.log(`\n📊 测试结果总结: ${passedTests}/${allTests.length} 通过`);
    console.log(`🎯 总体结果: ${overall ? '✅ 全部通过' : '❌ 部分失败'}`);

    return { overall, results };
  }
}
