/**
 * 媒体文件下载服务
 * 实现媒体文件下载功能
 * 支持从服务器下载媒体文件，服务器内部处理企业微信API调用
 */

import RNFS from 'react-native-fs';
import { Platform } from 'react-native';
import { userDeviceManager } from '../utils/UserDeviceManager';
import { weChatConfig } from '../config/appConfig';
import { detectFileTypeFromName } from '../utils/fileTypes';

export interface MediaDownloadOptions {
  mediaId: string;
  mediaType: 'image' | 'voice' | 'video' | 'file';
  fileName?: string;
  maxRetries?: number;
  timeout?: number;
  onProgress?: (progress: number) => void;
}

export interface MediaDownloadResult {
  success: boolean;
  filePath?: string;
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
  error?: string;
  retryCount?: number;
}

export interface DownloadProgress {
  mediaId: string;
  progress: number;
  totalSize: number;
  downloadedSize: number;
  status: 'pending' | 'downloading' | 'completed' | 'failed' | 'cancelled';
}

interface DownloadTask {
  promise: Promise<MediaDownloadResult>;
  cancelToken: { cancelled: boolean };
  startTime: number;
}

class MediaDownloadService {
  private activeDownloads: Map<string, DownloadTask> = new Map();
  private progressCallbacks: Map<string, (progress: DownloadProgress) => void> = new Map();
  private downloadDir: string;
  private maxConcurrentDownloads: number = 3;
  private downloadSemaphore: number = 0;
  private waitingQueue: Array<{ mediaId: string; resolve: () => void }> = [];
  private cleanupTimer: number | null = null;

  constructor() {
    // 设置下载目录
    this.downloadDir = Platform.OS === 'ios'
      ? RNFS.DocumentDirectoryPath + '/media'
      : RNFS.ExternalDirectoryPath + '/media';

    this.initializeDownloadDirectory();
    this.startCleanupTimer();
  }

  /**
   * 启动定时清理任务
   */
  private startCleanupTimer(): void {
    // 每30分钟清理一次过期的下载任务
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredTasks();
    }, 30 * 60 * 1000);
  }

  /**
   * 清理过期的下载任务
   */
  private cleanupExpiredTasks(): void {
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5分钟超时

    for (const [mediaId, task] of this.activeDownloads.entries()) {
      if (now - task.startTime > maxAge) {
        console.warn(`[MediaDownload] 清理过期下载任务: ${mediaId}`);
        task.cancelToken.cancelled = true;
        this.activeDownloads.delete(mediaId);
        this.progressCallbacks.delete(mediaId);
        this.releaseSemaphore();
      }
    }
  }

  /**
   * 获取信号量
   */
  private async acquireSemaphore(mediaId: string): Promise<void> {
    return new Promise((resolve) => {
      if (this.downloadSemaphore < this.maxConcurrentDownloads) {
        this.downloadSemaphore++;
        resolve();
      } else {
        this.waitingQueue.push({ mediaId, resolve });
      }
    });
  }

  /**
   * 释放信号量
   */
  private releaseSemaphore(): void {
    this.downloadSemaphore = Math.max(0, this.downloadSemaphore - 1);

    if (this.waitingQueue.length > 0) {
      const next = this.waitingQueue.shift();
      if (next) {
        this.downloadSemaphore++;
        next.resolve();
      }
    }
  }

  /**
   * 设置最大并发下载数
   */
  setMaxConcurrentDownloads(max: number): void {
    if (max > 0 && max <= 10) {
      this.maxConcurrentDownloads = max;
      console.log(`[MediaDownload] 最大并发下载数设置为: ${max}`);
    } else {
      console.warn('[MediaDownload] 无效的并发下载数，应在1-10之间');
    }
  }

  /**
   * 初始化下载目录
   */
  private async initializeDownloadDirectory(): Promise<void> {
    try {
      const exists = await RNFS.exists(this.downloadDir);
      if (!exists) {
        await RNFS.mkdir(this.downloadDir);
        console.log('[MediaDownload] 下载目录已创建:', this.downloadDir);
      }
    } catch (error) {
      console.error('[MediaDownload] 初始化下载目录失败:', error);
    }
  }

  /**
   * 下载媒体文件
   */
  async downloadMedia(options: MediaDownloadOptions): Promise<MediaDownloadResult> {
    const { mediaId, mediaType, fileName } = options;

    console.log('[MediaDownload] 开始下载媒体文件:', { mediaId, mediaType, fileName });

    // 检查是否已在下载队列中
    if (this.activeDownloads.has(mediaId)) {
      console.log('[MediaDownload] 媒体文件已在下载队列中:', mediaId);
      return this.activeDownloads.get(mediaId)!.promise;
    }

    // 获取信号量
    await this.acquireSemaphore(mediaId);

    try {
      // 创建取消令牌
      const cancelToken = { cancelled: false };

      // 创建下载任务
      const downloadPromise = this.performDownload(options, cancelToken);
      const downloadTask: DownloadTask = {
        promise: downloadPromise,
        cancelToken,
        startTime: Date.now(),
      };

      this.activeDownloads.set(mediaId, downloadTask);

      const result = await downloadPromise;
      return result;
    } catch (error) {
      console.error('[MediaDownload] 下载失败:', error);
      throw error;
    } finally {
      // 清理下载任务
      this.activeDownloads.delete(mediaId);
      this.progressCallbacks.delete(mediaId);
      this.releaseSemaphore();
    }
  }

  /**
   * 执行下载操作
   */
  private async performDownload(
    options: MediaDownloadOptions,
    cancelToken: { cancelled: boolean }
  ): Promise<MediaDownloadResult> {
    const { mediaId, mediaType, fileName, maxRetries = 3, timeout = 30000, onProgress } = options;
    let retryCount = 0;
    let lastError: string = '';

    // 注册进度回调
    if (onProgress) {
      this.progressCallbacks.set(mediaId, (progress) => {
        if (!cancelToken.cancelled) {
          onProgress(progress.progress);
        }
      });
    }

    while (retryCount < maxRetries && !cancelToken.cancelled) {
      try {
        // 更新下载状态
        this.updateProgress(mediaId, {
          mediaId,
          progress: 0,
          totalSize: 0,
          downloadedSize: 0,
          status: 'pending',
        });

        // 由于方法已废弃，直接抛出错误
        throw new Error('此下载方法已废弃，请使用 downloadMediaFromSyncUrl 方法从消息同步返回的 download_url 进行下载');

      } catch (error) {
        if (cancelToken.cancelled) {
          throw new Error('下载已取消');
        }

        retryCount++;
        lastError = error instanceof Error ? error.message : String(error);

        console.warn(`[MediaDownload] 下载失败 (${retryCount}/${maxRetries}):`, lastError);

        // 更新下载状态
        this.updateProgress(mediaId, {
          mediaId,
          progress: 0,
          totalSize: 0,
          downloadedSize: 0,
          status: 'failed',
        });

        // 如果是media_id过期错误，不进行重试
        if (lastError.includes('media_id') && lastError.includes('expired')) {
          console.error('[MediaDownload] media_id已过期，停止重试:', mediaId);
          break;
        }

        // 等待一段时间后重试
        if (retryCount < maxRetries && !cancelToken.cancelled) {
          const delay = Math.min(1000 * Math.pow(2, retryCount), 10000); // 指数退避
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    if (cancelToken.cancelled) {
      throw new Error('下载已取消');
    }

    return {
      success: false,
      error: lastError,
      retryCount,
    };
  }

  /**
   * 使用消息同步返回的download_url进行下载
   */
  private buildDownloadUrlFromSync(downloadUrl: string): string {
    const baseUrl = weChatConfig.API_BASE_URL;
    // 如果download_url是相对路径，添加baseUrl
    if (downloadUrl.startsWith('/')) {
      return `${baseUrl}${downloadUrl}`;
    }
    // 如果已经是完整URL，直接返回
    return downloadUrl;
  }

  /**
   * 使用消息同步返回的download_url进行下载（推荐方法）
   */
  async downloadMediaFromSyncUrl(downloadUrl: string, options: Omit<MediaDownloadOptions, 'mediaId' | 'mediaType'>): Promise<MediaDownloadResult> {
    try {
      const finalUrl = this.buildDownloadUrlFromSync(downloadUrl);
      console.log('[MediaDownload] 使用同步URL下载:', { downloadUrl, finalUrl });

      // 生成文件路径（从URL中提取文件名）
      const fileName = this.extractFileNameFromUrl(downloadUrl);
      const filePath = await this.generateFilePath('sync', 'file', fileName);

      // 执行下载
      const result = await this.downloadFile(finalUrl, filePath, 'sync', options.timeout || 30000, { cancelled: false });

      return result;
    } catch (error) {
      console.error('[MediaDownload] 同步URL下载失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 从URL中提取文件名
   */
  private extractFileNameFromUrl(url: string): string {
    try {
      // 尝试从URL路径中提取文件名
      const urlObj = new URL(url, 'http://localhost');
      const pathname = urlObj.pathname;
      const lastSlash = pathname.lastIndexOf('/');
      if (lastSlash > 0) {
        const fileName = pathname.substring(lastSlash + 1);
        if (fileName && fileName !== '') {
          return fileName;
        }
      }
    } catch (error) {
      console.warn('[MediaDownload] 无法从URL提取文件名:', error);
    }
    return 'downloaded_file';
  }

  /**
   * 生成文件路径
   */
  private async generateFilePath(mediaId: string, mediaType: string, fileName?: string): Promise<string> {
    const extension = this.getFileExtension(mediaType, fileName);
    const finalFileName = fileName || `${mediaId}${extension}`;
    const filePath = `${this.downloadDir}/${finalFileName}`;

    // 确保目录存在
    await this.initializeDownloadDirectory();

    return filePath;
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(mediaType: string, fileName?: string): string {
    // 优先从文件名中提取扩展名
    if (fileName) {
      const typeInfo = detectFileTypeFromName(fileName);
      return `.${typeInfo.extension}`;
    }

    // 如果没有文件名，根据媒体类型返回默认扩展名
    switch (mediaType) {
      case 'image':
        return '.jpg';
      case 'voice':
        return '.amr';
      case 'video':
        return '.mp4';
      case 'file':
        return '.bin';
      default:
        return '.bin';
    }
  }

  /**
   * 执行文件下载
   */
  private async downloadFile(
    url: string,
    filePath: string,
    mediaId: string,
    timeout: number,
    cancelToken: { cancelled: boolean }
  ): Promise<MediaDownloadResult> {
    return new Promise((resolve, reject) => {
      if (cancelToken.cancelled) {
        reject(new Error('下载已取消'));
        return;
      }

      // 更新下载状态
      this.updateProgress(mediaId, {
        mediaId,
        progress: 0,
        totalSize: 0,
        downloadedSize: 0,
        status: 'downloading',
      });

      // 设置基本请求头
      const headers: Record<string, string> = {
        'Accept': '*/*',
        'User-Agent': 'GongZhiMall-Mobile/1.0.0',
      };

      const downloadTask = RNFS.downloadFile({
        fromUrl: url,
        toFile: filePath,
        headers,
        connectionTimeout: timeout,
        readTimeout: timeout,
        progress: (res) => {
          if (cancelToken.cancelled) {
            return;
          }

          const progress = res.contentLength > 0 ? (res.bytesWritten / res.contentLength) * 100 : 0;

          // 更新下载进度
          this.updateProgress(mediaId, {
            mediaId,
            progress,
            totalSize: res.contentLength,
            downloadedSize: res.bytesWritten,
            status: 'downloading',
          });
        },
      });

      // 监听取消信号
      const cancelCheck = setInterval(() => {
        if (cancelToken.cancelled) {
          clearInterval(cancelCheck);
          RNFS.stopDownload(downloadTask.jobId);
          reject(new Error('下载已取消'));
        }
      }, 100);

      downloadTask.promise
        .then(async (response) => {
          clearInterval(cancelCheck);

          if (cancelToken.cancelled) {
            reject(new Error('下载已取消'));
            return;
          }

          if (response.statusCode === 200) {
            // 获取文件信息
            const fileInfo = await RNFS.stat(filePath);

            // 更新下载状态
            this.updateProgress(mediaId, {
              mediaId,
              progress: 100,
              totalSize: fileInfo.size,
              downloadedSize: fileInfo.size,
              status: 'completed',
            });

            resolve({
              success: true,
              filePath,
              fileName: filePath.split('/').pop(),
              fileSize: fileInfo.size,
              mimeType: this.getMimeType(filePath),
            });
          } else {
            throw new Error(`下载失败，HTTP状态码: ${response.statusCode}`);
          }
        })
        .catch((error) => {
          clearInterval(cancelCheck);

          // 清理部分下载的文件
          RNFS.exists(filePath).then(exists => {
            if (exists) {
              RNFS.unlink(filePath).catch(console.error);
            }
          });

          reject(error);
        });
    });
  }

  /**
   * 获取MIME类型
   */
  private getMimeType(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'amr':
        return 'audio/amr';
      case 'mp3':
        return 'audio/mpeg';
      case 'mp4':
        return 'video/mp4';
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default:
        return 'application/octet-stream';
    }
  }

  /**
   * 更新下载进度
   */
  private updateProgress(mediaId: string, progress: DownloadProgress): void {
    const callback = this.progressCallbacks.get(mediaId);
    if (callback) {
      callback(progress);
    }
  }

  /**
   * 取消下载
   */
  async cancelDownload(mediaId: string): Promise<boolean> {
    try {
      const task = this.activeDownloads.get(mediaId);
      if (task) {
        task.cancelToken.cancelled = true;
        console.log('[MediaDownload] 下载已标记为取消:', mediaId);
      }

      // 更新状态
      this.updateProgress(mediaId, {
        mediaId,
        progress: 0,
        totalSize: 0,
        downloadedSize: 0,
        status: 'cancelled',
      });

      console.log('[MediaDownload] 下载已取消:', mediaId);
      return true;
    } catch (error) {
      console.error('[MediaDownload] 取消下载失败:', error);
      return false;
    }
  }

  /**
   * 取消所有下载
   */
  async cancelAllDownloads(): Promise<void> {
    console.log('[MediaDownload] 取消所有下载任务');

    for (const [_mediaId, task] of this.activeDownloads.entries()) {
      task.cancelToken.cancelled = true;
    }

    // 清空等待队列
    this.waitingQueue = [];

    // 重置信号量
    this.downloadSemaphore = 0;

    console.log('[MediaDownload] 所有下载任务已取消');
  }

  /**
   * 检查文件是否已存在
   */
  async isFileExists(mediaId: string, mediaType: string, fileName?: string): Promise<{ exists: boolean; filePath?: string }> {
    try {
      const filePath = await this.generateFilePath(mediaId, mediaType, fileName);
      const exists = await RNFS.exists(filePath);

      return { exists, filePath: exists ? filePath : undefined };
    } catch (error) {
      console.error('[MediaDownload] 检查文件存在性失败:', error);
      return { exists: false };
    }
  }

  /**
   * 清理下载缓存
   */
  async clearDownloadCache(olderThanDays: number = 30): Promise<{ success: boolean; deletedCount: number }> {
    try {
      const files = await RNFS.readDir(this.downloadDir);
      const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
      let deletedCount = 0;

      for (const file of files) {
        if (file.mtime && file.mtime.getTime() < cutoffTime) {
          await RNFS.unlink(file.path);
          deletedCount++;
          console.log('[MediaDownload] 已删除过期文件:', file.name);
        }
      }

      console.log(`[MediaDownload] 缓存清理完成，删除了 ${deletedCount} 个文件`);
      return { success: true, deletedCount };
    } catch (error) {
      console.error('[MediaDownload] 清理缓存失败:', error);
      return { success: false, deletedCount: 0 };
    }
  }

  /**
   * 获取下载目录大小
   */
  async getDownloadCacheSize(): Promise<{ success: boolean; size: number; fileCount: number }> {
    try {
      const files = await RNFS.readDir(this.downloadDir);
      let totalSize = 0;
      let fileCount = 0;

      for (const file of files) {
        if (file.isFile()) {
          totalSize += file.size;
          fileCount++;
        }
      }

      return { success: true, size: totalSize, fileCount };
    } catch (error) {
      console.error('[MediaDownload] 获取缓存大小失败:', error);
      return { success: false, size: 0, fileCount: 0 };
    }
  }

  /**
   * 获取当前下载状态
   */
  getDownloadStatus(): {
    activeDownloads: number;
    waitingQueue: number;
    maxConcurrent: number;
  } {
    return {
      activeDownloads: this.activeDownloads.size,
      waitingQueue: this.waitingQueue.length,
      maxConcurrent: this.maxConcurrentDownloads,
    };
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    console.log('[MediaDownload] 销毁服务');

    // 取消所有下载
    this.cancelAllDownloads();

    // 清理定时器
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    // 清理所有映射
    this.activeDownloads.clear();
    this.progressCallbacks.clear();
    this.waitingQueue = [];

    console.log('[MediaDownload] 服务已销毁');
  }
}

export default new MediaDownloadService();

