/**
 * 文件预览服务
 * 提供统一的文件预览接口，支持多种文件格式
 * 采用原生预览 + 降级机制的策略
 */

import FileViewer from 'react-native-file-viewer';
import { Alert } from 'react-native';
import { FileErrorHandler, FileErrorType } from '../utils/fileErrorHandler';

export interface FilePreviewOptions {
  showOpenWithDialog?: boolean;
  showAppsSuggestions?: boolean;
  onError?: (error: Error) => void;
  onSuccess?: () => void;
}

export interface FilePreviewResult {
  success: boolean;
  strategy: 'native' | 'custom' | 'error';
  fileType?: string;
  error?: any;
}

export interface FileData {
  uri: string;
  name: string;
  size: number;
  type: string;
}

class FilePreviewService {

  /**
   * 预览文件 - 统一入口
   */
  static async previewFile(
    fileData: FileData,
    options: FilePreviewOptions = {}
  ): Promise<FilePreviewResult> {
    const {
      showOpenWithDialog = false,
      showAppsSuggestions = false,
      onError,
      onSuccess,
    } = options;

    try {
      console.log('[FilePreviewService] 开始预览文件:', {
        name: fileData.name,
        type: fileData.type,
        size: `${(fileData.size / 1024 / 1024).toFixed(2)}MB`,
      });

      // 根据文件类型选择预览策略
      const fileExtension = this.getFileExtension(fileData.name).toLowerCase();

      console.log('[FilePreviewService] 文件预览策略分析:', {
        fileName: fileData.name,
        fileExtension,
        shouldUseNative: this.shouldUseNativePreview(fileExtension),
        customViewerType: this.getFileTypeForCustomViewer(fileData),
      });

      if (this.shouldUseNativePreview(fileExtension)) {
        console.log('[FilePreviewService] 使用原生预览器');
        await this.previewWithNative(fileData, { showOpenWithDialog, showAppsSuggestions });
        onSuccess?.();
        return {
          success: true,
          strategy: 'native',
        };
      } else {
        console.log('[FilePreviewService] 需要使用自定义预览器');
        const fileType = this.getFileTypeForCustomViewer(fileData);
        console.log('[FilePreviewService] 返回自定义预览策略，文件类型:', fileType);
        return {
          success: true,
          strategy: 'custom',
          fileType,
        };
      }

    } catch (error: any) {
      console.error('[FilePreviewService] 预览失败:', error);
      onError?.(error);
      await this.handlePreviewError(fileData, error);

      return {
        success: false,
        strategy: 'error',
        error,
      };
    }
  }

  /**
   * 使用原生预览器
   */
  private static async previewWithNative(
    fileData: FileData,
    options: { showOpenWithDialog: boolean; showAppsSuggestions: boolean }
  ): Promise<void> {
    try {
      await FileViewer.open(fileData.uri, {
        showOpenWithDialog: options.showOpenWithDialog,
        showAppsSuggestions: options.showAppsSuggestions,
        onDismiss: () => {
          console.log('[FilePreviewService] 原生预览器已关闭');
        },
      });

      console.log('[FilePreviewService] 原生预览成功');

    } catch (error: any) {
      console.log('[FilePreviewService] 原生预览失败，尝试降级方案:', error.message);

      // 如果原生预览失败，尝试显示系统选择器
      if (!options.showOpenWithDialog) {
        await FileViewer.open(fileData.uri, {
          showOpenWithDialog: true,
          showAppsSuggestions: true,
        });
      } else {
        throw error;
      }
    }
  }



  /**
   * 处理预览错误
   */
  private static async handlePreviewError(fileData: FileData, error: Error): Promise<void> {
    const fileExtension = this.getFileExtension(fileData.name);
    console.log('[FilePreviewService] 处理预览错误:', fileExtension, error);
    Alert.alert(
      '文件预览',
      `暂时无法预览此${fileExtension.toUpperCase()}文件，您可以：`,
      [
        {
          text: '用其他应用打开',
          onPress: () => this.openWithOtherApp(fileData),
        },
        {
          text: '查看文件信息',
          onPress: () => this.showFileInfo(fileData),
        },
        {
          text: '取消',
          style: 'cancel',
        },
      ],
    );
  }

  /**
   * 用其他应用打开文件
   */
  private static async openWithOtherApp(fileData: FileData): Promise<void> {
    try {
      await FileViewer.open(fileData.uri, {
        showOpenWithDialog: true,
        showAppsSuggestions: true,
      });
    } catch (error) {
      FileErrorHandler.showError(
        FileErrorType.UNKNOWN_ERROR,
        '无法找到合适的应用来打开此文件'
      );
    }
  }

  /**
   * 显示文件信息
   */
  private static showFileInfo(fileData: FileData): void {
    const sizeText = this.formatFileSize(fileData.size);
    const typeText = fileData.type || '未知类型';
    Alert.alert(
      '文件信息',
      `文件名：${fileData.name}\n文件大小：${sizeText}\n文件类型：${typeText}`,
      [{ text: '确定' }]
    );
  }

  /**
   * 判断是否应该使用原生预览
   * 🔧 修复：恢复正确的预览策略，解决文件预览回归问题
   */
  private static shouldUseNativePreview(extension: string): boolean {
    // 🔧 修复回归：明确哪些文件类型应该使用原生预览器
    const nativePreviewExtensions: string[] = [
      // 这些文件类型使用react-native-file-viewer原生预览
      // 注意：PDF、Excel、Word、OFD、PPT都应该使用自定义预览器（FilePreviewScreen）
      // 目前暂时不使用原生预览器，所有文件都路由到FilePreviewScreen
    ];

    // 🔧 策略修正：
    // - PDF/Excel/Word/OFD/PPT: 使用自定义预览器（FilePreviewScreen）
    // - 其他格式: 可以考虑使用原生预览器
    return nativePreviewExtensions.includes(extension);
  }

  /**
   * 获取自定义预览器的文件类型
   * 🔧 添加PPT文件类型映射，确保正确路由到FilePreviewScreen
   */
  private static getFileTypeForCustomViewer(fileData: FileData): string {
    const extension = this.getFileExtension(fileData.name).toLowerCase();

    const typeMap: { [key: string]: string } = {
      '.pdf': 'pdf',
      '.ofd': 'ofd',
      '.doc': 'word',
      '.docx': 'word',
      '.xls': 'excel',
      '.xlsx': 'excel',
      '.ppt': 'ppt',      // 🔧 添加PPT支持
      '.pptx': 'ppt',     // 🔧 添加PPTX支持
      '.pptm': 'ppt',     // 🔧 添加PPTM支持
      '.dps': 'ppt',      // 🔧 添加WPS演示文档支持
    };

    return typeMap[extension] || 'unknown';
  }

  /**
   * 获取文件扩展名
   */
  private static getFileExtension(fileName: string): string {
    if (!fileName) {
      return '';
    }
    const lastDot = fileName.lastIndexOf('.');
    return lastDot > 0 ? fileName.substring(lastDot) : '';
  }

  /**
   * 格式化文件大小
   */
  private static formatFileSize(bytes: number): string {
    if (bytes < 1024) {return `${bytes} B`;}
    if (bytes < 1024 * 1024) {return `${(bytes / 1024).toFixed(1)} KB`;}
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
}

export default FilePreviewService;
