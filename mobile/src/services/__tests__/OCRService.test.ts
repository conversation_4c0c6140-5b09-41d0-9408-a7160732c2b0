/**
 * OCR服务准确率测试
 * 验证OCR功能是否达到MVP要求的60%准确率
 *
 * 测试场景：
 * 1. 微信截图识别
 * 2. 政务文档拍照识别
 * 3. 不同光线条件下的识别效果
 * 4. 手写批注识别
 * 5. 多语言混合文本识别
 */

import OCRService, { OCRResult, OCROptions } from '../OCRService';
import { Platform } from 'react-native';
import * as path from 'path';

// Mock react-native-fs for testing
const mockFS = {
  exists: jest.fn().mockResolvedValue(false),
  DocumentDirectoryPath: '/mock/documents',
};

// 导入测试图片配置
const testImagesConfig = require('../../../assets/test-images/expected-results.json');

describe('OCRService 真实功能测试', () => {
  let ocrService: typeof OCRService;
  const testImagesPath = path.join(__dirname, '../../../assets/test-images');

  beforeAll(async () => {
    ocrService = OCRService;

    // 检查测试图片是否存在
    const testImagesExist = await mockFS.exists(testImagesPath);
    if (!testImagesExist) {
      console.warn('测试图片目录不存在，请按照 assets/test-images/README.md 准备测试图片');
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('OCR能力检测', () => {
    it('应该能够检测设备OCR能力', async () => {
      const capability = await ocrService.checkOCRCapability();

      expect(capability).toBeDefined();
      expect(capability.available).toBeDefined();
      expect(capability.primaryEngine).toBeDefined();

      console.log('OCR能力检测结果:', capability);
    });

    it('iOS设备应该支持Vision OCR', async () => {
      if (Platform.OS === 'ios') {
        const capability = await ocrService.checkOCRCapability();
        expect(capability.available).toBe(true);
        expect(capability.primaryEngine).toBe('ios_vision');
      }
    });

    it('Android设备应该支持MLKit OCR', async () => {
      if (Platform.OS === 'android') {
        const capability = await ocrService.checkOCRCapability();
        expect(capability.available).toBe(true);
        expect(capability.primaryEngine).toBe('android_mlkit');
      }
    });
  });

  describe('基础OCR功能测试', () => {
    const basicTests = [
      {
        filename: 'basic-chinese-text.jpg',
        testType: 'basic',
        description: '基础中文文本识别',
      },
      {
        filename: 'mixed-content.jpg',
        testType: 'mixed',
        description: '混合内容识别',
      },
    ];

    basicTests.forEach(test => {
      it(`应该能够识别${test.description}`, async () => {
        const imagePath = path.join(testImagesPath, test.filename);
        const imageExists = await mockFS.exists(imagePath);

        if (!imageExists) {
          console.warn(`测试图片 ${test.filename} 不存在，跳过测试`);
          return;
        }

        const imageUri = `file://${imagePath}`;
        const result = await ocrService.recognizeFromImageUri(imageUri);
        const expectedResult = testImagesConfig[test.filename];

        expect(result).toBeDefined();
        expect(result.text).toBeDefined();
        expect(result.confidence).toBeGreaterThanOrEqual(expectedResult.minConfidence);
        expect(result.blocks).toBeDefined();
        expect(result.blocks.length).toBeGreaterThan(0);
        expect(result.engine).toBeDefined();
        expect(result.processingTime).toBeGreaterThan(0);

        // 验证关键词识别
        if (expectedResult.keywords && expectedResult.keywords.length > 0) {
          const recognizedKeywords = expectedResult.keywords.filter((keyword: string) =>
            result.text.includes(keyword)
          );
          const keywordAccuracy = recognizedKeywords.length / expectedResult.keywords.length;
          expect(keywordAccuracy).toBeGreaterThanOrEqual(0.5); // 至少50%关键词识别
        }

        console.log(`${test.description} - 置信度: ${result.confidence}, 引擎: ${result.engine}, 处理时间: ${result.processingTime}ms`);
      });
    });
  });

  describe('政务文档识别测试', () => {
    const governmentTests = [
      {
        filename: 'government-notice.jpg',
        testType: 'government',
        description: '政府公告识别',
      },
      {
        filename: 'legal-document.jpg',
        testType: 'legal',
        description: '法律文件识别',
      },
    ];

    governmentTests.forEach(test => {
      it(`应该能够识别${test.description}`, async () => {
        const imagePath = path.join(testImagesPath, test.filename);
        const imageExists = await mockFS.exists(imagePath);

        if (!imageExists) {
          console.warn(`测试图片 ${test.filename} 不存在，跳过测试`);
          return;
        }

        const imageUri = `file://${imagePath}`;
        const result = await ocrService.recognizeFromImageUri(imageUri);
        const expectedResult = testImagesConfig[test.filename];

        expect(result.confidence).toBeGreaterThanOrEqual(expectedResult.minConfidence);
        expect(result.text.length).toBeGreaterThan(50); // 政务文档通常内容较多
        expect(result.blocks.length).toBeGreaterThan(0);

        // 验证文档结构元素识别
        let structureScore = 0;
        if (expectedResult.structureElements && expectedResult.structureElements.length > 0) {
          const recognizedElements = expectedResult.structureElements.filter((element: string) =>
            result.text.includes(element)
          );
          structureScore = recognizedElements.length / expectedResult.structureElements.length;
          expect(structureScore).toBeGreaterThanOrEqual(0.3); // 至少30%结构元素识别
        }

        console.log(`${test.description} - 置信度: ${result.confidence}, 结构识别率: ${structureScore || 'N/A'}`);
      });
    });
  });

  describe('证件识别测试', () => {
    const idTests = [
      {
        filename: 'id-card-front.jpg',
        testType: 'id',
        description: '身份证正面识别',
      },
      {
        filename: 'work-permit.jpg',
        testType: 'permit',
        description: '工作证件识别',
      },
    ];

    idTests.forEach(test => {
      it(`应该能够识别${test.description}`, async () => {
        const imagePath = path.join(testImagesPath, test.filename);
        const imageExists = await mockFS.exists(imagePath);

        if (!imageExists) {
          console.warn(`测试图片 ${test.filename} 不存在，跳过测试`);
          return;
        }

        const imageUri = `file://${imagePath}`;
        const result = await ocrService.recognizeFromImageUri(imageUri);
        const expectedResult = testImagesConfig[test.filename];

        expect(result.confidence).toBeGreaterThanOrEqual(expectedResult.minConfidence);

        // 验证信息提取
        let extractionRate = 0;
        if (expectedResult.extractionTargets) {
          const extractedInfo: Record<string, boolean> = {};

          expectedResult.extractionTargets.forEach((target: string) => {
            switch (target) {
              case 'phone':
                extractedInfo.phone = /1[3-9]\d{9}/.test(result.text);
                break;
              case 'email':
                extractedInfo.email = /\S+@\S+\.\S+/.test(result.text);
                break;
              case 'idNumber':
                extractedInfo.idNumber = /\d{17}[\dX]/.test(result.text);
                break;
              case 'name':
                extractedInfo.name = /[\u4e00-\u9fa5]{2,4}/.test(result.text);
                break;
            }
          });

          const extractionSuccess = Object.values(extractedInfo).filter(Boolean).length;
          extractionRate = extractionSuccess / expectedResult.extractionTargets.length;
          expect(extractionRate).toBeGreaterThanOrEqual(0.5); // 至少50%信息提取成功
        }

        console.log(`${test.description} - 置信度: ${result.confidence}, 信息提取率: ${extractionRate || 'N/A'}`);
      });
    });
  });

  describe('不同质量图片识别测试', () => {
    const qualityTests = [
      {
        filename: 'high-quality-text.jpg',
        expectedConfidence: 0.8,
        description: '高质量图片',
      },
      {
        filename: 'medium-quality-text.jpg',
        expectedConfidence: 0.65,
        description: '中等质量图片',
      },
      {
        filename: 'low-quality-text.jpg',
        expectedConfidence: 0.5,
        description: '低质量图片',
      },
    ];

    qualityTests.forEach(test => {
      it(`应该能够处理${test.description}`, async () => {
        const imagePath = path.join(testImagesPath, test.filename);
        const imageExists = await mockFS.exists(imagePath);

        if (!imageExists) {
          console.warn(`测试图片 ${test.filename} 不存在，跳过测试`);
          return;
        }

        const imageUri = `file://${imagePath}`;
        const result = await ocrService.recognizeFromImageUri(imageUri);

        expect(result.confidence).toBeGreaterThanOrEqual(test.expectedConfidence);

        // 即使是低质量图片，也应该有一定的识别结果
        if (test.expectedConfidence >= 0.6) {
          expect(result.text.length).toBeGreaterThan(20);
        } else {
          expect(result.text.length).toBeGreaterThan(0);
        }

        console.log(`${test.description} - 置信度: ${result.confidence}, 文本长度: ${result.text.length}`);
      });
    });
  });

  describe('批量OCR处理测试', () => {
    it('应该能够批量处理多张图片', async () => {
      const testFiles = ['basic-chinese-text.jpg', 'mixed-content.jpg', 'high-quality-text.jpg'];
      const existingFiles: string[] = [];

      // 检查哪些测试文件存在
      for (const filename of testFiles) {
        const imagePath = path.join(testImagesPath, filename);
        const exists = await mockFS.exists(imagePath);
        if (exists) {
          existingFiles.push(`file://${imagePath}`);
        }
      }

      if (existingFiles.length === 0) {
        console.warn('没有可用的测试图片，跳过批量处理测试');
        return;
      }

      const results = await ocrService.batchRecognize(existingFiles);

      expect(results).toHaveLength(existingFiles.length);

      // 计算批量处理的平均准确率
      const validResults = results.filter((result: OCRResult) => result.confidence > 0);
      const averageConfidence = validResults.reduce((sum: number, result: OCRResult) => sum + result.confidence, 0) / validResults.length;

      expect(averageConfidence).toBeGreaterThanOrEqual(0.5); // 平均准确率应达到50%
      expect(validResults.length / results.length).toBeGreaterThanOrEqual(0.8); // 至少80%的图片能成功识别

      console.log(`批量处理 - 平均置信度: ${averageConfidence}, 成功率: ${validResults.length / results.length}`);
    });
  });

  describe('OCR性能测试', () => {
    it('单张图片处理时间应在合理范围内', async () => {
      const testFile = 'basic-chinese-text.jpg';
      const imagePath = path.join(testImagesPath, testFile);
      const imageExists = await mockFS.exists(imagePath);

      if (!imageExists) {
        console.warn(`测试图片 ${testFile} 不存在，跳过性能测试`);
        return;
      }

      const imageUri = `file://${imagePath}`;
      const startTime = Date.now();
      const result = await ocrService.recognizeFromImageUri(imageUri);
      const totalTime = Date.now() - startTime;

      expect(result.processingTime).toBeLessThan(10000); // 处理时间应少于10秒
      expect(totalTime).toBeLessThan(12000); // 总时间应少于12秒

      console.log(`性能测试 - OCR处理时间: ${result.processingTime}ms, 总时间: ${totalTime}ms`);
    });

    it('应该能够处理大尺寸图片', async () => {
      const testFile = 'high-quality-text.jpg';
      const imagePath = path.join(testImagesPath, testFile);
      const imageExists = await mockFS.exists(imagePath);

      if (!imageExists) {
        console.warn(`测试图片 ${testFile} 不存在，跳过大图片测试`);
        return;
      }

      const options: OCROptions = {
        maxImageSize: 4096, // 4K图片
        quality: 0.9,
      };

      const imageUri = `file://${imagePath}`;
      const result = await ocrService.recognizeFromImageUri(imageUri, options);

      expect(result.confidence).toBeGreaterThanOrEqual(0.5);
      expect(result.processingTime).toBeLessThan(15000); // 大图片处理时间稍长但应控制在15秒内

      console.log(`大图片处理 - 置信度: ${result.confidence}, 处理时间: ${result.processingTime}ms`);
    });
  });

  describe('特殊场景测试', () => {
    it('应该能够处理微信截图优化', async () => {
      const testFile = 'wechat-screenshot.jpg';
      const imagePath = path.join(testImagesPath, testFile);
      const imageExists = await mockFS.exists(imagePath);

      if (!imageExists) {
        console.warn(`测试图片 ${testFile} 不存在，跳过微信截图测试`);
        return;
      }

      const options: OCROptions = {
        optimizeForWeChatScreenshot: true,
      };

      const imageUri = `file://${imagePath}`;
      const result = await ocrService.recognizeFromImageUri(imageUri, options);

      expect(result.confidence).toBeGreaterThanOrEqual(0.6);
      expect(result.text).toBeDefined();
      expect(result.text.length).toBeGreaterThan(0);

      console.log(`微信截图优化 - 置信度: ${result.confidence}, 文本: ${result.text.substring(0, 50)}...`);
    });

    it('应该能够处理手写文本', async () => {
      const testFile = 'handwritten-text.jpg';
      const imagePath = path.join(testImagesPath, testFile);
      const imageExists = await mockFS.exists(imagePath);

      if (!imageExists) {
        console.warn(`测试图片 ${testFile} 不存在，跳过手写文本测试`);
        return;
      }

      const imageUri = `file://${imagePath}`;
      const result = await ocrService.recognizeFromImageUri(imageUri);

      // 手写文本识别要求较低，但应该有基本识别能力
      expect(result.confidence).toBeGreaterThanOrEqual(0.3);
      expect(result.text).toBeDefined();

      console.log(`手写文本识别 - 置信度: ${result.confidence}, 文本: ${result.text.substring(0, 50)}...`);
    });
  });

  describe('错误处理测试', () => {
    it('应该能够处理无效图片路径', async () => {
      const invalidUri = 'file:///invalid/path/image.jpg';

      try {
        const result = await ocrService.recognizeFromImageUri(invalidUri);
        // 如果没有抛出错误，应该返回降级结果
        expect(result.confidence).toBe(0);
        expect(result.engine).toBe('tesseract_fallback');
      } catch (error) {
        // 抛出错误也是可接受的
        expect(error).toBeDefined();
      }
    });

    it('应该能够处理空图片', async () => {
      const emptyUri = '';

      try {
        const result = await ocrService.recognizeFromImageUri(emptyUri);
        expect(result.confidence).toBe(0);
        expect(result.engine).toBe('tesseract_fallback');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('综合准确率测试', () => {
    it('整体OCR准确率应达到MVP要求', async () => {
      const allTestFiles = [
        'basic-chinese-text.jpg',
        'mixed-content.jpg',
        'government-notice.jpg',
        'high-quality-text.jpg',
      ];

      const testResults: OCRResult[] = [];

      for (const filename of allTestFiles) {
        const imagePath = path.join(testImagesPath, filename);
        const imageExists = await mockFS.exists(imagePath);

        if (imageExists) {
          try {
            const imageUri = `file://${imagePath}`;
            const result = await ocrService.recognizeFromImageUri(imageUri);
            testResults.push(result);
          } catch (error) {
            console.warn(`测试文件 ${filename} 处理失败:`, error);
          }
        }
      }

      if (testResults.length === 0) {
        console.warn('没有可用的测试图片，跳过综合准确率测试');
        return;
      }

      // 计算整体准确率
      const averageConfidence = testResults.reduce((sum, result) => sum + result.confidence, 0) / testResults.length;
      const highAccuracyCount = testResults.filter(result => result.confidence >= 0.7).length;
      const highAccuracyRate = highAccuracyCount / testResults.length;

      // MVP要求：平均准确率60%，高准确率(>70%)占比40%
      expect(averageConfidence).toBeGreaterThanOrEqual(0.6);
      expect(highAccuracyRate).toBeGreaterThanOrEqual(0.4);

      console.log(`综合测试结果 - 平均准确率: ${(averageConfidence * 100).toFixed(1)}%, 高准确率占比: ${(highAccuracyRate * 100).toFixed(1)}%`);

      // 生成详细报告
      const report = OCRAccuracyReporter.generateReport(testResults);
      console.log('OCR准确率详细报告:', report);
    });
  });
});

/**
 * OCR准确率报告生成器
 */
export class OCRAccuracyReporter {
  static generateReport(testResults: OCRResult[]): {
    averageAccuracy: number;
    highAccuracyRate: number;
    engineDistribution: Record<string, number>;
    averageProcessingTime: number;
    recommendations: string[];
  } {
    if (testResults.length === 0) {
      return {
        averageAccuracy: 0,
        highAccuracyRate: 0,
        engineDistribution: {},
        averageProcessingTime: 0,
        recommendations: ['没有测试数据'],
      };
    }

    const averageAccuracy = testResults.reduce((sum, result) => sum + result.confidence, 0) / testResults.length;
    const highAccuracyCount = testResults.filter(result => result.confidence >= 0.7).length;
    const highAccuracyRate = highAccuracyCount / testResults.length;

    // 引擎使用分布
    const engineDistribution: Record<string, number> = {};
    testResults.forEach(result => {
      engineDistribution[result.engine] = (engineDistribution[result.engine] || 0) + 1;
    });

    const averageProcessingTime = testResults.reduce((sum, result) => sum + result.processingTime, 0) / testResults.length;

    // 生成建议
    const recommendations: string[] = [];
    if (averageAccuracy < 0.6) {
      recommendations.push('平均准确率低于60%，建议优化OCR算法或图片预处理');
    }
    if (highAccuracyRate < 0.4) {
      recommendations.push('高准确率样本占比低于40%，建议改进图片质量或OCR引擎');
    }
    if (averageProcessingTime > 5000) {
      recommendations.push('平均处理时间超过5秒，建议优化性能');
    }

    return {
      averageAccuracy,
      highAccuracyRate,
      engineDistribution,
      averageProcessingTime,
      recommendations,
    };
  }
}
