import { analyzeText } from '../AIBridge';

describe('AIBridge Enhanced Rule Engine', () => {
  test('应该识别会议通知意图和时间信息', async () => {
    const text = '请各位同志参加明天上午9点在三楼会议室召开的工作推进会';
    const result = await analyzeText(text);

    expect(result.intent).toBe('会议通知');
    expect(result.entities.location).toBe('三楼'); // 修正：现在优先识别楼层
    expect(result.processedBy).toBe('rule_engine');
    expect(result.confidence).toBeGreaterThan(0.1);
    expect(result.matchedRules).toBeDefined();
    expect(result.matchedRules!.length).toBeGreaterThan(0);
  });

  test('应该识别任务分配和角色信息', async () => {
    const text = '请办公室牵头负责此项工作，财政局协办，各部门配合';
    const result = await analyzeText(text);

    expect(result.intent).toBe('任务分配');
    expect(result.entities.role).toBe('主办');
    expect(result.entities.department).toBe('办公室');
    expect(result.processedBy).toBe('rule_engine');
    expect(result.confidence).toBeGreaterThan(0.1);
  });

  test('应该识别紧急程度', async () => {
    const text = '请立即安排相关人员参加紧急会议';
    const result = await analyzeText(text);

    expect(result.entities.urgency).toBeDefined();
    expect(['从速', '紧急', '加急'].includes(result.entities.urgency!)).toBe(true);
    expect(result.confidence).toBeGreaterThan(0.1);
  });

  test('应该使用chrono-node或基础方法识别时间', async () => {
    const text = '会议定于2024年12月15日下午3点举行';
    const result = await analyzeText(text);

    // 应该识别出日期（可能通过chrono或基础方法）
    expect(result.entities.date).toBeDefined();
    expect(result.matchedRules?.some(rule =>
      rule.includes('chrono时间识别') || rule.includes('基础时间识别')
    )).toBe(true);
  });

  test('应该使用jieba分词提高识别准确性', async () => {
    const text = '张主任将主持明天的党委会';
    const result = await analyzeText(text);

    expect(result.matchedRules?.some(rule => rule.includes('jieba'))).toBe(true);
    // 应该能识别出职位和可能的人名
  });

  test('应该处理联系方式', async () => {
    const text = '如有问题请联系：13812345678';
    const result = await analyzeText(text);

    expect(result.entities.contact).toBe('13812345678');
    expect(result.matchedRules?.some(rule => rule.includes('联系方式识别'))).toBe(true);
  });

  test('应该优雅处理错误并回退到基础规则', async () => {
    // 测试异常处理
    const text = '简单测试文本';
    const result = await analyzeText(text);

    expect(result.processedBy).toBe('rule_engine');
    expect(result.confidence).toBeGreaterThanOrEqual(0.1);
    expect(result.confidence).toBeLessThanOrEqual(0.8);
  });

  test('应该识别督查督办类型文档', async () => {
    const text = '请对上次会议决定事项进行督办检查，并上报落实情况';
    const result = await analyzeText(text);

    expect(result.intent).toBe('督查督办');
    expect(result.entities.role).toBe('督办');
  });

  test('应该识别请示报告类型文档', async () => {
    const text = '现就此事向领导请示，请批示';
    const result = await analyzeText(text);

    expect(result.intent).toBe('请示报告');
    expect(result.entities.role).toBe('报告');
  });
});
