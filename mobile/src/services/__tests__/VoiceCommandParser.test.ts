/**
 * 语音命令解析器测试
 * 验证各种语音输入场景下的解析准确性
 */

import { VoiceCommandParser } from '../VoiceCommandParser';

describe('VoiceCommandParser', () => {
  let parser: VoiceCommandParser;

  beforeEach(() => {
    parser = new VoiceCommandParser();
  });

  describe('任务类型识别', () => {
    it('应该正确识别任务命令', () => {
      const testCases = [
        '明天下午3点完成报告撰写',
        '安排起草文件任务给张主任',
        '周五前处理完这个工作',
        '准备材料给领导审核',
      ];

      testCases.forEach(text => {
        const result = parser.parseVoiceText(text);
        expect(result.type).toBe('task');
        expect(result.confidence).toBeGreaterThan(0.5);
      });
    });

    it('应该正确识别会议命令', () => {
      const testCases = [
        '明天上午9点开会讨论项目',
        '安排周一的工作会议',
        '参加视频会议在会议室203',
        '组织专题座谈会',
      ];

      testCases.forEach(text => {
        const result = parser.parseVoiceText(text);
        expect(result.type).toBe('meeting');
        expect(result.confidence).toBeGreaterThan(0.5);
      });
    });

    it('应该正确识别提醒命令', () => {
      const testCases = [
        '提醒我明天交材料',
        '别忘了下周的汇报',
        '记住要准备PPT',
      ];

      testCases.forEach(text => {
        const result = parser.parseVoiceText(text);
        expect(result.type).toBe('reminder');
        expect(result.confidence).toBeGreaterThan(0.4);
      });
    });

    it('应该正确识别查询命令', () => {
      const testCases = [
        '查看今天的安排',
        '什么时候开会',
        '怎么处理这个问题',
      ];

      testCases.forEach(text => {
        const result = parser.parseVoiceText(text);
        expect(result.type).toBe('query');
        expect(result.confidence).toBeGreaterThan(0.4);
      });
    });
  });

  describe('实体提取测试', () => {
    it('应该正确提取时间信息', () => {
      const testCases = [
        { text: '明天下午3点开会', expectedTime: '下午', expectedDate: '明天' },
        { text: '周五上午9:30分汇报', expectedTime: '9:30', expectedDate: '周五' },
        { text: '12月15日晚上开会', expectedTime: '晚上', expectedDate: '12月15日' },
      ];

      testCases.forEach(({ text, expectedTime, expectedDate }) => {
        const result = parser.parseVoiceText(text);
        if (expectedTime) {
          expect(result.entities.time).toContain(expectedTime);
        }
        if (expectedDate) {
          expect(result.entities.date).toBe(expectedDate);
        }
      });
    });

    it('应该正确提取人员信息', () => {
      const testCases = [
        { text: '通知张主任参加会议', expectedPersons: ['张主任'] },
        { text: '和李局长、王科长讨论', expectedPersons: ['李局长', '王科长'] },
        { text: '请刘书记审核材料', expectedPersons: ['刘书记'] },
      ];

      testCases.forEach(({ text, expectedPersons }) => {
        const result = parser.parseVoiceText(text);
        expect(result.entities.person).toBeDefined();
        expectedPersons.forEach(person => {
          expect(result.entities.person).toContain(person);
        });
      });
    });

    it('应该正确提取地点信息', () => {
      const testCases = [
        { text: '在会议室205开会', expectedLocation: '会议室205' },
        { text: '去办公大厅处理', expectedLocation: '办公大厅' },
        { text: '到局长办公室汇报', expectedLocation: '局长办公室' },
      ];

      testCases.forEach(({ text, expectedLocation }) => {
        const result = parser.parseVoiceText(text);
        expect(result.entities.location).toContain(expectedLocation);
      });
    });

    it('应该正确提取优先级信息', () => {
      const testCases = [
        { text: '紧急处理这个文件', expectedPriority: 'high' },
        { text: '不急，有时间做就行', expectedPriority: 'low' },
        { text: '正常安排任务', expectedPriority: 'medium' },
      ];

      testCases.forEach(({ text, expectedPriority }) => {
        const result = parser.parseVoiceText(text);
        expect(result.entities.priority).toBe(expectedPriority);
      });
    });
  });

  describe('置信度计算测试', () => {
    it('清晰的命令应该有高置信度', () => {
      const result = parser.parseVoiceText('明天下午3点在会议室101和张主任开会讨论项目');
      expect(result.confidence).toBeGreaterThan(0.7);
      expect(result.type).toBe('meeting');
    });

    it('模糊的命令应该有较低置信度', () => {
      const result = parser.parseVoiceText('那个东西明天弄一下');
      expect(result.confidence).toBeLessThan(0.5);
    });

    it('包含多个关键词的命令应该有高置信度', () => {
      const result = parser.parseVoiceText('紧急任务：明天上午完成重要文件撰写，交给李局长审核');
      expect(result.confidence).toBeGreaterThan(0.8);
      expect(result.type).toBe('task');
    });
  });

  describe('体制内特殊用语测试', () => {
    it('应该识别体制内常用动词', () => {
      const testCases = [
        '汇报工作进展',
        '起草文件材料',
        '审核报告内容',
        '办理相关手续',
      ];

      testCases.forEach(text => {
        const result = parser.parseVoiceText(text);
        expect(result.type).toBe('task');
        expect(result.entities.action).toBeDefined();
      });
    });

    it('应该识别体制内职务称谓', () => {
      const result = parser.parseVoiceText('通知陈书记、李局长、王处长参加会议');
      expect(result.entities.person).toEqual(
        expect.arrayContaining(['陈书记', '李局长', '王处长'])
      );
    });
  });

  describe('结果格式化测试', () => {
    it('应该生成可读的格式化结果', () => {
      const result = parser.parseVoiceText('明天下午3点和张主任在会议室开会');
      const formatted = parser.formatResult(result);

      expect(formatted).toContain('会议');
      expect(formatted).toContain('明天');
      expect(formatted).toContain('下午');
      expect(formatted).toContain('张主任');
      expect(formatted).toContain('会议室');
    });

    it('应该显示置信度信息', () => {
      const result = parser.parseVoiceText('明天开会');
      const formatted = parser.formatResult(result);

      expect(formatted).toMatch(/置信度.*\d+%/);
    });
  });

  describe('边界情况测试', () => {
    it('应该处理空文本', () => {
      const result = parser.parseVoiceText('');
      expect(result.type).toBe('unknown');
      expect(result.confidence).toBe(0);
    });

    it('应该处理纯噪音文本', () => {
      const result = parser.parseVoiceText('啊啊啊嗯嗯嗯');
      expect(result.type).toBe('unknown');
      expect(result.confidence).toBeLessThan(0.3);
    });

    it('应该处理超长文本', () => {
      const longText = '明天'.repeat(100) + '开会';
      const result = parser.parseVoiceText(longText);
      expect(result.type).toBe('meeting');
      expect(result.text.length).toBeLessThan(longText.length); // 应该被清理
    });
  });
});
