import errorHandlingService, { ErrorType, ErrorSeverity, ErrorCategory } from '../ErrorHandlingService';

// Mock dependencies
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

jest.mock('react-native', () => ({
  Alert: {
    alert: jest.fn(),
  },
  Platform: {
    OS: 'ios',
  },
}));

jest.mock('../WeChatAPIService', () => ({
  default: {
    syncMessages: jest.fn(),
    getBindingStatus: jest.fn(),

  },
}));

jest.mock('../JPushService', () => ({
  default: {
    init: jest.fn(),
    getRegistrationId: jest.fn(),
  },
}));

describe('ErrorHandlingService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('错误处理', () => {
    it('应该正确处理网络超时错误', async () => {
      const networkTimeoutError = new Error('Network timeout');
      networkTimeoutError.message = 'Request timeout';

      const result = await errorHandlingService.handleError(networkTimeoutError);

      // 可重试错误在第一次调用时返回success: true (进入重试逻辑)
      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(1);
    });

    it('应该正确处理网络连接错误', async () => {
      const networkError = new Error('Network request failed');
      networkError.message = 'Network error';

      const result = await errorHandlingService.handleError(networkError);

      // 可重试错误在第一次调用时返回success: true (进入重试逻辑)
      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(1);
    });

    it('应该正确处理Token过期错误', async () => {
      const tokenError = new Error('Token expired');
      tokenError.message = 'Access token expired';

      const result = await errorHandlingService.handleError(tokenError);

      // 可重试错误在第一次调用时返回success: true (进入重试逻辑)
      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(1);
    });

    it('应该正确处理用户未绑定错误', async () => {
      const bindingError = new Error('User not bound') as Error & { code: string };
      bindingError.code = 'USER_NOT_BOUND';

      const result = await errorHandlingService.handleError(bindingError);

      expect(result.success).toBe(false);
      expect(result.error?.type).toBe(ErrorType.USER_NOT_BOUND);
      expect(result.error?.severity).toBe(ErrorSeverity.INFO);
      expect(result.error?.category).toBe(ErrorCategory.BINDING);
      expect(result.error?.shouldRetry).toBe(false);
      expect(result.error?.userMessage).toContain('您还未绑定微信');
    });

    it('应该正确处理无效绑定令牌错误', async () => {
      const invalidTokenError = new Error('Invalid binding token');
      invalidTokenError.message = 'invalid token provided';

      const result = await errorHandlingService.handleError(invalidTokenError);

      expect(result.success).toBe(false);
      expect(result.error?.type).toBe(ErrorType.INVALID_BINDING_TOKEN);
      expect(result.error?.severity).toBe(ErrorSeverity.ERROR);
      expect(result.error?.category).toBe(ErrorCategory.BINDING);
      expect(result.error?.shouldRetry).toBe(false);
      expect(result.error?.userMessage).toContain('绑定令牌无效');
    });

    it('应该正确处理服务器错误', async () => {
      const serverError = new Error('Server error') as Error & { status: number };
      serverError.status = 500;

      const result = await errorHandlingService.handleError(serverError);

      // 可重试错误在第一次调用时返回success: true (进入重试逻辑)
      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(1);
    });

    it('应该正确处理媒体下载失败错误', async () => {
      const mediaError = new Error('Media download failed');
      mediaError.message = 'download failed';

      const result = await errorHandlingService.handleError(mediaError);

      // 可重试错误在第一次调用时返回success: true (进入重试逻辑)
      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(1);
    });

    it('应该正确处理推送服务不可用错误', async () => {
      const pushError = new Error('Push service unavailable');
      pushError.message = 'push unavailable';

      const result = await errorHandlingService.handleError(pushError);

      // 可重试错误在第一次调用时返回success: true (进入重试逻辑)
      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(1);
    });

    it('应该正确处理未知错误', async () => {
      const unknownError = new Error('Unknown error');

      const result = await errorHandlingService.handleError(unknownError);

      expect(result.success).toBe(false);
      expect(result.error?.type).toBe(ErrorType.UNKNOWN_ERROR);
      expect(result.error?.severity).toBe(ErrorSeverity.ERROR);
      expect(result.error?.category).toBe(ErrorCategory.UNKNOWN);
      expect(result.error?.shouldRetry).toBe(false);
      expect(result.error?.userMessage).toContain('操作失败');
    });
  });

  describe('错误日志记录', () => {
    it('应该记录错误到AsyncStorage', async () => {
      const AsyncStorage = require('@react-native-async-storage/async-storage');
      AsyncStorage.setItem.mockResolvedValue(undefined);

      const error = new Error('Test error');
      await errorHandlingService.handleError(error);

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'error_log',
        expect.any(String)
      );
    });

    it('应该在控制台输出错误信息', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const error = new Error('Test error');
      await errorHandlingService.handleError(error);

      expect(consoleSpy).toHaveBeenCalledWith(
        '[ErrorHandling]',
        expect.objectContaining({
          type: ErrorType.UNKNOWN_ERROR,
          message: 'Test error',
        })
      );

      consoleSpy.mockRestore();
    });
  });

  describe('用户友好提示', () => {
    it('应该为警告级别错误显示Alert', async () => {
      const { Alert } = require('react-native');

      const warningError = new Error('Network timeout');
      warningError.message = 'Request timeout';

      await errorHandlingService.handleError(warningError);

      expect(Alert.alert).toHaveBeenCalledWith(
        '提示',
        expect.stringContaining('网络连接超时'),
        [{ text: '确定' }]
      );
    });

    it('应该为错误级别错误显示带重试按钮的Alert', async () => {
      const { Alert } = require('react-native');

      const networkError = new Error('Network error');
      networkError.message = 'Network request failed';

      await errorHandlingService.handleError(networkError);

      expect(Alert.alert).toHaveBeenCalledWith(
        '错误',
        expect.stringContaining('网络连接失败'),
        [
          { text: '确定' },
          { text: '重试', onPress: expect.any(Function) },
        ]
      );
    });

    it('应该为信息级别错误只在控制台输出', async () => {
      const consoleSpy = jest.spyOn(console, 'info').mockImplementation(() => {});

      const infoError = new Error('User not bound') as Error & { code: string };
      infoError.code = 'USER_NOT_BOUND';

      await errorHandlingService.handleError(infoError);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('您还未绑定微信')
      );

      consoleSpy.mockRestore();
    });
  });

  describe('错误回调注册', () => {
    it('应该支持注册错误回调', async () => {
      const callback = jest.fn();

      errorHandlingService.registerErrorCallback(ErrorType.NETWORK_ERROR, callback);

      // 触发网络错误
      const networkError = new Error('Network error');
      networkError.message = 'Network request failed';

      await errorHandlingService.handleError(networkError);

      // 等待异步处理完成
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(callback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: ErrorType.NETWORK_ERROR,
        })
      );
    });
  });

  describe('恢复策略', () => {
    it('应该为不可重试错误提供恢复策略', async () => {
      const unknownError = new Error('Unknown error');

      const result = await errorHandlingService.handleError(unknownError);

      expect(result.success).toBe(false);
      expect(result.recoveryAction).toBe('contact_support');
    });

    it('应该为用户未绑定提供跳转绑定的恢复策略', async () => {
      const bindingError = new Error('User not bound') as Error & { code: string };
      bindingError.code = 'USER_NOT_BOUND';

      const result = await errorHandlingService.handleError(bindingError);

      expect(result.success).toBe(false);
      expect(result.recoveryAction).toBe('redirect_to_binding');
    });

    it('应该为无效绑定令牌提供重新生成令牌的恢复策略', async () => {
      const invalidTokenError = new Error('Invalid binding token');
      invalidTokenError.message = 'invalid token provided';

      const result = await errorHandlingService.handleError(invalidTokenError);

      expect(result.success).toBe(false);
      expect(result.recoveryAction).toBe('regenerate_binding_token');
    });
  });
});
