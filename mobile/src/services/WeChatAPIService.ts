/**
 * 企业微信API服务
 * 封装企业微信相关API调用，支持消息获取、用户信息等功能
 * 已适配新的零存储架构和签名验证机制
 */



import { weChatConfig } from '../config/appConfig';
import SignatureService from './SignatureService';

// 新的API消息类型定义（基于零存储架构）
export interface WeChatMessage {
  id: number; // 服务端消息ID
  message_type: 'text' | 'image' | 'voice' | 'video' | 'file' | 'event' | string;
  wechat_message_id: string; // 企业微信消息ID
  media_id?: string; // 媒体文件ID（如有）
  media_id_expires_at?: string; // 媒体文件过期时间
  metadata: {
    timestamp: number;
    from_user: string;
    to_user: string;
    file_name?: string;
    file_size?: number;
    [key: string]: any;
  };
  created_at: string;

  // 兼容旧接口的字段
  type?: 'text' | 'image' | 'file' | 'voice' | 'video' | 'location' | 'link' | 'miniprogram';
  content?: string;
  sender?: {
    id: string;
    name: string;
    avatar?: string;
  };
  timestamp?: number;
  chatId?: string;
  chatName?: string;
  isGroup?: boolean;
}

// 新的API响应格式
export interface WeChatAPIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  details?: {
    user_uuid?: string;
    device_id?: string;
    [key: string]: any;
  };
  // 分页信息
  pagination?: {
    has_more: boolean;
    next_since_id?: number;
    total?: number;
  };
}

// 同步消息响应
export interface SyncMessagesResponse {
  messages: WeChatMessage[];
  has_more: boolean;
  next_since_id: number;
}

// 设备注册响应
export interface DeviceRegistrationResponse {
  device_id: string;
  registered_at: string;
}

// 访问令牌响应


// 导入统一的设备管理器
import { userDeviceManager } from '../utils/UserDeviceManager';

class WeChatAPIService {
  private baseURL: string;
  private timeout: number;

  constructor() {
    this.baseURL = weChatConfig.API_BASE_URL || 'https://wechat.api.gongzhimall.com';
    this.timeout = weChatConfig.api?.timeout || 30000;
  }

  /**
   * 带超时的fetch请求
   */
  private async fetchWithTimeout(url: string, options: any, timeoutMs?: number): Promise<Response> {
    const controller = new AbortController();
    const timeout = timeoutMs || this.timeout;
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });
      clearTimeout(timeoutId);
      return response;
    } catch (err: unknown) {
      clearTimeout(timeoutId);
      if ((err as Error).name === 'AbortError') {
        throw new Error('Request timeout');
      }
      throw err;
    }
  }

  /**
   * 带重试机制的API调用
   */
  private async callAPIWithRetry<T>(
    endpoint: string,
    method: 'GET' | 'POST' = 'POST',
    requestBody?: any,
    queryParams?: Record<string, string>,
    maxRetries: number = 3
  ): Promise<WeChatAPIResponse<T>> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[WeChatAPI] 尝试第 ${attempt} 次调用 ${endpoint}`);
        return await this.callAPI<T>(endpoint, method, requestBody, queryParams);
      } catch (error) {
        lastError = error as Error;
        console.warn(`[WeChatAPI] 第 ${attempt} 次调用失败:`, error);

        // 如果是网络错误且还有重试机会，则等待后重试
        if (attempt < maxRetries && this.isRetryableError(error as Error)) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // 指数退避，最大5秒
          console.log(`[WeChatAPI] 等待 ${delay}ms 后重试...`);
          await this.delay(delay);
          continue;
        }

        // 最后一次尝试失败或不可重试的错误
        break;
      }
    }

    // 所有重试都失败了
    return this.handleFinalError(lastError!, endpoint, maxRetries);
  }

  /**
   * 判断错误是否可重试
   */
  private isRetryableError(error: Error): boolean {
    const retryableErrors = [
      'Request timeout',
      'Network request failed',
      'Connection refused',
      'HTTP 5', // 5xx服务器错误
      'HTTP 408', // 请求超时
      'HTTP 429', // 请求过多
    ];

    return retryableErrors.some(pattern => error.message.includes(pattern));
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 处理最终错误
   */
  private handleFinalError<T>(error: Error, endpoint: string, attempts: number): WeChatAPIResponse<T> {
    console.error(`[WeChatAPI] ${endpoint} 经过 ${attempts} 次尝试后最终失败:`, error);

    // 增强错误分类和用户友好提示
    let errorCode = 'API_ERROR';
    let userMessage = '网络请求失败，请检查网络连接';
    let shouldRetry = false;
    let suggestedAction = '请稍后重试';

    if (error.name === 'AbortError' || error.message === 'Request timeout') {
      errorCode = 'TIMEOUT_ERROR';
      userMessage = '请求超时，请检查网络连接';
      shouldRetry = true;
      suggestedAction = '请检查网络后重试';
    } else if (error.message.includes('HTTP 401')) {
      errorCode = 'AUTH_ERROR';
      userMessage = '身份验证失败，请重新绑定微信';
      shouldRetry = false;
      suggestedAction = '请重新绑定微信账号';
    } else if (error.message.includes('HTTP 403')) {
      errorCode = 'PERMISSION_ERROR';
      userMessage = '权限不足，请联系管理员';
      shouldRetry = false;
      suggestedAction = '请联系技术支持';
    } else if (error.message.includes('HTTP 404')) {
      errorCode = 'NOT_FOUND_ERROR';
      userMessage = '服务暂时不可用';
      shouldRetry = true;
      suggestedAction = '请稍后重试或联系技术支持';
    } else if (error.message.includes('HTTP 5')) {
      errorCode = 'SERVER_ERROR';
      userMessage = '服务器繁忙，请稍后重试';
      shouldRetry = true;
      suggestedAction = '服务器正在维护，请稍后重试';
    } else if (error.message.includes('Network request failed')) {
      errorCode = 'NETWORK_ERROR';
      userMessage = '网络连接失败，请检查网络设置';
      shouldRetry = true;
      suggestedAction = '请检查网络连接后重试';
    } else if (error.message.includes('Token过期') || error.message.includes('TIMESTAMP_EXPIRED')) {
      errorCode = 'TOKEN_EXPIRED';
      userMessage = 'Token已过期，请重新绑定';
      shouldRetry = false;
      suggestedAction = '请重新绑定微信账号';
    } else if (error.message.includes('签名校验失败') || error.message.includes('INVALID_SIGNATURE')) {
      errorCode = 'SIGNATURE_ERROR';
      userMessage = '安全验证失败，请重新绑定';
      shouldRetry = false;
      suggestedAction = '请重新绑定微信账号';
    }

    return {
      success: false,
      error: userMessage,
      details: {
        code: errorCode,
        originalError: error.message,
        attempts: attempts,
        shouldRetry: shouldRetry,
        suggestedAction: suggestedAction,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * 通用API调用方法（使用签名验证）
   */
  private async callAPI<T>(
    endpoint: string,
    method: 'GET' | 'POST' = 'POST',
    requestBody?: any,
    queryParams?: Record<string, string>
  ): Promise<WeChatAPIResponse<T>> {
    try {
      let url = `${this.baseURL}${endpoint}`;

      // 添加查询参数
      if (queryParams && Object.keys(queryParams).length > 0) {
        const searchParams = new URLSearchParams(queryParams);
        url += `?${searchParams.toString()}`;
      }

      // 使用SignatureService生成带签名的请求选项
      const options = SignatureService.generateFetchOptions(method, requestBody, {}, url);

      const response = await this.fetchWithTimeout(url, options);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('[WeChatAPI] API调用失败:', endpoint, error);
      throw error; // 让上层的重试机制处理
    }
  }

  /**
   * 注册设备
   */
  async registerDevice(params?: {
    userUuid?: string;
    deviceId?: string;
    deviceName?: string;
    platform?: string;
    appVersion?: string;
    pushToken?: string;
  }): Promise<WeChatAPIResponse<DeviceRegistrationResponse>> {
    console.log('[WeChatAPI] 注册设备:', params);

    const userUuid = params?.userUuid || await userDeviceManager.getUserUuid();
    const deviceId = params?.deviceId || await userDeviceManager.getDeviceId();

    const requestBody = {
      user_uuid: userUuid,
      device_id: deviceId,
      device_name: params?.deviceName || 'Mobile Device',
      platform: params?.platform || 'mobile',
      app_version: params?.appVersion || '1.0.0',
      push_token: params?.pushToken,
    };

    return this.callAPIWithRetry<DeviceRegistrationResponse>('/api/sync/register-device', 'POST', requestBody);
  }

  /**
   * 增量同步消息
   */
  async syncMessages(params: {
    userUuid?: string;
    deviceId?: string;
    sinceId?: number;
    limit?: number;
  }): Promise<WeChatAPIResponse<SyncMessagesResponse>> {
    console.log('[WeChatAPI] 增量同步消息:', params);

    const userUuid = params.userUuid || await userDeviceManager.getUserUuid();
    const deviceId = params.deviceId || await userDeviceManager.getDeviceId();

    const queryParams = {
      user_uuid: userUuid,
      device_id: deviceId,
      since_id: (params.sinceId || 0).toString(),
      limit: (params.limit || 100).toString(),
    };

    return this.callAPIWithRetry<SyncMessagesResponse>('/api/sync/messages', 'GET', undefined, queryParams);
  }

  /**
   * 获取认证信息（用户UUID和设备ID）
   */
  private async getAuthInfo(): Promise<{ user_uuid: string; device_id: string }> {
    const user_uuid = await userDeviceManager.getUserUuid();
    const device_id = await userDeviceManager.getDeviceId();
    return { user_uuid, device_id };
  }

  /**
   * 确认消息同步
   */
  async ackSync(data: { lastSyncedId: number }): Promise<any> {
    const { user_uuid, device_id } = await this.getAuthInfo();
    const body = {
      user_uuid,
      device_id,
      last_synced_id: data.lastSyncedId,
    };
    return this.callAPIWithRetry<any>('/api/sync/ack', 'POST', body);
  }



  /**
   * 检查绑定状态
   */
  async checkBindingStatus(userUuid?: string): Promise<WeChatAPIResponse<{
    user_uuid: string;
    external_userid: string;
    binding_status: 'active' | 'inactive' | 'pending' | 'expired';
    binding_time: string;
  }>> {
    console.log('[WeChatAPI] 检查绑定状态');

    const uuid = userUuid || await userDeviceManager.getUserUuid();
    const queryParams = {
      user_uuid: uuid,
    };

    return this.callAPIWithRetry('/api/bind/status', 'GET', undefined, queryParams);
  }

  /**
   * 兼容旧接口：获取用户消息列表
   */
  async getMessages(params: {
    userUuid: string;
    chatId?: string;
    messageType?: string[];
    startTime?: number;
    endTime?: number;
    limit?: number;
    cursor?: string;
  }): Promise<WeChatAPIResponse<WeChatMessage[]>> {
    console.log('[WeChatAPI] 获取消息列表（兼容接口）:', params);

    // 转换为新的增量同步接口
    const syncResult = await this.syncMessages({
      userUuid: params.userUuid,
      sinceId: 0, // 从头开始获取
      limit: params.limit || 50,
    });

    if (!syncResult.success || !syncResult.data) {
      return {
        success: false,
        error: syncResult.error || '获取消息失败',
      };
    }

    // 转换消息格式以保持兼容性
    const messages = syncResult.data.messages.map(msg => ({
      ...msg,
      // 兼容字段
      type: this.mapMessageType(msg.message_type),
      content: '', // 内容需要通过其他方式获取
      sender: {
        id: msg.metadata.from_user,
        name: msg.metadata.from_user,
      },
      timestamp: msg.metadata.timestamp,
      chatId: 'default',
      chatName: 'WeChat',
      isGroup: false,
    }));

    return {
      success: true,
      data: messages,
      pagination: {
        has_more: syncResult.data.has_more,
        next_since_id: syncResult.data.next_since_id,
      },
    };
  }

  /**
   * 增量获取新消息（用于同步）
   */
  async fetchNewMessages(lastSyncTime: number): Promise<WeChatMessage[]> {
    try {
      console.log('[WeChatAPI] 增量获取新消息，上次同步时间:', new Date(lastSyncTime).toLocaleString());

      // 使用新的增量同步接口
      const syncResult = await this.syncMessages({
        sinceId: 0, // 这里需要转换时间戳到消息ID
        limit: 100,
      });

      if (!syncResult.success || !syncResult.data) {
        console.warn('[WeChatAPI] 获取消息失败:', syncResult.error);
        return [];
      }

      const messages = syncResult.data.messages;
      console.log('[WeChatAPI] 成功获取', messages.length, '条新消息');

      // 按时间排序（旧的在前）
      messages.sort((a, b) => a.metadata.timestamp - b.metadata.timestamp);

      return messages;
    } catch (error) {
      console.error('[WeChatAPI] fetchNewMessages失败:', error);
      return [];
    }
  }

  /**
   * 映射消息类型
   */
  private mapMessageType(messageType: string): 'text' | 'image' | 'file' | 'voice' | 'video' | 'location' | 'link' | 'miniprogram' {
    switch (messageType) {
      case 'text':
        return 'text';
      case 'image':
        return 'image';
      case 'voice':
        return 'voice';
      case 'video':
        return 'video';
      case 'file':
        return 'file';
      case 'event':
        return 'text'; // 事件消息作为文本处理
      default:
        return 'text';
    }
  }

  /**
   * 检查API连接状态
   */
  async checkConnection(): Promise<boolean> {
    try {
      const response = await this.fetchWithTimeout(`${this.baseURL}/api/health`, {
        method: 'GET',
      }, 5000);

      return response.ok;
    } catch (error) {
      console.warn('[WeChatAPI] 连接检查失败:', error);
      return false;
    }
  }

  /**
   * 获取用户UUID和设备ID（工具方法）
   */
  async getUserUuid(): Promise<string> {
    return userDeviceManager.getUserUuid();
  }

  async getDeviceId(): Promise<string> {
    return userDeviceManager.getDeviceId();
  }
}

export default new WeChatAPIService();
