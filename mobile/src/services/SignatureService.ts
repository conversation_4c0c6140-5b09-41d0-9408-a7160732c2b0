/**
 * 签名验证服务
 * 实现HMAC-SHA256签名算法，生成API调用所需的认证头部
 */

import CryptoJS from 'crypto-js';


export interface AuthHeaders {
  'X-Token': string;
  'X-Timestamp': string;
  'X-Signature': string;
  'Content-Type': string;
}

export class SignatureService {
  private static readonly TOKEN_SECRET = process.env.TOKEN_SECRET || 'gongzhimall-app-2025'; // 从环境变量读取
  private static readonly MOBILE_APP_TOKEN = process.env.WECHAT_API_TOKEN || 'gongzhimall-app-2025'; // 从环境变量读取

  /**
   * 生成API调用的认证头部
   * @param requestBody 请求体对象
   * @returns 包含签名的认证头部
   */
  static generateAuthHeaders(requestBody: any = {}, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'POST', url: string = ''): AuthHeaders {
    const timestamp = Date.now().toString();
    const token = this.MOBILE_APP_TOKEN;

    let signatureData = '';
    let bodyString = '';

    if (method === 'GET') {
      const queryParams = this.getQueryParams(url);
      bodyString = JSON.stringify(queryParams);
    } else {
      bodyString = JSON.stringify(requestBody);
    }

    signatureData = `${bodyString}${timestamp}${token}`;
    const signature = CryptoJS.HmacSHA256(signatureData, this.TOKEN_SECRET).toString();

    if (__DEV__) {
      console.log('🔐 [SignatureService] 签名生成详情:', {
        timestamp,
        token,
        bodyLength: bodyString.length,
        signatureData: signatureData.substring(0, 100) + '...',
        signature: signature.substring(0, 16) + '...',
      });
    }

    return {
      'X-Token': token,
      'X-Timestamp': timestamp,
      'X-Signature': signature,
      'Content-Type': 'application/json',
    };
  }

  /**
   * 验证签名是否有效（用于调试）
   * @param signature 待验证的签名
   * @param requestBody 请求体
   * @param timestamp 时间戳
   * @param token 令牌
   * @returns 是否验证通过
   */
  static verifySignature(
    signature: string,
    requestBody: any,
    timestamp: string,
    token: string
  ): boolean {
    try {
      const bodyString = JSON.stringify(requestBody);
      const signatureData = `${bodyString}${timestamp}${token}`;
      const expectedSignature = CryptoJS.HmacSHA256(signatureData, this.TOKEN_SECRET).toString();

      return signature === expectedSignature;
    } catch (error) {
      console.error('[SignatureService] 签名验证失败:', error);
      return false;
    }
  }

  /**
   * 检查时间戳是否有效（5分钟内）
   * @param timestamp 时间戳字符串
   * @returns 是否有效
   */
  static isTimestampValid(timestamp: string): boolean {
    try {
      const timestampNum = parseInt(timestamp, 10);
      const now = Date.now();
      const diff = Math.abs(now - timestampNum);
      const maxDiff = 5 * 60 * 1000; // 5分钟

      return diff <= maxDiff;
    } catch (error) {
      console.error('[SignatureService] 时间戳验证失败:', error);
      return false;
    }
  }

  /**
   * 生成带签名的fetch选项
   * @param method HTTP方法
   * @param requestBody 请求体
   * @param additionalHeaders 额外的头部
   * @returns fetch选项对象
   */
  static generateFetchOptions(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'POST',
    requestBody?: any,
    additionalHeaders?: Record<string, string>,
    url: string = ''
  ): RequestInit {
    const authHeaders = this.generateAuthHeaders(requestBody, method, url);

    return {
      method,
      headers: {
        ...authHeaders,
        ...additionalHeaders,
      },
      body: requestBody && method !== 'GET' ? JSON.stringify(requestBody) : undefined,
    };
  }

  /**
   * 带签名的fetch请求封装
   * @param url 请求URL
   * @param method HTTP方法
   * @param requestBody 请求体
   * @param additionalHeaders 额外头部
   * @returns fetch Promise
   */
  static async signedFetch(
    url: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'POST',
    requestBody?: any,
    additionalHeaders?: Record<string, string>
  ): Promise<Response> {
    const options = this.generateFetchOptions(method, requestBody, additionalHeaders, url);

    if (__DEV__) {
      console.log(`🌐 [SignatureService] ${method} ${url}`,
        {
          hasBody: !!requestBody,
          headers: Object.keys(options.headers || {}),
        }
      );
    }

    return fetch(url, options);
  }

  /**
   * 从URL中提取查询参数
   * @param url URL字符串
   * @returns 包含查询参数的对象
   */
  private static getQueryParams(url: string): Record<string, string> {
    const queryParams: Record<string, string> = {};
    const queryString = url.split('?')[1];
    if (queryString) {
      const pairs = queryString.split('&');
      for (const pair of pairs) {
        const [key, value] = pair.split('=');
        queryParams[decodeURIComponent(key)] = decodeURIComponent(value || '');
      }
    }
    return queryParams;
  }
}

export default SignatureService;
