import React, { useRef, useEffect, useMemo } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import {
  Camera,
  useCameraDevices,
} from 'react-native-vision-camera';
import Animated, {
  useAnimatedProps,
} from 'react-native-reanimated';
import { useCameraStore } from '../stores/cameraStore';
import CameraControls from '../components/InputPanel/CameraControls';
import CameraHeader from '../components/InputPanel/CameraHeader';
import CameraZoomHandler from '../components/InputPanel/CameraZoomHandler';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RootStackParamList } from '../../App';
import { useIsFocused } from '@react-navigation/native';

const ReanimatedCamera = Animated.createAnimatedComponent(Camera);
Animated.addWhitelistedNativeProps({
  zoom: true,
});

const CameraScreen = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const {
    hideCamera,
    zoom,
    setZoomRange,
    sceneMode,
    setSceneMode,
    startSession,
    activeSession,
  } = useCameraStore();

  const capturedMedia = useCameraStore(state => state.capturedMedia);

  const cameraRef = useRef<Camera>(null);
  const devices = useCameraDevices();
  const isInitialMount = useRef(true);
  const isFocused = useIsFocused();

  useEffect(() => {
    startSession();
    return () => {
      hideCamera();
    };
  }, [startSession, hideCamera]);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }
    if (!activeSession) {
      setTimeout(() => navigation.goBack(), 100);
    }
  }, [activeSession, navigation]);
  const latestPhotoUri = useMemo(() =>
    capturedMedia.length > 0 ? capturedMedia[capturedMedia.length - 1].uri : null,
    [capturedMedia]
  );

  const device = useMemo(() => {
    const backDevices = devices.filter(d => d.position === 'back');
    if (backDevices.length === 0) {return undefined;}
    return backDevices.sort(
      (a, b) => (b.physicalDevices?.length ?? 0) - (a.physicalDevices?.length ?? 0),
    )[0];
  }, [devices]);

  useEffect(() => {
    if (device) {
      const hasUltraWide =
        device.physicalDevices.includes('ultra-wide-angle-camera');
      const minZoom = hasUltraWide ? 0.5 : device.minZoom;
      setZoomRange({ min: minZoom, max: device.maxZoom });
    }
  }, [device, setZoomRange]);

  useEffect(() => {
    if (device) {
      const { physicalDevices, neutralZoom } = device;
      const hasUltraWide = physicalDevices.includes('ultra-wide-angle-camera');
      const hasTelephoto = physicalDevices.includes('telephoto-camera');
      let newMode: '超广角' | '广角' | '长焦' = '广角';
      if (hasUltraWide && zoom < neutralZoom) {
        newMode = '超广角';
      } else if (hasTelephoto && zoom > neutralZoom) {
        newMode = '长焦';
      }
      if (newMode !== sceneMode) {
        setSceneMode(newMode);
      }
    }
  }, [device, zoom, sceneMode, setSceneMode]);

  const animatedProps = useAnimatedProps(() => ({ zoom }), [zoom]);

  const handlePreview = () => {
    if (capturedMedia.length > 0) {
      navigation.navigate('CaptureReviewScreen');
    } else {
      console.log('No media captured, cannot navigate to preview.');
    }
  };

  if (!device) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>正在加载摄像头...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ReanimatedCamera
        ref={cameraRef}
        style={StyleSheet.absoluteFill}
        device={device}
        isActive={isFocused}
        photo={true}
        video={false}
        animatedProps={animatedProps}
      />
      <CameraHeader
        onClose={() => navigation.goBack()}
        onPreview={handlePreview}
        mediaCount={capturedMedia.length}
        latestPhotoUri={latestPhotoUri}
      />
      <View style={styles.flexOne} />
      <CameraControls cameraRef={cameraRef} />
      <CameraZoomHandler />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  flexOne: {
    flex: 1,
  },
  errorText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 100,
  },
});

export default CameraScreen;
