import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
  Share,
  ScrollView,
  FlatList,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { WebView } from 'react-native-webview';
import RNFS from 'react-native-fs';
import Pdf from 'react-native-pdf';
import XLSX from 'xlsx';
import { FileErrorHandler} from '../utils/fileErrorHandler';
import { SvgXml } from 'react-native-svg';
import { defaultTheme } from '../styles/theme';

// 警告图标SVG
const warningIconXml = `
<svg fill="#000000" height="800px" width="800px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
	 viewBox="0 0 494.942 494.942" xml:space="preserve">
<g>
	<g>
		<path d="M494.942,247.471C494.942,110.982,383.959,0,247.471,0C110.982,0,0,110.982,0,247.471s110.982,247.471,247.471,247.471
			C383.959,494.942,494.942,383.959,494.942,247.471z M247.471,444.942C138.74,444.942,50,356.202,50,247.471
			S138.74,50,247.471,50s197.471,88.74,197.471,197.471S356.202,444.942,247.471,444.942z"/>
		<path d="M247.471,121.514c-13.807,0-25,11.193-25,25v150c0,13.807,11.193,25,25,25s25-11.193,25-25v-150
			C272.471,132.706,261.278,121.514,247.471,121.514z"/>
		<path d="M247.471,331.453c-13.807,0-25,11.193-25,25s11.193,25,25,25s25-11.193,25-25S261.278,331.453,247.471,331.453z"/>
	</g>
</g>
</svg>
`;

type FilePreviewRouteProp = {
  FilePreviewScreen: {
    fileData: {
      uri: string;
      name: string;
      type: string;
      size: number;
    };
  };
};

type Nav = {
  goBack: () => void;
};

const FilePreviewScreen = () => {
  const route = useRoute<RouteProp<FilePreviewRouteProp, 'FilePreviewScreen'>>();
  const { fileData } = route.params;
  const navigation = useNavigation<Nav>();
  const webViewRef = useRef<WebView>(null);

  const [htmlContent, setHtmlContent] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [excelData, setExcelData] = useState<{ [key: string]: any[][] } | null>(null);
  const [sheetNames, setSheetNames] = useState<string[]>([]);
  const [activeSheetName, setActiveSheetName] = useState<string | null>(null);
  const [fileType, setFileType] = useState<string>('other');
  const [wordHtmlContent, setWordHtmlContent] = useState<string | null>(null);

  // 🔧 新增：详细的加载进度状态
  const [loadingProgress, setLoadingProgress] = useState({
    stage: '准备中',
    progress: 0,
    message: '正在准备文档预览...',
  });

  // 统一错误处理函数
  const handleError = useCallback((error: any, context: string) => {
    console.error(`[FilePreview] ${context}:`, error);
    const errorMessage = error?.message || error || '未知错误';
    setError(`${context}: ${errorMessage}`);

    // 对于严重错误，也使用FileErrorHandler显示用户友好的提示
    if (context.includes('加载失败') || context.includes('解析失败')) {
      FileErrorHandler.handleError(error, context);
    }
  }, []);


  useEffect(() => {
    console.log('[FilePreview] 🚀 FilePreviewScreen启动，文件信息:', {
      name: fileData.name,
      type: fileData.type,
      size: fileData.size,
      uri: fileData.uri.substring(0, 50) + '...',
    });

    const type = getFileType(fileData.name, fileData.type);
    console.log('[FilePreview] 🎯 检测到的文件类型:', type);
    setFileType(type);

    // 🔧 修复：为PDF文件设置加载完成状态
    if (type === 'pdf') {
      console.log('[PDF] PDF文件无需额外加载，直接设置为完成状态');
      setIsLoading(false);
    } else if (type === 'excel') {
      // 修复Excel预览功能
      const loadExcelFile = async () => {
        try {
          console.log('[Excel] 开始加载Excel文件:', fileData.name);

          // 🔧 更新加载进度
          setLoadingProgress({
            stage: '读取文件',
            progress: 10,
            message: '正在读取Excel文件...',
          });

          // 处理文件路径
          let filePath = fileData.uri;
          if (filePath.startsWith('file://')) {
            filePath = filePath.replace('file://', '');
          }

          console.log('[Excel] 读取文件路径:', filePath);

          // 读取文件为base64
          setLoadingProgress({
            stage: '读取文件',
            progress: 30,
            message: '正在读取Excel文件内容...',
          });

          const base64Data = await RNFS.readFile(filePath, 'base64');
          console.log('[Excel] 文件读取成功，大小:', base64Data.length);

          // 使用XLSX直接解析base64数据，避免Buffer兼容性问题
          setLoadingProgress({
            stage: '解析数据',
            progress: 50,
            message: '正在解析Excel数据结构...',
          });

          const workbook = XLSX.read(base64Data, { type: 'base64' });
          console.log('[Excel] 工作簿解析成功，工作表数量:', workbook.SheetNames.length);

          if (workbook.SheetNames.length === 0) {
            throw new Error('Excel文件中没有找到工作表');
          }

          const allSheetsData: { [key: string]: any[][] } = {};
          const validSheetNames: string[] = [];

          for (const sheetName of workbook.SheetNames) {
            const worksheet = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet, {
              header: 1,
              defval: '', // 空单元格默认值
              raw: false, // 格式化数值
            }) as any[][];

            // 过滤掉完全空白的行
            const filteredData = jsonData.filter(row =>
              row.some(cell => cell !== null && cell !== undefined && cell !== '')
            );

            if (filteredData.length > 0) {
              allSheetsData[sheetName] = filteredData;
              validSheetNames.push(sheetName);
            }
          }

          if (validSheetNames.length === 0) {
            throw new Error('当前Excel文件的所有工作表均为空');
          }

          // ✅ 恢复完整数据显示，使用FlatList虚拟化渲染
          console.log(`[Excel] 数据解析完成，共${validSheetNames.length}个有效工作表`);

          setLoadingProgress({
            stage: '完成',
            progress: 100,
            message: `Excel加载完成，共${validSheetNames.length}个工作表`,
          });

          setSheetNames(validSheetNames);
          setExcelData(allSheetsData);
          setActiveSheetName(validSheetNames[0]);
          setIsLoading(false);
          console.log('[Excel] Excel预览加载成功');

        } catch (e: any) {
          console.error('[Excel] Excel预览加载失败:', e);
          setIsLoading(false); // 🔧 修复：Excel加载失败后也要设置loading为false
          handleError(e, 'Excel文件解析失败');
        }
      };

      loadExcelFile();
    } else if (type === 'word') {
      // 🚀 Word文档预览功能 - 内联库文件
      const loadWordFile = async () => {
        try {
          console.log('[Word] 开始加载Word文档HTML模板和库文件');

          setLoadingProgress({
            stage: '准备预览器',
            progress: 20,
            message: '正在准备Word文档预览器...',
          });

          const htmlPath = Platform.OS === 'android'
            ? 'docx-viewer.html'
            : `${RNFS.MainBundlePath}/assets/docx-viewer.html`;
          const libBasePath = Platform.OS === 'android' ? 'libs/' : `${RNFS.MainBundlePath}/assets/libs/`;

          const readFile = (path: string) => Platform.OS === 'android'
            ? RNFS.readFileAssets(path, 'utf8')
            : RNFS.readFile(path, 'utf8');

          // 加载HTML模板和必需的库文件
          setLoadingProgress({
            stage: '加载组件',
            progress: 50,
            message: '正在加载Word预览组件...',
          });

          const [htmlTemplate, jszipContent, docxPreviewContent] = await Promise.all([
            readFile(htmlPath),
            readFile(`${libBasePath}jszip.min.js`),
            readFile(`${libBasePath}docx-preview.min.js`),
          ]);

          // 将库文件内联到HTML中
          setLoadingProgress({
            stage: '构建预览器',
            progress: 80,
            message: '正在构建Word文档预览器...',
          });

          let finalHtml = htmlTemplate;
          finalHtml = finalHtml.replace(
            '<script src="./libs/jszip.min.js"></script>',
            `<script>${jszipContent}</script>`
          );
          finalHtml = finalHtml.replace(
            '<script src="./libs/docx-preview.min.js"></script>',
            `<script>${docxPreviewContent}</script>`
          );

          setLoadingProgress({
            stage: '完成',
            progress: 100,
            message: 'Word文档预览器准备就绪',
          });

          setWordHtmlContent(finalHtml);
          setIsLoading(false);
          console.log('[Word] Word文档HTML模板和库文件加载成功');

        } catch (e: any) {
          console.error('[Word] Word文档预览器加载失败:', e);
          setIsLoading(false);
          handleError(e, 'Word文档预览器加载失败');
        }
      };
      loadWordFile();
    } else if (type === 'ppt' || type === 'other') {
      // 🔧 修复：为不支持的文件类型设置加载完成状态
      console.log(`[PPT/Other] 不支持或由外部处理的文件类型 (${type})，设置加载完成`);
      setIsLoading(false);
    }
  }, [fileData, handleError]);

  useEffect(() => {
    // 🔧 性能优化：只有OFD文件才需要加载复杂的WebView资源
    if (fileType !== 'ofd') {
      console.log('[Performance] 跳过OFD WebView资源加载，文件类型:', fileType);
      // 🔧 修复：不要在这里设置loading为false，因为其他文件类型可能还在加载中
      // 其他文件类型的加载状态由各自的加载逻辑控制
      return;
    }

    const loadAssetsAndConstructHtml = async () => {
      try {
        console.log('🚀 [1/4] Loading assets for WebView...');

        const htmlPath = Platform.OS === 'android' ? 'ofd-viewer.html' : `${RNFS.MainBundlePath}/assets/ofd-viewer.html`;
        const libBasePath = Platform.OS === 'android' ? 'libs/' : `${RNFS.MainBundlePath}/assets/libs/`;

        const libFileNames = [
          'jszip.min.js',
          'opentype.min.js',
          'x2js.js',
          'easyjbig2.js',
          'easyofd.js',
        ];

        const readFile = (path: string) => Platform.OS === 'android'
          ? RNFS.readFileAssets(path, 'utf8')
          : RNFS.readFile(path, 'utf8');

        const [htmlTemplate, ...jsContents] = await Promise.all([
          readFile(htmlPath),
          ...libFileNames.map(name => readFile(`${libBasePath}${name}`)),
        ]);
        console.log('✅ [2/4] All assets loaded successfully.');

        let finalHtml = htmlTemplate;
        libFileNames.forEach((fileName, index) => {
          const srcPattern = new RegExp(`<script src="\\./libs/${fileName}"></script>`);
          finalHtml = finalHtml.replace(srcPattern, `<script>${jsContents[index]}</script>`);
        });
        console.log('✅ [3/4] HTML constructed with inline scripts.');

        setHtmlContent(finalHtml);
        console.log('✅ [4/4] WebView environment is ready.');

      } catch (e: any) {
        console.error('❌ Failed to load assets for WebView', e);
        setError(`加载预览器资源失败: ${e.message}`);
      } finally {
        setIsLoading(false);
      }
    };

    loadAssetsAndConstructHtml();
  }, [fileType]);

  // This function will be called ONLY after the WebView signals it's ready.
  const sendOfdData = useCallback(async (webView: WebView) => {
    try {
      console.log('🚀 Reading file from URI:', fileData.uri);
      const filePath = fileData.uri.replace('file://', '');
      const base64 = await RNFS.readFile(filePath, 'base64');
      console.log(`✅ File read successfully, size: ${base64.length}. Injecting JS to WebView.`);

      // CRITICAL FIX: Adopt a robust event-driven pattern.
      // 1. Inject the data into a global variable on the WebView's window.
      // 2. Dispatch a custom event to notify the WebView that data is ready.
      // We wrap the base64 string in backticks to handle any special characters.
      const escapedBase64 = base64.replace(/`/g, '\\`').replace(/\\/g, '\\\\');
      const script = `
        window.OFD_DATA = \`${escapedBase64}\`;
        window.dispatchEvent(new Event('ofdDataReady'));
        true; // Required for Android
      `;

      console.log(' injecting script into WebView...');
      // For debugging, you can log the script, but be aware it contains the full base64 string.
      // console.log('INJECTING SCRIPT:', script);

      webView.injectJavaScript(script);

    } catch (err: any) {
      console.error('❌ Error sending OFD data:', err);
      setError('发送数据失败: ' + err.message);
    }
  }, [fileData]);

  // 🚀 发送Word文档数据到WebView - 采用OFD成功模式
  const sendWordData = useCallback(async (webView: WebView) => {
    try {
      console.log('🚀 Reading Word file from URI:', fileData.uri);
      const filePath = fileData.uri.replace('file://', '');
      const base64 = await RNFS.readFile(filePath, 'base64');
      console.log(`✅ Word file read successfully, size: ${base64.length}. Injecting JS to WebView.`);

      // 🔧 采用OFD成功模式：injectJavaScript + 自定义事件
      // 1. 将数据注入到全局变量
      // 2. 触发自定义事件通知WebView数据就绪
      const escapedBase64 = base64.replace(/`/g, '\\`').replace(/\\/g, '\\\\');
      const script = `
        window.WORD_DATA = \`${escapedBase64}\`;
        window.WORD_FILENAME = '${fileData.name}';
        window.dispatchEvent(new Event('wordDataReady'));
        true; // Required for Android
      `;

      console.log('📄 Injecting Word script into WebView...');
      webView.injectJavaScript(script);

    } catch (err: any) {
      console.error('❌ Error sending Word data:', err);
      setError('发送Word文档失败: ' + err.message);
    }
  }, [fileData]);

  // PPT预览已改为使用原生预览器，不再需要WebView数据发送

  // 🔧 扩展WebView消息处理，支持OFD、Word和PPT文档
  const onWebViewMessage = (event: any) => {
    const data = JSON.parse(event.nativeEvent.data);

    switch (data.type) {
      case 'ready':
        // WebView就绪 - 统一处理OFD、Word和PPT
        console.log('✅ WebView is ready, file type:', fileType);
        if (webViewRef.current) {
          if (fileType === 'ofd') {
            console.log('📄 Sending OFD data...');
            sendOfdData(webViewRef.current);
          } else if (fileType === 'word') {
            console.log('📄 Sending Word data...');
            sendWordData(webViewRef.current);
          }
        }
        break;
      case 'RENDER_SUCCESS':
        console.log('✅ Word文档渲染成功:', data.fileName);
        break;
      case 'RENDER_ERROR':
        console.error('❌ Word文档渲染失败:', data.error);
        setError('Word文档渲染失败: ' + data.error);
        break;

      case 'error':
        console.error('❌ OFD 渲染失败:', data.message);
        setError(`渲染失败: ${data.message}`);
        break;
    }
  };

  const getFileType = (fileName: string, mimeType: string) => {
    const ext = fileName.toLowerCase().split('.').pop();

    console.log('[FilePreview] 🔍 详细文件类型检测:', {
      fileName,
      mimeType,
      ext,
      mimeTypeIncludes: {
        ofd: mimeType.includes('ofd'),
        pdf: mimeType.includes('pdf'),
        spreadsheet: mimeType.includes('spreadsheet'),
        excel: mimeType.includes('excel'),
        presentation: mimeType.includes('presentation'),
        powerpoint: mimeType.includes('powerpoint'),
      },
    });

    // OFD文档
    if (mimeType.includes('ofd') || ext === 'ofd') {
      console.log('[FilePreview] ✅ 识别为OFD文档');
      return 'ofd';
    }

    // PDF文档
    if (mimeType.includes('pdf') || ext === 'pdf') {
      console.log('[FilePreview] ✅ 识别为PDF文档');
      return 'pdf';
    }

    // 🔧 PowerPoint文档 - 优先检测，避免被Word误识别
    if (mimeType.includes('presentation') ||
        mimeType.includes('powerpoint') ||
        mimeType.includes('vnd.ms-powerpoint') ||
        ext === 'ppt' ||
        ext === 'pptx' ||
        ext === 'pptm' ||
        ext === 'dps') {
      console.log('[FilePreview] 识别为PowerPoint文档');
      return 'ppt';
    }

    // Excel文档
    if (mimeType.includes('spreadsheet') ||
        mimeType.includes('excel') ||
        mimeType.includes('vnd.ms-excel') ||
        ext === 'xls' ||
        ext === 'xlsx' ||
        ext === 'xlsm' ||
        ext === 'et') {
      console.log('[FilePreview] ✅ 识别为Excel文档');
      return 'excel';
    }

    // Word文档 - 使用更精确的检测条件
    if (mimeType.includes('wordprocessingml') ||
        mimeType.includes('msword') ||
        mimeType.includes('vnd.ms-word') ||
        (mimeType.includes('document') && !mimeType.includes('presentation')) ||
        ext === 'doc' ||
        ext === 'docx' ||
        ext === 'docm' ||
        ext === 'wps') {
      console.log('[FilePreview] 识别为Word文档');
      return 'word';
    }

    console.log('[FilePreview] ❌ 未能识别文件类型，归类为其他类型');
    console.log('[FilePreview] 🔍 文件类型识别失败详情:', {
      fileName,
      mimeType,
      ext,
      '可能的问题': '文件扩展名或MIME类型不在支持列表中',
    });
    return 'other';
  };

  // 🔧 优化：友好的加载进度指示器
  const renderLoadingIndicator = () => (
    <View style={styles.loadingContainer}>
      <View style={styles.loadingContent}>
        <ActivityIndicator size="large" color="#1890ff" />
        <Text style={styles.loadingTitle}>{loadingProgress.stage}</Text>
        <Text style={styles.loadingMessage}>{loadingProgress.message}</Text>

        {/* 进度条 */}
        <View style={styles.progressBarContainer}>
          <View style={[styles.progressBar, { width: `${loadingProgress.progress}%` }]} />
        </View>
        <Text style={styles.progressText}>{Math.round(loadingProgress.progress)}%</Text>

        {/* 友好提示 */}
        <Text style={styles.loadingTip}>
          {fileType === 'excel' && '正在解析表格数据，请稍候...'}
          {fileType === 'word' && '正在准备文档预览器...'}
          {fileType === 'ofd' && '正在加载OFD预览组件...'}
          {fileType === 'pdf' && '正在准备PDF阅读器...'}
          {fileType === 'ppt' && '正在启动演示文稿预览...'}
          {fileType === 'other' && '正在识别文档格式...'}
        </Text>
      </View>
    </View>
  );

  // 渲染Excel数据
  const renderExcel = () => {
    if (!excelData || !activeSheetName) {
      return <Text style={styles.errorText}>没有可显示的Excel数据</Text>;
    }

    const currentSheetData = excelData[activeSheetName];
    if (!currentSheetData) {
      return <Text style={styles.errorText}>无法加载工作表：{activeSheetName}</Text>;
    }

    // 动态计算列宽
    const columnWidths = currentSheetData[0]?.map((_, colIndex) => {
      let maxWidth = 0;
      for (let rowIndex = 0; rowIndex < currentSheetData.length; rowIndex++) {
        const cellValue = String(currentSheetData[rowIndex][colIndex] || '');
        // 更准确地估算中文和英文混合的宽度
        const cellLength = cellValue.replace(/[^\x00-\xff]/g, 'aa').length; // 一个中文字符约等于两个英文字符
        const cellWidth = cellLength * 6 + 20; // 字体大小为12，每个字符约6px，加上padding
        if (cellWidth > maxWidth) {
          maxWidth = cellWidth;
        }
      }
      return Math.min(Math.max(maxWidth, 80), 300); // 最小宽度80，最大宽度300
    });

    const renderRow = ({ item, index: rowIndex }: { item: any[], index: number }) => (
      <View style={[styles.excelRow, rowIndex === 0 && styles.excelHeaderRow]}>
        {item.map((cell, cellIndex) => (
          <View key={cellIndex} style={[
            styles.excelCell,
            { width: columnWidths[cellIndex] },
            rowIndex === 0 && styles.excelHeaderCell,
          ]}>
            <Text style={styles.excelCellText} selectable>{String(cell ?? '')}</Text>
          </View>
        ))}
      </View>
    );

    const renderSheetTabs = () => {
      if (sheetNames.length <= 1) {return null;}

      return (
        <View style={styles.sheetTabsContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {sheetNames.map(name => (
              <TouchableOpacity
                key={name}
                style={[styles.sheetTab, name === activeSheetName && styles.activeSheetTab]}
                onPress={() => setActiveSheetName(name)}
              >
                <Text style={[styles.sheetTabText, name === activeSheetName && styles.activeSheetTabText]}>{name}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      );
    };

    return (
      <View style={{ flex: 1 }}>
        {renderSheetTabs()}
        <ScrollView horizontal contentContainerStyle={styles.excelOuterScrollView}>
          <FlatList
            data={currentSheetData}
            renderItem={renderRow}
            keyExtractor={(item, index) => `row-${index}`}
            stickyHeaderIndices={[0]}
            contentContainerStyle={styles.excelContainer}
            // 性能优化
            initialNumToRender={20}
            windowSize={10}
          />
        </ScrollView>
      </View>
    );
  };

  const renderContent = () => {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#fff" />
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
            <Text style={styles.backButtonText}>返回</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle} numberOfLines={1}>{fileData.name}</Text>
          <TouchableOpacity onPress={() => Share.share({ url: fileData.uri, title: fileData.name })} style={{ position: 'absolute', right: 15 }}>
            <Text style={{ color: '#007AFF', fontSize: 16 }}>分享</Text>
          </TouchableOpacity>
        </View>

        {isLoading ? (
          renderLoadingIndicator()
        ) : error ? (
          <View style={styles.centered}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        ) : fileType === 'ofd' && htmlContent ? (
          <WebView
            ref={webViewRef}
            source={{
              html: htmlContent,
              baseUrl: Platform.OS === 'android' ? 'file:///android_asset/' : RNFS.MainBundlePath,
            }}
            originWhitelist={['*']}
            allowFileAccess={true}
            javaScriptEnabled={true}
            onMessage={onWebViewMessage}
            bounces={false}
            startInLoadingState={true}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={true}
            scrollEnabled={true}
            style={styles.webview}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.warn('WebView error: ', nativeEvent);
              setError(`WebView加载失败: ${nativeEvent.description}`);
            }}
          />
        ) : fileType === 'pdf' ? (
          <Pdf
            source={{ uri: fileData.uri }}
            style={{ flex: 1, margin: 10, borderRadius: 8 }}
            onError={e => {
              let msg = '';
              if (typeof e === 'string') {msg = e;}
              else if (e && typeof e === 'object' && 'message' in e) {msg = (e as any).message;}
              else {msg = JSON.stringify(e);}
              handleError(msg, 'PDF预览失败');
            }}
          />
        ) : fileType === 'excel' ? (
          renderExcel()
        ) : fileType === 'word' && wordHtmlContent ? (
          <WebView
            ref={webViewRef}
            source={{
              html: wordHtmlContent,
              baseUrl: Platform.OS === 'android' ? 'file:///android_asset/' : RNFS.MainBundlePath,
            }}
            originWhitelist={['*']}
            allowFileAccess={true}
            javaScriptEnabled={true}
            onMessage={onWebViewMessage}
            bounces={false}
            startInLoadingState={true}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={true}
            scrollEnabled={true}
            style={styles.webview}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.warn('Word WebView error: ', nativeEvent);
              setError(`Word预览失败: ${nativeEvent.description}`);
            }}
          />
        ) : (
          <FallbackPreviewer
            fileData={fileData}
            errorMessage={
              error ? error : '暂不支持该类型文件的预览。'
            }
          />
        )}
      </SafeAreaView>
    );
  };

  const FallbackPreviewer = ({
    fileData,
    errorMessage,
  }: {
    fileData: { uri: string; name: string };
    errorMessage: string;
  }) => {
    const openWithThirdPartyApp = useCallback(async () => {
      try {
        const FileViewer = (await import('react-native-file-viewer')).default;
        await FileViewer.open(fileData.uri, {
          showOpenWithDialog: true,
          showAppsSuggestions: true,
        });
      } catch (e) {
        FileErrorHandler.handleError(e, '无法找到合适的应用打开此文件');
      }
    }, [fileData]);

    return (
      <View style={styles.fallbackContainer}>
        <View style={styles.fallbackCard}>
          <SvgXml
            xml={warningIconXml}
            width={60}
            height={60}
            fill={defaultTheme.colors.warning}
          />
          <Text style={styles.fallbackTitle}>无法预览文件</Text>
          <Text style={styles.fallbackMessage}>{errorMessage}</Text>
          <TouchableOpacity
            style={styles.fallbackButton}
            onPress={openWithThirdPartyApp}>
            <Text style={styles.fallbackButtonText}>用其他应用打开</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return renderContent();
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
    paddingHorizontal: 15,
    // 🔧 SafeAreaView已经处理了安全区域，这里不需要额外的paddingTop
  },
  backButton: {
    position: 'absolute',
    left: 15,
  },
  backButtonText: {
    color: '#007AFF',
    fontSize: 16,
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    maxWidth: '70%',
  },
  webview: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    marginHorizontal: 10,
    marginVertical: 10,
    borderRadius: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
    textAlign: 'center',
  },
  // Excel预览样式
  excelContainer: {
    paddingBottom: 20,
  },
  excelOuterScrollView: {
    flexGrow: 1,
  },
  excelRow: {
    flexDirection: 'row',
  },
  excelHeaderRow: {
    backgroundColor: '#f0f0f0',
  },
  excelCell: {
    borderWidth: 1,
    borderColor: '#eee',
    paddingHorizontal: 8,
    paddingVertical: 10,
    justifyContent: 'center',
    minHeight: 36,
  },
  excelHeaderCell: {
    backgroundColor: '#f0f0f0',
  },
  excelCellText: {
    fontSize: 12,
    color: '#333',
  },
  sheetName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },

  // PPT预览相关样式
  pptContainer: {
    // This style is not used in the current renderPPT function,
    // but it's included in the new_code for completeness.
  },

  // 工作表切换样式
  sheetTabsContainer: {
    paddingVertical: 8,
    paddingHorizontal: 10,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
  },
  sheetTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#e9ecef',
  },
  activeSheetTab: {
    backgroundColor: '#007AFF',
  },
  sheetTabText: {
    color: '#495057',
    fontWeight: '500',
  },
  activeSheetTabText: {
    color: '#fff',
    fontWeight: 'bold',
  },

  // 🔧 新增：加载进度指示器样式
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 20,
  },
  loadingContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    minWidth: 280,
  },
  loadingTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  loadingMessage: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  progressBarContainer: {
    width: '100%',
    height: 6,
    backgroundColor: '#e9ecef',
    borderRadius: 3,
    marginBottom: 12,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#1890ff',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#1890ff',
    fontWeight: '600',
    marginBottom: 16,
  },
  loadingTip: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 16,
    marginBottom: 8,
  },
  fileSize: {
    fontSize: 12,
    color: '#999',
    marginBottom: 16,
  },
  previewNote: {
    backgroundColor: '#f5f5f5',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  previewNoteText: {
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
    lineHeight: 20,
  },
  openExternalButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 6,
  },
  openExternalButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  infoText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  actionButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 10,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  // Fallback Previewer Styles
  fallbackContainer: {
    flex: 1,
    backgroundColor: defaultTheme.colors.backgroundPrimary,
    justifyContent: 'center',
    alignItems: 'center',
    padding: defaultTheme.spacing.lg,
  },
  fallbackCard: {
    backgroundColor: defaultTheme.colors.backgroundSecondary,
    borderRadius: defaultTheme.borderRadius.large,
    padding: defaultTheme.spacing.xxl,
    alignItems: 'center',
    ...defaultTheme.shadows.medium,
    width: '100%',
    maxWidth: 400,
  },
  fallbackTitle: {
    fontSize: defaultTheme.fonts.titleMedium,
    fontWeight: defaultTheme.fontWeights.bold,
    color: defaultTheme.colors.textPrimary,
    marginTop: defaultTheme.spacing.lg,
    marginBottom: defaultTheme.spacing.sm,
  },
  fallbackMessage: {
    fontSize: defaultTheme.fonts.bodyMedium,
    color: defaultTheme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: defaultTheme.spacing.xl,
    lineHeight: 22,
  },
  fallbackButton: {
    backgroundColor: defaultTheme.colors.primary,
    paddingHorizontal: defaultTheme.spacing.xl,
    paddingVertical: defaultTheme.spacing.md,
    borderRadius: defaultTheme.borderRadius.medium,
    width: '100%',
  },
  fallbackButtonText: {
    color: defaultTheme.colors.backgroundSecondary,
    fontSize: defaultTheme.fonts.button,
    fontWeight: defaultTheme.fontWeights.medium,
    textAlign: 'center',
  },
});

export default FilePreviewScreen;
