/**
 * 隐私政策详情页面
 * 使用WebView显示原始HTML内容，提供简洁的文档阅读体验
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { WebView } from 'react-native-webview';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { defaultTheme } from '../styles/theme';
import { PrivacyPolicyManager } from '../utils/privacyPolicyManager';

const PrivacyPolicyDetailScreen = () => {
  const navigation = useNavigation();
  const theme = defaultTheme;
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  // 获取隐私政策URL
  const privacyPolicyUrl = PrivacyPolicyManager.getPrivacyPolicyUrl();

  console.log('[PrivacyPolicy] Loading URL:', privacyPolicyUrl);
  console.log('[PrivacyPolicy] Component state - loading:', loading, 'error:', error);

  return (
    <SafeAreaView style={styles(theme).container}>
      {/* Header */}
      <View style={styles(theme).header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles(theme).backButton}
          activeOpacity={0.7}
        >
          <Ionicons
            name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}
            size={24}
            color={theme.colors.textPrimary}
          />
        </TouchableOpacity>
        <Text style={styles(theme).headerTitle}>隐私政策</Text>
        <View style={styles(theme).placeholder} />
      </View>

      {/* WebView Content */}
      <View style={styles(theme).webViewContainer}>
        {loading && (
          <View style={styles(theme).loadingContainer}>
            <ActivityIndicator size="large" color="#FF8C00" />
            <Text style={styles(theme).loadingText}>正在加载隐私政策...</Text>
          </View>
        )}

        {error && (
          <View style={styles(theme).errorContainer}>
            <Text style={styles(theme).errorText}>加载失败，请检查网络连接</Text>
            <TouchableOpacity
              style={styles(theme).retryButton}
              onPress={() => {
                setError(false);
                setLoading(true);
              }}
            >
              <Text style={styles(theme).retryButtonText}>重试</Text>
            </TouchableOpacity>
          </View>
        )}

        {!error && (
          <WebView
            source={{ uri: privacyPolicyUrl }}
            style={styles(theme).webView}
            onLoadStart={() => {
              console.log('[PrivacyPolicy] WebView load start');
              setLoading(true);
            }}
            onLoadEnd={() => {
              console.log('[PrivacyPolicy] WebView load end');
              setLoading(false);
            }}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.log('[PrivacyPolicy] WebView error:', nativeEvent);
              setLoading(false);
              setError(true);
            }}
            // 🔧 修复：禁用可能触发权限请求的功能
            javaScriptEnabled={false}  // 禁用JavaScript，防止触发定位等权限请求
            domStorageEnabled={false}  // 禁用DOM存储
            geolocationEnabled={false} // 明确禁用定位功能
            allowsInlineMediaPlayback={false} // 禁用内联媒体播放
            mediaPlaybackRequiresUserAction={true} // 媒体播放需要用户操作
            startInLoadingState={true}
            // 🔧 添加权限处理回调
            onPermissionRequest={(request: any) => {
              console.log('[PrivacyPolicy] Permission request blocked:', request);
              // 拒绝所有权限请求
              return false;
            }}
          />
        )}
      </View>

    </SafeAreaView>
  );
};


const styles = (theme: typeof defaultTheme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.backgroundPrimary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.backgroundSecondary,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: theme.colors.backgroundTertiary,
    ...theme.shadows.small,
  },
  backButton: {
    padding: theme.spacing.sm,
    marginLeft: -theme.spacing.sm,
  },
  headerTitle: {
    fontSize: theme.fonts.titleSmall,
    fontWeight: theme.fontWeights.bold,
    color: theme.colors.textPrimary,
  },
  placeholder: {
    width: 40,
  },
  webViewContainer: {
    flex: 1,
    backgroundColor: theme.colors.backgroundPrimary,
  },
  webView: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.backgroundPrimary,
    zIndex: 10,
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: theme.fonts.bodyMedium,
    color: theme.colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.backgroundPrimary,
    paddingHorizontal: theme.spacing.xl,
  },
  errorText: {
    fontSize: theme.fonts.bodyLarge,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  retryButton: {
    backgroundColor: '#FF8C00',
    borderRadius: theme.borderRadius.medium,
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    ...theme.shadows.small,
  },
  retryButtonText: {
    color: theme.colors.backgroundSecondary,
    fontSize: theme.fonts.bodyMedium,
    fontWeight: theme.fontWeights.bold,
  },
});

export default PrivacyPolicyDetailScreen;
