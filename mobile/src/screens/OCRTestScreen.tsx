import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import RNFS from 'react-native-fs';
import OCRService, { OCRResult } from '../services/OCRService';

interface TestImage {
  name: string;
  path: string;
  uri: string;
  size: number;
}

interface TestResult {
  image: TestImage;
  result: OCRResult | null;
  error: string | null;
  duration: number;
}

const OCRTestScreen: React.FC = () => {
  const [testImages, setTestImages] = useState<TestImage[]>([]);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');
  const [ocrCapability, setOcrCapability] = useState<any>(null);



  useEffect(() => {
    loadTestImages();
    checkOCRCapability();
  }, []);

  /**
   * 检查OCR能力
   */
  const checkOCRCapability = async () => {
    try {
      const capability = await OCRService.checkOCRCapability();
      setOcrCapability(capability);
      console.log('OCR能力检查:', capability);
    } catch (error) {
      console.error('OCR能力检查失败:', error);
    }
  };

  /**
   * 加载测试图片文件夹
   */
  const loadTestImages = async () => {
    try {
      setIsLoading(true);

      // 测试图片文件夹路径
      const testImagesPath = `${RNFS.MainBundlePath}/assets/test-images/generated`;

      console.log('正在扫描测试图片文件夹:', testImagesPath);

      // 检查文件夹是否存在
      const exists = await RNFS.exists(testImagesPath);
      if (!exists) {
        Alert.alert('错误', '测试图片文件夹不存在');
        return;
      }

      // 读取文件夹内容
      const files = await RNFS.readDir(testImagesPath);
      console.log('找到文件:', files.length);

      // 过滤图片文件
      const imageFiles = files.filter(file => {
        const ext = file.name.toLowerCase();
        return ext.endsWith('.jpg') || ext.endsWith('.jpeg') ||
               ext.endsWith('.png') || ext.endsWith('.webp');
      });

      console.log('图片文件数量:', imageFiles.length);

      // 转换为TestImage格式
      const images: TestImage[] = imageFiles.map(file => ({
        name: file.name,
        path: file.path,
        uri: `file://${file.path}`,
        size: file.size,
      }));

      setTestImages(images);
      console.log('加载的测试图片:', images.map(img => img.name));

    } catch (error) {
      console.error('加载测试图片失败:', error);
      Alert.alert('错误', `加载测试图片失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 测试单个图片
   */
  const testSingleImage = async (image: TestImage) => {
    try {
      setCurrentTest(image.name);
      console.log(`开始测试图片: ${image.name}`);

      const startTime = Date.now();

      // 使用OCR服务识别图片
              const result = await OCRService.recognizeFromImageUri(
        image.uri,
        {
          optimizeForWeChatScreenshot: image.name.includes('wechat'),
          language: 'zh-Hans',
          quality: 0.8,
        }
      );

      const duration = Date.now() - startTime;

      console.log(`图片 ${image.name} 识别完成:`, {
        engine: result.engine,
        confidence: result.confidence,
        textLength: result.text.length,
        duration,
      });

      const testResult: TestResult = {
        image,
        result,
        error: null,
        duration,
      };

      setTestResults(prev => [...prev, testResult]);

    } catch (error) {
      console.error(`图片 ${image.name} 识别失败:`, error);

      const testResult: TestResult = {
        image,
        result: null,
        error: error instanceof Error ? error.message : String(error),
        duration: 0,
      };

      setTestResults(prev => [...prev, testResult]);
    } finally {
      setCurrentTest('');
    }
  };

  /**
   * 批量测试所有图片
   */
  const testAllImages = async () => {
    if (testImages.length === 0) {
      Alert.alert('提示', '没有找到测试图片');
      return;
    }

    setIsLoading(true);
    setTestResults([]);

    try {
      console.log(`开始批量测试 ${testImages.length} 张图片`);

      for (const image of testImages) {
        await testSingleImage(image);
        // 添加小延迟避免过快处理
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      console.log('批量测试完成');
      Alert.alert('完成', `已完成 ${testImages.length} 张图片的OCR测试`);

    } catch (error) {
      console.error('批量测试失败:', error);
      Alert.alert('错误', `批量测试失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 清除测试结果
   */
  const clearResults = () => {
    setTestResults([]);
  };

  /**
   * 渲染OCR能力信息
   */
  const renderCapabilityInfo = () => {
    if (!ocrCapability) {return null;}

    return (
      <View style={styles.capabilityContainer}>
        <Text style={styles.sectionTitle}>OCR引擎能力</Text>
        <Text style={styles.capabilityText}>
          主要引擎: {ocrCapability.primaryEngine}
        </Text>
        <Text style={styles.capabilityText}>
          可用引擎: {ocrCapability.engines.join(', ')}
        </Text>
        {ocrCapability.reason && (
          <Text style={styles.reasonText}>
            说明: {ocrCapability.reason}
          </Text>
        )}
      </View>
    );
  };

  /**
   * 渲染测试结果
   */
  const renderTestResult = (testResult: TestResult, index: number) => {
    const { image, result, error, duration } = testResult;

    return (
      <View key={index} style={styles.resultContainer}>
        <View style={styles.resultHeader}>
          <Text style={styles.imageName}>{image.name}</Text>
          <Text style={styles.imageSize}>
            {Math.round(image.size / 1024)}KB | {duration}ms
          </Text>
        </View>

        {/* 显示图片缩略图 */}
        <Image
          source={{ uri: image.uri }}
          style={styles.thumbnail}
          resizeMode="contain"
        />

        {error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>识别失败: {error}</Text>
          </View>
        ) : result ? (
          <View style={styles.successContainer}>
            <Text style={styles.engineText}>
              引擎: {result.engine} | 置信度: {(result.confidence * 100).toFixed(1)}%
            </Text>
            <Text style={styles.resultText} numberOfLines={10}>
              {result.text || '未识别到文字'}
            </Text>
            <Text style={styles.blocksText}>
              文本块数量: {result.blocks.length}
            </Text>
          </View>
        ) : null}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>OCR功能真实测试</Text>

      {renderCapabilityInfo()}

      <View style={styles.statsContainer}>
        <Text style={styles.statsText}>
          测试图片: {testImages.length} 张
        </Text>
        <Text style={styles.statsText}>
          已测试: {testResults.length} 张
        </Text>
        <Text style={styles.statsText}>
          成功率: {testResults.length > 0 ?
            ((testResults.filter(r => r.result !== null).length / testResults.length) * 100).toFixed(1) : 0}%
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={testAllImages}
          disabled={isLoading || testImages.length === 0}
        >
          <Text style={styles.buttonText}>
            {isLoading ? '测试中...' : '批量测试所有图片'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={loadTestImages}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>重新加载图片</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.warningButton]}
          onPress={clearResults}
          disabled={testResults.length === 0}
        >
          <Text style={styles.buttonText}>清除结果</Text>
        </TouchableOpacity>
      </View>

      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>
            {currentTest ? `正在测试: ${currentTest}` : '加载中...'}
          </Text>
        </View>
      )}

      {testResults.length > 0 && (
        <View style={styles.resultsSection}>
          <Text style={styles.sectionTitle}>测试结果</Text>
          {testResults.map((result, index) => renderTestResult(result, index))}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  capabilityContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  capabilityText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  reasonText: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  statsText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: '#34C759',
  },
  warningButton: {
    backgroundColor: '#FF9500',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: '#666',
  },
  resultsSection: {
    marginTop: 20,
  },
  resultContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  imageName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  imageSize: {
    fontSize: 12,
    color: '#999',
  },
  thumbnail: {
    width: '100%',
    height: 150,
    borderRadius: 8,
    marginBottom: 12,
    backgroundColor: '#f0f0f0',
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 12,
    borderRadius: 6,
  },
  errorText: {
    color: '#c62828',
    fontSize: 14,
  },
  successContainer: {
    backgroundColor: '#e8f5e8',
    padding: 12,
    borderRadius: 6,
  },
  engineText: {
    fontSize: 12,
    color: '#2e7d32',
    marginBottom: 8,
    fontWeight: '600',
  },
  resultText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 8,
  },
  blocksText: {
    fontSize: 12,
    color: '#666',
  },
});

export default OCRTestScreen;
