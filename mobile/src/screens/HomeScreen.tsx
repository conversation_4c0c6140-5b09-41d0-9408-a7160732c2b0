import React, { useMemo, useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  Platform,
  Dimensions,
  Alert,
} from 'react-native';
import { GestureHandlerRootView, GestureDetector, Gesture } from 'react-native-gesture-handler';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import Animated, {
  useSharedValue,
  withSpring,
  useAnimatedStyle,
  withTiming,
  Easing,
  runOnJS,
  useAnimatedScrollHandler,
  interpolate,
  measure,
  useAnimatedRef,
} from 'react-native-reanimated';
import ScheduleList from './ScheduleList';
import ImportantTasksContainer from '../components/ImportantTasksContainer';
import BottomActionBar from '../components/BottomActionBar/BottomActionBar';
import NotesSubmenu from '../components/BottomActionBar/NotesSubmenu';
import { generateMockSchedules } from '../utils/mockSchedules';
import { isSameDay } from 'date-fns';
import VoiceInputModal from '../components/InputPanel/VoiceInputModal';
import { analyzeText } from '../services/AIBridge';
import { RootStackParamList } from '../../App';
import { ensurePermission } from '../services/PermissionService';

// We need to define the ParamList for the navigator that this screen is a part of.
// Assuming your RootStackParamList is in App.tsx, you might want to move it to a central types file.
type HomeScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Home'
>;

const { width: screenWidth } = Dimensions.get('window');

// 滚动检测配置 - 简化逻辑，任何滚动都隐藏
const SCROLL_CONFIG = {
  THRESHOLD: 8, // 滚动触发阈值
  AUTO_SHOW_DELAY: 800, // 滚动停止后自动显示延迟
  HIDE_ANIMATION_DURATION: 200, // 隐藏动画时长
  SHOW_ANIMATION_DURATION: 250, // 显示动画时长
  INITIAL_PULL_DOWN_THRESHOLD: 15, // 初始下拉容忍阈值（避免抖动）
  INITIAL_SCROLL_UP_THRESHOLD: 5, // 初始上滑触发阈值
};

// --- Utils ---
const formatDate = (date: Date): { day: string, weekday: string, month: string, fullDate: string } => {
  // 获取星期几
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const weekday = weekdays[date.getDay()];

  // 获取月份
  const month = `${date.getMonth() + 1}月`;

  // 获取完整日期
  const fullDate = `${date.getMonth() + 1}月${date.getDate()}日`;

  const today = new Date();
  const tomorrow = new Date();
  tomorrow.setDate(today.getDate() + 1);
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);

  const checkDateIsSame = (d1: Date, d2: Date) =>
    d1.getFullYear() === d2.getFullYear() &&
    d1.getMonth() === d2.getMonth() &&
    d1.getDate() === d2.getDate();

  if (checkDateIsSame(date, today)) {
    return { day: '今天', weekday, month, fullDate };
  }
  if (checkDateIsSame(date, tomorrow)) {
    return { day: '明天', weekday, month, fullDate };
  }
  if (checkDateIsSame(date, yesterday)) {
    return { day: '昨天', weekday, month, fullDate };
  }

  return { day: fullDate, weekday, month: '', fullDate };
};

// --- Components ---

const Header: React.FC<{ currentDate: Date }> = ({ currentDate }) => {
  const { day, weekday, fullDate } = formatDate(currentDate);
  const navigation = useNavigation<HomeScreenNavigationProp>();

  const handleNavigateToChat = () => {
    navigation.navigate('Chat');
  };

  return (
    <View style={styles.headerContainer}>
      <TouchableOpacity onPress={handleNavigateToChat}>
      <Text style={styles.headerGreeting}>🐱 喵～</Text>
      </TouchableOpacity>
      <View style={styles.dateContainer}>
        <View style={styles.dateTextContainer}>
          <Text style={styles.headerDate}>
            <Text style={styles.dayText}>{day}</Text>
            <Text style={styles.weekdayText}> {weekday}</Text>
            <Text style={styles.fullDateText}> {fullDate}</Text>
          </Text>
        </View>
        <TouchableOpacity
          onPress={() => {
            ReactNativeHapticFeedback.trigger('impactLight');
            navigation.navigate('Schedule');
          }}
          activeOpacity={0.7}
        >
          <Text style={styles.moreText}>更多</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// --- Main Screen ---

const HomeScreen: React.FC = () => {
  const [dateOffset, setDateOffset] = useState(0);
  const [isVoiceModalVisible, setIsVoiceModalVisible] = useState(false);
  const slideAnim = useSharedValue(0);
  const fadeAnim = useSharedValue(1);
  const lastOffsetY = useSharedValue(0);
  const isScrolling = useSharedValue(false);
  const isPanningHorizontally = useSharedValue(false);
  const scheduleListRef = useRef<Animated.ScrollView>(null);

  // 动态高度跟踪
  const importantTasksRef = useAnimatedRef<Animated.View>();
  const bottomActionBarRef = useAnimatedRef<Animated.View>();
  const [_hasImportantTasks] = useState(true); // 追踪是否有重要任务（暂时固定为true）
  const [_importantTasksHeight, setImportantTasksHeight] = useState(112); // 默认高度
  const [_bottomActionBarHeight, setBottomActionBarHeight] = useState(88); // 默认高度

  // 滚动位置跟踪
  const currentScrollOffset = useSharedValue(0);

  // 动态topPadding状态
  const [topPadding, setTopPadding] = useState(128); // 默认为ImportantTasksContainer高度 + 16

  // 下拉刷新状态管理
  const [_isRefreshing, _setIsRefreshing] = useState(false); // 预留给未来下拉刷新功能

  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  // --- Modal Handlers ---
  const handleOpenCamera = useCallback(async () => {
    ReactNativeHapticFeedback.trigger('impactMedium');
    const hasPermission = await ensurePermission('camera');
    if (hasPermission) {
      navigation.navigate('CameraScreen');
    }
  }, [navigation]);

  const handleOpenVoiceModal = useCallback(() => {
    ReactNativeHapticFeedback.trigger('impactMedium');
    setIsVoiceModalVisible(true);
  }, []);

  const handleCloseVoiceModal = useCallback(async (text: string) => {
    setIsVoiceModalVisible(false);
    if (text && text.trim().length > 0) {
      try {
        const analysisResult = await analyzeText(text.trim());
        Alert.alert(
          'AI语音分析结果',
          `意图: ${analysisResult.intent}\n置信度: ${analysisResult.confidence}\n实体: ${JSON.stringify(analysisResult.entities, null, 2)}`,
          [{ text: '确认' }]
        );
        // TODO: 后续处理分析结果，例如创建任务
      } catch (error) {
        console.error('AI voice analysis failed in HomeScreen:', error);
        Alert.alert('语音分析失败', '处理您的语音指令时出现错误。');
      }
    }
  }, []);

  // 使用 useRef 来管理定时器
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 动态检测组件高度
  const measureComponentHeights = useCallback(() => {
    'worklet';

    try {
      if (importantTasksRef.current) {
        const measurements = measure(importantTasksRef);
        if (measurements) {
          runOnJS(setImportantTasksHeight)(measurements.height);
        }
      }
    } catch (error) {
      console.log('Failed to measure ImportantTasksContainer:', error);
    }

    try {
      if (bottomActionBarRef.current) {
        const measurements = measure(bottomActionBarRef);
        if (measurements) {
          runOnJS(setBottomActionBarHeight)(measurements.height);
        }
      }
    } catch (error) {
      console.log('Failed to measure BottomActionBar:', error);
    }
  }, [importantTasksRef, bottomActionBarRef]);

  useEffect(() => {
    fadeAnim.value = withTiming(1, { duration: 500, easing: Easing.ease });

    // 延迟测量以确保组件渲染完成
    const timer = setTimeout(measureComponentHeights, 100);

    // 组件卸载时清理定时器
    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
        hideTimeoutRef.current = null;
      }
      clearTimeout(timer);
    };
  }, [fadeAnim, measureComponentHeights]);

  // 清除定时器的工具函数
  const clearHideTimeout = () => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = null;
    }
  };

  // 设置自动显示定时器
  const setAutoShowTimer = () => {
    hideTimeoutRef.current = setTimeout(() => {
      fadeAnim.value = withTiming(1, {
        duration: SCROLL_CONFIG.SHOW_ANIMATION_DURATION,
        easing: Easing.out(Easing.quad),
      });
    }, SCROLL_CONFIG.AUTO_SHOW_DELAY);
  };

  // 简化的滚动处理器 - 直观的初始滚动处理 + 慢速拖动支持
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      const offsetY = event.contentOffset.y;
      const scrollDiff = offsetY - lastOffsetY.value;

      // 更新当前滚动位置跟踪
      currentScrollOffset.value = offsetY;

      // 简化逻辑：
      // 1. 如果在很顶部（offsetY <= 15）且向下拉（scrollDiff < 0），不隐藏
      // 2. 如果向上滑超过5px，隐藏
      // 3. 如果不在顶部且滚动超过阈值，隐藏

      if (offsetY <= SCROLL_CONFIG.INITIAL_PULL_DOWN_THRESHOLD && scrollDiff < 0) {
        // 在顶部向下拉，不隐藏，保持显示
        if (fadeAnim.value < 0.9) {
          runOnJS(clearHideTimeout)();
          fadeAnim.value = withTiming(1, {
            duration: SCROLL_CONFIG.SHOW_ANIMATION_DURATION,
            easing: Easing.out(Easing.quad),
          });
        }
      } else if (offsetY <= SCROLL_CONFIG.INITIAL_PULL_DOWN_THRESHOLD && scrollDiff > SCROLL_CONFIG.INITIAL_SCROLL_UP_THRESHOLD) {
        // 在顶部向上滑超过阈值，隐藏
        if (fadeAnim.value > 0.5) {
          runOnJS(clearHideTimeout)();
          fadeAnim.value = withTiming(0, {
            duration: SCROLL_CONFIG.HIDE_ANIMATION_DURATION,
            easing: Easing.in(Easing.quad),
          });
        }
      } else if (offsetY > SCROLL_CONFIG.INITIAL_PULL_DOWN_THRESHOLD && Math.abs(scrollDiff) > SCROLL_CONFIG.THRESHOLD) {
        // 不在顶部，任何滚动都隐藏
        if (fadeAnim.value > 0.5) {
          runOnJS(clearHideTimeout)();
          fadeAnim.value = withTiming(0, {
            duration: SCROLL_CONFIG.HIDE_ANIMATION_DURATION,
            easing: Easing.in(Easing.quad),
          });
        }
      }

      lastOffsetY.value = offsetY;
    },
    onBeginDrag: (event) => {
      if (isPanningHorizontally.value) {
        return;
      }
      const offsetY = event.contentOffset.y;
      isScrolling.value = true;
      runOnJS(clearHideTimeout)();

      // 如果不在顶部区域，立即隐藏（支持慢速拖动）
      if (offsetY > SCROLL_CONFIG.INITIAL_PULL_DOWN_THRESHOLD && fadeAnim.value > 0.5) {
        fadeAnim.value = withTiming(0, {
          duration: SCROLL_CONFIG.HIDE_ANIMATION_DURATION,
          easing: Easing.in(Easing.quad),
        });
      }
      // 如果在顶部区域且有一定偏移（表示要开始上滑），也隐藏
      else if (offsetY > SCROLL_CONFIG.INITIAL_SCROLL_UP_THRESHOLD &&
               offsetY <= SCROLL_CONFIG.INITIAL_PULL_DOWN_THRESHOLD &&
               fadeAnim.value > 0.5) {
        fadeAnim.value = withTiming(0, {
          duration: SCROLL_CONFIG.HIDE_ANIMATION_DURATION,
          easing: Easing.in(Easing.quad),
        });
      }

      // 更新起始位置
      lastOffsetY.value = offsetY;
    },
    onEndDrag: () => {
      isScrolling.value = false;
      runOnJS(setAutoShowTimer)();
    },
    onMomentumBegin: () => {
      isScrolling.value = true;
      runOnJS(clearHideTimeout)();
    },
    onMomentumEnd: () => {
      isScrolling.value = false;
      runOnJS(setAutoShowTimer)();
    },
  });

  const changeDate = (amount: number) => {
    // Haptic feedback for date change
    ReactNativeHapticFeedback.trigger('impactLight');
    setDateOffset(prev => prev + amount);
  };

  const panGesture = Gesture.Pan()
    .activeOffsetX([-20, 20])
    .failOffsetY([-15, 15])
    .onBegin(() => {
      isPanningHorizontally.value = true;
    })
    .onUpdate((e) => {
      slideAnim.value = e.translationX;
    })
    .onEnd((e) => {
      const threshold = screenWidth / 4.5;
      const direction = e.translationX < 0 ? 1 : -1;
      const newOffset = dateOffset + direction;

      if (Math.abs(e.translationX) > threshold) {
        // Check if the new offset is within the allowed range [-1, 1]
        if (newOffset < -1 || newOffset > 1) {
          // If not, provide a "bounce" feedback and don't change the date
          slideAnim.value = withSpring(0, { stiffness: 180, damping: 12 });
          return;
        }

        slideAnim.value = withTiming(
            direction * -screenWidth,
            { duration: 200, easing: Easing.ease },
            (finished) => {
                if (finished) {
                    runOnJS(changeDate)(direction);
                    slideAnim.value = direction * screenWidth; // Prepare for entry
                    slideAnim.value = withSpring(0, { stiffness: 120, damping: 18 }); // Animate in
                }
            }
        );
      } else {
        slideAnim.value = withSpring(0, { stiffness: 180, damping: 12 });
      }
    })
    .onFinalize(() => {
      isPanningHorizontally.value = false;
    });

  const currentDate = useMemo(() => {
    const date = new Date();
    date.setDate(date.getDate() + dateOffset);
    return date;
  }, [dateOffset]);

  const schedulesForSelectedDay = useMemo(() => {
    return generateMockSchedules().filter(schedule => isSameDay(schedule.date, currentDate));
  }, [currentDate]);

  // ImportantTasksContainer动画样式
  const importantTasksAnimatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      fadeAnim.value,
      [0, 0.3, 1],
      [0, 0.2, 1],
      'clamp'
    );

    const translateY = interpolate(
      fadeAnim.value,
      [0, 1],
      [-56, 0], // 向上移动
      'clamp'
    );

    const scaleY = interpolate(
      fadeAnim.value,
      [0, 0.4, 1],
      [0.4, 0.8, 1],
      'clamp'
    );

    return {
      opacity,
      transform: [{ translateY }, { scaleY }],
    };
  });

  const bottomActionBarAnimatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      fadeAnim.value,
      [0, 0.3, 1],
      [0, 0.2, 1],
      'clamp'
    );

    const translateY = interpolate(
      fadeAnim.value,
      [0, 1],
      [44, 0], // 向下移动
      'clamp'
    );

    const scaleY = interpolate(
      fadeAnim.value,
      [0, 0.4, 1],
      [0.4, 0.8, 1],
      'clamp'
    );

    return {
      opacity,
      transform: [{ translateY }, { scaleY }],
    };
  });

  // ScheduleList容器样式（简化版，仅处理左右滑动）
  const scheduleListContainerStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: slideAnim.value }],
    };
  });

  // 监听fadeAnim变化，更新topPadding
  useEffect(() => {
    const updatePadding = () => {
      // 简单的示例：根据是否有重要任务来调整padding
      // 后续可以加入更复杂的逻辑，例如根据滚动位置动态调整
      setTopPadding(_hasImportantTasks ? _importantTasksHeight + 16 : 16);
    };

    // 初始设置
    updatePadding();
  }, [fadeAnim.value, _importantTasksHeight, _hasImportantTasks]);

  // 状态提升：将NotesSubmenu的状态提升到HomeScreen层级
  const [showNotesMenu, setShowNotesMenu] = useState(false);
  const [menuButtonPosition, setMenuButtonPosition] = useState<{ x: number; y: number; width: number } | null>(null);

  const handleNotesPress = (position: { x: number; y: number; width: number }) => {
    setMenuButtonPosition(position);
    setShowNotesMenu(true);
  };

  const handleNotesMenuSelect = (option: string) => {
    setShowNotesMenu(false);
    console.log('Selected notes option:', option);
  };

  return (
    <GestureHandlerRootView style={styles.gestureHandlerRoot}>
      <GestureDetector gesture={panGesture}>
        <View style={styles.rootScreen}>
          <SafeAreaView style={styles.safeAreaTop} />
          <View style={styles.container}>
            <Header currentDate={currentDate} />
            <View style={styles.contentContainer}>
              {/* ScheduleList占据整个可用空间 */}
              <Animated.View style={[{flex: 1}, scheduleListContainerStyle]}>
                <ScheduleList
                  ref={scheduleListRef}
                  schedules={schedulesForSelectedDay}
                  selectedDate={currentDate}
                  onScroll={scrollHandler}
                  topPadding={topPadding}
                />
              </Animated.View>

              {/* ImportantTasksContainer浮在顶部 */}
              <Animated.View
                ref={importantTasksRef}
                style={[
                  {
                    position: 'absolute',
                    top: 16,
                    left: 0,
                    right: 0,
                    zIndex: 10,
                  },
                  importantTasksAnimatedStyle,
                ]}
              >
                <ImportantTasksContainer />
              </Animated.View>

              {/* BottomActionBar浮在底部 */}
              <Animated.View
                ref={bottomActionBarRef}
                style={[
                  {
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    zIndex: 10,
                  },
                  bottomActionBarAnimatedStyle,
                ]}
              >
                <BottomActionBar
                  onOpenCamera={handleOpenCamera}
                  onOpenVoice={handleOpenVoiceModal}
                  onNotesPress={handleNotesPress}
                  showNotesMenu={showNotesMenu}
                />
              </Animated.View>
            </View>
          </View>

          {/* 全屏层级的NotesSubmenu遮罩 - 可以覆盖整个屏幕包括header */}
          {showNotesMenu && (
            <NotesSubmenu
              visible={showNotesMenu}
              buttonPosition={menuButtonPosition}
              onClose={() => setShowNotesMenu(false)}
              onSelectOption={handleNotesMenuSelect}
            />
          )}

          {isVoiceModalVisible && (
            <VoiceInputModal
              isVisible={isVoiceModalVisible}
              onClose={handleCloseVoiceModal}
            />
          )}

        </View>
      </GestureDetector>
    </GestureHandlerRootView>
  );
};

// --- Styles ---

const styles = StyleSheet.create({
  safeAreaTop: {
    flex: 0,
    backgroundColor: '#FFA500', // 品牌橙色
  },
  container: {
    flex: 1,
    backgroundColor: '#FFA500', // 品牌橙色
    justifyContent: 'space-between',
  },
  headerContainer: {
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 20,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerGreeting: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 16,
  },
  dateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginTop: 8,
  },
  dateTextContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  headerDate: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  dayText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#374151',
  },
  weekdayText: {
    fontSize: 20,
    fontWeight: '500',
    color: '#374151',
    marginLeft: 8,
  },
  fullDateText: {
    fontSize: 16,
    color: '#6B7280',
    marginLeft: 8,
  },
  moreText: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '500',
    paddingBottom: 6, // Align with larger date text
  },
  contentContainer: {
    flex: 1,
    backgroundColor: '#F7F8FA',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 16,
    paddingTop: 16,
    overflow: 'hidden',
  },
  emptyStateContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%', // Ensure it takes full width inside content container
  },
  emptyStateImage: {
    width: 100,
    height: 100,
    opacity: 0.5,
  },
  emptyStateText: {
    marginTop: 20,
    fontSize: 16,
    color: '#9CA3AF',
  },
  rootScreen: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  gestureHandlerRoot: {
    flex: 1,
  },
  placeholderContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 12,
    marginBottom: 16,
    minHeight: 80,
  },
  placeholderText: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
  },
});

export default HomeScreen;
