/**
 * 简化的微信绑定界面
 * 去掉复杂的轮询机制，改为手动操作
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Alert,
  ActivityIndicator,
  AppState,
  ScrollView,
} from 'react-native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RootStackParamList } from '../../App';
import Ionicons from 'react-native-vector-icons/Ionicons';

import SimpleWeChatBindingService, { SimpleWeChatBinding } from '../services/SimpleWeChatBindingService';
import WeChatMessageSyncService from '../services/WeChatMessageSyncService';
import { formatBindingTime } from '../utils/dateTime';

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface Props {
  navigation: NavigationProp;
}

const SimpleWeChatBindingScreen: React.FC<Props> = ({ navigation }) => {
  const [binding, setBinding] = useState<SimpleWeChatBinding | null>(null);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const [bindingStatus, setBindingStatus] = useState<string>('未绑定');

  useEffect(() => {
    loadCurrentBinding();

    // 监听APP状态变化，从微信返回时自动刷新绑定状态
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        console.log('[SimpleWeChatBindingScreen] APP回到前台，刷新绑定状态');
        // 延迟1秒后刷新，确保绑定处理完成
        setTimeout(() => {
          loadCurrentBinding();
        }, 1000);
      }
    };

    // 监听绑定状态变更（实时更新UI）
    const handleBindingStatusChange = (newBinding: any) => {
      console.log('[SimpleWeChatBindingScreen] 收到绑定状态变更通知:', newBinding);
      setBinding(newBinding);
      if (newBinding && newBinding.isBinding) {
        setBindingStatus('已绑定');
      } else {
        setBindingStatus('未绑定');
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // 注册绑定状态监听器
    SimpleWeChatBindingService.addBindingStatusListener(handleBindingStatusChange);

    return () => {
      subscription?.remove();
      SimpleWeChatBindingService.removeBindingStatusListener(handleBindingStatusChange);
    };
  }, []);

  const loadCurrentBinding = async () => {
    try {
      // 1. 优先从本地获取绑定状态
      const localBinding = await SimpleWeChatBindingService.getCurrentBinding();
      console.log('[SimpleWeChatBindingScreen] 本地绑定状态:', localBinding);

      if (localBinding) {
        // 本地有绑定信息，直接使用（本地优先策略）
        setBinding(localBinding);
        setBindingStatus('已绑定');
        console.log('[SimpleWeChatBindingScreen] 使用本地绑定状态');
      } else {
        // 2. 本地无绑定信息，从服务器获取
        console.log('[SimpleWeChatBindingScreen] 本地无绑定信息，从服务器获取');
        const serverBinding = await SimpleWeChatBindingService.getCurrentBindingWithVerification();
        console.log('[SimpleWeChatBindingScreen] 服务器绑定状态:', serverBinding);

        setBinding(serverBinding);
        if (serverBinding && serverBinding.isBinding) {
          setBindingStatus('已绑定');
        } else {
          setBindingStatus('未绑定');
        }
      }
    } catch (error) {
      console.error('[SimpleWeChatBindingScreen] 加载绑定状态失败:', error);
      setBindingStatus('检查失败');
    }
  };

  const handleRefreshStatus = async () => {
    setRefreshing(true);
    setBindingStatus('检查中...');
    try {
      console.log('[SimpleWeChatBindingScreen] 手动刷新绑定状态');
      const result = await SimpleWeChatBindingService.refreshBindingStatus();
      console.log('[SimpleWeChatBindingScreen] 刷新结果:', result);

      if (result.success && result.binding) {
        setBinding(result.binding);
        setBindingStatus('已绑定');
      } else {
        setBinding(null);
        setBindingStatus('未绑定');
      }
      // 确保消息是字符串类型
      const message = typeof result.message === 'string' ? result.message : '刷新完成';
      Alert.alert('提示', message);
    } catch (error) {
      console.error('[SimpleWeChatBindingScreen] 刷新绑定状态失败:', error);
      setBindingStatus('检查失败');
      Alert.alert('错误', '刷新状态失败');
    } finally {
      setRefreshing(false);
    }
  };



  const handleUnbind = () => {
    Alert.alert(
      '确认解除绑定',
      '解除绑定后将无法接收微信消息转发，确定要继续吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确定',
          style: 'destructive',
          onPress: async () => {
            const result = await SimpleWeChatBindingService.unbind();
            if (result.success) {
              // 更新本地状态
              setBinding(null);
              setBindingStatus('未绑定');

              // 解除绑定后停止消息同步服务
              WeChatMessageSyncService.stopSync();

              // 确保消息是字符串类型
              const message = typeof result.message === 'string' ? result.message : '解除绑定成功';
              Alert.alert('成功', message + '\n消息同步服务已停止');
            } else {
              // 确保消息是字符串类型
              const message = typeof result.message === 'string' ? result.message : '解除绑定失败';
              Alert.alert('失败', message);
            }
          },
        },
      ]
    );
  };

  const renderBindingStatus = () => {
    // 获取状态颜色 - 使用微信主题色系
    const getStatusColor = () => {
      switch (bindingStatus) {
        case '已绑定': return '#07C160'; // 微信主绿
        case '已绑定（离线）': return '#FF9800';
        case '检查中...': return '#FF9800';
        case '检查失败': return '#F44336';
        default: return '#9E9E9E';
      }
    };

    if (!binding || !binding.isBinding) {
      return (
        <View style={styles.statusContainer}>
          <View style={styles.statusIconContainer}>
            <Ionicons name="close-circle" size={64} color="#ff6b6b" />
          </View>
          <Text style={styles.statusTitle}>微信绑定状态</Text>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>当前状态：</Text>
            <Text style={[styles.statusValue, { color: getStatusColor() }]}>
              {bindingStatus}
            </Text>

            {/* 小的刷新按钮 */}
            <TouchableOpacity
              style={styles.smallRefreshButton}
              onPress={handleRefreshStatus}
              disabled={refreshing}
            >
              {refreshing ? (
                <ActivityIndicator size="small" color="#07C160" />
              ) : (
                <Ionicons name="refresh" size={16} color="#666" />
              )}
            </TouchableOpacity>
          </View>

        </View>
      );
    }

    return (
      <View style={styles.statusContainer}>
        <View style={styles.statusIconContainer}>
          <Ionicons name="checkmark-circle" size={64} color="#07C160" />
        </View>
        <Text style={styles.statusTitle}>已绑定微信</Text>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>当前状态：</Text>
          <Text style={[styles.statusValue, { color: getStatusColor() }]}>
            {bindingStatus}
          </Text>

          {/* 小的刷新按钮 */}
          <TouchableOpacity
            style={styles.smallRefreshButton}
            onPress={handleRefreshStatus}
            disabled={refreshing}
          >
            {refreshing ? (
              <ActivityIndicator size="small" color="#07C160" />
            ) : (
              <Ionicons name="refresh" size={16} color="#666" />
            )}
          </TouchableOpacity>
        </View>
        <Text style={styles.statusDescription}>
          绑定时间: {binding.bindingTime ? formatBindingTime(binding.bindingTime) : '未知'}
        </Text>
        <Text style={styles.statusDescription}>
          验证时间: {formatBindingTime(Date.now())}
        </Text>
      </View>
    );
  };

  const handleOneClickBinding = async () => {
    setLoading(true);
    try {
      const result = await SimpleWeChatBindingService.startBinding();

      if (result.success) {
        Alert.alert(
          '绑定提示',
          result.message || '已跳转到微信客服，进入对话即可完成绑定。\n\n请在微信中进行操作，系统将自动检测绑定状态。',
          [{ text: '我知道了' }]
        );

        // 启动轮询检查绑定状态
        setTimeout(() => {
          handleRefreshStatus();
        }, 3000); // 3秒后开始检查
      } else {
        // 改善错误提示
        let errorMessage = result.message || '一键绑定失败，请重试';
        if (errorMessage.includes('已被其他账号绑定')) {
          errorMessage += '\n\n如需重新绑定，请联系客服或使用其他微信账号。';
        }
        Alert.alert('绑定失败', errorMessage);
      }
    } catch (error) {
      console.error('一键绑定异常:', error);
      Alert.alert('错误', '一键绑定过程中发生异常，请重试');
    } finally {
      setLoading(false);
    }
  };

  const renderActionButtons = () => {
    if (!binding || !binding.isBinding) {
      return (
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.wechatButton]}
            onPress={handleOneClickBinding}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <>
                <Ionicons name="logo-wechat" size={22} color="#fff" />
                <Text style={styles.buttonText}>一键跳转微信绑定</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.dangerButton]}
          onPress={handleUnbind}
        >
          <Ionicons name="unlink" size={20} color="#fff" />
          <Text style={styles.buttonText}>解除绑定</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>微信绑定</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Content */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderBindingStatus()}

        <View style={styles.infoContainer}>
          <Text style={styles.infoTitle}>功能说明</Text>

          <View style={styles.featureItem}>
            <Text style={styles.featureTitle}>• 消息整理</Text>
            <Text style={styles.featureDescription}>
              选择您微信里的数字资产，同步到公职猫。
            </Text>
          </View>

          <View style={styles.featureItem}>
            <Text style={styles.featureTitle}>• 取后即焚</Text>
            <Text style={styles.featureDescription}>
              消息下载后云端不存数据，确保信息安全。
            </Text>
          </View>

          <View style={styles.featureItem}>
            <Text style={styles.featureTitle}>• 匿名绑定</Text>
            <Text style={styles.featureDescription}>
            微信专用通道，匿名身份绑定，安全可靠。
            </Text>
          </View>

        </View>
      </ScrollView>

      {/* 固定在底部的按钮 */}
      <View style={styles.bottomButtonContainer}>
        {renderActionButtons()}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  bottomButtonContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },

  statusContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  statusIconContainer: {
    marginBottom: 8,
  },
  statusTitle: {
    fontSize: 22,
    fontWeight: '600',
    color: '#333',
    marginTop: 12,
    marginBottom: 8,
  },
  statusDescription: {
    fontSize: 15,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 22,
    paddingHorizontal: 16,
  },

  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  smallRefreshButton: {
    marginLeft: 8,
    padding: 4,
    borderRadius: 4,
    backgroundColor: '#f5f5f5',
  },
  statusLabel: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  statusValue: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  infoContainer: {
    backgroundColor: '#f8f9fa',
    padding: 20,
    borderRadius: 12,
    marginVertical: 24,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  featureItem: {
    marginBottom: 16,
  },
  featureTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
    marginBottom: 6,
  },
  featureDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    paddingLeft: 12,
  },
  featureList: {
    marginTop: 8,
  },
  featurePoint: {
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
    marginBottom: 4,
  },
  buttonContainer: {
    gap: 12,
    marginTop: 8,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 10,
    minHeight: 52,
  },
  wechatButton: {
    backgroundColor: '#07C160', // 微信主绿
    shadowColor: '#07C160',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  refreshButton: {
    backgroundColor: '#fff',
    borderWidth: 1.5,
    borderColor: '#07C160',
  },
  refreshButtonText: {
    color: '#07C160',
  },
  dangerButton: {
    backgroundColor: '#ff6b6b',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});

export default SimpleWeChatBindingScreen;
