import React, { useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, SafeAreaView, Platform } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList, AppContext, ScheduleItem } from '../../App';

// type Role = 'host' | 'assist' | 'attend' | null; // 已移至types/declarations.ts

// 保留用于将来测试 - 暂时注释掉未使用的模拟数据
/*
const MOCK_SCHEDULE_DATA: { [key: string]: ScheduleItem[] } = {
  '2025-06-23': [
    { id: '1', date: '2025-06-23', time: '09:00 - 10:00', title: '全区数字化改革推进会', role: 'host' },
    { id: '2', date: '2025-06-23', time: '10:30 - 12:00', title: '研究XX公司上市材料', role: 'assist' },
  ],
  '2025-06-24': [
    { id: '3', date: '2025-06-24', time: '14:00 - 15:30', title: '接待省厅调研组一行', role: 'attend' },
  ],
  '2025-06-25': [
      { id: '4', date: '2025-06-25', time: '16:00 - 16:30', title: '与晓阳同志谈话', role: 'host' },
      { id: '5', date: '2025-06-25', time: '全天', title: '撰写《关于推进数字化办公的调研报告》', role: 'host'},
  ],
  '2025-06-27': [
      { id: '6', date: '2025-06-27', time: '09:30 - 11:00', title: '参加"七一"主题党日活动', role: 'attend'},
  ],
};
*/

type DayDetailRouteProp = RouteProp<RootStackParamList, 'DayDetail'>;

const DayDetailScreen = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const route = useRoute<DayDetailRouteProp>();
  const { scheduleData } = useContext(AppContext);

  const selectedDateISO = route.params.date;
  const selectedDate = new Date(selectedDateISO);
  const dateKey = format(selectedDate, 'yyyy-MM-dd');

  const daySchedule = scheduleData[dateKey] || [];

  const getRoleStyle = (role: ScheduleItem['role']) => {
    switch (role) {
      case 'host': return styles.hostIndicator;
      case 'assist': return styles.assistIndicator;
      case 'attend': return styles.attendIndicator;
      default: return {};
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="chevron-back" size={28} color="#FF8C00" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
            {format(selectedDate, 'M月d日 cccc', { locale: zhCN })}
        </Text>
        <TouchableOpacity style={styles.addButton} onPress={() => {
          // 跳转到聚合式创作入口，初始Tab为日程，context传递当前详情页日期
          navigation.navigate('CreationScreen', {
            initialTab: 'schedule',
            context: { from: 'DayDetailScreen', date: selectedDateISO },
          });
        }}>
          <Ionicons name="add" size={32} color="#FF8C00" />
        </TouchableOpacity>
      </View>
      <ScrollView style={styles.scrollView}>
        {daySchedule.length > 0 ? (
          daySchedule.map(item => (
            <View key={item.id} style={styles.scheduleItem}>
              <View style={[styles.indicator, getRoleStyle(item.role)]} />
              <View style={styles.timeContainer}>
                <Text style={styles.timeText}>{item.time.split(' - ')[0]}</Text>
                {item.time.includes(' - ') && <Text style={styles.timeText}>{item.time.split(' - ')[1]}</Text>}
              </View>
              <View style={styles.titleWrapper}>
                <Text style={styles.titleText}>{item.title}</Text>
              </View>
            </View>
          ))
        ) : (
          <View style={styles.noEventsContainer}>
            <Text style={styles.noEventsText}>今日无日程安排</Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingBottom: 10,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    color: '#000000',
    fontSize: 17,
    fontWeight: '600',
  },
  addButton: {
    padding: 5,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  scheduleItem: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 16,
    minHeight: 70,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.08,
    shadowRadius: 2.22,
    elevation: 3,
  },
  indicator: {
    width: 6,
  },
  timeContainer: {
      width: 90,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 10,
      paddingHorizontal: 8,
      borderRightWidth: 1,
      borderRightColor: '#F2F2F7',
  },
  timeText: {
      color: '#6B7280',
      fontSize: 13,
  },
  titleWrapper: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 16,
  },
  titleText: {
      color: '#000000',
      fontSize: 17,
      fontWeight: '500',
      lineHeight: 22,
  },
  noEventsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  noEventsText: {
    color: '#8E8E93',
    fontSize: 16,
  },
  hostIndicator: {
    backgroundColor: 'rgba(255, 71, 87, 0.8)', // 红色
  },
  assistIndicator: {
    backgroundColor: 'rgba(255, 204, 0, 0.8)', // 黄色
  },
  attendIndicator: {
    backgroundColor: 'rgba(52, 199, 89, 0.8)', // 绿色
  },
});

export default DayDetailScreen;
