import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, Modal } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { format, addMonths, subMonths, isSameMonth, isToday, isWithinInterval, startOfWeek, endOfWeek, setYear, setMonth } from 'date-fns';
// import { zhCN } from 'date-fns/locale'; // 暂时不用，但保留供将来扩展
import { getCalendarGridData } from '../utils/calendar';
import { RootStackParamList } from '../../App';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';

type FullScreenMonthNavigationProp = NativeStackNavigationProp<RootStackParamList, 'FullScreenMonth'>;

const FullScreenMonthScreen = () => {
  const navigation = useNavigation<FullScreenMonthNavigationProp>();
  const [displayDate, setDisplayDate] = useState(new Date());
  const [showYearPicker, setShowYearPicker] = useState(false);
  const [showMonthPicker, setShowMonthPicker] = useState(false);

  const calendarGrid = useMemo(() => getCalendarGridData(displayDate), [displayDate]);

  const goToPreviousMonth = () => {
    ReactNativeHapticFeedback.trigger('impactLight');
    setDisplayDate(subMonths(displayDate, 1));
  };

  const goToNextMonth = () => {
    ReactNativeHapticFeedback.trigger('impactLight');
    setDisplayDate(addMonths(displayDate, 1));
  };

  const handleYearSelect = (year: number) => {
    ReactNativeHapticFeedback.trigger('impactLight');
    setDisplayDate(setYear(displayDate, year));
    setShowYearPicker(false);
  };

  const handleMonthSelect = (monthIndex: number) => {
    ReactNativeHapticFeedback.trigger('impactLight');
    setDisplayDate(setMonth(displayDate, monthIndex));
    setShowMonthPicker(false);
  };

  const currentYear = displayDate.getFullYear();
  const years = Array.from({ length: 20 }, (_, i) => currentYear - 10 + i); // 10 years before and after, total 20 years
  const months = [
    '1月', '2月', '3月', '4月', '5月', '6月',
    '7月', '8月', '9月', '10月', '11月', '12月',
  ];

  const weekStart = startOfWeek(displayDate, { weekStartsOn: 1 });
  const weekEnd = endOfWeek(displayDate, { weekStartsOn: 1 });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="close-outline" size={32} color="#FF8C00" />
        </TouchableOpacity>
        <View style={styles.monthSelector}>
            <TouchableOpacity onPress={goToPreviousMonth}>
                <Ionicons name="chevron-back-outline" size={24} color="#FF8C00" />
            </TouchableOpacity>
            <View style={styles.dateSelector}>
              <TouchableOpacity onPress={() => setShowYearPicker(true)} activeOpacity={0.7}>
                <Text style={styles.yearText}>{displayDate.getFullYear()}年</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => setShowMonthPicker(true)} activeOpacity={0.7}>
                <Text style={styles.monthText}>{displayDate.getMonth() + 1}月</Text>
              </TouchableOpacity>
            </View>
            <TouchableOpacity onPress={goToNextMonth}>
                <Ionicons name="chevron-forward-outline" size={24} color="#FF8C00" />
            </TouchableOpacity>
        </View>
        <TouchableOpacity onPress={() => setDisplayDate(new Date())} style={styles.todayButton}>
            <Text style={styles.todayButtonText}>本月</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.weekDaysContainer}>
          {['一', '二', '三', '四', '五', '六', '日'].map(day => (
              <Text key={day} style={styles.weekDayText}>{day}</Text>
          ))}
      </View>
      <View style={styles.calendarGridContainer}>
        {calendarGrid.map((day, index) => {
            if (!day) {
                return <View key={index} style={[styles.dayCell, { backgroundColor: '#F2F2F7'}]} />;
            }
            const isCurrentMonth = isSameMonth(day, displayDate);
            const isCurrentDay = isToday(day);
            const isDayInSelectedWeek = isWithinInterval(day, { start: weekStart, end: weekEnd });

            return (
                <TouchableOpacity
                    key={index}
                    style={[
                      styles.dayCell,
                      isDayInSelectedWeek && styles.weekHighlight,
                    ]}
                    onPress={() => setDisplayDate(day)}
                    onLongPress={() => navigation.navigate('DayDetail', { date: day.toISOString() })}
                >
                    <View style={[styles.dayContainer, isCurrentDay && styles.todayContainer]}>
                        <Text style={[
                            styles.dayText,
                            !isCurrentMonth && styles.dayTextNotInMonth,
                            isCurrentDay && styles.todayText,
                        ]}>
                            {format(day, 'd')}
                        </Text>
                    </View>
                </TouchableOpacity>
            );
        })}
      </View>

      {/* Year Picker Modal */}
      <Modal visible={showYearPicker} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <View style={styles.pickerContainer}>
            <View style={styles.pickerHeader}>
              <Text style={styles.pickerTitle}>选择年份</Text>
              <TouchableOpacity onPress={() => setShowYearPicker(false)}>
                <Ionicons name="close-outline" size={24} color="#666666" />
              </TouchableOpacity>
            </View>
                         <View style={styles.yearGrid}>
               {years.map(year => (
                 <TouchableOpacity
                   key={year}
                   style={[styles.yearGridItem, year === currentYear && styles.yearGridItemActive]}
                   onPress={() => handleYearSelect(year)}
                 >
                   <Text style={[styles.yearGridText, year === currentYear && styles.yearGridTextActive]}>
                     {year}
                   </Text>
                 </TouchableOpacity>
               ))}
             </View>
          </View>
        </View>
      </Modal>

      {/* Month Picker Modal */}
      <Modal visible={showMonthPicker} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <View style={styles.pickerContainer}>
            <View style={styles.pickerHeader}>
              <Text style={styles.pickerTitle}>选择月份</Text>
              <TouchableOpacity onPress={() => setShowMonthPicker(false)}>
                <Ionicons name="close-outline" size={24} color="#666666" />
              </TouchableOpacity>
            </View>
            <View style={styles.monthGrid}>
              {months.map((month, index) => (
                <TouchableOpacity
                  key={index}
                  style={[styles.monthGridItem, index === displayDate.getMonth() && styles.monthGridItemActive]}
                  onPress={() => handleMonthSelect(index)}
                >
                  <Text style={[styles.monthGridText, index === displayDate.getMonth() && styles.monthGridTextActive]}>
                    {month}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    padding: 5,
  },
  monthSelector: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    color: '#000000',
    fontSize: 17,
    fontWeight: '600',
    marginHorizontal: 15,
  },
  todayButton: {
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 12,
    backgroundColor: '#FFEFE0',
  },
  todayButtonText: {
    color: '#FF8C00',
    fontSize: 14,
  },
  weekDaysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 10,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  weekDayText: {
    color: '#3C3C43',
    fontSize: 14,
  },
  calendarGridContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  dayCell: {
    width: '14.28%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  weekHighlight: {
    backgroundColor: '#FFF7E6',
  },
  dayContainer: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
  },
  todayContainer: {
    backgroundColor: '#FF8C00',
  },
  todayText: {
    color: '#FFFFFF',
  },
  dayText: {
    color: '#1C1C1E',
    fontSize: 16,
  },
  dayTextNotInMonth: {
    color: '#C7C7CC',
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  yearText: {
    color: '#000000',
    fontSize: 17,
    fontWeight: '600',
    marginRight: 4,
  },
  monthText: {
    color: '#000000',
    fontSize: 17,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pickerContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    width: '80%',
    maxHeight: '70%',
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  pickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
  },
  yearGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
  },
  yearGridItem: {
    width: '20%',
    aspectRatio: 1.5,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    margin: 4,
  },
  yearGridItemActive: {
    backgroundColor: '#FF8C00',
  },
  yearGridText: {
    fontSize: 16,
    color: '#000000',
  },
  yearGridTextActive: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  monthGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
  },
  monthGridItem: {
    width: '25%',
    aspectRatio: 2,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    margin: 4,
  },
  monthGridItemActive: {
    backgroundColor: '#FF8C00',
  },
  monthGridText: {
    fontSize: 16,
    color: '#000000',
  },
  monthGridTextActive: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
});

export default FullScreenMonthScreen;
