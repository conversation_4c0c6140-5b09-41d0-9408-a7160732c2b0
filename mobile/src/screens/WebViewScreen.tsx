import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Platform } from 'react-native';
import WebView from 'react-native-webview';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import type { RootStackParamList } from '../../App';
import { SafeAreaView } from 'react-native-safe-area-context';
import { defaultTheme } from '../styles/theme';

const WebViewScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<RootStackParamList, 'WebViewScreen'>>();
  const theme = defaultTheme;
  // 支持通过params传递url
  const url = route.params?.url;

  if (!url) {
    return (
      <View style={styles(theme).centered}>
        <Text style={styles(theme).errorText}>未指定URL</Text>
      </View>
    );
  }

  return (
    <View style={styles(theme).container}>
      <SafeAreaView edges={['top']} style={styles(theme).safeArea}>
        <View style={styles(theme).header}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles(theme).backBtn}>
            <Ionicons
              name={Platform.OS === 'ios' ? 'chevron-back' : 'arrow-back'}
              size={24}
              color={theme.colors.textPrimary}
            />
          </TouchableOpacity>
          <Text style={styles(theme).title} numberOfLines={1}>{url}</Text>
        </View>
      </SafeAreaView>
      <WebView source={{ uri: url }} style={styles(theme).webview} startInLoadingState />
    </View>
  );
};

const styles = (theme: typeof defaultTheme) => StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    backgroundColor: theme.colors.backgroundSecondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 48,
    backgroundColor: theme.colors.backgroundSecondary,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: theme.colors.backgroundTertiary,
    paddingHorizontal: 8,
  },
  backBtn: {
    padding: 8,
    marginRight: 8,
  },
  title: {
    fontSize: theme.fonts.bodyMedium,
    color: theme.colors.textPrimary,
    flex: 1,
  },
  webview: {
    flex: 1,
  },
  centered: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: theme.fonts.bodyLarge,
    color: theme.colors.textSecondary,
  },
});

export default WebViewScreen;
