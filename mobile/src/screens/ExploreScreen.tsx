import React from 'react';
import { Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView } from 'react-native';

const FeatureButton = ({ title, description, onPress }: { title: string; description: string; onPress: () => void }) => (
  <TouchableOpacity style={styles.featureButton} onPress={onPress}>
    <Text style={styles.featureTitle}>{title}</Text>
    <Text style={styles.featureDescription}>{description}</Text>
  </TouchableOpacity>
);

const ExploreScreen = () => {
  const handleFeaturePress = (feature: string) => {
    console.log(`Feature pressed: ${feature}`);
    // 在这里可以添加具体的功能逻辑或导航
    // Alert.alert('提示', `${feature} 功能开发中...`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <Text style={styles.header}>探索中心</Text>
        <Text style={styles.subHeader}>这里是高级功能和测试模块的入口。</Text>
        <FeatureButton
          title="AI 功能测试"
          description="测试应用的各项AI能力，包括文本分析和OCR。"
          onPress={() => handleFeaturePress('AI_TEST')}
        />
        <FeatureButton
          title="原生 OCR 测试"
          description="使用iOS Vision框架和Android MLKit进行OCR识别测试。"
          onPress={() => handleFeaturePress('NATIVE_OCR_TEST')}
        />
        <FeatureButton
          title="设备 OCR 测试"
          description="运行设备内置的OCR引擎进行全流程测试。"
          onPress={() => handleFeaturePress('DEVICE_OCR_TEST')}
        />
        <FeatureButton
          title="规则引擎测试"
          description="验证政务文本解析和任务生成的规则。"
          onPress={() => handleFeaturePress('RULE_ENGINE_TEST')}
        />
        <FeatureButton
          title="添加新任务"
          description="手动添加一个重要任务或今日待办。"
          onPress={() => handleFeaturePress('ADD_NEW_TASK')}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F4F4F5', // A light gray background
  },
  content: {
    padding: 20,
  },
  header: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#18181B',
    marginBottom: 8,
  },
  subHeader: {
    fontSize: 16,
    color: '#71717A',
    marginBottom: 24,
  },
  featureButton: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#3F3F46',
  },
  featureDescription: {
    fontSize: 14,
    color: '#A1A1AA',
    marginTop: 4,
  },
});

export default ExploreScreen;
