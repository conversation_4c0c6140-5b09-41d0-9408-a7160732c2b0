import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Text,
  Alert,
  Image,
  SafeAreaView,
  Keyboard,
  TouchableWithoutFeedback,
  Modal,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { TabView, TabBar, SceneRendererProps } from 'react-native-tab-view';
import TextInputComponent from '../components/InputPanel/TextInputComponent';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { v4 as uuidv4 } from 'uuid';
import { getDisplayUri } from '../utils/media';
import { database } from '../db';
import Media from '../db/models/Media';
import { useGlobalImagePickerStore } from '../stores/globalImagePickerStore';
import { imageSelectionConfig, currentUserTier } from '../config/appConfig';

// Tab keys
type TabKey = 'note' | 'schedule' | 'notebook';

// 增加 RootStackParamList 的定义以修复类型错误
type RootStackParamList = {
  CreationScreen: { sessionId?: string; initialTab?: TabKey; context?: any };
  // Add other screens here if needed
};

// Props from navigation
type CreationScreenRouteProp = RouteProp<RootStackParamList, 'CreationScreen'>;

const initialLayout = { width: Dimensions.get('window').width };

const TAB_LABELS: Record<TabKey, string> = {
  note: '便签',
  schedule: '日程',
  notebook: '笔记',
};

type ImageItem = { id: string; uri: string };

interface ContentState {
  text: string;
  images: ImageItem[];
}

// 单个Tab的内容组件
function TabContent({
  value,
  onTextChange,
  images,
  onImagesChange,
  tabKey,
}: {
  value: string;
  onTextChange: (text: string) => void;
  images: ImageItem[];
  onImagesChange: (images: ImageItem[]) => void;
  tabKey: TabKey;
}) {
  const [preview, setPreview] = useState<{ visible: boolean; uri: string | null }>({
    visible: false,
    uri: null,
  });

  // 从配置中获取当前用户的图片上限
  const maxImages = currentUserTier === 'pro'
    ? imageSelectionConfig.proMaxSelection
    : imageSelectionConfig.maxSelection;

  const handleAddImage = () => {
    const { openPicker } = useGlobalImagePickerStore.getState();
    openPicker({
      mode: 'multiple',
      selectionLimit: maxImages - images.length,
      onSelect: (uris) => {
        if (uris && uris.length > 0) {
          const newImages = uris.map(uri => ({
            id: uuidv4(),
            uri: uri,
          }));
          onImagesChange([...images, ...newImages]);
        }
      },
      // onClose is not explicitly needed here as the store handles it.
    });
  };

  const handleDeleteImage = (idToDelete: string) => {
    onImagesChange(images.filter(img => img.id !== idToDelete));
  };

  const openPreview = (uri: string) => {
    setPreview({ visible: true, uri });
  };

  const closePreview = () => {
    setPreview({ visible: false, uri: null });
  };

  return (
    <View style={styles.tabContent}>
      <TextInputComponent
        value={value}
        onTextChange={onTextChange}
        placeholder={`请输入${TAB_LABELS[tabKey]}内容...`}
        selectionColor="#FF8C00"
      />
      <View style={styles.imageSection}>
        <View style={styles.imageHeader}>
          <Text style={styles.imageCountText}>{`${images.length}/${maxImages}`}</Text>
        </View>
        <View style={styles.imageContainer}>
          {images.map(img => (
            <TouchableOpacity key={img.id} onPress={() => openPreview(img.uri)}>
              <View style={styles.imageWrapper}>
                <Image source={{ uri: img.uri }} style={styles.imageThumbnail} />
                <TouchableOpacity style={styles.deleteButton} onPress={() => handleDeleteImage(img.id)}>
                  <Ionicons name="close-circle" size={24} color="#000" />
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          ))}
          {images.length < maxImages && (
            <TouchableOpacity style={styles.addImageButton} onPress={handleAddImage}>
              <Ionicons name="image-outline" size={32} color="#888" />
              <Text style={styles.addImageButtonText}>添加图片</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
      <Modal visible={preview.visible} transparent={true} onRequestClose={closePreview}>
        <View style={styles.modalContainer}>
          <TouchableOpacity onPress={closePreview} activeOpacity={1} style={styles.previewImage}>
            <Image source={{ uri: preview.uri || undefined }} style={styles.fillImage} resizeMode="contain" />
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
}

const MemoizedTabContent = React.memo(TabContent);

const CreationScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<CreationScreenRouteProp>();
  const { sessionId, initialTab = 'note' } = route.params || {};

  const [index, setIndex] = useState(0);
  const [routes] = useState<Array<{ key: TabKey; title: string }>>([
    { key: 'note', title: TAB_LABELS.note },
    { key: 'schedule', title: TAB_LABELS.schedule },
    { key: 'notebook', title: TAB_LABELS.notebook },
  ]);

  const [content, setContent] = useState<ContentState>({ text: '', images: [] });

  useEffect(() => {
    const initialIndex = routes.findIndex(r => r.key === initialTab);
    if (initialIndex !== -1) {
      setIndex(initialIndex);
    }
  }, [initialTab, routes]);

  useEffect(() => {
    if (sessionId) {
      const loadMedia = async () => {
        const allMedia = await database.collections
          .get<Media>('media')
          .query()
          .fetch();

        const mediaRecords = allMedia.filter(m => m.sessionId === sessionId);

        const loadedImages = mediaRecords.map(m => ({
          id: m.id,
          uri: getDisplayUri(m.uri),
        }));

        setContent(prev => ({ ...prev, images: loadedImages }));
      };

      loadMedia();
    }
  }, [sessionId]);

  const handleTextChange = (newText: string) => {
    setContent(prev => ({ ...prev, text: newText }));
  };

  const handleImagesChange = (newImages: ImageItem[]) => {
    setContent(prev => ({ ...prev, images: newImages }));
  };

  const renderScene = ({
    route: sceneRoute,
  }: SceneRendererProps & { route: { key: TabKey } }) => {
    return (
      <MemoizedTabContent
        value={content.text}
        onTextChange={handleTextChange}
        images={content.images}
        onImagesChange={handleImagesChange}
        tabKey={sceneRoute.key}
      />
    );
  };

  const handleIndexChange = (newIndex: number) => {
    setIndex(newIndex);
  };

  const handleCancel = () => {
    // ToDo: check for unsaved changes before going back
    navigation.goBack();
  };

  const handleDone = () => {
    // ToDo: Implement save logic
    console.log('Done pressed. Current states:', content);
    Alert.alert('完成', '内容已保存（功能开发中）');
  };

  const renderTabBar = (
    props: SceneRendererProps & {
      navigationState: {
        routes: { key: string; title: string }[];
        index: number;
      };
    },
  ) => (
    <TabBar
      {...props}
      indicatorStyle={styles.indicator}
      style={styles.tabBar}
      activeColor="#FF8C00"
      inactiveColor="#666"
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
        <View style={{ flex: 1 }}>
          <View style={styles.header}>
            <TouchableOpacity onPress={handleCancel}>
              <Text style={styles.headerButton}>取消</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>创建</Text>
            <TouchableOpacity onPress={handleDone}>
              <Text style={[styles.headerButton, styles.doneButton]}>完成</Text>
            </TouchableOpacity>
          </View>
          <TabView
            navigationState={{ index, routes }}
            renderScene={renderScene}
            onIndexChange={handleIndexChange}
            initialLayout={initialLayout}
            renderTabBar={renderTabBar}
            lazy
          />
        </View>
      </TouchableWithoutFeedback>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
  },
  headerButton: {
    fontSize: 16,
    color: '#666',
  },
  doneButton: {
    fontWeight: '600',
    color: '#FF8C00',
  },
  tabBar: {
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    elevation: 0,
  },
  indicator: {
    backgroundColor: '#FF8C00',
    height: 3,
  },
  tabContent: {
    flex: 1,
  },
  imageSection: {
    marginTop: 24,
  },
  imageHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginBottom: 8,
  },
  imageCountText: {
    fontSize: 14,
    color: '#666',
  },
  imageContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  imageWrapper: {
    position: 'relative',
    marginRight: 10,
    marginBottom: 10,
  },
  imageThumbnail: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  deleteButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'white',
    borderRadius: 12,
  },
  addImageButton: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  addImageButtonText: {
    marginTop: 4,
    fontSize: 12,
    color: '#888',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewImage: {
    width: '100%',
    height: '80%',
  },
  fillImage: {
    width: '100%',
    height: '100%',
  },
});

export default CreationScreen;
