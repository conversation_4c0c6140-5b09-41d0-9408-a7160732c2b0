import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, FlatList } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { defaultTheme as theme } from '../styles/theme';

// Define types locally for now to resolve module issue
// This can be moved to a central types file later, e.g., 'src/types/index.ts'
export interface StickyNote {
  id: string;
  title: string;
  category?: string;
  order: number;
  completed?: boolean;
  isReference?: boolean; // 标记是否为引用型便签
  sourceType?: 'direct' | 'schedule' | 'note' | 'file' | 'photo'; // 来源类型
}

// Placeholder data for now
const mockNotes: StickyNote[] = [
  { id: '1', title: '党校培训', category: 'study', order: 1 },
  { id: '2', title: '考核准备', category: 'work', order: 2 },
  { id: '3', title: '总结报告', category: 'report', order: 3, isReference: true, sourceType: 'note' },
  { id: '4', title: '接待任务', category: 'meeting', order: 4 },
  { id: '5', title: '项目评审', category: 'work', order: 5, isReference: true, sourceType: 'schedule' },
  { id: '6', title: '外出调研', category: 'trip', order: 6 },
];

// Assuming a RootStackParamList that will be defined in App.tsx or a navigation types file
type RootStackParamList = {
  Home: undefined;
  AllStickyNotes: { notes: StickyNote[] };
  // ... other screens
};

type Props = NativeStackScreenProps<RootStackParamList, 'AllStickyNotes'>;

const AllStickyNotesScreen = ({ route: _route, navigation: _navigation }: Props) => {
  // const { notes } = route.params;
  const notes = mockNotes; // Using mock data for now

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>全部便签</Text>
      </View>
      <FlatList
        data={notes}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <View style={styles.noteItem}>
            <Text style={styles.noteTitle}>{item.title}</Text>
            {item.isReference && (
              <Text style={styles.referenceIndicator}>
                {item.sourceType === 'schedule' ? '日程引用' :
                 item.sourceType === 'note' ? '笔记引用' :
                 item.sourceType === 'file' ? '文件引用' :
                 item.sourceType === 'photo' ? '图片引用' : '引用'}
              </Text>
            )}
          </View>
        )}
        contentContainerStyle={styles.listContainer}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.backgroundPrimary,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.backgroundTertiary,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: theme.fonts.titleMedium,
    fontWeight: theme.fontWeights.bold,
    color: theme.colors.textPrimary,
  },
  listContainer: {
    padding: theme.spacing.lg,
  },
  noteItem: {
    backgroundColor: theme.colors.backgroundSecondary,
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.medium,
    marginBottom: theme.spacing.md,
  },
  noteTitle: {
    fontSize: theme.fonts.bodyLarge,
    color: theme.colors.textPrimary,
  },
  referenceIndicator: {
    fontSize: theme.fonts.bodySmall,
    color: theme.colors.textSecondary,
    marginTop: 4,
    fontStyle: 'italic',
  },
});

export default AllStickyNotesScreen;
