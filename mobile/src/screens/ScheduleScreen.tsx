import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SectionList,
  Dimensions,
  ScrollView,
  StatusBar,
} from 'react-native';
import { GestureHandlerRootView, GestureDetector, Gesture } from 'react-native-gesture-handler';
import { runOnJS } from 'react-native-reanimated';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { format, isSameMonth, isToday, addWeeks, subWeeks, isWithinInterval, startOfWeek, endOfWeek } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { getCalendarGridData, getWeekGridData } from '../utils/calendar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';

const { width, height } = Dimensions.get('window');

// Calculate optimal grid item height based on screen size
const calculateGridItemHeight = (screenHeight: number, insets: any, hasLeaderSelector: boolean) => {
  // Fixed space occupied by other components
  const headerHeight = 80; // Header + status bar
  const leaderSelectorHeight = hasLeaderSelector ? 50 : 0;
  const bottomTabHeight = 90; // Bottom tab + safe area
  const gridPadding = 8; // Grid container padding
  const gridMargins = 32; // 4 rows × 8px margin per row

  // Available height for grid content
  const availableHeight = screenHeight - headerHeight - leaderSelectorHeight - bottomTabHeight - gridPadding - gridMargins;

  // Calculate height for 4 rows of grid items
  const calculatedHeight = availableHeight / 4;

  // Set minimum height to ensure mini calendar displays properly
  // Mini calendar needs: week header (20px) + max 6 rows of dates (120px) + padding (14px) = 154px
  // Add some buffer for comfortable viewing
  const minHeight = 160; // Ensure mini calendar has enough space
  const maxHeight = (width - 24) / 2 * 0.9; // Maximum height for good proportions

  return Math.max(minHeight, Math.min(maxHeight, calculatedHeight));
};

// Haptic feedback options
const hapticOptions = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

// --- Type Definitions ---
type Role = 'host' | 'assist' | 'attend' | null;

type ScheduleItem = {
  id: string;
  time: string;
  title: string;
  role: Role;
};

type ScheduleGrid = {
  [key: string]: {
    am: Role;
    pm: Role;
  }
};

// New mock data structure for SectionList
type AgendaSection = {
  title: string; // e.g., "6月25日 星期三"
  data: ScheduleItem[];
};

// Mock Data
const MOCK_LEADERS = ['张晓虎', '戴小平', '李立光', '孙建国', '周华健'];

// New data structure to hold schedules for multiple leaders
const MOCK_LEADER_SCHEDULES: { [leader: string]: { grid: ScheduleGrid, agenda: AgendaSection[] } } = {
  '张晓虎': {
    grid: { /* 张晓虎的周视图数据 */ },
    agenda: [
        { title: '6月23日 星期一', data: [{ id: '1', time: '09:00', title: '【张】推进会', role: 'host' }] },
        { title: '6月24日 星期二', data: [{ id: '2', time: '14:00', title: '【张】接待调研组', role: 'attend' }] },
    ],
  },
  '戴小平': {
    grid: { /* 戴小平的周视图数据 */ },
    agenda: [
        { title: '6月25日 星期三', data: [{ id: '3', time: '10:00', title: '【戴】专题研究', role: 'host' }] },
    ],
  },
  '李立光': { grid: {}, agenda: [] },
  '孙建国': { grid: {}, agenda: [] },
  '周华健': { grid: {}, agenda: [] },
  'my': { // User's own schedule
    grid: { /* 张晓虎的周视图数据 */ },
    agenda: [
        { title: '6月23日 星期一', data: [{ id: '1', time: '09:00', title: '【张】推进会', role: 'host' }] },
        { title: '6月24日 星期二', data: [{ id: '2', time: '14:00', title: '【张】接待调研组', role: 'attend' }] },
    ],
  },
};

// --- Main Component ---
const ScheduleScreen = () => {
  const navigation = useNavigation<any>();
  const insets = useSafeAreaInsets();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState<'my' | 'leader' | 'station'>('my');
  const [selectedLeader, setSelectedLeader] = useState<string>('my');

  // Determine which schedule to display based on active tab and selected leader
  const activeScheduleKey = activeTab === 'my' ? 'my' : selectedLeader;
  const currentSchedule = MOCK_LEADER_SCHEDULES[activeScheduleKey] || { grid: {}, agenda: [] };

  const calendarGrid = useMemo(() => getCalendarGridData(currentDate), [currentDate]);
  const weekGrid = useMemo(() => getWeekGridData(currentDate), [currentDate]);

  // Calculate dynamic grid item height
  const hasLeaderSelector = activeTab === 'leader';
  const gridItemHeight = useMemo(() =>
    calculateGridItemHeight(height, insets, hasLeaderSelector),
    [insets, hasLeaderSelector]
  );

  const toggleViewMode = () => {
    ReactNativeHapticFeedback.trigger('impactLight', hapticOptions);
    setViewMode(viewMode === 'grid' ? 'list' : 'grid');
  };

  const handleWeekChange = (direction: 'next' | 'prev') => {
    ReactNativeHapticFeedback.trigger('impactMedium', hapticOptions);
    setCurrentDate(current => (direction === 'next' ? addWeeks(current, 1) : subWeeks(current, 1)));
  };

  const panGesture = Gesture.Pan()
    .minDistance(10)
    .onEnd((e) => {
      // Vertical swipe for week change
      if (e.translationY < -50) { // Swipe Up
        runOnJS(handleWeekChange)('next');
      } else if (e.translationY > 50) { // Swipe Down
        runOnJS(handleWeekChange)('prev');
      }
    });

  const handleLeaderSelect = (leader: string) => {
    ReactNativeHapticFeedback.trigger('impactLight', hapticOptions);
    setSelectedLeader(leader);
    // Also switch to the leader tab if it's not already active
    if (activeTab !== 'leader') {
      setActiveTab('leader');
    }
  };

  const handleTabPress = (tab: 'my' | 'leader' | 'station') => {
    ReactNativeHapticFeedback.trigger('impactLight', hapticOptions);
    setActiveTab(tab);
  };

  const handleDatePress = (day: Date) => {
    ReactNativeHapticFeedback.trigger('impactLight', hapticOptions);
    setCurrentDate(day);
  };

  const handleLongPress = (date: string) => {
    ReactNativeHapticFeedback.trigger('impactHeavy', hapticOptions);
    navigation.navigate('DayDetail', { date });
  };

  const handleHeaderPress = () => {
    ReactNativeHapticFeedback.trigger('impactLight', hapticOptions);
    navigation.navigate('FullScreenMonth', { date: currentDate.toISOString() });
  };

  const handleTodayPress = () => {
    ReactNativeHapticFeedback.trigger('impactMedium', hapticOptions);
    setCurrentDate(new Date());
  };

  const handleAddPress = () => {
    ReactNativeHapticFeedback.trigger('impactMedium', hapticOptions);
    // 跳转到聚合式创作入口，初始Tab为日程，context传递当前日期
    navigation.navigate('CreationScreen', {
      initialTab: 'schedule',
      context: { from: 'ScheduleScreen', date: currentDate.toISOString() },
    });
  };

  // --- Render Functions ---

  const renderHeader = () => (
    <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
       <TouchableOpacity onPress={handleHeaderPress} activeOpacity={0.7}>
        <Text style={styles.headerTitle}>{format(currentDate, 'yyyy年 M月', { locale: zhCN })}</Text>
       </TouchableOpacity>
      <View style={styles.headerActions}>
        <TouchableOpacity style={styles.headerButton} onPress={handleTodayPress} activeOpacity={0.7}>
          <Text style={styles.headerActionText}>本周</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.headerButton} onPress={toggleViewMode} activeOpacity={0.7}>
          <Ionicons name={viewMode === 'grid' ? 'list-outline' : 'grid-outline'} size={24} color="#FF8C00" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.headerButton} onPress={handleAddPress} activeOpacity={0.7}>
           <Ionicons name="add-circle-outline" size={26} color="#FF8C00" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderLeaderSelector = () => {
    if (activeTab !== 'leader') {return null;}

    return (
      <View style={styles.leaderSelectorContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {MOCK_LEADERS.map(leader => (
            <TouchableOpacity
              key={leader}
              style={[
                styles.leaderTab,
                selectedLeader === leader && styles.leaderTabActive,
              ]}
              onPress={() => handleLeaderSelect(leader)}
              activeOpacity={0.7}
            >
              <Text style={[
                styles.leaderTabText,
                selectedLeader === leader && styles.leaderTabTextActive,
              ]}>{leader}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const getRoleStyle = (role: Role) => {
    switch (role) {
      case 'host': return styles.roleHost;
      case 'assist': return styles.roleAssist;
      case 'attend': return styles.roleAttend;
      default: return {};
    }
  };

  const renderGridView = () => {
    const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 });

     // First item is always the month calendar
    const monthView = (
      <View style={[styles.gridItem, { height: gridItemHeight }]}>
        {/* Week header */}
        <View style={styles.weekHeaderContainer}>
          {['一', '二', '三', '四', '五', '六', '日'].map((day, index) => (
            <View key={index} style={styles.weekHeaderDay}>
              <Text style={styles.weekHeaderText}>{day}</Text>
            </View>
          ))}
        </View>
        <View style={styles.monthGridContainer}>
          {calendarGrid.map((day, index) => {
            const isDayInSelectedWeek = day ? isWithinInterval(day, { start: weekStart, end: weekEnd }) : false;
            return (
              <TouchableOpacity
                key={index}
                style={[styles.monthDayContainer, isDayInSelectedWeek && styles.weekHighlight]}
                onPress={() => day && handleDatePress(day)}
                activeOpacity={0.7}
              >
                {day ? (
                  <Text style={[
                    styles.monthDayText,
                    !isSameMonth(day, currentDate) && styles.monthDayTextNotInMonth,
                    isToday(day) && styles.monthDayTextToday,
                  ]}>
                    {format(day, 'd')}
                  </Text>
                ) : <View />}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );

    const dayViews = weekGrid.map((day, _index) => {
      const dayKey = format(day, 'yyyy-MM-dd'); // Use a consistent key format
      const schedule = currentSchedule.grid[dayKey] || { am: null, pm: null };

      return (
        <TouchableOpacity
          key={day.toISOString()}
          style={[styles.gridItem, { height: gridItemHeight }]}
          onLongPress={() => handleLongPress(day.toISOString())}
          activeOpacity={0.8}
        >
          <Text style={styles.dayText}>{format(day, 'EEE d', { locale: zhCN })}</Text>
           <View style={[styles.halfDay, getRoleStyle(schedule.am)]} />
           <View style={[styles.halfDay, getRoleStyle(schedule.pm)]} />
        </TouchableOpacity>
      );
    });

    return (
       <GestureDetector gesture={panGesture}>
        <View style={styles.gridContainer}>
            {monthView}
            {dayViews}
        </View>
      </GestureDetector>
    );
  };

  const renderListView = () => (
    <SectionList
      sections={currentSchedule.agenda}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <TouchableOpacity
          style={styles.listItem}
          onPress={() => {
            ReactNativeHapticFeedback.trigger('impactLight', hapticOptions);
            // Handle item press if needed
          }}
          activeOpacity={0.7}
        >
          <Text style={styles.listTime}>{item.time}</Text>
          <View style={[styles.listIndicator, getRoleStyle(item.role)]} />
          <Text style={styles.listTitle}>{item.title}</Text>
        </TouchableOpacity>
      )}
      renderSectionHeader={({ section: { title } }) => (
        <Text style={styles.listSectionHeader}>{title}</Text>
      )}
      renderSectionFooter={() => <View style={{ height: 1, backgroundColor: '#3a3a3c' }} />}
      ListEmptyComponent={() => (
          <View style={styles.emptyListContainer}>
              <Text style={styles.emptyListText}>本周无日程安排</Text>
          </View>
      )}
    />
  );

  const renderBottomTabBar = () => (
    <View style={[styles.bottomTabBar, { paddingBottom: insets.bottom + 8 }]}>
       <TouchableOpacity
         style={styles.bottomTab}
         onPress={() => {
           ReactNativeHapticFeedback.trigger('impactLight', hapticOptions);
           navigation.goBack();
         }}
         activeOpacity={0.7}
       >
        <Ionicons name="arrow-back-outline" size={24} color={'#666666'} />
        <Text style={[styles.bottomTabText, { color: '#666666'}]}>返回</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.bottomTab}
        onPress={() => handleTabPress('my')}
        activeOpacity={0.7}
      >
        <Ionicons name="person-outline" size={24} color={activeTab === 'my' ? '#FF8C00' : '#666666'} />
        <Text style={[styles.bottomTabText, { color: activeTab === 'my' ? '#FF8C00' : '#666666' }]}>我的</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.bottomTab}
        onPress={() => handleTabPress('leader')}
        activeOpacity={0.7}
      >
        <Ionicons name="people-outline" size={24} color={activeTab === 'leader' ? '#FF8C00' : '#666666'} />
        <Text style={[styles.bottomTabText, { color: activeTab === 'leader' ? '#FF8C00' : '#666666' }]}>领导</Text>
      </TouchableOpacity>
       <TouchableOpacity
         style={styles.bottomTab}
         onPress={() => handleTabPress('station')}
         activeOpacity={0.7}
       >
        <Ionicons name="archive-outline" size={24} color={activeTab === 'station' ? '#FF8C00' : '#666666'} />
        <Text style={[styles.bottomTabText, { color: activeTab === 'station' ? '#FF8C00' : '#666666' }]}>收集站</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={{flex: 1}}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <GestureHandlerRootView style={{flex: 1}}>
        <View style={styles.container}>
        {renderHeader()}
        {renderLeaderSelector()}
        <View style={styles.contentContainer}>
            {viewMode === 'grid' ? renderGridView() : renderListView()}
        </View>
        {renderBottomTabBar()}
        </View>
      </GestureHandlerRootView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7', // 浅色背景
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  headerTitle: {
    color: '#000000',
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  headerButton: {
    padding: 4,
  },
  headerActionText: {
    color: '#FF8C00',
    fontSize: 17,
    fontWeight: '500',
  },
  gridContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 4,
    justifyContent: 'space-around',
  },
  gridItem: {
    width: (width - 24) / 2, // 2 items per row, with padding
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 14,
    margin: 4,
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2.22,
    elevation: 3,
  },
  currentMonthGridItem: {
    backgroundColor: '#FFFFFF',
    borderColor: '#FFDDC1',
    borderWidth: 1.5,
  },
  dayText: {
    color: '#000000',
    fontSize: 15,
    fontWeight: 'bold',
  },
  halfDay: {
    width: '100%',
    height: '40%',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  listViewContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  agendaItem: {
    backgroundColor: '#2d2d2d',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 5,
  },
  agendaTime: {
      color: '#9E9E9E',
      fontSize: 12,
      marginBottom: 4,
  },
  agendaTitle: {
      color: '#FFFFFF',
      fontSize: 16,
  },
  bottomTabBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    backgroundColor: '#FFFFFF',
    paddingTop: 8,
  },
  bottomTab: {
    alignItems: 'center',
  },
  bottomTabText: {
    fontSize: 12,
    fontWeight: '400',
    marginTop: 4,
  },
  weekHeaderContainer: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  weekHeaderDay: {
    width: '14.2%',
    alignItems: 'center',
    justifyContent: 'center',
    height: 16,
  },
  weekHeaderText: {
    color: '#8E8E93',
    fontSize: 10,
    fontWeight: '500',
  },
  monthGridContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  monthDayContainer: {
    width: '14.2%',
    alignItems: 'center',
    justifyContent: 'center',
    height: 20,
  },
  monthDayText: {
    color: '#1C1C1E',
    fontSize: 13,
  },
  monthDayTextNotInMonth: {
    color: '#C7C7CC',
  },
  monthDayTextToday: {
    backgroundColor: '#FF8C00',
    color: '#FFFFFF',
    borderRadius: 10,
    width: 20,
    height: 20,
    textAlign: 'center',
    lineHeight: 20,
    overflow: 'hidden',
  },
  listSectionHeader: {
    backgroundColor: '#F2F2F7',
    paddingVertical: 8,
    paddingHorizontal: 16,
    fontSize: 14,
    fontWeight: 'bold',
    color: '#3C3C43',
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  listTime: {
    color: '#6B7280',
    fontSize: 13,
    marginRight: 16,
  },
  listIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 16,
  },
  listTitle: {
    color: '#000000',
    fontSize: 17,
    fontWeight: '500',
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyListText: {
    color: '#6B7280',
    fontSize: 16,
  },
  roleHost: {
    backgroundColor: 'rgba(255, 71, 87, 0.25)', //淡红色
  },
  roleAssist: {
    backgroundColor: 'rgba(255, 204, 0, 0.25)', //淡黄色
  },
  roleAttend: {
    backgroundColor: 'rgba(52, 199, 89, 0.25)', //淡绿色
  },
  contentContainer: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  weekHighlight: {
    backgroundColor: '#FFF7E6', // Light orange for the selected week
    borderRadius: 4,
  },
  leaderSelectorContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginTop: 8,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  leaderTab: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
    marginRight: 8,
    backgroundColor: '#F2F2F7',
  },
  leaderTabActive: {
    backgroundColor: '#FF8C00',
  },
  leaderTabText: {
    color: '#000000',
    fontWeight: '500',
  },
  leaderTabTextActive: {
    color: '#FFFFFF',
  },
});

export default ScheduleScreen;

