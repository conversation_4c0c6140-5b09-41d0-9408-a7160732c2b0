import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Image,
  Modal,
  Keyboard,
  Alert,
  Linking,
  useWindowDimensions,
  StatusBar,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RootStackParamList } from '../../App';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MediaPreviewView from '../components/MediaPreviewView';
import ChatActionPanel, { getActionPanelHeight } from '../components/InputPanel/ChatActionPanel';
import Animated, { useAnimatedStyle, withTiming, useSharedValue } from 'react-native-reanimated';
import Clipboard from '@react-native-clipboard/clipboard';
import Share from 'react-native-share'; // Import from react-native-share
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import { getAllUserAppMessages, parseExt, parseParticipants, addMessage } from '../db/messageService';
// 新增组件和工具函数引用
import ChatHeader from '../components/Chat/ChatHeader';
import FilePickerService from '../services/FilePickerService';
import FileStorageService, { FileUploadProgress as ProgressData, FileStorageService as FileStorageServiceClass } from '../services/FileStorageService';
import FilePreviewService from '../services/FilePreviewService';
import { FileErrorDialog } from '../components/FileErrorDialog';
import FileMessageBubble, { FileMessageData } from '../components/FileMessageBubble';
import FileUploadProgress from '../components/FileUploadProgress';
import BatchFileUploadProgress, { FileUploadItem } from '../components/BatchFileUploadProgress';
import FileManagementDialog from '../components/FileManagementDialog';
import { FileErrorHandler, FileErrorType } from '../utils/fileErrorHandler';
import MessageCard from '../components/Chat/MessageCard';
import MessageUrl from '../components/Chat/MessageUrl';
import BubbleMenu from '../components/Chat/BubbleMenu';
import { shouldShowTimestamp, formatTimestamp, Message as ChatMessageType } from '../utils/chat';
import { styles } from '../components/Chat/styles';
import { defaultTheme } from '../styles/theme';
import { useChatMessages } from '../hooks/useChatMessages';
import { useAudioPlayer } from '../hooks/useAudioPlayer';
import { useImageViewer } from '../hooks/useImageViewer';
import { useGlobalImagePickerStore } from '../stores/globalImagePickerStore';
// 移除不存在的配置导入
import ParsedText from 'react-native-parsed-text';
import { SafeAreaView } from 'react-native-safe-area-context';
import { getWeChatBindingService } from '../services/WeChatServiceRegistry';
import JPushService from '../services/JPushService';

const audioRecorderPlayer = new AudioRecorderPlayer(); // 组件外部定义，防止作用域问题

// 1. 单图图片消息独立组件，处理宽高自适应和阴影
const SingleImageMessage = React.memo(({ uri, onPress, windowWidth }: { uri: string, onPress: () => void, windowWidth: number }) => {
  const [imgSize, setImgSize] = React.useState<{ w: number, h: number } | null>(null);
  React.useEffect(() => {
    let mounted = true;
    Image.getSize(uri, (w, h) => { if (mounted) {setImgSize({ w, h });} }, () => { });
    return () => { mounted = false; };
  }, [uri]);
  let displayW = 120, displayH = 120;
  if (imgSize) {
    const aspect = imgSize.w / imgSize.h;
    if (aspect >= 1) {
      displayW = Math.min(220, imgSize.w, windowWidth * 0.6);
      displayH = Math.min(displayW / aspect, 200);
    } else {
      displayH = Math.min(200, imgSize.h);
      displayW = Math.min(displayH * aspect, 150);
    }
  }
  return (
    <TouchableOpacity onPress={onPress}>
      <View style={{
        borderRadius: 12,
        backgroundColor: '#181818',
        shadowColor: '#000',
        shadowOpacity: 0.08,
        shadowRadius: 6,
        shadowOffset: { width: 0, height: 2 },
      }}>
        <Image
          source={{ uri }}
          style={{
            width: displayW,
            height: displayH,
            borderRadius: 12,
            backgroundColor: '#181818',
          }}
          resizeMode="cover"
        />
      </View>
    </TouchableOpacity>
  );
});

const ChatScreen = () => {
  const [inputText, setInputText] = useState('');
  const [menuState, setMenuState] = useState<{ visible: boolean; position: {x: number, y: number}; item: ChatMessageType | null; }>({ visible: false, position: { x: 0, y: 0 }, item: null });
  const [isActionPanelVisible, setActionPanelVisible] = useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  // 🆕 文件上传进度状态
  const [uploadProgress, setUploadProgress] = useState<{
    visible: boolean;
    fileName: string;
    progress: ProgressData | null;
  }>({
    visible: false,
    fileName: '',
    progress: null,
  });

  // 🆕 批量文件上传状态
  const [batchUpload, setBatchUpload] = useState<{
    visible: boolean;
    files: FileUploadItem[];
  }>({
    visible: false,
    files: [],
  });

  // 🆕 文件管理状态
  const [fileManagement, setFileManagement] = useState<{
    visible: boolean;
    fileData: FileMessageData | null;
  }>({
    visible: false,
    fileData: null,
  });

  const theme = defaultTheme; // 恢复 theme 变量定义
  const flatListRef = useRef<FlatList<ChatMessageType>>(null);

  // Animation for Action Panel
  const actionPanelHeight = useSharedValue(0);
  const animatedPanelStyle = useAnimatedStyle(() => {
    return {
      height: actionPanelHeight.value,
      opacity: actionPanelHeight.value === 0 ? 0 : 1,
    };
  });

  // --- Start of refined interaction logic ---

  const scrollToEnd = useCallback((animated = true) => {
    // A short delay ensures the layout has updated before scrolling
    setTimeout(() => {
      flatListRef.current?.scrollToOffset({ offset: 0, animated });
    }, 100);
  }, []);

  // Closes bubble menu and action panel, but NOT keyboard.
  // This is used when focusing the text input to avoid keyboard flicker.
  const dismissPanels = () => {
    if (isActionPanelVisible) {
      actionPanelHeight.value = withTiming(0, { duration: 250 });
      setActionPanelVisible(false);
    }
    if (menuState.visible) {
      setMenuState({ visible: false, position: { x: 0, y: 0 }, item: null });
    }
  };

  // The main dismiss function for scroll/tap on list. Closes everything.
  const dismissAllInputs = () => {
    Keyboard.dismiss();
    dismissPanels();
  };

  const toggleActionPanel = () => {
    // If we are about to open the panel, first dismiss keyboard and menu.
    if (!isActionPanelVisible) {
      Keyboard.dismiss();
      if (menuState.visible) {
        setMenuState({ visible: false, position: { x: 0, y: 0 }, item: null });
      }
      scrollToEnd(); // Scroll to end when opening panel
    }

    // Now toggle the panel visibility
    const targetHeight = isActionPanelVisible ? 0 : getActionPanelHeight();
    actionPanelHeight.value = withTiming(targetHeight, { duration: 250 });
    setActionPanelVisible(!isActionPanelVisible);
  };

  // --- End of refined interaction logic ---

  // 🆕 智能文件选择处理函数 - 支持单个或多个文件，根据用户实际选择智能处理
  const handleSmartFileSelect = async () => {
    try {
      // iOS平台直接选择文件，Android平台显示多选提示
      if (Platform.OS === 'ios') {
        // iOS用户熟悉系统文件选择器，直接调用
        const fileResults = await FilePickerService.pickFile({
          allowMultiSelection: true,
          maxFiles: 5,
          maxSizeBytes: 100 * 1024 * 1024, // 100MB - 适应政务文档
        });

        if (fileResults && fileResults.length > 0) {
          if (fileResults.length === 1) {
            // 用户只选择了一个文件，使用单文件模式展示
            await handleSingleFileUpload(fileResults[0]);
          } else {
            // 用户选择了多个文件，使用批量上传模式展示
            await handleBatchFileUpload(fileResults);
          }
          await refreshMessages();
        }
      } else {
        // Android平台显示简化的多选提示
        Alert.alert(
          '选择文件',
          '支持选择多个文件，最多5个',
          [
            { text: '取消', style: 'cancel' },
            {
              text: '开始选择',
              onPress: async () => {
                // 调用文件选择服务 - 允许多选，最多5个文件
                const fileResults = await FilePickerService.pickFile({
                  allowMultiSelection: true,
                  maxFiles: 5,
                  maxSizeBytes: 100 * 1024 * 1024, // 100MB - 适应政务文档
                });

                if (fileResults && fileResults.length > 0) {
                  if (fileResults.length === 1) {
                    // 用户只选择了一个文件，使用单文件模式展示
                    await handleSingleFileUpload(fileResults[0]);
                  } else {
                    // 用户选择了多个文件，使用批量上传模式展示
                    await handleBatchFileUpload(fileResults);
                  }
                  await refreshMessages();
                }
              },
            },
          ]
        );
      }
    } catch (error) {
      console.error('[ChatScreen] 文件选择失败:', error);
      // 错误处理已在FilePickerService中处理，这里不需要重复显示
    }
  };

  /**
   * 处理单个文件上传
   */
  const handleSingleFileUpload = async (file: any) => {
    try {
      // 显示上传进度
      setUploadProgress({
        visible: true,
        fileName: file.name,
        progress: {
          bytesWritten: 0,
          totalBytes: file.size,
          progress: 0,
        },
      });

      // 保存文件到本地存储（带进度回调）
      const savedFile = await FileStorageService.saveFile(file, (progress) => {
        setUploadProgress(prev => ({
          ...prev,
          progress,
        }));
      });

      // 创建文件消息 - 使用相对路径存储，解决iOS覆盖安装问题
      await addMessage({
        type: 'file',
        content: file.name,
        ext: JSON.stringify({
          fileData: {
            uri: savedFile.relativePath, // 🔧 使用相对路径而非绝对路径
            name: file.name,
            size: file.size,
            type: file.type,
            originalUri: file.uri,
          },
        }),
        createdAt: Date.now(),
        sender: 'user',
        participants: JSON.stringify(['user', 'assistant']),
        sessionId: 'default',
      });

      // 延迟隐藏进度对话框，让用户看到完成状态
      setTimeout(() => {
        setUploadProgress({
          visible: false,
          fileName: '',
          progress: null,
        });
      }, 1000);

    } catch (fileError) {
      console.error('[ChatScreen] 单个文件处理失败:', fileError);
      // 隐藏进度对话框
      setUploadProgress({
        visible: false,
        fileName: '',
        progress: null,
      });
      FileErrorHandler.handleError(fileError, `文件 ${file.name} 保存`);
    }
  };

  /**
   * 处理批量文件上传
   */
  const handleBatchFileUpload = async (files: any[]) => {
    // 初始化批量上传状态
    const uploadItems: FileUploadItem[] = files.map((file, index) => ({
      id: `${Date.now()}_${index}`,
      name: file.name,
      size: file.size,
      status: 'waiting',
      progress: 0,
    }));

    setBatchUpload({
      visible: true,
      files: uploadItems,
    });

    // 逐个处理文件
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const itemId = uploadItems[i].id;

      try {
        // 更新状态为上传中
        setBatchUpload(prev => ({
          ...prev,
          files: prev.files.map(item =>
            item.id === itemId
              ? { ...item, status: 'uploading', progress: 0 }
              : item
          ),
        }));

        // 保存文件到本地存储（带进度回调）
        const savedFile = await FileStorageService.saveFile(file, (progress) => {
          setBatchUpload(prev => ({
            ...prev,
            files: prev.files.map(item =>
              item.id === itemId
                ? { ...item, progress: progress.progress }
                : item
            ),
          }));
        });

        // 创建文件消息 - 使用相对路径存储，解决iOS覆盖安装问题
        await addMessage({
          type: 'file',
          content: file.name,
          ext: JSON.stringify({
            fileData: {
              uri: savedFile.relativePath, // 🔧 使用相对路径而非绝对路径
              name: file.name,
              size: file.size,
              type: file.type,
              originalUri: file.uri,
            },
          }),
          createdAt: Date.now(),
          sender: 'user',
          participants: JSON.stringify(['user', 'assistant']),
          sessionId: 'default',
        });

        // 更新状态为完成
        setBatchUpload(prev => ({
          ...prev,
          files: prev.files.map(item =>
            item.id === itemId
              ? { ...item, status: 'completed', progress: 1 }
              : item
          ),
        }));

      } catch (fileError) {
        console.error('[ChatScreen] 批量上传文件失败:', fileError);

        // 更新状态为失败
        setBatchUpload(prev => ({
          ...prev,
          files: prev.files.map(item =>
            item.id === itemId
              ? {
                  ...item,
                  status: 'failed',
                  error: '上传失败，请重试',
                }
              : item
          ),
        }));
      }
    }
  };

  /**
   * 重试单个文件上传
   */
  const handleRetryFileUpload = async (fileId: string) => {
    const uploadItem = batchUpload.files.find(f => f.id === fileId);
    if (!uploadItem) {return;}

    // 这里需要保存原始文件信息，暂时显示提示
    FileErrorHandler.showError(
      FileErrorType.UNKNOWN_ERROR,
      '重试功能正在开发中，请重新选择文件。'
    );
  };

  /**
   * 取消批量上传
   */
  const handleCancelBatchUpload = () => {
    setBatchUpload({
      visible: false,
      files: [],
    });
  };

  /**
   * 显示文件管理对话框
   */
  const handleFileManagement = (fileData: FileMessageData) => {
    setFileManagement({
      visible: true,
      fileData,
    });
  };

  /**
   * 关闭文件管理对话框
   */
  const handleCloseFileManagement = () => {
    setFileManagement({
      visible: false,
      fileData: null,
    });
  };

  /**
   * 删除文件
   */
  const handleDeleteFile = async (fileData: FileMessageData) => {
    try {
      // 删除本地文件
      await FileStorageServiceClass.deleteFile(fileData.uri);

      // 刷新消息列表
      await refreshMessages();

      Alert.alert('成功', '文件已删除');
    } catch (error) {
      console.error('[ChatScreen] 删除文件失败:', error);
      FileErrorHandler.handleError(error, '文件删除');
    }
  };

  /**
   * 重命名文件
   */
  const handleRenameFile = async (fileData: FileMessageData, newName: string) => {
    try {
      // 重命名本地文件
      await FileStorageServiceClass.renameFile(fileData.uri, newName);

      // 刷新消息列表
      await refreshMessages();

      Alert.alert('成功', '文件已重命名');
    } catch (error) {
      console.error('[ChatScreen] 重命名文件失败:', error);
      FileErrorHandler.handleError(error, '文件重命名');
    }
  };

  /**
   * 分享文件
   */
  const handleShareFile = async (fileData: FileMessageData) => {
    try {
      await FileStorageServiceClass.shareFile(fileData.uri, fileData.name);
    } catch (error) {
      console.error('[ChatScreen] 分享文件失败:', error);
      FileErrorHandler.handleError(error, '文件分享');
    }
  };

  // 🆕 文件预览处理函数 - 智能路径处理和用户友好错误提示
  const handleFilePreview = async (fileData: FileMessageData) => {
    try {
      console.log('[ChatScreen] 开始预览文件:', fileData.name);

      // 使用智能路径获取，支持自动迁移
      const validPath = await FileStorageService.getValidFilePath(fileData.uri);
      if (!validPath) {
        // 文件确实不存在，显示友好的错误提示
        FileErrorDialog.showFileMissingDialog({
          fileName: fileData.name,
          errorType: 'missing',
          showReuploadOption: true,
          onReupload: () => {
            // TODO: 触发重新上传流程
            console.log('[ChatScreen] 用户选择重新上传文件:', fileData.name);
            // 这里可以触发文件选择器，让用户重新上传
          },
          onDismiss: () => {
            console.log('[ChatScreen] 用户确认文件丢失提示');
          },
        });
        return;
      }

      // 如果路径发生了变化，更新fileData
      const updatedFileData = validPath !== fileData.uri
        ? { ...fileData, uri: validPath }
        : fileData;

      // 🔧 使用FilePreviewService进行预览
      console.log('[ChatScreen] 🚀 开始调用FilePreviewService.previewFile');
      const previewResult = await FilePreviewService.previewFile(updatedFileData, {
        onSuccess: () => {
          console.log('[ChatScreen] ✅ 原生预览成功:', fileData.name);
        },
        onError: (error) => {
          console.error('[ChatScreen] ❌ 预览失败:', error);
          // FilePreviewService已经处理了错误显示，这里不需要重复显示
        },
      });

      console.log('[ChatScreen] 📋 预览策略结果:', previewResult);

      // 根据预览策略决定下一步操作
      if (previewResult.strategy === 'custom') {
        console.log('[ChatScreen] 🎯 使用自定义预览器，导航到FilePreviewScreen');
        console.log('[ChatScreen] 📄 文件信息:', {
          name: updatedFileData.name,
          type: updatedFileData.type,
          size: updatedFileData.size,
          fileType: previewResult.fileType,
          uri: updatedFileData.uri.substring(0, 50) + '...',
        });
        navigation.navigate('FilePreviewScreen', { fileData: updatedFileData });
      } else if (previewResult.strategy === 'native') {
        console.log('[ChatScreen] ✅ 原生预览已完成');
      } else if (previewResult.strategy === 'error') {
        console.error('[ChatScreen] ❌ 预览失败，错误已由FilePreviewService处理');
      }

    } catch (error) {
      console.error('[ChatScreen] 文件预览异常:', error);
      FileErrorHandler.handleError(error, '文件预览');
    }
  };

  const handleActionSelect = (key: string) => {
    if (key === 'album') {
      const { openPicker } = useGlobalImagePickerStore.getState();
      const maxImages = 9; // 默认最大图片数量
      openPicker({
        mode: 'multiple',
        selectionLimit: maxImages,
        onSelect: async (uris) => {
          console.log('[ChatScreen] onSelect uris:', uris);
          if (uris && uris.length > 0) {
            try {
              const result = await addMessage({
                type: 'image',
                ext: JSON.stringify({ mediaUris: uris }),
                createdAt: Date.now(),
                sender: 'user',
                participants: JSON.stringify(['user', 'assistant']),
                sessionId: 'default',
              });
              console.log('[ChatScreen] addMessage result:', result);
              await refreshMessages();
            } catch (err) {
              console.error('[ChatScreen] addMessage error:', err);
            }
          }
        },
      });
      toggleActionPanel();
      return;
    }

    // 🆕 文件选择处理 - 直接进入智能文件选择
    if (key === 'file') {
      handleSmartFileSelect();
      toggleActionPanel();
      return;
    }

    // 其他 action...
    toggleActionPanel();
  };

  // 使用 useChatMessages hook
  const { messages, refreshMessages, handleSend } = useChatMessages({
    getAllUserAppMessages,
    parseExt,
    parseParticipants,
  });

  // Keyboard visibility tracking for iOS SafeAreaView optimization
  useEffect(() => {
    if (Platform.OS !== 'ios') {return;}

    const keyboardWillShowListener = Keyboard.addListener('keyboardWillShow', () => {
      setIsKeyboardVisible(true);
    });
    const keyboardWillHideListener = Keyboard.addListener('keyboardWillHide', () => {
      setIsKeyboardVisible(false);
    });

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  // Scroll to end when new messages are added
  useEffect(() => {
    if (messages.length > 0) {
      scrollToEnd();
    }
  }, [messages, scrollToEnd]);

  // 微信消息同步服务检查（App启动时已初始化，这里只做状态检查）
  useEffect(() => {
    const checkServices = async () => {
      try {
        // 检查极光推送服务状态
        if (!JPushService.isReady()) {
          console.log('[ChatScreen] JPush服务未就绪，可能影响消息推送');
        } else {
          console.log('[ChatScreen] JPush服务已就绪');
        }

        // 检查微信绑定状态（不重复启动服务，只记录状态）
        const bindingService = getWeChatBindingService();
        const canSync = await bindingService.canSync();
        if (canSync) {
          console.log('[ChatScreen] 微信已绑定，消息同步服务应该已在App启动时启动');
        } else {
          console.log('[ChatScreen] 微信未绑定，消息同步服务未启动');
        }
      } catch (error) {
        console.error('[ChatScreen] 检查服务状态失败:', error);
      }
    };

    checkServices();

    // 清理函数（保留，但不在这里停止全局服务）
    return () => {
      // 注意：不在这里停止全局服务，因为其他页面可能还需要
      console.log('[ChatScreen] 页面卸载，但保持微信服务运行');
    };
  }, []);

  const handleMenuSelect = async (action: string) => {
    const message = menuState.item;
    if (!message) {return;}

    setMenuState({ visible: false, position: { x: 0, y: 0 }, item: null });

    switch (action) {
      case '复制':
        // Note: Copy is only available for text and url messages, as defined in BubbleMenu
        if (message.type === 'text') {
          Clipboard.setString(message.text);
        } else if (message.type === 'url' && message.urlData) {
          Clipboard.setString(message.urlData.url);
        }
        break;

      case '分享':
        try {
          const shareOptions: any = {};

          if (message.type === 'text') {
            shareOptions.title = '分享文本';
            shareOptions.message = message.text;
          } else if (message.type === 'url' && message.urlData) {
            shareOptions.title = message.urlData.title;
            shareOptions.message = `标题：${message.urlData.title}\n链接：`;
            shareOptions.url = message.urlData.url;
          } else {
            Alert.alert('功能开发中', '该类型的消息分享功能将在后续版本提供。');
            return;
          }

          await Share.open(shareOptions);

        } catch (error: any) {
          // react-native-share throws an error if user cancels, so we check the message
          if (error.message !== 'User did not share') {
            console.warn('Share Error:', error);
          }
        }
        break;
    }
  };

  // 替换图片预览相关 state 和方法
  const { imageViewerState, openImageViewer, closeImageViewer, setImageIndex } = useImageViewer();

  // 替换音频播放相关 state 和方法
  const { playingAudioId, audioProgress, handleAudioPlayPause } = useAudioPlayer(audioRecorderPlayer);

  // 发送消息并清空输入
  const onSend = async () => {
    await handleSend(inputText);
    setInputText('');
  };

  const { width: windowWidth } = useWindowDimensions();

  const renderMessageContent = (item: ChatMessageType, _index?: number) => {
    if (item.type === 'audio') {
      const isPlaying = playingAudioId === item.id.toString();
      return (
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity onPress={() => handleAudioPlayPause(item)}>
            <Ionicons name={isPlaying ? 'pause-circle' : 'play-circle'} size={32} color={'#FF8C00'} />
          </TouchableOpacity>
          <Text style={{ marginLeft: 8, color: '#fff' }}>{item.audioData?.duration ?? 0}s</Text>
          {isPlaying && (
            <Text style={{ marginLeft: 8, color: '#FF8C00' }}>{audioProgress}s</Text>
          )}
        </View>
      );
    }
    switch (item.type) {
      case 'text': {
        return (
          <ParsedText
            style={styles(theme).messageText}
            parse={[
              {
                type: 'url',
                style: { color: '#007aff', textDecorationLine: 'underline' },
                onPress: (url: string) => {
                  let jumpUrl = url;
                  if (!/^https?:\/\//i.test(jumpUrl)) {
                    jumpUrl = 'https://' + jumpUrl;
                  }
                  navigation.navigate('WebViewScreen', { url: jumpUrl });
                },
              },
              {
                // 自定义域名检测规则，支持无协议前缀的域名
                pattern: /\b[\w-]+\.[\w.-]+(?:\/[\w\-.?&=/#%:;,@!$'()*+~]*)?(?=\s|$|[^\w\-.?&=/#%:;,@!$'()*+~])/g,
                style: { color: '#007aff', textDecorationLine: 'underline' },
                onPress: (url: string) => {
                  let jumpUrl = url;
                  if (!/^https?:\/\//i.test(jumpUrl)) {
                    jumpUrl = 'https://' + jumpUrl;
                  }
                  navigation.navigate('WebViewScreen', { url: jumpUrl });
                },
              },
              {
                type: 'phone',
                style: { color: '#007aff', textDecorationLine: 'underline' },
                onPress: (phone: string) => {
                  Linking.openURL(`tel:${phone}`);
                },
              },
              {
                type: 'email',
                style: { color: '#007aff', textDecorationLine: 'underline' },
                onPress: (email: string) => {
                  Linking.openURL(`mailto:${email}`);
                },
              },
            ]}
            childrenProps={{ allowFontScaling: false }}
          >
            {item.text}
          </ParsedText>
        );
      }
      case 'image': {
        if (!item.mediaUris || item.mediaUris.length === 0) {
          // 检查是否是微信转发的图片消息（下载中）
          const ext = item.ext ? JSON.parse(item.ext) : {};
          if (ext.downloadPending) {
            return (
              <View style={{
                width: 120,
                height: 120,
                backgroundColor: '#f0f0f0',
                borderRadius: 12,
                justifyContent: 'center',
                alignItems: 'center',
                marginVertical: 4,
              }}>
                <Text style={{ color: '#666', fontSize: 12 }}>图片下载中...</Text>
              </View>
            );
          }
          return null;
        }

        // 检查是否是占位符URI
        const firstUri = item.mediaUris[0];
        if (firstUri.startsWith('wechat_image_placeholder_')) {
          return (
            <View style={{
              width: 120,
              height: 120,
              backgroundColor: '#f0f0f0',
              borderRadius: 12,
              justifyContent: 'center',
              alignItems: 'center',
              marginVertical: 4,
            }}>
              <Text style={{ color: '#666', fontSize: 12 }}>图片下载中...</Text>
            </View>
          );
        }

        if (item.mediaUris.length === 1) {
          return (
            <SingleImageMessage
              uri={item.mediaUris[0]}
              onPress={() => openImageViewer(item.mediaUris ? [item.mediaUris[0]] : [], 0)}
              windowWidth={windowWidth}
            />
          );
        }
        // 多图
        const uris = item.mediaUris as string[];
        const previewUris = uris.slice(0, 3);
        const extraCount = uris.length - 3;
        return (
          <View style={{
            flexDirection: 'row',
            padding: 4,
            borderRadius: 12,
            shadowColor: '#000',
            shadowOpacity: 0.08,
            shadowRadius: 6,
            shadowOffset: { width: 0, height: 2 },
            backgroundColor: 'transparent',
            alignSelf: 'flex-start',
          }}>
            {previewUris.map((uri, idx) => {
              const isLast = idx === 2 && extraCount > 0;
              return (
                <TouchableOpacity key={uri} onPress={() => openImageViewer(uris, idx)}>
                  <View style={{ position: 'relative', marginRight: idx < previewUris.length - 1 ? 2 : 0 }}>
                    <Image
                      source={{ uri }}
                      style={{
                        width: 72,
                        height: 72,
                        borderRadius: 8,
                        backgroundColor: '#181818',
                      }}
                      resizeMode="cover"
                    />
                    {isLast && (
                      <View style={{
                        position: 'absolute',
                        right: 0,
                        bottom: 0,
                        left: 0,
                        top: 0,
                        backgroundColor: 'rgba(0,0,0,0.32)',
                        borderRadius: 8,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                        <Text style={{ color: '#fff', fontSize: 18, fontWeight: '600', letterSpacing: 0.5 }}>+{extraCount}</Text>
                      </View>
                    )}
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        );
      }
      case 'url':
        return <MessageUrl data={item.urlData} />;
      case 'card':
        return <MessageCard data={item.cardData} />;
      case 'file': {
        // 🆕 文件消息处理
        if (!item.fileData) { return null; }
        return (
          <FileMessageBubble
            fileData={item.fileData}
            onPress={() => handleFilePreview(item.fileData!)}
            onLongPress={() => handleFileManagement(item.fileData!)}
            isUser={item.sender === 'user'}
          />
        );
      }
      default:
        return null;
    }
  };

  const renderMessageBubble = ({ item, index: _index }: { item: ChatMessageType, index: number }) => {
    const isUser = item.sender === 'user';
    const isImage = item.type === 'image';
    const isUrl = item.type === 'url';
    const isFile = item.type === 'file';
    const bubbleStyle = isUser ? styles(theme).userMessageBubble : styles(theme).assistantMessageBubble;
    const imageBubbleStyle = { backgroundColor: 'transparent', padding: 0 };
    const urlBubbleStyle = { backgroundColor: 'transparent', padding: 0 };
    const fileBubbleStyle = { backgroundColor: 'transparent', padding: 0 };

    return (
      <View style={[
        styles(theme).messageRow,
        isUser ? { justifyContent: 'flex-end' } : { justifyContent: 'flex-start' },
      ]}>
        {!isUser && (
          <View style={styles(theme).avatarContainer}>
            <Text style={styles(theme).avatar}>🐱</Text>
          </View>
        )}
        <View style={[
          styles(theme).messageContent,
          isUser ? { alignItems: 'flex-end' } : { alignItems: 'flex-start' },
        ]}>
          <View
            style={[
              styles(theme).messageBubble,
              bubbleStyle,
              isImage && imageBubbleStyle,
              isUrl && urlBubbleStyle,
              isFile && fileBubbleStyle,
            ]}
          >
            {renderMessageContent(item)}
          </View>
        </View>
      </View>
    );
  };

  // 渲染消息和时间戳的复合列表
  const renderChatItem = ({ item, index }: { item: ChatMessageType, index: number }) => {
    const showTimestamp = shouldShowTimestamp(index, messages);
    return (
      <>
        {renderMessageBubble({ item, index })}
        {showTimestamp && (
          <View style={styles(theme).timestampRow}>
            <Text style={styles(theme).timestampText}>{formatTimestamp(new Date(item.createdAt))}</Text>
          </View>
        )}
      </>
    );
  };

  // 渲染输入区
  const renderInputArea = () => (
    <View style={styles(theme).inputBar}>
      {/* 左侧"+"按钮，点击弹出 ChatActionPanel */}
      <TouchableOpacity
        onPress={toggleActionPanel}
        style={{ justifyContent: 'center', alignItems: 'center', marginRight: 8 }}
        accessibilityLabel="更多操作"
      >
        <Ionicons name="add-circle-outline" size={28} color={'#999999'} />
      </TouchableOpacity>
      <TextInput
        style={styles(theme).input}
        value={inputText}
        onChangeText={setInputText}
        placeholder="粘贴 / 输入要存档的内容..."
        placeholderTextColor={'#999999'}
        multiline
        onFocus={() => {
          dismissPanels();
          scrollToEnd();
        }}
      />
      <TouchableOpacity
        style={[
          styles(theme).sendButton,
          inputText.trim().length === 0 && styles(theme).sendButtonDisabled,
        ]}
        onPress={onSend}
        disabled={inputText.trim().length === 0}
      >
        <Text style={styles(theme).sendButtonText}>发送</Text>
      </TouchableOpacity>
    </View>
  );

  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  // 在 FlatList 渲染前加日志
  console.log('[ChatScreen] messages:', messages);
  console.log('[ChatScreen] messages with undefined id:', messages.filter(m => !m.id));

  return (
    <SafeAreaView style={[styles(theme).container, { backgroundColor: theme.colors.backgroundPrimary }]} edges={['top']}>
      <StatusBar barStyle="dark-content" backgroundColor={theme.colors.backgroundPrimary} />
      <ChatHeader onBack={() => navigation.goBack()} />
      {Platform.OS === 'ios' ? (
        <KeyboardAvoidingView
          style={{ flex: 1, backgroundColor: theme.colors.backgroundPrimary }}
          behavior="padding"
          keyboardVerticalOffset={0}
        >
          <FlatList
            ref={flatListRef}
            style={styles(theme).messageList}
            data={messages}
            renderItem={({ item, index }) => renderChatItem({ item, index })}
            keyExtractor={item => item.id.toString()}
            inverted
            onScrollBeginDrag={dismissAllInputs}
            keyboardShouldPersistTaps="handled"
          />
          {/* 输入区 - 包装SafeAreaView处理底部安全区，键盘显示时不应用底部安全区域 */}
          <SafeAreaView
            style={{ backgroundColor: theme.colors.backgroundSecondary }}
            edges={Platform.OS === 'ios' && isKeyboardVisible ? [] : ['bottom']}
          >
            {renderInputArea()}
            <Animated.View style={animatedPanelStyle}>
              <ChatActionPanel onOptionSelect={handleActionSelect} />
            </Animated.View>
          </SafeAreaView>
        </KeyboardAvoidingView>
      ) : (
        <View style={{ flex: 1, backgroundColor: theme.colors.backgroundPrimary }}>
          <FlatList
            ref={flatListRef}
            style={styles(theme).messageList}
            data={messages}
            renderItem={({ item, index }) => renderChatItem({ item, index })}
            keyExtractor={item => item.id.toString()}
            inverted
            onScrollBeginDrag={dismissAllInputs}
            keyboardShouldPersistTaps="handled"
          />
          {/* 输入区 - 包装SafeAreaView处理底部安全区 */}
          <SafeAreaView style={{ backgroundColor: theme.colors.backgroundSecondary }} edges={['bottom']}>
            {renderInputArea()}
            <Animated.View style={animatedPanelStyle}>
              <ChatActionPanel onOptionSelect={handleActionSelect} />
            </Animated.View>
          </SafeAreaView>
        </View>
      )}

      <Modal
        visible={menuState.visible}
        transparent={true}
        animationType="none"
        onRequestClose={() => setMenuState({ visible: false, position: { x: 0, y: 0 }, item: null })}
      >
        <BubbleMenu
          isVisible={menuState.visible}
          position={menuState.position}
          onClose={() => setMenuState({ visible: false, position: { x: 0, y: 0 }, item: null })}
          onSelect={handleMenuSelect}
          messageType={menuState.item?.type || 'text'}
        />
      </Modal>

      <Modal
        visible={imageViewerState.visible}
        transparent={true}
        onRequestClose={closeImageViewer}
      >
        <MediaPreviewView
          mediaList={imageViewerState.uris}
          currentIndex={imageViewerState.index}
          onChangeIndex={setImageIndex}
          onClose={closeImageViewer}
        />
      </Modal>

      {/* 🆕 文件上传进度对话框 */}
      <FileUploadProgress
        visible={uploadProgress.visible}
        fileName={uploadProgress.fileName}
        progress={uploadProgress.progress}
        onCancel={() => {
          // TODO: 实现取消上传功能
          setUploadProgress({
            visible: false,
            fileName: '',
            progress: null,
          });
        }}
      />

      {/* 🆕 批量文件上传进度对话框 */}
      <BatchFileUploadProgress
        visible={batchUpload.visible}
        files={batchUpload.files}
        onCancel={handleCancelBatchUpload}
        onRetry={handleRetryFileUpload}
      />

      {/* 🆕 文件管理对话框 */}
      <FileManagementDialog
        visible={fileManagement.visible}
        fileData={fileManagement.fileData}
        onClose={handleCloseFileManagement}
        onDelete={handleDeleteFile}
        onRename={handleRenameFile}
        onShare={handleShareFile}
      />

    </SafeAreaView>
  );
};

export default ChatScreen;
