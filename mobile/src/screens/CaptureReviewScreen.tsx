import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useCameraStore } from '../stores/cameraStore';
import { useNavigation } from '@react-navigation/native';
import MediaPreviewView from '../components/MediaPreviewView';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RootStackParamList } from '../../App';

const CaptureReviewScreen = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  // --- Correct state selection to prevent re-renders ---
  const mediaList = useCameraStore(state => state.capturedMedia);
  // ----------------------------------------------------

  const [currentIndex, setCurrentIndex] = useState(mediaList.length > 0 ? mediaList.length - 1 : 0);

  useEffect(() => {
    // 当所有媒体都被删除时，自动关闭预览界面
    if (mediaList.length === 0) {
      navigation.goBack();
    }
  }, [mediaList, navigation]);

  const handleClose = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <MediaPreviewView
        mediaList={mediaList}
        currentIndex={currentIndex}
        onChangeIndex={setCurrentIndex}
        onClose={handleClose}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
});

export default CaptureReviewScreen;
