import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Schedule } from '../utils/mockSchedules'; // Import Schedule type

// --- Types ---
type Role = Schedule['role'];
type TimeSegment = 'dawn' | 'morning' | 'afternoon' | 'evening';

// --- Utility Functions ---

const getRoleStyle = (role: Role) => {
  switch (role) {
    case 'meeting':
      return { backgroundColor: 'rgba(230, 243, 255, 0.9)', borderColor: '#B3D9FF' }; // Light Blue
    case 'document':
      return { backgroundColor: 'rgba(255, 245, 230, 0.9)', borderColor: '#FFD699' }; // Light Orange
    case 'inspection':
      return { backgroundColor: 'rgba(240, 230, 255, 0.9)', borderColor: '#D3B3FF' }; // Light Purple
    case 'research':
      return { backgroundColor: 'rgba(230, 255, 250, 0.9)', borderColor: '#99FFEB' }; // Light Teal
    case 'coordination':
      return { backgroundColor: 'rgba(255, 230, 230, 0.9)', borderColor: '#FFB3B3' }; // Light Red
    default:
      return { backgroundColor: 'rgba(240, 240, 240, 0.9)', borderColor: '#E0E0E0' };
  }
};

const getTimeSegment = (hour: number): TimeSegment => {
  if (hour >= 0 && hour < 6) {return 'dawn';}
  if (hour >= 6 && hour < 12) {return 'morning';}
  if (hour >= 12 && hour < 18) {return 'afternoon';}
  return 'evening';
};

const getTimeSegmentLabel = (segment: TimeSegment) => {
  switch (segment) {
    case 'dawn':
      return '凌晨';
    case 'morning':
      return '上午';
    case 'afternoon':
      return '下午';
    case 'evening':
      return '晚上';
  }
};

// --- Mock Data - REMOVED ---

// --- Components ---

const EventCard = ({ event, isPassed = false }: { event: Schedule; isPassed?: boolean }) => {
  const cardStyle = getRoleStyle(event.role);
  return (
    <View style={[styles.eventCard, cardStyle, isPassed && styles.passedEventCard]}>
      <Text style={[styles.eventTitle, isPassed && styles.passedEventTitle]} numberOfLines={1}>
        {event.title}
      </Text>
      {event.location && (
        <Text style={[styles.eventLocation, isPassed && styles.passedEventLocation]}>
          {`📍 ${event.location}`}
        </Text>
      )}
    </View>
  );
};

const EventRow = ({ event, isPassed = false }: { event: Schedule; isPassed?: boolean }) => (
  <View style={styles.eventRowContainer}>
    <View style={styles.eventTimeContainer}>
      {event.endTime ? (
        <>
          <Text style={[styles.eventTime, isPassed && styles.passedEventTime]}>{event.startTime}</Text>
          <Text style={[styles.timeSeparator, isPassed && styles.passedTimeSeparator]}>|</Text>
          <Text style={[styles.eventTime, isPassed && styles.passedEventTime]}>{event.endTime}</Text>
        </>
      ) : (
        <Text style={[styles.eventTime, isPassed && styles.passedEventTime]}>{event.startTime}</Text>
      )}
    </View>
    <View style={[styles.timelineDot, isPassed && styles.passedTimelineDot]} />
    <EventCard event={event} isPassed={isPassed} />
  </View>
);

// Helper function to determine if a time segment has passed (only for today)
const isTimeSegmentPassed = (segment: TimeSegment, selectedDate: Date): boolean => {
  const now = new Date();
  const today = new Date();

  // Only apply fading effect for today's date
  if (selectedDate.toDateString() !== today.toDateString()) {
    return false;
  }

  const currentHour = now.getHours();

  switch (segment) {
    case 'dawn':
      return currentHour >= 6; // 凌晨已过 (6点后)
    case 'morning':
      return currentHour >= 12; // 上午已过 (12点后)
    case 'afternoon':
      return currentHour >= 18; // 下午已过 (18点后)
    case 'evening':
      return false; // 晚上永远不算"已过去"，因为这是一天的最后时段
    default:
      return false;
  }
};

const ScheduleSection = ({ segment, events, selectedDate }: { segment: TimeSegment, events: Schedule[], selectedDate: Date }) => {
  // "凌晨" section only shows if it has events.
  if (segment === 'dawn' && events.length === 0) {
    return null;
  }

  const isPassed = isTimeSegmentPassed(segment, selectedDate);

  return (
    <View style={[styles.sectionContainer, isPassed && styles.passedSection]}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, isPassed && styles.passedSectionTitle]}>
          {getTimeSegmentLabel(segment)}
        </Text>
        {events.length === 0 && (
          <Text style={[styles.emptySectionText, isPassed && styles.passedEmptyText]}>
            无日程安排
          </Text>
        )}
      </View>
      {events.length > 0 && events.map(event =>
        <EventRow key={event.id} event={event} isPassed={isPassed} />
      )}
    </View>
  );
};


interface ScheduleListProps {
  schedules: Schedule[];
  selectedDate: Date;
  onScroll?: any; // 修改为使用Reanimated的onScroll类型
  topPadding?: number; // 新增：动态顶部内边距
}

const ScheduleList = forwardRef<Animated.ScrollView, ScheduleListProps>(({ schedules, selectedDate, onScroll, topPadding = 128 }, ref) => {
  const scrollViewRef = useRef<Animated.ScrollView>(null);
  const insets = useSafeAreaInsets();

  useImperativeHandle(ref, () => scrollViewRef.current as Animated.ScrollView);

  // 动态底部留白计算
  const dynamicPaddingBottom = 100 + insets.bottom;

  // Handle case where the entire day is empty
  if (!schedules || schedules.length === 0) {
    return (
      <View style={styles.fullEmptyContainer}>
        <Text style={styles.fullEmptyText}>今日无日程安排，轻松一天</Text>
      </View>
    );
  }

  // 1. Group events by time segment
  const groupedEvents = (schedules || []).reduce((acc, event) => {
    const startHour = parseInt(event.startTime.split(':')[0], 10);
    const segment = getTimeSegment(startHour);
    if (!acc[segment]) {
      acc[segment] = [];
    }
    acc[segment].push(event);
    return acc;
  }, {} as Record<TimeSegment, Schedule[]>);

  const sections: TimeSegment[] = ['dawn', 'morning', 'afternoon', 'evening'];

  return (
    <Animated.ScrollView
      ref={scrollViewRef}
      style={styles.container}
      contentContainerStyle={[
        styles.contentContainer,
        {
          paddingTop: topPadding,
          paddingBottom: dynamicPaddingBottom,
        },
      ]}
      showsVerticalScrollIndicator={false}
      onScroll={onScroll}
      scrollEventThrottle={16}
      bounces={true}
      bouncesZoom={false}
      alwaysBounceVertical={true}
      directionalLockEnabled={true}
    >
      {sections.map(segment => (
        <ScheduleSection
          key={segment}
          segment={segment}
          events={groupedEvents[segment] || []}
          selectedDate={selectedDate}
        />
      ))}
    </Animated.ScrollView>
  );
});

// Set displayName for ScrollAwareBottomContainer to recognize this component
ScheduleList.displayName = 'ScheduleList';

// --- Styles ---

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F8FA',
  },
  contentContainer: {
    paddingHorizontal: 0,
    paddingTop: 20,
    // paddingBottom 现在由组件动态计算
  },
  sectionContainer: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  eventRowContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 15,
    paddingHorizontal: 15,
  },
  eventTimeContainer: {
    width: 45,
    alignItems: 'center',
    marginRight: 10,
  },
  eventTime: {
    fontSize: 13,
    fontWeight: '500',
    color: '#666',
    lineHeight: 16,
    textAlign: 'center',
  },
  timeSeparator: {
    fontSize: 10,
    color: '#C0C0C0',
    marginVertical: 1,
  },
  timelineDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FFC785',
    marginRight: 12,
    marginTop: 4,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  eventCard: {
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderLeftWidth: 4,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 6,
  },
  eventLocation: {
    fontSize: 13,
    color: 'rgba(0,0,0,0.7)',
    marginTop: 4,
  },
  emptySectionText: {
    color: '#999',
    fontSize: 14,
    fontStyle: 'italic',
    marginLeft: 15,
  },
  fullEmptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 50,
  },
  fullEmptyText: {
    fontSize: 16,
    color: '#888',
  },
  // Passed time segment styles (subtle faded appearance)
  passedSection: {
    opacity: 0.8, // 减轻淡化程度，从0.6调整到0.8
  },
  passedSectionTitle: {
    color: '#777', // 从#999调整到#777，不那么淡
  },
  passedEmptyText: {
    color: '#AAA', // 从#CCC调整到#AAA
  },
  passedEventTime: {
    color: '#888', // 从#AAA调整到#888
  },
  passedTimeSeparator: {
    color: '#BBB', // 从#DDD调整到#BBB
  },
  passedTimelineDot: {
    backgroundColor: '#CCC', // 从#DDD调整到#CCC
    borderColor: '#F0F0F0',
  },
  passedEventCard: {
    opacity: 0.85, // 从0.7调整到0.85
  },
  passedEventTitle: {
    color: '#777', // 从#999调整到#777
  },
  passedEventLocation: {
    color: '#999', // 从#BBB调整到#999
  },
});

export default ScheduleList;
