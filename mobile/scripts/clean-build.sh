#!/bin/bash

# 清理构建缓存脚本
# 用于移除react-native-vision-camera和react-native-worklets-core后的清理工作

echo "🧹 开始清理构建缓存..."

# 清理 React Native 缓存
echo "📱 清理 React Native 缓存..."
npx react-native start --reset-cache &
sleep 2
pkill -f "react-native start" || true

# 清理 npm/yarn 缓存
echo "📦 清理 npm 缓存..."
npm cache clean --force

# 清理 node_modules
echo "🗂️  重新安装依赖..."
rm -rf node_modules
npm install

# 清理 iOS 构建缓存
if [ -d "ios" ]; then
    echo "🍎 清理 iOS 构建缓存..."
    cd ios
    
    # 清理 Pods
    rm -rf Pods
    rm -rf Podfile.lock
    rm -rf build
    rm -rf ~/Library/Developer/Xcode/DerivedData/*
    
    # 重新安装 Pods
    pod deintegrate || true
    pod setup || true
    pod install --repo-update
    
    cd ..
fi

# 清理 Android 构建缓存
if [ -d "android" ]; then
    echo "🤖 清理 Android 构建缓存..."
    cd android
    
    # 清理 Gradle 缓存
    ./gradlew clean
    rm -rf .gradle
    rm -rf build
    rm -rf app/build
    
    # 清理全局 Gradle 缓存
    rm -rf ~/.gradle/caches/
    
    cd ..
fi

# 清理 Metro 缓存
echo "🚇 清理 Metro 缓存..."
rm -rf /tmp/metro-*
rm -rf /tmp/haste-map-*

# 清理 React Native 临时文件
echo "🗑️  清理临时文件..."
rm -rf /tmp/react-*

echo "✅ 清理完成！"
echo ""
echo "📋 后续步骤："
echo "1. 运行 'npm run ios' 或 'npm run android' 测试构建"
echo "2. 验证 OCR 功能是否正常工作"
echo "3. 检查是否还有相关编译错误"
echo ""
echo "🔍 如果仍有问题，请检查："
echo "- Xcode 版本是否支持 iOS 13.0+ Vision 框架"
echo "- Android SDK 和 NDK 版本是否正确"
echo "- 网络连接是否正常（用于下载依赖）" 