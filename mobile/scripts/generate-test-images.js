/**
 * OCR测试图片生成器
 * 使用Canvas API生成包含文字的测试图片
 */

const fs = require('fs');
const path = require('path');

// 检查是否在Node.js环境中有Canvas支持
let Canvas, createCanvas, registerFont;
try {
  const canvas = require('canvas');
  Canvas = canvas.Canvas;
  createCanvas = canvas.createCanvas;
  registerFont = canvas.registerFont;
} catch (error) {
  console.log('Canvas模块未安装，将生成图片占位符');
  console.log('要生成真实图片，请运行: npm install canvas');
}

// 测试图片配置
const testImages = {
  'wechat_meeting.png': {
    text: '明天上午9:30在三楼会议室\n召开季度总结会\n请各部门负责人准时参加',
    width: 400,
    height: 200,
    fontSize: 16,
    backgroundColor: '#f0f0f0',
    textColor: '#333333',
    scenario: 'wechat_screenshot',
  },
  'wechat_task.png': {
    text: '紧急任务：\n请各部门负责人于今日下午5点前\n提交本月工作总结报告',
    width: 450,
    height: 180,
    fontSize: 15,
    backgroundColor: '#e8f5e8',
    textColor: '#2d5a2d',
    scenario: 'wechat_screenshot',
  },
  'wechat_notice.png': {
    text: '通知：根据上级要求\n本周五下午组织全体员工\n进行安全培训',
    width: 380,
    height: 160,
    fontSize: 14,
    backgroundColor: '#fff3cd',
    textColor: '#856404',
    scenario: 'wechat_screenshot',
  },
  'official_notice.png': {
    text: '关于加强公文办理规范性的通知\n\n各科室：\n为进一步规范公文处理流程\n现就有关事项通知如下',
    width: 500,
    height: 250,
    fontSize: 18,
    backgroundColor: '#ffffff',
    textColor: '#000000',
    scenario: 'official_document',
  },
  'meeting_minutes.png': {
    text: '会议纪要\n\n时间：2024年1月10日上午\n地点：会议室A\n参会人员：各部门负责人',
    width: 450,
    height: 220,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
    textColor: '#212529',
    scenario: 'official_document',
  },
  'work_plan.png': {
    text: '2024年第一季度工作计划\n\n一、重点工作目标\n1. 完成年度预算编制\n2. 推进数字化转型项目',
    width: 480,
    height: 240,
    fontSize: 15,
    backgroundColor: '#ffffff',
    textColor: '#333333',
    scenario: 'official_document',
  },
  'high_quality.png': {
    text: '高质量图片识别测试\n\n这是一张高分辨率、光线充足的图片\nOCR识别效果应该很好\n包含清晰的中文文字、数字123',
    width: 600,
    height: 300,
    fontSize: 20,
    backgroundColor: '#ffffff',
    textColor: '#000000',
    scenario: 'quality_test',
  },
  'medium_quality.png': {
    text: '中等质量图片识别测试\n\n这是一张中等分辨率的图片\n可能有轻微的模糊或光线不足\n但仍能较好识别文字内容',
    width: 400,
    height: 200,
    fontSize: 14,
    backgroundColor: '#f5f5f5',
    textColor: '#555555',
    scenario: 'quality_test',
  },
  'low_quality.png': {
    text: '低质量图片识别测试\n\n这是一张低分辨率图片\nOCR识别可能存在错误\n但基本内容仍可识别',
    width: 300,
    height: 150,
    fontSize: 12,
    backgroundColor: '#e0e0e0',
    textColor: '#666666',
    scenario: 'quality_test',
  },
};

// 生成图片的函数
function generateImage(filename, config) {
  if (!createCanvas) {
    // 如果没有Canvas支持，创建占位符文件
    const placeholderContent = `# ${filename}\n\n这是一个图片占位符。\n\n预期文本：\n${config.text}\n\n要生成真实图片，请安装canvas模块：\nnpm install canvas`;
    return placeholderContent;
  }

  const canvas = createCanvas(config.width, config.height);
  const ctx = canvas.getContext('2d');

  // 设置背景
  ctx.fillStyle = config.backgroundColor;
  ctx.fillRect(0, 0, config.width, config.height);

  // 设置文字样式
  ctx.fillStyle = config.textColor;
  ctx.font = `${config.fontSize}px Arial, "Microsoft YaHei", sans-serif`;
  ctx.textAlign = 'left';
  ctx.textBaseline = 'top';

  // 绘制文字（支持多行）
  const lines = config.text.split('\n');
  const lineHeight = config.fontSize * 1.4;
  let y = 20;

  lines.forEach(line => {
    ctx.fillText(line, 20, y);
    y += lineHeight;
  });

  // 如果是低质量图片，添加模糊效果
  if (filename.includes('low_quality')) {
    // 简单的模糊效果模拟
    ctx.globalAlpha = 0.8;
  }

  return canvas.toBuffer('image/png');
}

// 创建测试数据映射文件
function createTestDataMapping() {
  const mapping = {};

  Object.entries(testImages).forEach(([filename, config]) => {
    const expectedKeywords = extractKeywords(config.text);

    mapping[filename] = {
      expectedText: config.text.replace(/\n/g, ' ').trim(),
      expectedKeywords,
      minConfidence: getMinConfidence(filename),
      scenario: config.scenario,
      width: config.width,
      height: config.height,
    };
  });

  return mapping;
}

// 提取关键词
function extractKeywords(text) {
  // 简单的关键词提取逻辑
  const keywords = [];

  // 时间相关
  if (text.includes('明天')) {keywords.push('明天');}
  if (text.includes('上午')) {keywords.push('上午');}
  if (text.includes('下午')) {keywords.push('下午');}
  if (text.includes('9:30')) {keywords.push('9:30');}
  if (text.includes('5点')) {keywords.push('5点');}

  // 地点相关
  if (text.includes('会议室')) {keywords.push('会议室');}
  if (text.includes('三楼')) {keywords.push('三楼');}

  // 人员相关
  if (text.includes('部门负责人')) {keywords.push('部门负责人');}
  if (text.includes('全体员工')) {keywords.push('全体员工');}

  // 活动相关
  if (text.includes('季度总结会')) {keywords.push('季度总结会');}
  if (text.includes('安全培训')) {keywords.push('安全培训');}
  if (text.includes('工作总结')) {keywords.push('工作总结');}

  // 文档相关
  if (text.includes('通知')) {keywords.push('通知');}
  if (text.includes('会议纪要')) {keywords.push('会议纪要');}
  if (text.includes('工作计划')) {keywords.push('工作计划');}

  return keywords.length > 0 ? keywords : ['测试', '文字', '识别'];
}

// 获取最小置信度要求
function getMinConfidence(filename) {
  if (filename.includes('high_quality')) {return 0.9;}
  if (filename.includes('medium_quality')) {return 0.75;}
  if (filename.includes('low_quality')) {return 0.6;}
  if (filename.includes('wechat')) {return 0.8;}
  return 0.7;
}

// 主函数
function main() {
  const outputDir = path.join(__dirname, '../src/assets/test-images');

  // 确保输出目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  console.log('🎨 开始生成OCR测试图片...');

  // 生成图片
  Object.entries(testImages).forEach(([filename, config]) => {
    try {
      const imageData = generateImage(filename, config);
      const outputPath = path.join(outputDir, filename);

      if (typeof imageData === 'string') {
        // 占位符文件
        fs.writeFileSync(outputPath.replace('.png', '.md'), imageData);
        console.log(`📝 创建占位符: ${filename.replace('.png', '.md')}`);
      } else {
        // 真实图片
        fs.writeFileSync(outputPath, imageData);
        console.log(`🖼️  生成图片: ${filename}`);
      }
    } catch (error) {
      console.error(`❌ 生成 ${filename} 失败:`, error.message);
    }
  });

  // 生成测试数据映射
  const mapping = createTestDataMapping();
  const mappingPath = path.join(outputDir, 'test-data-mapping.json');
  fs.writeFileSync(mappingPath, JSON.stringify(mapping, null, 2));
  console.log('📊 生成测试数据映射: test-data-mapping.json');

  console.log('\n✅ OCR测试图片生成完成！');
  console.log('\n📋 下一步：');
  console.log('1. 检查生成的图片质量');
  console.log('2. 在真机环境中运行OCR测试');
  console.log('3. 根据真实识别结果调整预期数据');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  generateImage,
  createTestDataMapping,
  testImages,
};
