#!/usr/bin/env node

/**
 * React Native + 微信SDK 缓存清理脚本
 * 
 * 用于清理开发环境中的各种缓存，解决微信绑定链接缓存问题
 * 
 * 使用方法：
 * npm run clear-cache
 * 或
 * node scripts/clear-cache.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧹 开始清理React Native + 微信SDK缓存...\n');

// 1. 清理Metro缓存
console.log('📱 清理Metro缓存...');
try {
  execSync('npx react-native start --reset-cache', { stdio: 'inherit' });
  console.log('✅ Metro缓存清理完成\n');
} catch (error) {
  console.log('⚠️ Metro缓存清理失败，继续其他清理...\n');
}

// 2. 清理npm/yarn缓存
console.log('📦 清理包管理器缓存...');
try {
  // 检查是否使用yarn
  if (fs.existsSync(path.join(process.cwd(), 'yarn.lock'))) {
    execSync('yarn cache clean', { stdio: 'inherit' });
    console.log('✅ Yarn缓存清理完成');
  } else {
    execSync('npm cache clean --force', { stdio: 'inherit' });
    console.log('✅ NPM缓存清理完成');
  }
} catch (error) {
  console.log('⚠️ 包管理器缓存清理失败:', error.message);
}

// 3. 清理React Native缓存目录
console.log('\n🗂️ 清理React Native缓存目录...');
const cacheDirectories = [
  path.join(require('os').homedir(), '.metro'),
  path.join(require('os').tmpdir(), 'metro-*'),
  path.join(require('os').tmpdir(), 'react-*'),
  path.join(process.cwd(), 'node_modules/.cache'),
];

cacheDirectories.forEach(dir => {
  try {
    if (fs.existsSync(dir)) {
      execSync(`rm -rf "${dir}"`, { stdio: 'inherit' });
      console.log(`✅ 清理目录: ${dir}`);
    }
  } catch (error) {
    console.log(`⚠️ 清理目录失败: ${dir}`);
  }
});

// 4. 清理Android缓存（如果存在）
console.log('\n🤖 清理Android缓存...');
const androidDir = path.join(process.cwd(), 'android');
if (fs.existsSync(androidDir)) {
  try {
    execSync('cd android && ./gradlew clean', { stdio: 'inherit' });
    console.log('✅ Android缓存清理完成');
  } catch (error) {
    console.log('⚠️ Android缓存清理失败:', error.message);
  }
}

// 5. 清理iOS缓存（如果存在）
console.log('\n🍎 清理iOS缓存...');
const iosDir = path.join(process.cwd(), 'ios');
if (fs.existsSync(iosDir)) {
  try {
    execSync('cd ios && rm -rf build/ && rm -rf Pods/ && rm -rf Podfile.lock', { stdio: 'inherit' });
    console.log('✅ iOS缓存清理完成');
  } catch (error) {
    console.log('⚠️ iOS缓存清理失败:', error.message);
  }
}

// 6. 清理Watchman缓存
console.log('\n👀 清理Watchman缓存...');
try {
  execSync('watchman watch-del-all', { stdio: 'inherit' });
  console.log('✅ Watchman缓存清理完成');
} catch (error) {
  console.log('⚠️ Watchman缓存清理失败（可能未安装）');
}

console.log('\n🎉 缓存清理完成！');
console.log('\n📋 建议的下一步操作：');
console.log('1. 重新安装依赖: npm install 或 yarn install');
console.log('2. 重新启动Metro: npm start 或 yarn start');
console.log('3. 重新构建应用: npm run android 或 npm run ios');
console.log('4. 如果问题仍然存在，请重启设备或模拟器');

console.log('\n💡 微信绑定缓存问题解决提示：');
console.log('- 清理缓存后，微信绑定链接将重新生成');
console.log('- 企业微信客户端可能仍有缓存，请等待几分钟或重启微信');
console.log('- 如果问题持续，请联系开发团队');
