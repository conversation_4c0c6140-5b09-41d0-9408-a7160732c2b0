{"name": "GongZhiMallMobile", "version": "0.0.1", "private": true, "scripts": {"android": "NODE_ENV=development react-native run-android", "ios": "NODE_ENV=development react-native run-ios", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "postinstall": "patch-package", "start": "NODE_ENV=development react-native start", "start:dev": "NODE_ENV=development react-native start --reset-cache", "start:clean": "NODE_ENV=development react-native start --reset-cache", "clear-cache": "node scripts/clear-cache.js", "clear-cache:full": "node scripts/clear-cache.js && yarn install", "test": "jest", "pod-install": "RCT_NEW_ARCH_ENABLED=1 bundle exec pod install", "check-env": "node scripts/check-env.js", "wechat:setup": "node scripts/setup-wechat-config.js", "wechat:validate": "node scripts/validate-wechat-config.js", "wechat:check-prod": "node scripts/check-production-config.js", "wechat:sync-config": "node scripts/sync-production-config.js"}, "dependencies": {"@node-rs/jieba": "^2.0.1", "@nozbe/watermelondb": "^0.28.0", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-camera-roll/camera-roll": "^7.10.1", "@react-native-clipboard/clipboard": "^1.16.3", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.0.0", "@react-native-documents/picker": "^10.1.3", "@react-native/gradle-plugin": "^0.79.3", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "base-64": "^1.0.0", "chrono-node": "^2.8.2", "crypto-js": "^4.2.0", "date-fns": "2.30.0", "docx-preview": "^0.3.5", "easyofd": "^1.1.0", "invariant": "^2.2.4", "jcore-react-native": "^2.3.0", "jpush-react-native": "^2.8.2", "jszip": "^3.10.1", "mammoth": "^1.9.1", "node-fetch": "^3.3.2", "react": "19.0.0", "react-native": "0.79.3", "react-native-audio-recorder-player": "^3.6.14", "react-native-blob-util": "^0.22.2", "react-native-device-info": "^14.0.4", "react-native-draggable-flatlist": "^4.0.3", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.25.0", "react-native-get-random-values": "^1.11.0", "react-native-haptic-feedback": "^2.3.3", "react-native-pager-view": "^6.8.1", "react-native-parsed-text": "^0.0.22", "react-native-pdf": "^6.7.7", "react-native-permissions": "^4.1.5", "react-native-reanimated": "3.18.0", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-share": "^12.0.10", "react-native-svg": "^15.12.0", "react-native-tab-view": "^4.1.2", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "4.6.4", "react-native-webview": "^13.15.0", "react-native-worklets-core": "1.5.0", "uuid": "^11.1.0", "x2js": "^3.4.4", "xlsx": "^0.18.5", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-shorthand-properties": "^7.27.1", "@babel/plugin-transform-template-literals": "^7.27.1", "@babel/preset-env": "^7.25.3", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.27.6", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.3", "@react-native/eslint-config": "0.79.3", "@react-native/metro-config": "0.79.3", "@react-native/typescript-config": "0.79.3", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.13", "@types/node": "^24.0.14", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "@types/uuid": "^10.0.0", "better-sqlite3": "9.6.0", "eslint": "^8.19.0", "jest": "^29.6.3", "jest-environment-jsdom": "^30.0.2", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "2.8.8", "react-dom": "19.0.0", "react-native-dotenv": "^3.4.11", "react-native-svg-transformer": "^1.5.1", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "overrides": {"@types/node": "^18.0.0"}, "resolutions": {"file-type": "^20.0.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}