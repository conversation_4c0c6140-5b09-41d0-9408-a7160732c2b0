/* eslint-env jest */
// Jest setup for React Native - 简化版本
// 避免复杂的React Native模块mock，专注于OCR测试

import 'react-native-gesture-handler/jestSetup';

jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');

  // Mock Platform
  RN.Platform = {
    ...RN.Platform,
    OS: 'ios',
    select: jest.fn(obj => obj.ios),
  };

  // Mock AppState
  RN.AppState = {
    ...RN.AppState,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
  };

  // Mock UIManager
  RN.UIManager = {
    ...RN.UIManager,
    setLayoutAnimationEnabledExperimental: jest.fn(),
  };

  // Mock TurboModuleRegistry for DevMenu
  RN.NativeModules.TurboModuleRegistry = {
    getEnforcing: name => {
      if (name === 'DevMenu') {
        return null;
      }
      return {};
    },
    get: name => {
      if (name === 'DevMenu') {
        return null;
      }
      return {};
    },
  };

  return RN;
});

jest.mock('react-native/Libraries/EventEmitter/NativeEventEmitter');

jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock')
);

jest.mock('react-native-haptic-feedback', () => ({
  trigger: jest.fn(),
}));

jest.mock('react-native-fs', () => ({
  stat: jest.fn().mockResolvedValue({ isFile: () => true }),
  exists: jest.fn().mockResolvedValue(true),
  // Add other functions as needed by your tests
}));

jest.mock('react-native-permissions', () => require('react-native-permissions/mock'));

jest.mock('react-native-vision-camera', () => ({
  ...jest.requireActual('react-native-vision-camera'),
  useCameraDevice: jest.fn().mockReturnValue({}),
  useCameraPermission: jest.fn().mockReturnValue({ hasPermission: true }),
}));

// Mock react-native-image-picker
// The following mock for 'react-native-image-picker' is causing a crash
// because the library is not installed. We are removing it as the project
// uses other libraries for image picking.
// jest.mock('react-native-image-picker', () => ({
//   launchImageLibrary: jest.fn((options, callback) => {
//     const mockResponse = {
//       assets: [{
//         uri: 'file:///mock/image.jpg',
//         type: 'image/jpeg',
//         fileName: 'image.jpg',
//       }],
//     };
//     if (callback) {
//       callback(mockResponse);
//     }
//     return Promise.resolve(mockResponse);
//   }),
// }));

// Mock console methods for cleaner test output
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Setup global test timeout
jest.setTimeout(30000);

// This mock is essential for WatermelonDB to work in a Node.js test environment.
// It redirects the native SQLite adapter calls to the Node.js-compatible version.
jest.mock(
  '@nozbe/watermelondb/adapters/sqlite/makeDispatcher/index.native.js',
  () => {

    return require('@nozbe/watermelondb/adapters/sqlite/makeDispatcher/index.js');
  },
);

jest.mock('react-native-reanimated', () =>
  require('react-native-reanimated/mock')
);
