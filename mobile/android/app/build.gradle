apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Node Configuration */
    // Explicitly specify Node.js executable path for Gradle
    nodeExecutableAndArgs = ["/opt/homebrew/bin/node"]
    cliFile = file("../../node_modules/react-native/cli.js")

    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '../..'
    // root = file("../../")
    //   The folder where the react-native NPM package is. Default is ../../node_modules/react-native
    // reactNativeDir = file("../../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../../node_modules/@react-native/codegen
    // codegenDir = file("../../node_modules/@react-native/codegen")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = io.github.react-native-community:jsc-android-intl:2026004.+`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'io.github.react-native-community:jsc-android:258089.+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.gongzhimall.mobile"
    defaultConfig {
        applicationId "com.gongzhimall.mobile"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"
        externalNativeBuild {
            cmake {
                cppFlags "-std=c++17"
            }
        }
        vectorDrawables.useSupportLibrary = true
        

        manifestPlaceholders = [
            JPUSH_APPKEY: project.hasProperty('JPUSH_APP_KEY') ? project.JPUSH_APP_KEY : "bd2c958b83dc679759a25664",
            JPUSH_CHANNEL: "gongzhimall-official"
        ]
    }
    sourceSets {
        main {
            assets.srcDirs += ['../../src/assets']
        }
    }
    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            if (project.hasProperty('MYAPP_UPLOAD_STORE_FILE')) {
                // signingConfig signingConfigs.release
                //  minifyEnabled enableProguardInReleaseBuilds
                //  proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            }
            // signingConfig signingConfigs.release
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    packagingOptions {
        pickFirst 'lib/x86/libjsc.so'
        pickFirst 'lib/x86_64/libjsc.so'
        pickFirst 'lib/armeabi-v7a/libjsc.so'
        pickFirst 'lib/arm64-v8a/libjsc.so'
        pickFirst 'lib/arm64-v8a/libfbjni.so'
        pickFirst 'lib/armeabi-v7a/libfbjni.so'
        pickFirst 'lib/x86/libfbjni.so'
        pickFirst 'lib/x86_64/libfbjni.so'
    }
}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")

    // React Native packages discovered by autolinking
    implementation project(':nozbe_watermelondb')
    implementation project(':react-native-async-storage_async-storage')
    implementation project(':react-native-camera-roll_camera-roll')
    implementation project(':react-native-clipboard_clipboard')
    implementation project(':react-native-community_blur')
    implementation project(':react-native-community_datetimepicker')
    implementation project(':react-native-documents_picker')
    implementation project(':jcore-react-native')
    implementation project(':jpush-react-native')
    implementation project(':react-native-audio-recorder-player')

    // 微信SDK依赖
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android:+'
    implementation project(':react-native-blob-util')
    implementation project(':react-native-device-info')
    implementation project(':react-native-file-viewer')
    implementation project(':react-native-fs')
    implementation project(':react-native-gesture-handler')
    implementation project(':react-native-get-random-values')
    implementation project(':react-native-haptic-feedback')

    implementation project(':react-native-pager-view')
    implementation project(':react-native-pdf')
    implementation project(':react-native-permissions')
    implementation project(':react-native-reanimated')
    implementation project(':react-native-safe-area-context')
    implementation project(':react-native-screens')
    implementation project(':react-native-share')
    implementation project(':react-native-svg')
    implementation project(':react-native-vector-icons')
    implementation project(':react-native-vision-camera')
    implementation project(':react-native-webview')
    implementation project(':react-native-worklets-core')

    // WeChat SDK for WXEntryActivity
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android:6.8.20'

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

// 全局排除所有 Support 库，强制使用 AndroidX
configurations.all {
    exclude group: 'com.android.support', module: 'support-compat'
    exclude group: 'com.android.support', module: 'support-annotations'
    exclude group: 'com.android.support', module: 'support-v4'
    exclude group: 'com.android.support', module: 'appcompat-v7'
    exclude group: 'com.android.support', module: 'support-vector-drawable'
    exclude group: 'com.android.support', module: 'animated-vector-drawable'
    exclude group: 'com.android.support', module: 'versionedparcelable'
}
