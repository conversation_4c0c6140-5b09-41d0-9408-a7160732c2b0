package com.gongzhimall.mobile.wxapi;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.content.Context;
import android.content.res.Resources;

import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;

public class WXEntryActivity extends Activity implements IWXAPIEventHandler {
    private IWXAPI api;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 从strings.xml读取微信AppID
        String wechatAppId = getString(getResources().getIdentifier("wechat_app_id", "string", getPackageName()));
        // 通过WXAPIFactory工厂，获取IWXAPI的实例
        api = WXAPIFactory.createWXAPI(this, wechatAppId, false);
        // 将该app注册到微信
        api.registerApp(wechatAppId);
        
        // 处理微信发送的请求
        api.handleIntent(getIntent(), this);
    }
    
    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        api.handleIntent(intent, this);
    }

    @Override
    public void onReq(BaseReq req) {
        // 微信向第三方应用发起请求，会回调到该方法
        finish();
    }

    @Override
    public void onResp(BaseResp resp) {
        // 第三方应用向微信发起请求，微信返回响应结果会回调到该方法
        finish();
    }
} 