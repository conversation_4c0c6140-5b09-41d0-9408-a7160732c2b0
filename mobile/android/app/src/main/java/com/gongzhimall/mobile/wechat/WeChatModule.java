package com.gongzhimall.mobile.wechat;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.Arguments;

import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.modelbiz.OpenWebview;
import com.tencent.mm.opensdk.modelbiz.WXOpenCustomerServiceChat;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;

import android.content.Intent;
import android.net.Uri;
import android.content.ComponentName;

/**
 * 自定义微信SDK模块
 * 直接封装微信官方SDK，避免第三方库的兼容性问题
 */
public class WeChatModule extends ReactContextBaseJavaModule {
    private static final String TAG = "WeChatModule";
    private static final String MODULE_NAME = "CustomWeChatSDK";
    
    private IWXAPI wxApi;
    private ReactApplicationContext reactContext;
    private boolean isRegistered = false;
    
    public WeChatModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }
    
    @NonNull
    @Override
    public String getName() {
        return MODULE_NAME;
    }
    
    /**
     * 注册微信应用
     */
    @ReactMethod
    public void registerApp(ReadableMap config, Promise promise) {
        try {
            String appId = config.getString("appid");
            if (appId == null || appId.isEmpty()) {
                promise.reject("INVALID_APPID", "AppID不能为空");
                return;
            }
            
            Log.d(TAG, "注册微信应用: " + appId);
            
            // 通过WXAPIFactory工厂，获取IWXAPI的实例
            wxApi = WXAPIFactory.createWXAPI(reactContext, appId, false);
            
            // 将该app注册到微信
            boolean result = wxApi.registerApp(appId);
            
            if (result) {
                isRegistered = true;
                Log.d(TAG, "微信应用注册成功");
                
                WritableMap resultMap = Arguments.createMap();
                resultMap.putBoolean("success", true);
                resultMap.putString("message", "微信应用注册成功");
                promise.resolve(resultMap);
            } else {
                Log.e(TAG, "微信应用注册失败");
                promise.reject("REGISTER_FAILED", "微信应用注册失败");
            }
        } catch (Exception e) {
            Log.e(TAG, "注册微信应用异常", e);
            promise.reject("REGISTER_ERROR", "注册微信应用异常: " + e.getMessage());
        }
    }
    
    /**
     * 检查微信是否安装
     */
    @ReactMethod
    public void isWeChatInstalled(Promise promise) {
        try {
            if (wxApi == null) {
                // 如果还没有初始化，使用临时实例检查
                IWXAPI tempApi = WXAPIFactory.createWXAPI(reactContext, null, false);
                boolean installed = tempApi.isWXAppInstalled();
                promise.resolve(installed);
            } else {
                boolean installed = wxApi.isWXAppInstalled();
                promise.resolve(installed);
            }
        } catch (Exception e) {
            Log.e(TAG, "检查微信安装状态异常", e);
            promise.resolve(false); // 出错时假设未安装
        }
    }
    
    /**
     * 打开企业微信客服
     */
    @ReactMethod
    public void openCustomerService(ReadableMap config, Promise promise) {
        try {
            if (!isRegistered || wxApi == null) {
                promise.reject("NOT_REGISTERED", "请先注册微信应用");
                return;
            }
            
            if (!wxApi.isWXAppInstalled()) {
                promise.reject("WECHAT_NOT_INSTALLED", "微信未安装");
                return;
            }
            
            String corpId = config.getString("corpid");
            String url = config.getString("url");
            
            if (corpId == null || corpId.isEmpty()) {
                promise.reject("INVALID_CORPID", "企业ID不能为空");
                return;
            }
            
            if (url == null || url.isEmpty()) {
                promise.reject("INVALID_URL", "客服链接不能为空");
                return;
            }
            
            Log.d(TAG, "打开企业微信客服: " + corpId + ", URL: " + url);

            // 方法1：使用正确的企业微信客服API（官方推荐方式）
            try {
                // 根据官方文档，微信SDK版本需要 >= 6.7.9 才支持客服功能
                // 对应的API版本号大约是 0x26070900
                int SUPPORT_OPEN_CUSTOMER_SERVICE_CHAT = 0x26070900;

                // 检查当前版本是否支持拉起客服会话
                if (wxApi.getWXAppSupportAPI() >= SUPPORT_OPEN_CUSTOMER_SERVICE_CHAT) {
                    WXOpenCustomerServiceChat.Req req = new WXOpenCustomerServiceChat.Req();
                    req.corpId = corpId;  // 企业ID
                    req.url = url;        // 客服URL

                    Log.d(TAG, "使用WXOpenCustomerServiceChat打开企业微信客服");
                    Log.d(TAG, "URL: " + url);
                    Log.d(TAG, "CorpID: " + corpId);
                    Log.d(TAG, "微信版本: " + wxApi.getWXAppSupportAPI() + " (需要: " + SUPPORT_OPEN_CUSTOMER_SERVICE_CHAT + ")");

                    boolean result = wxApi.sendReq(req);

                    if (result) {
                        Log.d(TAG, "企业微信客服打开请求发送成功");
                        WritableMap resultMap = Arguments.createMap();
                        resultMap.putBoolean("success", true);
                        resultMap.putString("message", "企业微信客服打开成功");
                        promise.resolve(resultMap);
                        return;
                    } else {
                        Log.w(TAG, "WXOpenCustomerServiceChat请求发送失败");
                    }
                } else {
                    Log.w(TAG, "当前微信版本不支持客服会话功能，当前版本: " + wxApi.getWXAppSupportAPI() +
                              ", 需要版本: " + SUPPORT_OPEN_CUSTOMER_SERVICE_CHAT + " (微信6.7.9+)");
                }
            } catch (Exception sdkError) {
                Log.w(TAG, "WXOpenCustomerServiceChat方式异常: " + sdkError.getMessage());
            }

            // 方法2：使用OpenWebview作为备用方案（兼容旧版本微信）
            try {
                OpenWebview.Req req = new OpenWebview.Req();
                req.url = url;

                Log.d(TAG, "使用OpenWebview作为备用方案: " + url);
                boolean result = wxApi.sendReq(req);

                if (result) {
                    Log.d(TAG, "企业微信客服打开成功（OpenWebview备用方案）");
                    WritableMap resultMap = Arguments.createMap();
                    resultMap.putBoolean("success", true);
                    resultMap.putString("message", "企业微信客服打开成功");
                    promise.resolve(resultMap);
                    return;
                } else {
                    Log.w(TAG, "OpenWebview备用方案也失败");
                }
            } catch (Exception webviewError) {
                Log.w(TAG, "OpenWebview备用方案异常: " + webviewError.getMessage());
            }

            // 如果所有方法都失败
            Log.e(TAG, "所有方式都失败，企业微信客服打开失败");
            promise.reject("OPEN_FAILED", "企业微信客服打开失败，请检查微信版本或网络连接");
        } catch (Exception e) {
            Log.e(TAG, "打开企业微信客服异常", e);
            promise.reject("OPEN_ERROR", "打开企业微信客服异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取微信SDK版本
     */
    @ReactMethod
    public void getWeChatSDKVersion(Promise promise) {
        try {
            if (wxApi == null) {
                IWXAPI tempApi = WXAPIFactory.createWXAPI(reactContext, null, false);
                int version = tempApi.getWXAppSupportAPI();
                promise.resolve(version);
            } else {
                int version = wxApi.getWXAppSupportAPI();
                promise.resolve(version);
            }
        } catch (Exception e) {
            Log.e(TAG, "获取微信SDK版本异常", e);
            promise.resolve(0);
        }
    }
    
    /**
     * 检查注册状态
     */
    @ReactMethod
    public void isRegistered(Promise promise) {
        promise.resolve(isRegistered);
    }
    
    /**
     * 获取模块状态信息
     */
    @ReactMethod
    public void getModuleInfo(Promise promise) {
        try {
            WritableMap info = Arguments.createMap();
            info.putBoolean("registered", isRegistered);
            info.putBoolean("wechatInstalled", wxApi != null ? wxApi.isWXAppInstalled() : false);
            info.putInt("sdkVersion", wxApi != null ? wxApi.getWXAppSupportAPI() : 0);
            info.putString("moduleName", MODULE_NAME);
            promise.resolve(info);
        } catch (Exception e) {
            Log.e(TAG, "获取模块信息异常", e);
            promise.reject("GET_INFO_ERROR", "获取模块信息异常: " + e.getMessage());
        }
    }
}
