<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- 相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    
    <!-- 麦克风权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    
    <!-- 相册/存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_DOCUMENTS" />

    <!-- Android 11+ 存储管理权限 (可选，用于访问所有文件) -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    
    <!-- 定位权限 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    
    <!-- 微信权限 -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <!-- 极光推送权限 -->
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
      android:networkSecurityConfig="@xml/network_security_config"
      android:usesCleartextTraffic="false"
      android:supportsRtl="true"
      tools:replace="android:usesCleartextTraffic">

        <activity
            android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:screenOrientation="portrait"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
        
        <!-- 微信URL Scheme配置 -->
        <intent-filter>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="gongzhimall" />
        </intent-filter>
      </activity>
      
      <!-- 微信回调Activity -->
      <activity
        android:name=".wxapi.WXEntryActivity"
        android:label="@string/app_name"
        android:exported="true"
        android:taskAffinity="com.gongzhimall.mobile"
        android:launchMode="singleTask">
        <intent-filter>
            <action android:name="android.intent.action.VIEW"/>
            <category android:name="android.intent.category.DEFAULT"/>
            <data android:scheme="wxwork_ww07a1d5b5a2554b19"/>
        </intent-filter>
      </activity>
      
      <!-- Required for JPush -->
      <service
          android:name="cn.jpush.android.service.PushService"
          android:process=":JPush"
          android:exported="false">
          <intent-filter>
              <action android:name="cn.jpush.android.intent.REGISTER" />
              <action android:name="cn.jpush.android.intent.REPORT" />
              <action android:name="cn.jpush.android.intent.PushService" />
              <action android:name="cn.jpush.android.intent.PUSH_TIME" />
          </intent-filter>
      </service>
      <receiver
          android:name="cn.jiguang.plugins.push.receiver.JPushBroadcastReceiver"
          android:exported="false" >
          <intent-filter>
              <action android:name="cn.jpush.android.intent.REGISTRATION" />
              <action android:name="cn.jpush.android.intent.MESSAGE_RECEIVED" />
              <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED" />
              <action android:name="cn.jpush.android.intent.NOTIFICATION_OPENED" />
              <action android:name="cn.jpush.android.intent.CONNECTION" />
              <category android:name="${applicationId}" />
          </intent-filter>
      </receiver>
      <receiver
          android:name="cn.jpush.android.service.PushReceiver"
          android:exported="true"
          android:enabled="true"
          tools:replace="android:exported">
          <intent-filter android:priority="1000">
              <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED_PROXY" />
              <category android:name="${applicationId}" />
          </intent-filter>
          <intent-filter>
              <action android:name="android.intent.action.USER_PRESENT" />
              <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
          </intent-filter>
          <intent-filter>
              <action android:name="android.intent.action.PACKAGE_ADDED" />
              <action android:name="android.intent.action.PACKAGE_REMOVED" />
              <data android:scheme="package" />
          </intent-filter>
      </receiver>
      <!-- End of JPush components -->

      <!-- JPush meta-data configuration -->
      <meta-data
          android:name="JPUSH_CHANNEL"
          android:value="${JPUSH_CHANNEL}" />
      <meta-data
          android:name="JPUSH_APPKEY"
          android:value="${JPUSH_APPKEY}" />
    </application>
</manifest>
