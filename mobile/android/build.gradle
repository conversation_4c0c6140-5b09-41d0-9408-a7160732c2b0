buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.1.20"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

subprojects {
    afterEvaluate {
        if (project.hasProperty("android")) {
            android {
                compileSdkVersion = 35
                targetSdkVersion = 35
            }
        }
    }
}

subprojects {
    afterEvaluate {
        if (project.hasProperty("android")) {
            android {
                compileSdkVersion = 35
                targetSdkVersion = 35
            }
        }
    }
}

apply plugin: "com.facebook.react.rootproject"