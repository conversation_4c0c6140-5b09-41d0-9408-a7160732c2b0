# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx512m -XX:MaxMetaspaceSize=256m
org.gradle.jvmargs=-Xmx2048m -XX:MaxMetaspaceSize=512m

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true

# Use this property to specify which architecture you want to build.
# You can also override it from the CLI using
# ./gradlew <task> -PreactNativeArchitectures=x86_64
reactNativeArchitectures=armeabi-v7a,arm64-v8a,x86,x86_64

# Use this property to enable or disable the Hermes JS engine.
# If set to false, you will be using JSC as the JS engine.
hermesEnabled=true

# Use this property to enable or disable the New Architecture.
# If set to true, you will be using Fabric as the renderer and TurboModules as the new native modules API.
newArchEnabled=true

# 启用 VisionCamera Frame Processors 以支持 OCR 图片识别
VisionCamera_enableFrameProcessors=false

# 启用 VisionCamera Code Scanner 功能
VisionCamera_enableCodeScanner=true

# Node.js 路径配置 - 确保 Gradle 能找到正确的 Node 环境
react.nodeExecutableAndArgs=/opt/homebrew/bin/node
react.cliPath=../node_modules/react-native/cli.js
