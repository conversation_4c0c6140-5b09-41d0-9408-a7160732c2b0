> Task :gradle-plugin:settings-plugin:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :gradle-plugin:shared:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :gradle-plugin:shared:compileKotlin UP-TO-DATE
> Task :gradle-plugin:shared:compileJava NO-SOURCE
> Task :gradle-plugin:shared:processResources NO-SOURCE
> Task :gradle-plugin:shared:classes UP-TO-DATE
> Task :gradle-plugin:shared:jar UP-TO-DATE
> Task :gradle-plugin:settings-plugin:compileKotlin UP-TO-DATE
> Task :gradle-plugin:settings-plugin:compileJava NO-SOURCE
> Task :gradle-plugin:settings-plugin:pluginDescriptors UP-TO-DATE
> Task :gradle-plugin:settings-plugin:processResources UP-TO-DATE
> Task :gradle-plugin:settings-plugin:classes UP-TO-DATE
> Task :gradle-plugin:settings-plugin:jar UP-TO-DATE
> Task :gradle-plugin:react-native-gradle-plugin:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :gradle-plugin:react-native-gradle-plugin:compileKotlin UP-TO-DATE
> Task :gradle-plugin:react-native-gradle-plugin:compileJava NO-SOURCE
> Task :gradle-plugin:react-native-gradle-plugin:pluginDescriptors UP-TO-DATE
> Task :gradle-plugin:react-native-gradle-plugin:processResources UP-TO-DATE
> Task :gradle-plugin:react-native-gradle-plugin:classes UP-TO-DATE
> Task :gradle-plugin:react-native-gradle-plugin:jar UP-TO-DATE

> Configure project :react-native-vision-camera
[VisionCamera] Thank you for using VisionCamera ❤️
[VisionCamera] If you enjoy using VisionCamera, please consider sponsoring this project: https://github.com/sponsors/mrousavy
[VisionCamera] node_modules found at /Users/<USER>/Documents/project/gongzhimall/mobile/node_modules
[VisionCamera] VisionCamera_enableFrameProcessors is set to true!
[VisionCamera] react-native-worklets-core found, Frame Processors are enabled!
[VisionCamera] VisionCamera_enableCodeScanner is set to false!

> Task :app:dependencies

------------------------------------------------------------
Project ':app'
------------------------------------------------------------

_internal-unified-test-platform-android-device-provider-ddmlib - A configuration to resolve the Unified Test Platform dependencies.
\--- com.android.tools.utp:android-device-provider-ddmlib:31.8.2
     +--- com.android.tools:common:31.8.2
     |    +--- com.android.tools:annotations:31.8.2
     |    +--- com.google.guava:guava:32.0.1-jre
     |    |    +--- com.google.guava:failureaccess:1.0.1
     |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- org.checkerframework:checker-qual:3.33.0
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    \--- com.google.j2objc:j2objc-annotations:2.8
     |    +--- net.java.dev.jna:jna-platform:5.6.0
     |    |    \--- net.java.dev.jna:jna:5.6.0
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21
     |         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |         |    +--- org.jetbrains:annotations:13.0 -> 23.0.0
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 2.0.21 (c)
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 2.0.21 (c)
     |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- com.android.tools.ddms:ddmlib:31.8.2
     |    +--- com.android.tools:common:31.8.2 (*)
     |    +--- com.google.protobuf:protobuf-java:3.22.3
     |    +--- net.sf.kxml:kxml2:2.3.0
     |    \--- org.jetbrains:annotations:23.0.0
     +--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.android.tools.utp:android-device-provider-ddmlib-proto:31.8.2
     |    \--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.android.tools.utp:android-device-provider-profile:31.8.2
     |    +--- com.android.tools:common:31.8.2 (*)
     |    +--- com.google.protobuf:protobuf-java:3.22.3
     |    +--- com.android.tools.utp:android-device-provider-profile-proto:31.8.2
     |    |    \--- com.google.protobuf:protobuf-java:3.22.3
     |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4
     |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4
     |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.6.4
     |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4 (c)
     |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4 (c)
     |         +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 2.0.21 (*)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.6.21 -> 2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- com.google.testing.platform:core-proto:0.0.9-alpha02
     \--- com.google.testing.platform:android-device-provider-local:0.0.9-alpha02

_internal-unified-test-platform-android-device-provider-gradle - A configuration to resolve the Unified Test Platform dependencies.
\--- com.android.tools.utp:android-device-provider-gradle:31.8.2
     +--- com.android.tools:common:31.8.2
     |    +--- com.android.tools:annotations:31.8.2
     |    +--- com.google.guava:guava:32.0.1-jre
     |    |    +--- com.google.guava:failureaccess:1.0.1
     |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- org.checkerframework:checker-qual:3.33.0
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    \--- com.google.j2objc:j2objc-annotations:2.8
     |    +--- net.java.dev.jna:jna-platform:5.6.0
     |    |    \--- net.java.dev.jna:jna:5.6.0
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21
     |         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |         |    +--- org.jetbrains:annotations:13.0
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 2.0.21 (c)
     |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 2.0.21 (c)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.android.tools.utp:android-device-provider-gradle-proto:31.8.2
     |    \--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.android.tools.utp:android-device-provider-profile:31.8.2
     |    +--- com.android.tools:common:31.8.2 (*)
     |    +--- com.google.protobuf:protobuf-java:3.22.3
     |    +--- com.android.tools.utp:android-device-provider-profile-proto:31.8.2
     |    |    \--- com.google.protobuf:protobuf-java:3.22.3
     |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- com.google.testing.platform:core-proto:0.0.9-alpha02
     \--- com.google.testing.platform:android-device-provider-local:0.0.9-alpha02

_internal-unified-test-platform-android-driver-instrumentation - A configuration to resolve the Unified Test Platform dependencies.
\--- com.google.testing.platform:android-driver-instrumentation:0.0.9-alpha02

_internal-unified-test-platform-android-test-plugin - A configuration to resolve the Unified Test Platform dependencies.
\--- com.google.testing.platform:android-test-plugin:0.0.9-alpha02

_internal-unified-test-platform-android-test-plugin-host-additional-test-output - A configuration to resolve the Unified Test Platform dependencies.
\--- com.android.tools.utp:android-test-plugin-host-additional-test-output:31.8.2
     +--- com.android.tools:common:31.8.2
     |    +--- com.android.tools:annotations:31.8.2
     |    +--- com.google.guava:guava:32.0.1-jre
     |    |    +--- com.google.guava:failureaccess:1.0.1
     |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- org.checkerframework:checker-qual:3.33.0
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    \--- com.google.j2objc:j2objc-annotations:2.8
     |    +--- net.java.dev.jna:jna-platform:5.6.0
     |    |    \--- net.java.dev.jna:jna:5.6.0
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21
     |         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |         |    +--- org.jetbrains:annotations:13.0
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 2.0.21 (c)
     |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 2.0.21 (c)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- com.android.tools.utp:utp-common:31.8.2
     |    +--- com.android.tools:common:31.8.2 (*)
     |    +--- com.google.testing.platform:launcher:0.0.9-alpha02
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- com.android.tools.utp:android-test-plugin-host-additional-test-output-proto:31.8.2
     |    \--- com.google.protobuf:protobuf-java:3.22.3
     +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- com.google.protobuf:protobuf-java:3.22.3
     \--- com.google.testing.platform:android-device-provider-local:0.0.9-alpha02

_internal-unified-test-platform-android-test-plugin-host-apk-installer - A configuration to resolve the Unified Test Platform dependencies.
\--- com.android.tools.utp:android-test-plugin-host-apk-installer:31.8.2
     +--- com.android.tools:common:31.8.2
     |    +--- com.android.tools:annotations:31.8.2
     |    +--- com.google.guava:guava:32.0.1-jre
     |    |    +--- com.google.guava:failureaccess:1.0.1
     |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- org.checkerframework:checker-qual:3.33.0
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    \--- com.google.j2objc:j2objc-annotations:2.8
     |    +--- net.java.dev.jna:jna-platform:5.6.0
     |    |    \--- net.java.dev.jna:jna:5.6.0
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21
     |         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |         |    +--- org.jetbrains:annotations:13.0
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 2.0.21 (c)
     |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 2.0.21 (c)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.android.tools.utp:android-test-plugin-host-apk-installer-proto:31.8.2
     |    \--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.google.testing.platform:android-device-provider-local:0.0.9-alpha02
     \--- com.google.testing.platform:core-proto:0.0.9-alpha02

_internal-unified-test-platform-android-test-plugin-host-coverage - A configuration to resolve the Unified Test Platform dependencies.
\--- com.android.tools.utp:android-test-plugin-host-coverage:31.8.2
     +--- com.android.tools:common:31.8.2
     |    +--- com.android.tools:annotations:31.8.2
     |    +--- com.google.guava:guava:32.0.1-jre
     |    |    +--- com.google.guava:failureaccess:1.0.1
     |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- org.checkerframework:checker-qual:3.33.0
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    \--- com.google.j2objc:j2objc-annotations:2.8
     |    +--- net.java.dev.jna:jna-platform:5.6.0
     |    |    \--- net.java.dev.jna:jna:5.6.0
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21
     |         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |         |    +--- org.jetbrains:annotations:13.0
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 2.0.21 (c)
     |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 2.0.21 (c)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- com.android.tools.utp:android-test-plugin-host-coverage-proto:31.8.2
     |    \--- com.google.protobuf:protobuf-java:3.22.3
     +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- com.google.protobuf:protobuf-java:3.22.3
     \--- com.google.testing.platform:android-device-provider-local:0.0.9-alpha02

_internal-unified-test-platform-android-test-plugin-host-device-info - A configuration to resolve the Unified Test Platform dependencies.
\--- com.android.tools.utp:android-test-plugin-host-device-info:31.8.2
     +--- com.android.tools:common:31.8.2
     |    +--- com.android.tools:annotations:31.8.2
     |    +--- com.google.guava:guava:32.0.1-jre
     |    |    +--- com.google.guava:failureaccess:1.0.1
     |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- org.checkerframework:checker-qual:3.33.0
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    \--- com.google.j2objc:j2objc-annotations:2.8
     |    +--- net.java.dev.jna:jna-platform:5.6.0
     |    |    \--- net.java.dev.jna:jna:5.6.0
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21
     |         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |         |    +--- org.jetbrains:annotations:13.0
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 2.0.21 (c)
     |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 2.0.21 (c)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.android.tools.utp:utp-common:31.8.2
     |    +--- com.android.tools:common:31.8.2 (*)
     |    +--- com.google.testing.platform:launcher:0.0.9-alpha02
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- com.android.tools.utp:android-test-plugin-host-device-info-proto:31.8.2
     |    \--- com.google.protobuf:protobuf-java:3.22.3
     \--- com.google.testing.platform:android-device-provider-local:0.0.9-alpha02

_internal-unified-test-platform-android-test-plugin-host-emulator-control - A configuration to resolve the Unified Test Platform dependencies.
\--- com.android.tools.utp:android-test-plugin-host-emulator-control:31.8.2
     +--- com.android.tools:common:31.8.2
     |    +--- com.android.tools:annotations:31.8.2
     |    +--- com.google.guava:guava:32.0.1-jre
     |    |    +--- com.google.guava:failureaccess:1.0.1
     |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- org.checkerframework:checker-qual:3.33.0
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    \--- com.google.j2objc:j2objc-annotations:2.8
     |    +--- net.java.dev.jna:jna-platform:5.6.0
     |    |    \--- net.java.dev.jna:jna:5.6.0
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21
     |         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |         |    +--- org.jetbrains:annotations:13.0
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 2.0.21 (c)
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 2.0.21 (c)
     |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4
     |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4
     |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.6.4
     |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4 (c)
     |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4 (c)
     |         +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 2.0.21 (*)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.6.21 -> 2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- commons-io:commons-io:2.13.0
     +--- com.google.code.gson:gson:2.10.1
     +--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.google.guava:guava:32.0.1-jre (*)
     +--- com.android.tools.utp:android-test-plugin-host-emulator-control-proto:31.8.2
     |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     |    \--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.android.tools.emulator:proto:31.8.2
     |    +--- com.google.code.gson:gson:2.10.1
     |    +--- com.google.guava:guava:32.0.1-jre (*)
     |    +--- io.grpc:grpc-core:1.57.0
     |    |    +--- io.grpc:grpc-api:1.57.0
     |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    |    \--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    |    +--- com.google.code.gson:gson:2.10.1
     |    |    +--- com.google.android:annotations:*******
     |    |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.23
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    +--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    |    +--- io.perfmark:perfmark-api:0.26.0
     |    |    \--- io.grpc:grpc-context:1.57.0
     |    |         \--- io.grpc:grpc-api:1.57.0 (*)
     |    +--- io.grpc:grpc-netty:1.57.0
     |    |    +--- io.grpc:grpc-core:1.57.0 (*)
     |    |    +--- io.netty:netty-codec-http2:4.1.93.Final
     |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.93.Final
     |    |    |    |    \--- io.netty:netty-common:4.1.93.Final
     |    |    |    +--- io.netty:netty-transport:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    |    \--- io.netty:netty-resolver:4.1.93.Final
     |    |    |    |         \--- io.netty:netty-common:4.1.93.Final
     |    |    |    +--- io.netty:netty-codec:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    |    \--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    +--- io.netty:netty-handler:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-resolver:4.1.93.Final (*)
     |    |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    |    +--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    |    +--- io.netty:netty-transport-native-unix-common:4.1.93.Final
     |    |    |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    |    |    \--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    |    \--- io.netty:netty-codec:4.1.93.Final (*)
     |    |    |    \--- io.netty:netty-codec-http:4.1.93.Final
     |    |    |         +--- io.netty:netty-common:4.1.93.Final
     |    |    |         +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |         +--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |         +--- io.netty:netty-codec:4.1.93.Final (*)
     |    |    |         \--- io.netty:netty-handler:4.1.93.Final (*)
     |    |    +--- io.netty:netty-handler-proxy:4.1.93.Final
     |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    +--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    +--- io.netty:netty-codec:4.1.93.Final (*)
     |    |    |    +--- io.netty:netty-codec-socks:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    |    +--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    |    \--- io.netty:netty-codec:4.1.93.Final (*)
     |    |    |    \--- io.netty:netty-codec-http:4.1.93.Final (*)
     |    |    +--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    +--- io.perfmark:perfmark-api:0.26.0
     |    |    \--- io.netty:netty-transport-native-unix-common:4.1.93.Final (*)
     |    +--- io.grpc:grpc-protobuf:1.57.0
     |    |    +--- io.grpc:grpc-api:1.57.0 (*)
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- com.google.protobuf:protobuf-java:3.22.3
     |    |    +--- com.google.api.grpc:proto-google-common-protos:2.17.0
     |    |    |    \--- com.google.protobuf:protobuf-java:3.21.12 -> 3.22.3
     |    |    +--- io.grpc:grpc-protobuf-lite:1.57.0
     |    |    |    +--- io.grpc:grpc-api:1.57.0 (*)
     |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    \--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    |    \--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    +--- io.grpc:grpc-stub:1.57.0
     |    |    +--- io.grpc:grpc-api:1.57.0 (*)
     |    |    +--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    |    \--- com.google.errorprone:error_prone_annotations:2.18.0
     |    +--- javax.annotation:javax.annotation-api:1.3.2
     |    \--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.google.testing.platform:android-device-provider-local:0.0.9-alpha02
     +--- com.google.crypto.tink:tink:1.7.0
     |    +--- com.google.protobuf:protobuf-java:3.19.3 -> 3.22.3
     |    \--- com.google.code.gson:gson:2.8.9 -> 2.10.1
     \--- com.google.testing.platform:android-driver-instrumentation:0.0.9-alpha02

_internal-unified-test-platform-android-test-plugin-host-logcat - A configuration to resolve the Unified Test Platform dependencies.
\--- com.android.tools.utp:android-test-plugin-host-logcat:31.8.2
     +--- com.android.tools:common:31.8.2
     |    +--- com.android.tools:annotations:31.8.2
     |    +--- com.google.guava:guava:32.0.1-jre
     |    |    +--- com.google.guava:failureaccess:1.0.1
     |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- org.checkerframework:checker-qual:3.33.0
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    \--- com.google.j2objc:j2objc-annotations:2.8
     |    +--- net.java.dev.jna:jna-platform:5.6.0
     |    |    \--- net.java.dev.jna:jna:5.6.0
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21
     |         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |         |    +--- org.jetbrains:annotations:13.0
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 2.0.21 (c)
     |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 2.0.21 (c)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- com.android.tools.utp:utp-common:31.8.2
     |    +--- com.android.tools:common:31.8.2 (*)
     |    +--- com.google.testing.platform:launcher:0.0.9-alpha02
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- com.android.tools.utp:android-test-plugin-host-logcat-proto:31.8.2
     |    \--- com.google.protobuf:protobuf-java:3.22.3
     +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- com.google.protobuf:protobuf-java:3.22.3
     \--- com.google.testing.platform:android-device-provider-local:0.0.9-alpha02

_internal-unified-test-platform-android-test-plugin-host-retention - A configuration to resolve the Unified Test Platform dependencies.
\--- com.android.tools.utp:android-test-plugin-host-retention:31.8.2
     +--- com.android.tools:common:31.8.2
     |    +--- com.android.tools:annotations:31.8.2
     |    +--- com.google.guava:guava:32.0.1-jre
     |    |    +--- com.google.guava:failureaccess:1.0.1
     |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- org.checkerframework:checker-qual:3.33.0
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    \--- com.google.j2objc:j2objc-annotations:2.8
     |    +--- net.java.dev.jna:jna-platform:5.6.0
     |    |    \--- net.java.dev.jna:jna:5.6.0
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21
     |         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |         |    +--- org.jetbrains:annotations:13.0
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 2.0.21 (c)
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 2.0.21 (c)
     |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4
     |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4
     |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.6.4
     |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4 (c)
     |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4 (c)
     |         +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 2.0.21 (*)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.6.21 -> 2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- commons-io:commons-io:2.13.0
     +--- com.google.code.gson:gson:2.10.1
     +--- io.grpc:grpc-core:1.57.0
     |    +--- io.grpc:grpc-api:1.57.0
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    \--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    +--- com.google.code.gson:gson:2.10.1
     |    +--- com.google.android:annotations:*******
     |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.23
     |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    +--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    +--- io.perfmark:perfmark-api:0.26.0
     |    \--- io.grpc:grpc-context:1.57.0
     |         \--- io.grpc:grpc-api:1.57.0 (*)
     +--- io.grpc:grpc-netty:1.57.0
     |    +--- io.grpc:grpc-core:1.57.0 (*)
     |    +--- io.netty:netty-codec-http2:4.1.93.Final
     |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    +--- io.netty:netty-buffer:4.1.93.Final
     |    |    |    \--- io.netty:netty-common:4.1.93.Final
     |    |    +--- io.netty:netty-transport:4.1.93.Final
     |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    \--- io.netty:netty-resolver:4.1.93.Final
     |    |    |         \--- io.netty:netty-common:4.1.93.Final
     |    |    +--- io.netty:netty-codec:4.1.93.Final
     |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    \--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    +--- io.netty:netty-handler:4.1.93.Final
     |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    +--- io.netty:netty-resolver:4.1.93.Final (*)
     |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    +--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    +--- io.netty:netty-transport-native-unix-common:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    |    \--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    \--- io.netty:netty-codec:4.1.93.Final (*)
     |    |    \--- io.netty:netty-codec-http:4.1.93.Final
     |    |         +--- io.netty:netty-common:4.1.93.Final
     |    |         +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |         +--- io.netty:netty-transport:4.1.93.Final (*)
     |    |         +--- io.netty:netty-codec:4.1.93.Final (*)
     |    |         \--- io.netty:netty-handler:4.1.93.Final (*)
     |    +--- io.netty:netty-handler-proxy:4.1.93.Final
     |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    +--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    +--- io.netty:netty-codec:4.1.93.Final (*)
     |    |    +--- io.netty:netty-codec-socks:4.1.93.Final
     |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    +--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    \--- io.netty:netty-codec:4.1.93.Final (*)
     |    |    \--- io.netty:netty-codec-http:4.1.93.Final (*)
     |    +--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    +--- io.perfmark:perfmark-api:0.26.0
     |    \--- io.netty:netty-transport-native-unix-common:4.1.93.Final (*)
     +--- io.grpc:grpc-protobuf:1.57.0
     |    +--- io.grpc:grpc-api:1.57.0 (*)
     |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    +--- com.google.protobuf:protobuf-java:3.22.3
     |    +--- com.google.api.grpc:proto-google-common-protos:2.17.0
     |    |    \--- com.google.protobuf:protobuf-java:3.21.12 -> 3.22.3
     |    +--- io.grpc:grpc-protobuf-lite:1.57.0
     |    |    +--- io.grpc:grpc-api:1.57.0 (*)
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    \--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    \--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     +--- io.grpc:grpc-stub:1.57.0
     |    +--- io.grpc:grpc-api:1.57.0 (*)
     |    +--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    \--- com.google.errorprone:error_prone_annotations:2.18.0
     +--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.google.guava:guava:32.0.1-jre (*)
     +--- com.android.tools.utp:utp-common:31.8.2
     |    +--- com.android.tools:common:31.8.2 (*)
     |    +--- com.google.testing.platform:launcher:0.0.9-alpha02
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- com.android.tools.utp:android-test-plugin-host-retention-proto:31.8.2
     |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     |    \--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.android.tools.emulator:proto:31.8.2
     |    +--- com.google.code.gson:gson:2.10.1
     |    +--- com.google.guava:guava:32.0.1-jre (*)
     |    +--- io.grpc:grpc-core:1.57.0 (*)
     |    +--- io.grpc:grpc-netty:1.57.0 (*)
     |    +--- io.grpc:grpc-protobuf:1.57.0 (*)
     |    +--- io.grpc:grpc-stub:1.57.0 (*)
     |    +--- javax.annotation:javax.annotation-api:1.3.2
     |    \--- com.google.protobuf:protobuf-java:3.22.3
     \--- com.google.testing.platform:android-device-provider-local:0.0.9-alpha02

_internal-unified-test-platform-android-test-plugin-result-listener-gradle - A configuration to resolve the Unified Test Platform dependencies.
\--- com.android.tools.utp:android-test-plugin-result-listener-gradle:31.8.2
     +--- com.android.tools:common:31.8.2
     |    +--- com.android.tools:annotations:31.8.2
     |    +--- com.google.guava:guava:32.0.1-jre
     |    |    +--- com.google.guava:failureaccess:1.0.1
     |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- org.checkerframework:checker-qual:3.33.0
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    \--- com.google.j2objc:j2objc-annotations:2.8
     |    +--- net.java.dev.jna:jna-platform:5.6.0
     |    |    \--- net.java.dev.jna:jna:5.6.0
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21
     |         +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |         |    +--- org.jetbrains:annotations:13.0
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 2.0.21 (c)
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 2.0.21 (c)
     |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- com.android.tools.utp:android-test-plugin-result-listener-gradle-proto:31.8.2
     |    +--- com.google.code.gson:gson:2.10.1
     |    +--- com.google.guava:guava:32.0.1-jre (*)
     |    +--- io.grpc:grpc-core:1.57.0
     |    |    +--- io.grpc:grpc-api:1.57.0
     |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    |    \--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    |    +--- com.google.code.gson:gson:2.10.1
     |    |    +--- com.google.android:annotations:*******
     |    |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.23
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    +--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    |    +--- io.perfmark:perfmark-api:0.26.0
     |    |    \--- io.grpc:grpc-context:1.57.0
     |    |         \--- io.grpc:grpc-api:1.57.0 (*)
     |    +--- io.grpc:grpc-netty:1.57.0
     |    |    +--- io.grpc:grpc-core:1.57.0 (*)
     |    |    +--- io.netty:netty-codec-http2:4.1.93.Final
     |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.93.Final
     |    |    |    |    \--- io.netty:netty-common:4.1.93.Final
     |    |    |    +--- io.netty:netty-transport:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    |    \--- io.netty:netty-resolver:4.1.93.Final
     |    |    |    |         \--- io.netty:netty-common:4.1.93.Final
     |    |    |    +--- io.netty:netty-codec:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    |    \--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    +--- io.netty:netty-handler:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-resolver:4.1.93.Final (*)
     |    |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    |    +--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    |    +--- io.netty:netty-transport-native-unix-common:4.1.93.Final
     |    |    |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    |    |    \--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    |    \--- io.netty:netty-codec:4.1.93.Final (*)
     |    |    |    \--- io.netty:netty-codec-http:4.1.93.Final
     |    |    |         +--- io.netty:netty-common:4.1.93.Final
     |    |    |         +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |         +--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |         +--- io.netty:netty-codec:4.1.93.Final (*)
     |    |    |         \--- io.netty:netty-handler:4.1.93.Final (*)
     |    |    +--- io.netty:netty-handler-proxy:4.1.93.Final
     |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    +--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    +--- io.netty:netty-codec:4.1.93.Final (*)
     |    |    |    +--- io.netty:netty-codec-socks:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.93.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.93.Final (*)
     |    |    |    |    +--- io.netty:netty-transport:4.1.93.Final (*)
     |    |    |    |    \--- io.netty:netty-codec:4.1.93.Final (*)
     |    |    |    \--- io.netty:netty-codec-http:4.1.93.Final (*)
     |    |    +--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    |    +--- com.google.errorprone:error_prone_annotations:2.18.0
     |    |    +--- io.perfmark:perfmark-api:0.26.0
     |    |    \--- io.netty:netty-transport-native-unix-common:4.1.93.Final (*)
     |    +--- io.grpc:grpc-protobuf:1.57.0
     |    |    +--- io.grpc:grpc-api:1.57.0 (*)
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- com.google.protobuf:protobuf-java:3.22.3
     |    |    +--- com.google.api.grpc:proto-google-common-protos:2.17.0
     |    |    |    \--- com.google.protobuf:protobuf-java:3.21.12 -> 3.22.3
     |    |    +--- io.grpc:grpc-protobuf-lite:1.57.0
     |    |    |    +--- io.grpc:grpc-api:1.57.0 (*)
     |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    \--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    |    \--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    +--- io.grpc:grpc-stub:1.57.0
     |    |    +--- io.grpc:grpc-api:1.57.0 (*)
     |    |    +--- com.google.guava:guava:32.0.1-android -> 32.0.1-jre (*)
     |    |    \--- com.google.errorprone:error_prone_annotations:2.18.0
     |    +--- javax.annotation:javax.annotation-api:1.3.2
     |    \--- com.google.protobuf:protobuf-java:3.22.3
     +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21 (*)
     +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4
     |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4
     |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.6.4
     |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4 (c)
     |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4 (c)
     |         +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 2.0.21 (*)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.6.21 -> 2.0.21
     |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     +--- com.google.code.gson:gson:2.10.1
     +--- io.grpc:grpc-core:1.57.0 (*)
     +--- io.grpc:grpc-protobuf:1.57.0 (*)
     +--- io.grpc:grpc-stub:1.57.0 (*)
     +--- io.grpc:grpc-netty:1.57.0 (*)
     +--- com.google.protobuf:protobuf-java:3.22.3
     +--- com.google.guava:guava:32.0.1-jre (*)
     +--- com.google.testing.platform:core-proto:0.0.9-alpha02
     \--- com.google.testing.platform:android-device-provider-local:0.0.9-alpha02

_internal-unified-test-platform-core - A configuration to resolve the Unified Test Platform dependencies.
\--- com.google.testing.platform:core:0.0.9-alpha02 FAILED

_internal-unified-test-platform-launcher - A configuration to resolve the Unified Test Platform dependencies.
\--- com.google.testing.platform:launcher:0.0.9-alpha02

_internal_prefab_binary - The Prefab tool to use for generating native build system bindings.
\--- com.google.prefab:cli:2.1.0

androidApis - Configuration providing various types of Android JAR file
No dependencies

androidJdkImage - Configuration providing JDK image for compiling Java 9+ sources
No dependencies

androidTestAnnotationProcessor - Classpath for the annotation processor for 'androidTest'. (n)
No dependencies

androidTestApi (n)
No dependencies

androidTestApiDependenciesMetadata
No dependencies

androidTestCompileOnly - Compile only dependencies for 'androidTest' sources. (n)
No dependencies

androidTestCompileOnlyDependenciesMetadata
No dependencies

androidTestDebugAnnotationProcessor - Classpath for the annotation processor for 'androidTestDebug'. (n)
No dependencies

androidTestDebugApi (n)
No dependencies

androidTestDebugApiDependenciesMetadata
No dependencies

androidTestDebugCompileOnly - Compile only dependencies for 'androidTestDebug' sources. (n)
No dependencies

androidTestDebugCompileOnlyDependenciesMetadata
No dependencies

androidTestDebugImplementation - Implementation only dependencies for 'androidTestDebug' sources. (n)
No dependencies

androidTestDebugImplementationDependenciesMetadata
No dependencies

androidTestDebugIntransitiveDependenciesMetadata
No dependencies

androidTestDebugRuntimeOnly - Runtime only dependencies for 'androidTestDebug' sources. (n)
No dependencies

androidTestDebugWearApp - Link to a wear app to embed for object 'androidTestDebug'. (n)
No dependencies

androidTestImplementation - Implementation only dependencies for 'androidTest' sources. (n)
No dependencies

androidTestImplementationDependenciesMetadata
No dependencies

androidTestIntransitiveDependenciesMetadata
No dependencies

androidTestReleaseAnnotationProcessor - Classpath for the annotation processor for 'androidTestRelease'. (n)
No dependencies

androidTestReleaseApi (n)
No dependencies

androidTestReleaseApiDependenciesMetadata
No dependencies

androidTestReleaseCompileOnly - Compile only dependencies for 'androidTestRelease' sources. (n)
No dependencies

androidTestReleaseCompileOnlyDependenciesMetadata
No dependencies

androidTestReleaseImplementation - Implementation only dependencies for 'androidTestRelease' sources. (n)
No dependencies

androidTestReleaseImplementationDependenciesMetadata
No dependencies

androidTestReleaseIntransitiveDependenciesMetadata
No dependencies

androidTestReleaseRuntimeOnly - Runtime only dependencies for 'androidTestRelease' sources. (n)
No dependencies

androidTestReleaseWearApp - Link to a wear app to embed for object 'androidTestRelease'. (n)
No dependencies

androidTestRuntimeOnly - Runtime only dependencies for 'androidTest' sources. (n)
No dependencies

androidTestUtil - Additional APKs used during instrumentation testing.
No dependencies

androidTestWearApp - Link to a wear app to embed for object 'androidTest'. (n)
No dependencies

annotationProcessor - Classpath for the annotation processor for 'main'. (n)
No dependencies

api - API dependencies for 'main' sources. (n)
No dependencies

apiDependenciesMetadata
\--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21

compileOnly - Compile only dependencies for 'main' sources. (n)
No dependencies

compileOnlyApi - Compile only API dependencies for 'main' sources. (n)
No dependencies

compileOnlyDependenciesMetadata
No dependencies

coreLibraryDesugaring - Configuration to desugar libraries
No dependencies

debugAndroidTestAnnotationProcessorClasspath - Resolved configuration for annotation-processor for variant: debugAndroidTest
No dependencies

debugAndroidTestApi (n)
No dependencies

debugAndroidTestApiDependenciesMetadata
No dependencies

debugAndroidTestCompilationApi - API dependencies for '/debugAndroidTest'. (n)
No dependencies

debugAndroidTestCompilationCompileOnly - Compile only dependencies for '/debugAndroidTest'. (n)
No dependencies

debugAndroidTestCompilationImplementation - Implementation only dependencies for '/debugAndroidTest'. (n)
No dependencies

debugAndroidTestCompilationRuntimeOnly - Runtime only dependencies for '/debugAndroidTest'. (n)
No dependencies

debugAndroidTestCompileClasspath - Compile classpath for '/debugAndroidTest'.
+--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
|    +--- org.jetbrains:annotations:13.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 (c)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 (c)
+--- project :app (*)
+--- project :react-native-voice_voice
+--- project :react-native-image-picker
+--- project :react-native-permissions
+--- project :react-native-vision-camera
|    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- project :react-native-worklets-core
+--- com.facebook.react:react-android -> 0.79.3
|    +--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1
|    |    +--- androidx.activity:activity:1.6.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0
|    |    |    |    \--- androidx.annotation:annotation-jvm:1.6.0
|    |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|    |    |    +--- androidx.core:core:1.8.0 -> 1.13.1
|    |    |    |    +--- androidx.annotation:annotation:1.6.0 (*)
|    |    |    |    +--- androidx.annotation:annotation-experimental:1.4.0
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
|    |    |    |    |    +--- androidx.arch.core:core-common:2.2.0
|    |    |    |    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.2
|    |    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
|    |    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|    |    |    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4
|    |    |    |    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4
|    |    |    |    |    |    |    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4
|    |    |    |    |    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.6.4
|    |    |    |    |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 (c)
|    |    |    |    |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4 (c)
|    |    |    |    |    |    |    |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4 (c)
|    |    |    |    |    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 1.8.0
|    |    |    |    |    |    |    |         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|    |    |    |    |    |    |    |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0
|    |    |    |    |    |    |    |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|    |    |    |    |    |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.6.21 -> 2.0.21
|    |    |    |    |    |    |    |              \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|    |    |    |    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.6.4 (*)
|    |    |    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 1.8.0 (*)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.6.2 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.2 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.2 (c)
|    |    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2 (c)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.2 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.2 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.2 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2 (c)
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-livedata:2.6.2 (c)
|    |    |    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
|    |    |    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    |    |    |         \--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.6.2 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.6.2
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.2 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.6.2 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.2 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1 -> 2.6.2
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.6.0 (*)
|    |    |    |    +--- androidx.core:core-ktx:1.2.0 FAILED
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.2
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.2 (*)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.2 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.6.2 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.2 (c)
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.2 (*)
|    |    |    |    +--- androidx.savedstate:savedstate:1.2.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.2 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.6.2 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.2 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.6.2 (c)
|    |    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    +--- androidx.annotation:annotation:1.3.0 -> 1.6.0 (*)
|    |    +--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0
|    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.6.0 (*)
|    |    |    +--- androidx.core:core:1.6.0 -> 1.13.1 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
|    |    |    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |    |    |    +--- androidx.interpolator:interpolator:1.0.0
|    |    |    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.6.0 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    \--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1 (c)
|    |    +--- androidx.core:core:1.9.0 -> 1.13.1 (*)
|    |    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.6.0 (*)
|    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.6.0 (*)
|    |    |         \--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    +--- androidx.fragment:fragment:1.3.6
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
|    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.6.0 (*)
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.customview:customview:1.0.0 (*)
|    |    |    +--- androidx.loader:loader:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.6.0 (*)
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.6.2
|    |    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0
|    |    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
|    |    |    |    |    |    \--- androidx.arch.core:core-common:2.1.0 -> 2.2.0 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.2 (*)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.2 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.2 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.2 (c)
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.6.2 (*)
|    |    |    +--- androidx.activity:activity:1.2.4 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 -> 2.6.2 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 -> 2.6.2 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 -> 2.6.2 (*)
|    |    |    +--- androidx.savedstate:savedstate:1.1.0 -> 1.2.1 (*)
|    |    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.4.0 (*)
|    |    \--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    +--- androidx.appcompat:appcompat-resources:1.7.0 (*)
|    +--- androidx.autofill:autofill:1.1.0
|    +--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    \--- androidx.interpolator:interpolator:1.0.0 (*)
|    +--- androidx.tracing:tracing:1.1.0
|    +--- com.facebook.fbjni:fbjni:0.7.0
|    +--- com.facebook.fresco:fresco:3.6.0
|    |    +--- com.facebook.fresco:fbcore:3.6.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:drawee:3.6.0
|    |    |    +--- com.facebook.fresco:ui-core:3.6.0
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:imagepipeline:3.6.0
|    |    |    \--- com.facebook.fresco:imagepipeline-base:3.6.0
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0
|    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0
|    |    +--- com.facebook.fresco:memory-type-native:3.6.0
|    |    +--- com.facebook.fresco:memory-type-java:3.6.0
|    |    +--- com.facebook.fresco:nativeimagefilters:3.6.0
|    |    +--- com.facebook.fresco:nativeimagetranscoder:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:imagepipeline-okhttp3:3.6.0
|    |    +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.2
|    |    |    +--- com.squareup.okio:okio:2.8.0 -> 2.9.0
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.10 -> 2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:middleware:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:ui-common:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.infer.annotation:infer-annotation:0.18.0
|    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    \--- org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72
|    +--- com.facebook.soloader:soloader:0.12.1
|    |    +--- com.facebook.soloader:annotation:0.12.1
|    |    \--- com.facebook.soloader:nativeloader:0.12.1
|    +--- com.facebook.yoga:proguard-annotations:1.19.0
|    +--- com.google.code.findbugs:jsr305:3.0.2
|    +--- com.squareup.okhttp3:okhttp-urlconnection:4.9.2
|    |    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.0 (*)
|    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|    +--- com.squareup.okio:okio:2.9.0 (*)
|    +--- javax.inject:javax.inject:1
|    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
\--- com.facebook.react:hermes-android -> 0.79.3

debugAndroidTestCompileOnly (n)
No dependencies

debugAndroidTestCompileOnlyDependenciesMetadata
No dependencies

debugAndroidTestImplementation (n)
No dependencies

debugAndroidTestImplementationDependenciesMetadata
No dependencies

debugAndroidTestIntransitiveDependenciesMetadata
No dependencies

debugAndroidTestRuntimeClasspath - Runtime classpath of '/debugAndroidTest'.
No dependencies

debugAndroidTestRuntimeOnly (n)
No dependencies

debugAnnotationProcessor - Classpath for the annotation processor for 'debug'. (n)
No dependencies

debugAnnotationProcessorClasspath - Resolved configuration for annotation-processor for variant: debug
No dependencies

debugApi - API dependencies for 'debug' sources. (n)
No dependencies

debugApiDependenciesMetadata
No dependencies

debugApiElements - API elements for debug (n)
No dependencies

debugCompilationApi - API dependencies for '/debug'. (n)
No dependencies

debugCompilationCompileOnly - Compile only dependencies for '/debug'. (n)
No dependencies

debugCompilationImplementation - Implementation only dependencies for '/debug'. (n)
No dependencies

debugCompilationRuntimeOnly - Runtime only dependencies for '/debug'. (n)
No dependencies

debugCompileClasspath - Compile classpath for '/debug'.
+--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
|    +--- org.jetbrains:annotations:13.0 -> 23.0.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 (c)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 (c)
+--- project :react-native-voice_voice
+--- project :react-native-image-picker
+--- project :react-native-permissions
+--- project :react-native-vision-camera
|    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- project :react-native-worklets-core
+--- com.facebook.react:react-android -> 0.79.3
|    +--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1
|    |    +--- androidx.activity:activity:1.6.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1
|    |    |    |    \--- androidx.annotation:annotation-jvm:1.8.1
|    |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    |    +--- androidx.core:core:1.8.0 -> 1.13.1
|    |    |    |    +--- androidx.annotation:annotation:1.6.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.annotation:annotation-experimental:1.4.0 -> 1.4.1
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 -> 2.8.7
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-runtime-android:2.8.7
|    |    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|    |    |    |    |         +--- androidx.arch.core:core-common:2.2.0
|    |    |    |    |         |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7
|    |    |    |    |         |    \--- androidx.lifecycle:lifecycle-common-jvm:2.8.7
|    |    |    |    |         |         +--- androidx.annotation:annotation:1.8.1 (*)
|    |    |    |    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0
|    |    |    |    |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0
|    |    |    |    |         |         |         +--- org.jetbrains:annotations:23.0.0
|    |    |    |    |         |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0
|    |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (c)
|    |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0 (c)
|    |    |    |    |         |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (c)
|    |    |    |    |         |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |         |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0
|    |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (*)
|    |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0 (*)
|    |    |    |    |         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    |    |    |         \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-android:2.8.7
|    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1 -> 2.8.7
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.13.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    +--- androidx.core:core:1.13.1 (*)
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (*)
|    |    |    |    +--- androidx.savedstate:savedstate:1.2.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    +--- androidx.annotation:annotation:1.3.0 -> 1.8.1 (*)
|    |    +--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0
|    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.6.0 -> 1.13.1 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |    |    |    +--- androidx.interpolator:interpolator:1.0.0
|    |    |    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    \--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1 (c)
|    |    +--- androidx.core:core:1.9.0 -> 1.13.1 (*)
|    |    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |         \--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    +--- androidx.fragment:fragment:1.3.6
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.customview:customview:1.0.0 (*)
|    |    |    +--- androidx.loader:loader:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.8.7
|    |    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0
|    |    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    |    \--- androidx.arch.core:core-common:2.2.0 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|    |    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.8.7 (*)
|    |    |    +--- androidx.activity:activity:1.2.4 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 -> 2.8.7 (*)
|    |    |    +--- androidx.savedstate:savedstate:1.1.0 -> 1.2.1 (*)
|    |    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.4.1 (*)
|    |    \--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    +--- androidx.appcompat:appcompat-resources:1.7.0 (*)
|    +--- androidx.autofill:autofill:1.1.0
|    +--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    \--- androidx.interpolator:interpolator:1.0.0 (*)
|    +--- androidx.tracing:tracing:1.1.0 -> 1.2.0
|    +--- com.facebook.fbjni:fbjni:0.7.0
|    +--- com.facebook.fresco:fresco:3.6.0
|    |    +--- com.facebook.fresco:fbcore:3.6.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:drawee:3.6.0
|    |    |    +--- com.facebook.fresco:ui-core:3.6.0
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:imagepipeline:3.6.0
|    |    |    \--- com.facebook.fresco:imagepipeline-base:3.6.0
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0
|    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0
|    |    +--- com.facebook.fresco:memory-type-native:3.6.0
|    |    +--- com.facebook.fresco:memory-type-java:3.6.0
|    |    +--- com.facebook.fresco:nativeimagefilters:3.6.0
|    |    +--- com.facebook.fresco:nativeimagetranscoder:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:imagepipeline-okhttp3:3.6.0
|    |    +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.2
|    |    |    +--- com.squareup.okio:okio:2.8.0 -> 2.9.0
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.10 -> 2.0.21
|    |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:middleware:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:ui-common:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.infer.annotation:infer-annotation:0.18.0
|    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    \--- org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72
|    +--- com.facebook.soloader:soloader:0.12.1
|    |    +--- com.facebook.soloader:annotation:0.12.1
|    |    \--- com.facebook.soloader:nativeloader:0.12.1
|    +--- com.facebook.yoga:proguard-annotations:1.19.0
|    +--- com.google.code.findbugs:jsr305:3.0.2
|    +--- com.squareup.okhttp3:okhttp-urlconnection:4.9.2
|    |    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.0
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0
|    |              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|    +--- com.squareup.okio:okio:2.9.0 (*)
|    +--- javax.inject:javax.inject:1
|    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- com.facebook.react:hermes-android -> 0.79.3
+--- org.jetbrains.kotlin:kotlin-stdlib:{strictly 2.0.21} -> 2.0.21 (c)
+--- com.facebook.react:react-android:{strictly 0.79.3} -> 0.79.3 (c)
+--- com.facebook.react:hermes-android:{strictly 0.79.3} -> 0.79.3 (c)
+--- org.jetbrains:annotations:{strictly 23.0.0} -> 23.0.0 (c)
+--- androidx.appcompat:appcompat:{strictly 1.6.1} -> 1.6.1 (c)
+--- androidx.appcompat:appcompat-resources:{strictly 1.7.0} -> 1.7.0 (c)
+--- androidx.autofill:autofill:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.swiperefreshlayout:swiperefreshlayout:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.tracing:tracing:{strictly 1.2.0} -> 1.2.0 (c)
+--- com.facebook.fbjni:fbjni:{strictly 0.7.0} -> 0.7.0 (c)
+--- com.facebook.fresco:fresco:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline-okhttp3:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:middleware:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:ui-common:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.infer.annotation:infer-annotation:{strictly 0.18.0} -> 0.18.0 (c)
+--- com.facebook.soloader:soloader:{strictly 0.12.1} -> 0.12.1 (c)
+--- com.facebook.yoga:proguard-annotations:{strictly 1.19.0} -> 1.19.0 (c)
+--- com.google.code.findbugs:jsr305:{strictly 3.0.2} -> 3.0.2 (c)
+--- com.squareup.okhttp3:okhttp-urlconnection:{strictly 4.9.2} -> 4.9.2 (c)
+--- com.squareup.okhttp3:okhttp:{strictly 4.9.2} -> 4.9.2 (c)
+--- com.squareup.okio:okio:{strictly 2.9.0} -> 2.9.0 (c)
+--- javax.inject:javax.inject:{strictly 1} -> 1 (c)
+--- androidx.activity:activity:{strictly 1.6.0} -> 1.6.0 (c)
+--- androidx.annotation:annotation:{strictly 1.8.1} -> 1.8.1 (c)
+--- androidx.core:core:{strictly 1.13.1} -> 1.13.1 (c)
+--- androidx.cursoradapter:cursoradapter:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.drawerlayout:drawerlayout:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.fragment:fragment:{strictly 1.3.6} -> 1.3.6 (c)
+--- androidx.savedstate:savedstate:{strictly 1.2.1} -> 1.2.1 (c)
+--- androidx.vectordrawable:vectordrawable:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.vectordrawable:vectordrawable-animated:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.interpolator:interpolator:{strictly 1.0.0} -> 1.0.0 (c)
+--- com.facebook.fresco:fbcore:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:drawee:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline-native:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:memory-type-ashmem:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:memory-type-native:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:memory-type-java:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:nativeimagefilters:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:nativeimagetranscoder:{strictly 3.6.0} -> 3.6.0 (c)
+--- org.jetbrains.kotlin:kotlin-annotations-jvm:{strictly 1.3.72} -> 1.3.72 (c)
+--- com.facebook.soloader:annotation:{strictly 0.12.1} -> 0.12.1 (c)
+--- com.facebook.soloader:nativeloader:{strictly 0.12.1} -> 0.12.1 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:{strictly 1.8.0} -> 1.8.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-common:{strictly 2.0.21} -> 2.0.21 (c)
+--- androidx.lifecycle:lifecycle-runtime:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-viewmodel:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-viewmodel-savedstate:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.annotation:annotation-jvm:{strictly 1.8.1} -> 1.8.1 (c)
+--- androidx.annotation:annotation-experimental:{strictly 1.4.1} -> 1.4.1 (c)
+--- androidx.versionedparcelable:versionedparcelable:{strictly 1.1.1} -> 1.1.1 (c)
+--- androidx.customview:customview:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.collection:collection:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.viewpager:viewpager:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.loader:loader:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core:{strictly 2.8.7} -> 2.8.7 (c)
+--- com.facebook.fresco:ui-core:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline-base:{strictly 3.6.0} -> 3.6.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:{strictly 1.8.0} -> 1.8.0 (c)
+--- androidx.lifecycle:lifecycle-runtime-android:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-viewmodel-android:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.core:core-ktx:{strictly 1.13.1} -> 1.13.1 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:{strictly 1.9.0} -> 1.9.0 (c)
+--- androidx.lifecycle:lifecycle-livedata:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-common:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.arch.core:core-common:{strictly 2.2.0} -> 2.2.0 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core:{strictly 1.9.0} -> 1.9.0 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:{strictly 1.9.0} -> 1.9.0 (c)
+--- androidx.arch.core:core-runtime:{strictly 2.2.0} -> 2.2.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core-ktx:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-common-jvm:{strictly 2.8.7} -> 2.8.7 (c)
\--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:{strictly 1.9.0} -> 1.9.0 (c)

debugCompileOnly - Compile only dependencies for 'debug' sources. (n)
No dependencies

debugCompileOnlyApi - Compile only API dependencies for 'debug' sources. (n)
No dependencies

debugCompileOnlyDependenciesMetadata
No dependencies

debugImplementation - Implementation only dependencies for 'debug' sources. (n)
No dependencies

debugImplementationDependenciesMetadata
No dependencies

debugIntransitiveDependenciesMetadata
No dependencies

debugReverseMetadataValues - Metadata Values dependencies for the base Split
No dependencies

debugRuntimeClasspath - Runtime classpath of '/debug'.
+--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
|    +--- org.jetbrains:annotations:13.0 -> 23.0.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 (c)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 (c)
+--- project :react-native-voice_voice
|    +--- com.android.support:appcompat-v7:28.0.0
|    |    +--- com.android.support:support-annotations:28.0.0
|    |    +--- com.android.support:support-compat:28.0.0
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:collections:28.0.0
|    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    +--- android.arch.lifecycle:runtime:1.1.1
|    |    |    |    +--- android.arch.lifecycle:common:1.1.1
|    |    |    |    |    \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    |    +--- android.arch.core:common:1.1.1
|    |    |    |    |    \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    |    \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    \--- com.android.support:versionedparcelable:28.0.0
|    |    |         +--- com.android.support:support-annotations:28.0.0
|    |    |         \--- com.android.support:collections:28.0.0 (*)
|    |    +--- com.android.support:collections:28.0.0 (*)
|    |    +--- com.android.support:cursoradapter:28.0.0
|    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    +--- com.android.support:support-core-utils:28.0.0
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    +--- com.android.support:documentfile:28.0.0
|    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:loader:28.0.0
|    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    +--- android.arch.lifecycle:livedata:1.1.1
|    |    |    |    |    +--- android.arch.core:runtime:1.1.1
|    |    |    |    |    |    +--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    |    |    |    \--- android.arch.core:common:1.1.1 (*)
|    |    |    |    |    +--- android.arch.lifecycle:livedata-core:1.1.1
|    |    |    |    |    |    +--- android.arch.lifecycle:common:1.1.1 (*)
|    |    |    |    |    |    +--- android.arch.core:common:1.1.1 (*)
|    |    |    |    |    |    \--- android.arch.core:runtime:1.1.1 (*)
|    |    |    |    |    \--- android.arch.core:common:1.1.1 (*)
|    |    |    |    \--- android.arch.lifecycle:viewmodel:1.1.1
|    |    |    |         \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    +--- com.android.support:localbroadcastmanager:28.0.0
|    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    \--- com.android.support:print:28.0.0
|    |    |         \--- com.android.support:support-annotations:28.0.0
|    |    +--- com.android.support:support-fragment:28.0.0
|    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    +--- com.android.support:support-core-ui:28.0.0
|    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    +--- com.android.support:support-core-utils:28.0.0 (*)
|    |    |    |    +--- com.android.support:customview:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    \--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    +--- com.android.support:viewpager:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:coordinatorlayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:drawerlayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:slidingpanelayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:interpolator:28.0.0
|    |    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    |    +--- com.android.support:swiperefreshlayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:interpolator:28.0.0 (*)
|    |    |    |    +--- com.android.support:asynclayoutinflater:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    \--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    \--- com.android.support:cursoradapter:28.0.0 (*)
|    |    |    +--- com.android.support:support-core-utils:28.0.0 (*)
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:loader:28.0.0 (*)
|    |    |    \--- android.arch.lifecycle:viewmodel:1.1.1 (*)
|    |    +--- com.android.support:support-vector-drawable:28.0.0
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    \--- com.android.support:support-compat:28.0.0 (*)
|    |    \--- com.android.support:animated-vector-drawable:28.0.0
|    |         +--- com.android.support:support-vector-drawable:28.0.0 (*)
|    |         \--- com.android.support:support-core-ui:28.0.0 (*)
|    \--- com.facebook.react:react-native:+ -> com.facebook.react:react-android:0.79.3
|         +--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1
|         |    +--- androidx.activity:activity:1.6.0
|         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1
|         |    |    |    \--- androidx.annotation:annotation-jvm:1.8.1
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|         |    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    +--- androidx.core:core:1.8.0 -> 1.13.1
|         |    |    |    +--- androidx.annotation:annotation:1.6.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.annotation:annotation-experimental:1.4.0 -> 1.4.1
|         |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    |    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    \--- com.google.guava:listenablefuture:1.0
|         |    |    |    +--- androidx.interpolator:interpolator:1.0.0
|         |    |    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 -> 2.8.7
|         |    |    |    |    \--- androidx.lifecycle:lifecycle-runtime-android:2.8.7
|         |    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|         |    |    |    |         +--- androidx.arch.core:core-common:2.2.0
|         |    |    |    |         |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |         +--- androidx.arch.core:core-runtime:2.2.0
|         |    |    |    |         |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |         |    \--- androidx.arch.core:core-common:2.2.0 (*)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7
|         |    |    |    |         |    \--- androidx.lifecycle:lifecycle-common-jvm:2.8.7
|         |    |    |    |         |         +--- androidx.annotation:annotation:1.8.1 (*)
|         |    |    |    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0
|         |    |    |    |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0
|         |    |    |    |         |         |         +--- org.jetbrains:annotations:23.0.0
|         |    |    |    |         |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0
|         |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (c)
|         |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (c)
|         |    |    |    |         |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0 (c)
|         |    |    |    |         |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |         |         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |         +--- androidx.profileinstaller:profileinstaller:1.3.1
|         |    |    |    |         |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    |    |         |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|         |    |    |    |         |    +--- androidx.startup:startup-runtime:1.1.1
|         |    |    |    |         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |         |    |    \--- androidx.tracing:tracing:1.0.0 -> 1.2.0
|         |    |    |    |         |    |         +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    |    |         |    |         \--- androidx.tracing:tracing-ktx:1.2.0 (c)
|         |    |    |    |         |    \--- com.google.guava:listenablefuture:1.0
|         |    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0
|         |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (*)
|         |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0 (*)
|         |    |    |    |         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    \--- androidx.core:core-ktx:1.13.1 (c)
|         |    |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-android:2.8.7
|         |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|         |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|         |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|         |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1 -> 2.8.7
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.13.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    +--- androidx.core:core:1.13.1 (*)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    \--- androidx.core:core:1.13.1 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7
|         |    |    |    |    +--- androidx.arch.core:core-common:2.2.0 (*)
|         |    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (*)
|         |    |    |    +--- androidx.savedstate:savedstate:1.2.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    +--- androidx.arch.core:core-common:2.1.0 -> 2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 -> 2.8.7 (*)
|         |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|         |    |    +--- androidx.tracing:tracing:1.0.0 -> 1.2.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    +--- androidx.annotation:annotation:1.3.0 -> 1.8.1 (*)
|         |    +--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0
|         |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    |    +--- androidx.core:core:1.6.0 -> 1.13.1 (*)
|         |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|         |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|         |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|         |    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|         |    |    |    +--- androidx.interpolator:interpolator:1.0.0 (*)
|         |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|         |    |    \--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1 (c)
|         |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    +--- androidx.core:core:1.9.0 -> 1.13.1 (*)
|         |    +--- androidx.core:core-ktx:1.8.0 -> 1.13.1 (*)
|         |    +--- androidx.cursoradapter:cursoradapter:1.0.0
|         |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    +--- androidx.drawerlayout:drawerlayout:1.0.0
|         |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    |    \--- androidx.customview:customview:1.0.0
|         |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |         \--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    +--- androidx.emoji2:emoji2:1.2.0
|         |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    +--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.core:core:1.3.0 -> 1.13.1 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-process:2.4.1 -> 2.8.7
|         |    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (*)
|         |    |    |    +--- androidx.startup:startup-runtime:1.1.1 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    \--- androidx.startup:startup-runtime:1.0.0 -> 1.1.1 (*)
|         |    +--- androidx.emoji2:emoji2-views-helper:1.2.0
|         |    |    +--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.core:core:1.3.0 -> 1.13.1 (*)
|         |    |    \--- androidx.emoji2:emoji2:1.2.0 (*)
|         |    +--- androidx.fragment:fragment:1.3.6
|         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|         |    |    +--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.viewpager:viewpager:1.0.0
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    |    |    \--- androidx.customview:customview:1.0.0 (*)
|         |    |    +--- androidx.loader:loader:1.0.0
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.8.7
|         |    |    |    |    +--- androidx.arch.core:core-common:2.2.0 (*)
|         |    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|         |    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.8.7 (*)
|         |    |    +--- androidx.activity:activity:1.2.4 -> 1.6.0 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 -> 2.8.7 (*)
|         |    |    +--- androidx.savedstate:savedstate:1.1.0 -> 1.2.1 (*)
|         |    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.4.1 (*)
|         |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7 (*)
|         |    +--- androidx.resourceinspection:resourceinspection-annotation:1.0.1
|         |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    \--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0 (c)
|         +--- androidx.appcompat:appcompat-resources:1.7.0 (*)
|         +--- androidx.autofill:autofill:1.1.0
|         |    \--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|         +--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|         |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|         |    \--- androidx.interpolator:interpolator:1.0.0 (*)
|         +--- androidx.tracing:tracing:1.1.0 -> 1.2.0 (*)
|         +--- com.facebook.fbjni:fbjni:0.7.0
|         |    \--- com.facebook.soloader:nativeloader:0.10.5 -> 0.12.1
|         +--- com.facebook.fresco:fresco:3.6.0
|         |    +--- com.facebook.fresco:soloader:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0
|         |    |    |    +--- androidx.core:core:1.13.1 (*)
|         |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    \--- com.facebook.soloader:soloader:0.11.0 -> 0.12.1
|         |    |         +--- com.facebook.soloader:annotation:0.12.1
|         |    |         \--- com.facebook.soloader:nativeloader:0.12.1
|         |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    +--- com.facebook.fresco:ui-common:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:ui-core:3.6.0
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:middleware:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:ui-common:3.6.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    +--- com.facebook.fresco:drawee:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:imagepipeline:3.6.0
|         |    |    |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    |    +--- com.facebook.soloader:annotation:0.11.0 -> 0.12.1
|         |    |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:urimod:3.6.0
|         |    |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    |    +--- com.facebook.fresco:ui-core:3.6.0 (*)
|         |    |    |    |    +--- com.facebook.fresco:vito-source:3.6.0
|         |    |    |    |    |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    \--- com.facebook.fresco:imagepipeline-base:3.6.0
|         |    |    |         +--- com.facebook.infer.annotation:infer-annotation:0.18.0
|         |    |    |         |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|         |    |    |         |    \--- org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72
|         |    |    |         +--- com.facebook.soloader:annotation:0.11.0 -> 0.12.1
|         |    |    |         +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    |         +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |         +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0
|         |    |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    \--- com.facebook.soloader:soloader:0.11.0 -> 0.12.1 (*)
|         |    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    \--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-native:3.6.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    |    |    \--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    +--- com.facebook.fresco:memory-type-java:3.6.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    |    \--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:ui-common:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:ui-core:3.6.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-java:3.6.0 (*)
|         |    +--- com.facebook.fresco:nativeimagefilters:3.6.0
|         |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-native:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-java:3.6.0 (*)
|         |    |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    \--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    +--- com.facebook.fresco:nativeimagetranscoder:3.6.0
|         |    |    +--- com.facebook.fresco:imagepipeline-base:3.6.0 (*)
|         |    |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    \--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         +--- com.facebook.fresco:imagepipeline-okhttp3:3.6.0
|         |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-java:3.6.0 (*)
|         |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.2
|         |    |    +--- com.squareup.okio:okio:2.8.0 -> 2.9.0
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|         |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.10 -> 2.0.21
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         +--- com.facebook.fresco:middleware:3.6.0 (*)
|         +--- com.facebook.fresco:ui-common:3.6.0 (*)
|         +--- com.facebook.infer.annotation:infer-annotation:0.18.0 (*)
|         +--- com.facebook.soloader:soloader:0.12.1 (*)
|         +--- com.facebook.yoga:proguard-annotations:1.19.0
|         +--- com.google.code.findbugs:jsr305:3.0.2
|         +--- com.squareup.okhttp3:okhttp-urlconnection:4.9.2
|         |    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.0
|         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|         |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0
|         |              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|         +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|         +--- com.squareup.okio:okio:2.9.0 (*)
|         +--- javax.inject:javax.inject:1
|         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- project :react-native-image-picker
|    +--- com.facebook.react:react-native:+ -> com.facebook.react:react-android:0.79.3 (*)
|    +--- androidx.core:core:1.3.1 -> 1.13.1 (*)
|    \--- androidx.exifinterface:exifinterface:1.3.3
|         \--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
+--- project :react-native-permissions
|    \--- com.facebook.react:react-native:+ -> com.facebook.react:react-android:0.79.3 (*)
+--- project :react-native-vision-camera
|    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|    +--- com.facebook.react:react-android:+ -> 0.79.3 (*)
|    +--- androidx.camera:camera-core:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.annotation:annotation-experimental:1.4.1 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    +--- androidx.concurrent:concurrent-futures-ktx:1.1.0
|    |    |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.71 -> 2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.3.4 -> 1.9.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- androidx.exifinterface:exifinterface:1.3.2 -> 1.3.3 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.1.0 -> 2.8.7 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata:2.1.0 -> 2.8.7 (*)
|    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-camera2:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    +--- androidx.concurrent:concurrent-futures-ktx:1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.1.0 -> 2.8.7 (*)
|    |    +--- androidx.tracing:tracing-ktx:1.2.0
|    |    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 -> 2.0.21 (*)
|    |    |    \--- androidx.tracing:tracing:1.2.0 (c)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-video:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-view:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.annotation:annotation-experimental:1.4.1 (*)
|    |    +--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (*)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.3.2 -> 1.13.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.0.0 -> 2.8.7 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-extensions:1.5.0-alpha03
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (*)
|    +--- com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1
|    |    +--- com.google.android.datatransport:transport-api:2.2.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    +--- com.google.android.datatransport:transport-backend-cct:2.3.3
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |    +--- com.google.android.datatransport:transport-runtime:2.2.5 -> 2.2.6
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |    |    \--- javax.inject:javax.inject:1
|    |    |    +--- com.google.firebase:firebase-encoders:16.1.0
|    |    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    \--- com.google.firebase:firebase-encoders-json:17.1.0
|    |    |         +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |         \--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    +--- com.google.android.datatransport:transport-runtime:2.2.6 (*)
|    |    +--- com.google.android.gms:play-services-base:18.5.0
|    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    |    |    +--- com.google.android.gms:play-services-basement:18.4.0
|    |    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|    |    |    \--- com.google.android.gms:play-services-tasks:18.2.0
|    |    |         \--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-tasks:18.2.0 (*)
|    |    +--- com.google.android.odml:image:1.0.0-beta1
|    |    +--- com.google.firebase:firebase-components:16.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    \--- com.google.firebase:firebase-annotations:16.0.0
|    |    +--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    +--- com.google.firebase:firebase-encoders-json:17.1.0 (*)
|    |    +--- com.google.mlkit:barcode-scanning-common:17.0.0
|    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    |    \--- com.google.mlkit:vision-common:17.0.0 -> 17.3.0
|    |    |         +--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3 (*)
|    |    |         +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |         +--- com.google.android.datatransport:transport-backend-cct:2.3.3 (*)
|    |    |         +--- com.google.android.datatransport:transport-runtime:2.2.6 (*)
|    |    |         +--- com.google.android.gms:play-services-base:18.1.0 -> 18.5.0 (*)
|    |    |         +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.4.0 (*)
|    |    |         +--- com.google.android.gms:play-services-tasks:18.0.2 -> 18.2.0 (*)
|    |    |         +--- com.google.android.odml:image:1.0.0-beta1
|    |    |         +--- com.google.firebase:firebase-components:16.1.0 (*)
|    |    |         +--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    |         +--- com.google.firebase:firebase-encoders-json:17.1.0 (*)
|    |    |         \--- com.google.mlkit:common:18.6.0 -> 18.11.0
|    |    |              +--- androidx.appcompat:appcompat:1.6.1 (*)
|    |    |              +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |              +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |              +--- com.google.android.datatransport:transport-backend-cct:2.3.3 (*)
|    |    |              +--- com.google.android.datatransport:transport-runtime:2.2.6 (*)
|    |    |              +--- com.google.android.gms:play-services-base:18.5.0 (*)
|    |    |              +--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    |              +--- com.google.android.gms:play-services-tasks:18.2.0 (*)
|    |    |              +--- com.google.firebase:firebase-components:16.1.0 (*)
|    |    |              +--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    |              \--- com.google.firebase:firebase-encoders-json:17.1.0 (*)
|    |    +--- com.google.mlkit:common:18.11.0 (*)
|    |    +--- com.google.mlkit:vision-common:17.3.0 (*)
|    |    \--- com.google.mlkit:vision-interfaces:16.3.0
|    |         +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.4.0 (*)
|    |         \--- com.google.android.gms:play-services-tasks:18.0.2 -> 18.2.0 (*)
|    \--- project :react-native-worklets-core
|         +--- com.facebook.react:react-android -> 0.79.3 (*)
|         \--- com.facebook.react:hermes-android -> 0.79.3
|              +--- com.facebook.fbjni:fbjni:0.7.0 (*)
|              +--- com.facebook.yoga:proguard-annotations:1.19.0
|              \--- androidx.annotation:annotation:1.6.0 -> 1.8.1 (*)
+--- project :react-native-worklets-core (*)
+--- com.facebook.react:react-android -> 0.79.3 (*)
\--- com.facebook.react:hermes-android -> 0.79.3 (*)

debugRuntimeElements - Runtime elements for debug (n)
No dependencies

debugRuntimeOnly - Runtime only dependencies for 'debug' sources. (n)
No dependencies

debugUnitTestAnnotationProcessorClasspath - Resolved configuration for annotation-processor for variant: debugUnitTest
No dependencies

debugUnitTestApi (n)
No dependencies

debugUnitTestApiDependenciesMetadata
No dependencies

debugUnitTestCompilationApi - API dependencies for '/debugUnitTest'. (n)
No dependencies

debugUnitTestCompilationCompileOnly - Compile only dependencies for '/debugUnitTest'. (n)
No dependencies

debugUnitTestCompilationImplementation - Implementation only dependencies for '/debugUnitTest'. (n)
No dependencies

debugUnitTestCompilationRuntimeOnly - Runtime only dependencies for '/debugUnitTest'. (n)
No dependencies

debugUnitTestCompileClasspath - Compile classpath for '/debugUnitTest'.
+--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
|    +--- org.jetbrains:annotations:13.0 -> 23.0.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 (c)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 (c)
+--- project :app (*)
+--- project :react-native-voice_voice
+--- project :react-native-image-picker
+--- project :react-native-permissions
+--- project :react-native-vision-camera
|    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- project :react-native-worklets-core
+--- com.facebook.react:react-android -> 0.79.3
|    +--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1
|    |    +--- androidx.activity:activity:1.6.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1
|    |    |    |    \--- androidx.annotation:annotation-jvm:1.8.1
|    |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    |    +--- androidx.core:core:1.8.0 -> 1.13.1
|    |    |    |    +--- androidx.annotation:annotation:1.6.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.annotation:annotation-experimental:1.4.0 -> 1.4.1
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 -> 2.8.7
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-runtime-android:2.8.7
|    |    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|    |    |    |    |         +--- androidx.arch.core:core-common:2.2.0
|    |    |    |    |         |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7
|    |    |    |    |         |    \--- androidx.lifecycle:lifecycle-common-jvm:2.8.7
|    |    |    |    |         |         +--- androidx.annotation:annotation:1.8.1 (*)
|    |    |    |    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0
|    |    |    |    |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0
|    |    |    |    |         |         |         +--- org.jetbrains:annotations:23.0.0
|    |    |    |    |         |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0
|    |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (c)
|    |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0 (c)
|    |    |    |    |         |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (c)
|    |    |    |    |         |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |         |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0
|    |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (*)
|    |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0 (*)
|    |    |    |    |         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    |    |    |         \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-android:2.8.7
|    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1 -> 2.8.7
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.13.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    +--- androidx.core:core:1.13.1 (*)
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (*)
|    |    |    |    +--- androidx.savedstate:savedstate:1.2.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    +--- androidx.annotation:annotation:1.3.0 -> 1.8.1 (*)
|    |    +--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0
|    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.6.0 -> 1.13.1 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |    |    |    +--- androidx.interpolator:interpolator:1.0.0
|    |    |    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    \--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1 (c)
|    |    +--- androidx.core:core:1.9.0 -> 1.13.1 (*)
|    |    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |         \--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    +--- androidx.fragment:fragment:1.3.6
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.customview:customview:1.0.0 (*)
|    |    |    +--- androidx.loader:loader:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.8.7
|    |    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0
|    |    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    |    \--- androidx.arch.core:core-common:2.2.0 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|    |    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.8.7 (*)
|    |    |    +--- androidx.activity:activity:1.2.4 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 -> 2.8.7 (*)
|    |    |    +--- androidx.savedstate:savedstate:1.1.0 -> 1.2.1 (*)
|    |    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.4.1 (*)
|    |    \--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    +--- androidx.appcompat:appcompat-resources:1.7.0 (*)
|    +--- androidx.autofill:autofill:1.1.0
|    +--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    \--- androidx.interpolator:interpolator:1.0.0 (*)
|    +--- androidx.tracing:tracing:1.1.0 -> 1.2.0
|    +--- com.facebook.fbjni:fbjni:0.7.0
|    +--- com.facebook.fresco:fresco:3.6.0
|    |    +--- com.facebook.fresco:fbcore:3.6.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:drawee:3.6.0
|    |    |    +--- com.facebook.fresco:ui-core:3.6.0
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:imagepipeline:3.6.0
|    |    |    \--- com.facebook.fresco:imagepipeline-base:3.6.0
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0
|    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0
|    |    +--- com.facebook.fresco:memory-type-native:3.6.0
|    |    +--- com.facebook.fresco:memory-type-java:3.6.0
|    |    +--- com.facebook.fresco:nativeimagefilters:3.6.0
|    |    +--- com.facebook.fresco:nativeimagetranscoder:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:imagepipeline-okhttp3:3.6.0
|    |    +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.2
|    |    |    +--- com.squareup.okio:okio:2.8.0 -> 2.9.0
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.10 -> 2.0.21
|    |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:middleware:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:ui-common:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.infer.annotation:infer-annotation:0.18.0
|    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    \--- org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72
|    +--- com.facebook.soloader:soloader:0.12.1
|    |    +--- com.facebook.soloader:annotation:0.12.1
|    |    \--- com.facebook.soloader:nativeloader:0.12.1
|    +--- com.facebook.yoga:proguard-annotations:1.19.0
|    +--- com.google.code.findbugs:jsr305:3.0.2
|    +--- com.squareup.okhttp3:okhttp-urlconnection:4.9.2
|    |    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.0
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0
|    |              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|    +--- com.squareup.okio:okio:2.9.0 (*)
|    +--- javax.inject:javax.inject:1
|    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- com.facebook.react:hermes-android -> 0.79.3
+--- org.jetbrains.kotlin:kotlin-stdlib:{strictly 2.0.21} -> 2.0.21 (c)
+--- com.facebook.react:react-android:{strictly 0.79.3} -> 0.79.3 (c)
+--- com.facebook.react:hermes-android:{strictly 0.79.3} -> 0.79.3 (c)
+--- org.jetbrains:annotations:{strictly 23.0.0} -> 23.0.0 (c)
+--- androidx.appcompat:appcompat:{strictly 1.6.1} -> 1.6.1 (c)
+--- androidx.appcompat:appcompat-resources:{strictly 1.7.0} -> 1.7.0 (c)
+--- androidx.autofill:autofill:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.swiperefreshlayout:swiperefreshlayout:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.tracing:tracing:{strictly 1.2.0} -> 1.2.0 (c)
+--- com.facebook.fbjni:fbjni:{strictly 0.7.0} -> 0.7.0 (c)
+--- com.facebook.fresco:fresco:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline-okhttp3:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:middleware:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:ui-common:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.infer.annotation:infer-annotation:{strictly 0.18.0} -> 0.18.0 (c)
+--- com.facebook.soloader:soloader:{strictly 0.12.1} -> 0.12.1 (c)
+--- com.facebook.yoga:proguard-annotations:{strictly 1.19.0} -> 1.19.0 (c)
+--- com.google.code.findbugs:jsr305:{strictly 3.0.2} -> 3.0.2 (c)
+--- com.squareup.okhttp3:okhttp-urlconnection:{strictly 4.9.2} -> 4.9.2 (c)
+--- com.squareup.okhttp3:okhttp:{strictly 4.9.2} -> 4.9.2 (c)
+--- com.squareup.okio:okio:{strictly 2.9.0} -> 2.9.0 (c)
+--- javax.inject:javax.inject:{strictly 1} -> 1 (c)
+--- androidx.activity:activity:{strictly 1.6.0} -> 1.6.0 (c)
+--- androidx.annotation:annotation:{strictly 1.8.1} -> 1.8.1 (c)
+--- androidx.core:core:{strictly 1.13.1} -> 1.13.1 (c)
+--- androidx.cursoradapter:cursoradapter:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.drawerlayout:drawerlayout:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.fragment:fragment:{strictly 1.3.6} -> 1.3.6 (c)
+--- androidx.savedstate:savedstate:{strictly 1.2.1} -> 1.2.1 (c)
+--- androidx.vectordrawable:vectordrawable:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.vectordrawable:vectordrawable-animated:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.interpolator:interpolator:{strictly 1.0.0} -> 1.0.0 (c)
+--- com.facebook.fresco:fbcore:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:drawee:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline-native:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:memory-type-ashmem:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:memory-type-native:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:memory-type-java:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:nativeimagefilters:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:nativeimagetranscoder:{strictly 3.6.0} -> 3.6.0 (c)
+--- org.jetbrains.kotlin:kotlin-annotations-jvm:{strictly 1.3.72} -> 1.3.72 (c)
+--- com.facebook.soloader:annotation:{strictly 0.12.1} -> 0.12.1 (c)
+--- com.facebook.soloader:nativeloader:{strictly 0.12.1} -> 0.12.1 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:{strictly 1.8.0} -> 1.8.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-common:{strictly 2.0.21} -> 2.0.21 (c)
+--- androidx.lifecycle:lifecycle-runtime:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-viewmodel:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-viewmodel-savedstate:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.annotation:annotation-jvm:{strictly 1.8.1} -> 1.8.1 (c)
+--- androidx.annotation:annotation-experimental:{strictly 1.4.1} -> 1.4.1 (c)
+--- androidx.versionedparcelable:versionedparcelable:{strictly 1.1.1} -> 1.1.1 (c)
+--- androidx.customview:customview:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.collection:collection:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.viewpager:viewpager:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.loader:loader:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core:{strictly 2.8.7} -> 2.8.7 (c)
+--- com.facebook.fresco:ui-core:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline-base:{strictly 3.6.0} -> 3.6.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:{strictly 1.8.0} -> 1.8.0 (c)
+--- androidx.lifecycle:lifecycle-runtime-android:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-viewmodel-android:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.core:core-ktx:{strictly 1.13.1} -> 1.13.1 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:{strictly 1.9.0} -> 1.9.0 (c)
+--- androidx.lifecycle:lifecycle-livedata:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-common:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.arch.core:core-common:{strictly 2.2.0} -> 2.2.0 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core:{strictly 1.9.0} -> 1.9.0 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:{strictly 1.9.0} -> 1.9.0 (c)
+--- androidx.arch.core:core-runtime:{strictly 2.2.0} -> 2.2.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core-ktx:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-common-jvm:{strictly 2.8.7} -> 2.8.7 (c)
\--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:{strictly 1.9.0} -> 1.9.0 (c)

debugUnitTestCompileOnly (n)
No dependencies

debugUnitTestCompileOnlyDependenciesMetadata
No dependencies

debugUnitTestImplementation (n)
No dependencies

debugUnitTestImplementationDependenciesMetadata
No dependencies

debugUnitTestIntransitiveDependenciesMetadata
No dependencies

debugUnitTestRuntimeClasspath - Runtime classpath of '/debugUnitTest'.
+--- project :app (*)
+--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
|    +--- org.jetbrains:annotations:13.0 -> 23.0.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 (c)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 (c)
+--- project :react-native-voice_voice
|    +--- com.android.support:appcompat-v7:28.0.0
|    |    +--- com.android.support:support-annotations:28.0.0
|    |    +--- com.android.support:support-compat:28.0.0
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:collections:28.0.0
|    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    +--- android.arch.lifecycle:runtime:1.1.1
|    |    |    |    +--- android.arch.lifecycle:common:1.1.1
|    |    |    |    |    \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    |    +--- android.arch.core:common:1.1.1
|    |    |    |    |    \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    |    \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    \--- com.android.support:versionedparcelable:28.0.0
|    |    |         +--- com.android.support:support-annotations:28.0.0
|    |    |         \--- com.android.support:collections:28.0.0 (*)
|    |    +--- com.android.support:collections:28.0.0 (*)
|    |    +--- com.android.support:cursoradapter:28.0.0
|    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    +--- com.android.support:support-core-utils:28.0.0
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    +--- com.android.support:documentfile:28.0.0
|    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:loader:28.0.0
|    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    +--- android.arch.lifecycle:livedata:1.1.1
|    |    |    |    |    +--- android.arch.core:runtime:1.1.1
|    |    |    |    |    |    +--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    |    |    |    \--- android.arch.core:common:1.1.1 (*)
|    |    |    |    |    +--- android.arch.lifecycle:livedata-core:1.1.1
|    |    |    |    |    |    +--- android.arch.lifecycle:common:1.1.1 (*)
|    |    |    |    |    |    +--- android.arch.core:common:1.1.1 (*)
|    |    |    |    |    |    \--- android.arch.core:runtime:1.1.1 (*)
|    |    |    |    |    \--- android.arch.core:common:1.1.1 (*)
|    |    |    |    \--- android.arch.lifecycle:viewmodel:1.1.1
|    |    |    |         \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    +--- com.android.support:localbroadcastmanager:28.0.0
|    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    \--- com.android.support:print:28.0.0
|    |    |         \--- com.android.support:support-annotations:28.0.0
|    |    +--- com.android.support:support-fragment:28.0.0
|    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    +--- com.android.support:support-core-ui:28.0.0
|    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    +--- com.android.support:support-core-utils:28.0.0 (*)
|    |    |    |    +--- com.android.support:customview:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    \--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    +--- com.android.support:viewpager:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:coordinatorlayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:drawerlayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:slidingpanelayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:interpolator:28.0.0
|    |    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    |    +--- com.android.support:swiperefreshlayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:interpolator:28.0.0 (*)
|    |    |    |    +--- com.android.support:asynclayoutinflater:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    \--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    \--- com.android.support:cursoradapter:28.0.0 (*)
|    |    |    +--- com.android.support:support-core-utils:28.0.0 (*)
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:loader:28.0.0 (*)
|    |    |    \--- android.arch.lifecycle:viewmodel:1.1.1 (*)
|    |    +--- com.android.support:support-vector-drawable:28.0.0
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    \--- com.android.support:support-compat:28.0.0 (*)
|    |    \--- com.android.support:animated-vector-drawable:28.0.0
|    |         +--- com.android.support:support-vector-drawable:28.0.0 (*)
|    |         \--- com.android.support:support-core-ui:28.0.0 (*)
|    \--- com.facebook.react:react-native:+ -> com.facebook.react:react-android:0.79.3
|         +--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1
|         |    +--- androidx.activity:activity:1.6.0
|         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1
|         |    |    |    \--- androidx.annotation:annotation-jvm:1.8.1
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|         |    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    +--- androidx.core:core:1.8.0 -> 1.13.1
|         |    |    |    +--- androidx.annotation:annotation:1.6.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.annotation:annotation-experimental:1.4.0 -> 1.4.1
|         |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    |    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    \--- com.google.guava:listenablefuture:1.0
|         |    |    |    +--- androidx.interpolator:interpolator:1.0.0
|         |    |    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 -> 2.8.7
|         |    |    |    |    \--- androidx.lifecycle:lifecycle-runtime-android:2.8.7
|         |    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|         |    |    |    |         +--- androidx.arch.core:core-common:2.2.0
|         |    |    |    |         |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |         +--- androidx.arch.core:core-runtime:2.2.0
|         |    |    |    |         |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |         |    \--- androidx.arch.core:core-common:2.2.0 (*)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7
|         |    |    |    |         |    \--- androidx.lifecycle:lifecycle-common-jvm:2.8.7
|         |    |    |    |         |         +--- androidx.annotation:annotation:1.8.1 (*)
|         |    |    |    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0
|         |    |    |    |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0
|         |    |    |    |         |         |         +--- org.jetbrains:annotations:23.0.0
|         |    |    |    |         |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0
|         |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (c)
|         |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (c)
|         |    |    |    |         |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0 (c)
|         |    |    |    |         |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |         |         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |         +--- androidx.profileinstaller:profileinstaller:1.3.1
|         |    |    |    |         |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    |    |         |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|         |    |    |    |         |    +--- androidx.startup:startup-runtime:1.1.1
|         |    |    |    |         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |         |    |    \--- androidx.tracing:tracing:1.0.0 -> 1.2.0
|         |    |    |    |         |    |         +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    |    |         |    |         \--- androidx.tracing:tracing-ktx:1.2.0 (c)
|         |    |    |    |         |    \--- com.google.guava:listenablefuture:1.0
|         |    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0
|         |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (*)
|         |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0 (*)
|         |    |    |    |         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    \--- androidx.core:core-ktx:1.13.1 (c)
|         |    |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-android:2.8.7
|         |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|         |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|         |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|         |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1 -> 2.8.7
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.13.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    +--- androidx.core:core:1.13.1 (*)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    \--- androidx.core:core:1.13.1 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7
|         |    |    |    |    +--- androidx.arch.core:core-common:2.2.0 (*)
|         |    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (*)
|         |    |    |    +--- androidx.savedstate:savedstate:1.2.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    +--- androidx.arch.core:core-common:2.1.0 -> 2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 -> 2.8.7 (*)
|         |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|         |    |    +--- androidx.tracing:tracing:1.0.0 -> 1.2.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    +--- androidx.annotation:annotation:1.3.0 -> 1.8.1 (*)
|         |    +--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0
|         |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    |    +--- androidx.core:core:1.6.0 -> 1.13.1 (*)
|         |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|         |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|         |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|         |    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|         |    |    |    +--- androidx.interpolator:interpolator:1.0.0 (*)
|         |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|         |    |    \--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1 (c)
|         |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    +--- androidx.core:core:1.9.0 -> 1.13.1 (*)
|         |    +--- androidx.core:core-ktx:1.8.0 -> 1.13.1 (*)
|         |    +--- androidx.cursoradapter:cursoradapter:1.0.0
|         |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    +--- androidx.drawerlayout:drawerlayout:1.0.0
|         |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    |    \--- androidx.customview:customview:1.0.0
|         |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |         \--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    +--- androidx.emoji2:emoji2:1.2.0
|         |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    +--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.core:core:1.3.0 -> 1.13.1 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-process:2.4.1 -> 2.8.7
|         |    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (*)
|         |    |    |    +--- androidx.startup:startup-runtime:1.1.1 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    \--- androidx.startup:startup-runtime:1.0.0 -> 1.1.1 (*)
|         |    +--- androidx.emoji2:emoji2-views-helper:1.2.0
|         |    |    +--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.core:core:1.3.0 -> 1.13.1 (*)
|         |    |    \--- androidx.emoji2:emoji2:1.2.0 (*)
|         |    +--- androidx.fragment:fragment:1.3.6
|         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|         |    |    +--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.viewpager:viewpager:1.0.0
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    |    |    \--- androidx.customview:customview:1.0.0 (*)
|         |    |    +--- androidx.loader:loader:1.0.0
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.8.7
|         |    |    |    |    +--- androidx.arch.core:core-common:2.2.0 (*)
|         |    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|         |    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.8.7 (*)
|         |    |    +--- androidx.activity:activity:1.2.4 -> 1.6.0 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 -> 2.8.7 (*)
|         |    |    +--- androidx.savedstate:savedstate:1.1.0 -> 1.2.1 (*)
|         |    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.4.1 (*)
|         |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7 (*)
|         |    +--- androidx.resourceinspection:resourceinspection-annotation:1.0.1
|         |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    \--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0 (c)
|         +--- androidx.appcompat:appcompat-resources:1.7.0 (*)
|         +--- androidx.autofill:autofill:1.1.0
|         |    \--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|         +--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|         |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|         |    \--- androidx.interpolator:interpolator:1.0.0 (*)
|         +--- androidx.tracing:tracing:1.1.0 -> 1.2.0 (*)
|         +--- com.facebook.fbjni:fbjni:0.7.0
|         |    \--- com.facebook.soloader:nativeloader:0.10.5 -> 0.12.1
|         +--- com.facebook.fresco:fresco:3.6.0
|         |    +--- com.facebook.fresco:soloader:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0
|         |    |    |    +--- androidx.core:core:1.13.1 (*)
|         |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    \--- com.facebook.soloader:soloader:0.11.0 -> 0.12.1
|         |    |         +--- com.facebook.soloader:annotation:0.12.1
|         |    |         \--- com.facebook.soloader:nativeloader:0.12.1
|         |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    +--- com.facebook.fresco:ui-common:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:ui-core:3.6.0
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:middleware:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:ui-common:3.6.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    +--- com.facebook.fresco:drawee:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:imagepipeline:3.6.0
|         |    |    |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    |    +--- com.facebook.soloader:annotation:0.11.0 -> 0.12.1
|         |    |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:urimod:3.6.0
|         |    |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    |    +--- com.facebook.fresco:ui-core:3.6.0 (*)
|         |    |    |    |    +--- com.facebook.fresco:vito-source:3.6.0
|         |    |    |    |    |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    \--- com.facebook.fresco:imagepipeline-base:3.6.0
|         |    |    |         +--- com.facebook.infer.annotation:infer-annotation:0.18.0
|         |    |    |         |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|         |    |    |         |    \--- org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72
|         |    |    |         +--- com.facebook.soloader:annotation:0.11.0 -> 0.12.1
|         |    |    |         +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    |         +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |         +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0
|         |    |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    \--- com.facebook.soloader:soloader:0.11.0 -> 0.12.1 (*)
|         |    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    \--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-native:3.6.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    |    |    \--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    +--- com.facebook.fresco:memory-type-java:3.6.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    |    \--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:ui-common:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:ui-core:3.6.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-java:3.6.0 (*)
|         |    +--- com.facebook.fresco:nativeimagefilters:3.6.0
|         |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-native:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-java:3.6.0 (*)
|         |    |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    \--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    +--- com.facebook.fresco:nativeimagetranscoder:3.6.0
|         |    |    +--- com.facebook.fresco:imagepipeline-base:3.6.0 (*)
|         |    |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    \--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         +--- com.facebook.fresco:imagepipeline-okhttp3:3.6.0
|         |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-java:3.6.0 (*)
|         |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.2
|         |    |    +--- com.squareup.okio:okio:2.8.0 -> 2.9.0
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|         |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.10 -> 2.0.21
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         +--- com.facebook.fresco:middleware:3.6.0 (*)
|         +--- com.facebook.fresco:ui-common:3.6.0 (*)
|         +--- com.facebook.infer.annotation:infer-annotation:0.18.0 (*)
|         +--- com.facebook.soloader:soloader:0.12.1 (*)
|         +--- com.facebook.yoga:proguard-annotations:1.19.0
|         +--- com.google.code.findbugs:jsr305:3.0.2
|         +--- com.squareup.okhttp3:okhttp-urlconnection:4.9.2
|         |    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.0
|         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|         |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0
|         |              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|         +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|         +--- com.squareup.okio:okio:2.9.0 (*)
|         +--- javax.inject:javax.inject:1
|         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- project :react-native-image-picker
|    +--- com.facebook.react:react-native:+ -> com.facebook.react:react-android:0.79.3 (*)
|    +--- androidx.core:core:1.3.1 -> 1.13.1 (*)
|    \--- androidx.exifinterface:exifinterface:1.3.3
|         \--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
+--- project :react-native-permissions
|    \--- com.facebook.react:react-native:+ -> com.facebook.react:react-android:0.79.3 (*)
+--- project :react-native-vision-camera
|    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|    +--- com.facebook.react:react-android:+ -> 0.79.3 (*)
|    +--- androidx.camera:camera-core:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.annotation:annotation-experimental:1.4.1 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    +--- androidx.concurrent:concurrent-futures-ktx:1.1.0
|    |    |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.71 -> 2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.3.4 -> 1.9.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- androidx.exifinterface:exifinterface:1.3.2 -> 1.3.3 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.1.0 -> 2.8.7 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata:2.1.0 -> 2.8.7 (*)
|    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-camera2:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    +--- androidx.concurrent:concurrent-futures-ktx:1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.1.0 -> 2.8.7 (*)
|    |    +--- androidx.tracing:tracing-ktx:1.2.0
|    |    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 -> 2.0.21 (*)
|    |    |    \--- androidx.tracing:tracing:1.2.0 (c)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-video:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-view:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.annotation:annotation-experimental:1.4.1 (*)
|    |    +--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (*)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.3.2 -> 1.13.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.0.0 -> 2.8.7 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-extensions:1.5.0-alpha03
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (*)
|    +--- com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1
|    |    +--- com.google.android.datatransport:transport-api:2.2.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    +--- com.google.android.datatransport:transport-backend-cct:2.3.3
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |    +--- com.google.android.datatransport:transport-runtime:2.2.5 -> 2.2.6
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |    |    \--- javax.inject:javax.inject:1
|    |    |    +--- com.google.firebase:firebase-encoders:16.1.0
|    |    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    \--- com.google.firebase:firebase-encoders-json:17.1.0
|    |    |         +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |         \--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    +--- com.google.android.datatransport:transport-runtime:2.2.6 (*)
|    |    +--- com.google.android.gms:play-services-base:18.5.0
|    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    |    |    +--- com.google.android.gms:play-services-basement:18.4.0
|    |    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|    |    |    \--- com.google.android.gms:play-services-tasks:18.2.0
|    |    |         \--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-tasks:18.2.0 (*)
|    |    +--- com.google.android.odml:image:1.0.0-beta1
|    |    +--- com.google.firebase:firebase-components:16.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    \--- com.google.firebase:firebase-annotations:16.0.0
|    |    +--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    +--- com.google.firebase:firebase-encoders-json:17.1.0 (*)
|    |    +--- com.google.mlkit:barcode-scanning-common:17.0.0
|    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    |    \--- com.google.mlkit:vision-common:17.0.0 -> 17.3.0
|    |    |         +--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3 (*)
|    |    |         +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |         +--- com.google.android.datatransport:transport-backend-cct:2.3.3 (*)
|    |    |         +--- com.google.android.datatransport:transport-runtime:2.2.6 (*)
|    |    |         +--- com.google.android.gms:play-services-base:18.1.0 -> 18.5.0 (*)
|    |    |         +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.4.0 (*)
|    |    |         +--- com.google.android.gms:play-services-tasks:18.0.2 -> 18.2.0 (*)
|    |    |         +--- com.google.android.odml:image:1.0.0-beta1
|    |    |         +--- com.google.firebase:firebase-components:16.1.0 (*)
|    |    |         +--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    |         +--- com.google.firebase:firebase-encoders-json:17.1.0 (*)
|    |    |         \--- com.google.mlkit:common:18.6.0 -> 18.11.0
|    |    |              +--- androidx.appcompat:appcompat:1.6.1 (*)
|    |    |              +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |              +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |              +--- com.google.android.datatransport:transport-backend-cct:2.3.3 (*)
|    |    |              +--- com.google.android.datatransport:transport-runtime:2.2.6 (*)
|    |    |              +--- com.google.android.gms:play-services-base:18.5.0 (*)
|    |    |              +--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    |              +--- com.google.android.gms:play-services-tasks:18.2.0 (*)
|    |    |              +--- com.google.firebase:firebase-components:16.1.0 (*)
|    |    |              +--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    |              \--- com.google.firebase:firebase-encoders-json:17.1.0 (*)
|    |    +--- com.google.mlkit:common:18.11.0 (*)
|    |    +--- com.google.mlkit:vision-common:17.3.0 (*)
|    |    \--- com.google.mlkit:vision-interfaces:16.3.0
|    |         +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.4.0 (*)
|    |         \--- com.google.android.gms:play-services-tasks:18.0.2 -> 18.2.0 (*)
|    \--- project :react-native-worklets-core
|         +--- com.facebook.react:react-android -> 0.79.3 (*)
|         \--- com.facebook.react:hermes-android -> 0.79.3
|              +--- com.facebook.fbjni:fbjni:0.7.0 (*)
|              +--- com.facebook.yoga:proguard-annotations:1.19.0
|              \--- androidx.annotation:annotation:1.6.0 -> 1.8.1 (*)
+--- project :react-native-worklets-core (*)
+--- com.facebook.react:react-android -> 0.79.3 (*)
\--- com.facebook.react:hermes-android -> 0.79.3 (*)

debugUnitTestRuntimeOnly (n)
No dependencies

debugWearApp - Link to a wear app to embed for object 'debug'. (n)
No dependencies

debugWearBundling - Resolved Configuration for wear app bundling for variant: debug
No dependencies

implementation - Implementation only dependencies for 'main' sources. (n)
+--- project react-native-voice_voice (n)
+--- project react-native-image-picker (n)
+--- project react-native-permissions (n)
+--- project react-native-vision-camera (n)
+--- project react-native-worklets-core (n)
+--- com.facebook.react:react-android (n)
\--- com.facebook.react:hermes-android (n)

implementationDependenciesMetadata
+--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
+--- project :react-native-voice_voice FAILED
+--- project :react-native-image-picker FAILED
+--- project :react-native-permissions FAILED
+--- project :react-native-vision-camera FAILED
+--- project :react-native-worklets-core FAILED
+--- com.facebook.react:react-android FAILED
\--- com.facebook.react:hermes-android FAILED

intransitiveDependenciesMetadata
No dependencies

kotlinBuildToolsApiClasspath
\--- org.jetbrains.kotlin:kotlin-build-tools-impl:{strictly 2.0.21} -> 2.0.21
     +--- org.jetbrains.kotlin:kotlin-build-tools-api:2.0.21
     +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |    \--- org.jetbrains:annotations:13.0
     +--- org.jetbrains.kotlin:kotlin-compiler-embeddable:2.0.21
     |    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     |    +--- org.jetbrains.kotlin:kotlin-script-runtime:2.0.21
     |    +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10
     |    +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:2.0.21
     |    +--- org.jetbrains.intellij.deps:trove4j:1.0.20200330
     |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4
     +--- org.jetbrains.kotlin:kotlin-compiler-runner:2.0.21
     |    +--- org.jetbrains.kotlin:kotlin-build-common:2.0.21
     |    +--- org.jetbrains.kotlin:kotlin-daemon-client:2.0.21
     |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4
     |    \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:2.0.21 (*)
     +--- org.jetbrains.kotlin:kotlin-scripting-compiler-embeddable:2.0.21
     |    +--- org.jetbrains.kotlin:kotlin-scripting-compiler-impl-embeddable:2.0.21
     |    |    +--- org.jetbrains.kotlin:kotlin-scripting-common:2.0.21
     |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     |    |    +--- org.jetbrains.kotlin:kotlin-scripting-jvm:2.0.21
     |    |    |    +--- org.jetbrains.kotlin:kotlin-script-runtime:2.0.21
     |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     |    |    |    \--- org.jetbrains.kotlin:kotlin-scripting-common:2.0.21 (*)
     |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
     \--- org.jetbrains.kotlin:kotlin-scripting-compiler-impl-embeddable:2.0.21 (*)

kotlinCompilerClasspath
\--- org.jetbrains.kotlin:kotlin-compiler-embeddable:2.0.21
     +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |    \--- org.jetbrains:annotations:13.0
     +--- org.jetbrains.kotlin:kotlin-script-runtime:2.0.21
     +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10
     +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:2.0.21
     +--- org.jetbrains.intellij.deps:trove4j:1.0.20200330
     \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4

kotlinCompilerPluginClasspath
No dependencies

kotlinCompilerPluginClasspathDebug - Kotlin compiler plugins for compilation
No dependencies

kotlinCompilerPluginClasspathDebugAndroidTest - Kotlin compiler plugins for compilation
No dependencies

kotlinCompilerPluginClasspathDebugUnitTest - Kotlin compiler plugins for compilation
No dependencies

kotlinCompilerPluginClasspathRelease - Kotlin compiler plugins for compilation
No dependencies

kotlinCompilerPluginClasspathReleaseUnitTest - Kotlin compiler plugins for compilation
No dependencies

kotlinKlibCommonizerClasspath
\--- org.jetbrains.kotlin:kotlin-klib-commonizer-embeddable:2.0.21
     +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
     |    \--- org.jetbrains:annotations:13.0
     \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:2.0.21
          +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
          +--- org.jetbrains.kotlin:kotlin-script-runtime:2.0.21
          +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10
          +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:2.0.21
          +--- org.jetbrains.intellij.deps:trove4j:1.0.20200330
          \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4

kotlinNativeBundleConfiguration
\--- org.jetbrains.kotlin:kotlin-native-prebuilt:2.0.21

kotlinNativeCompilerPluginClasspath
No dependencies

lintChecks - Configuration to apply external lint check jar
No dependencies

lintPublish - Configuration to publish external lint check jar
No dependencies

releaseAnnotationProcessor - Classpath for the annotation processor for 'release'. (n)
No dependencies

releaseAnnotationProcessorClasspath - Resolved configuration for annotation-processor for variant: release
No dependencies

releaseApi - API dependencies for 'release' sources. (n)
No dependencies

releaseApiDependenciesMetadata
No dependencies

releaseApiElements - API elements for release (n)
No dependencies

releaseCompilationApi - API dependencies for '/release'. (n)
No dependencies

releaseCompilationCompileOnly - Compile only dependencies for '/release'. (n)
No dependencies

releaseCompilationImplementation - Implementation only dependencies for '/release'. (n)
No dependencies

releaseCompilationRuntimeOnly - Runtime only dependencies for '/release'. (n)
No dependencies

releaseCompileClasspath - Compile classpath for '/release'.
+--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
|    +--- org.jetbrains:annotations:13.0 -> 23.0.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 (c)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 (c)
+--- project :react-native-voice_voice
+--- project :react-native-image-picker
+--- project :react-native-permissions
+--- project :react-native-vision-camera
|    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- project :react-native-worklets-core
+--- com.facebook.react:react-android -> 0.79.3
|    +--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1
|    |    +--- androidx.activity:activity:1.6.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1
|    |    |    |    \--- androidx.annotation:annotation-jvm:1.8.1
|    |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    |    +--- androidx.core:core:1.8.0 -> 1.13.1
|    |    |    |    +--- androidx.annotation:annotation:1.6.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.annotation:annotation-experimental:1.4.0 -> 1.4.1
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 -> 2.8.7
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-runtime-android:2.8.7
|    |    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|    |    |    |    |         +--- androidx.arch.core:core-common:2.2.0
|    |    |    |    |         |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7
|    |    |    |    |         |    \--- androidx.lifecycle:lifecycle-common-jvm:2.8.7
|    |    |    |    |         |         +--- androidx.annotation:annotation:1.8.1 (*)
|    |    |    |    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0
|    |    |    |    |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0
|    |    |    |    |         |         |         +--- org.jetbrains:annotations:23.0.0
|    |    |    |    |         |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0
|    |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (c)
|    |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0 (c)
|    |    |    |    |         |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (c)
|    |    |    |    |         |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |         |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0
|    |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (*)
|    |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0 (*)
|    |    |    |    |         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    |    |    |         \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-android:2.8.7
|    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1 -> 2.8.7
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.13.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    +--- androidx.core:core:1.13.1 (*)
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (*)
|    |    |    |    +--- androidx.savedstate:savedstate:1.2.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    +--- androidx.annotation:annotation:1.3.0 -> 1.8.1 (*)
|    |    +--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0
|    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.6.0 -> 1.13.1 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |    |    |    +--- androidx.interpolator:interpolator:1.0.0
|    |    |    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    \--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1 (c)
|    |    +--- androidx.core:core:1.9.0 -> 1.13.1 (*)
|    |    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |         \--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    +--- androidx.fragment:fragment:1.3.6
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.customview:customview:1.0.0 (*)
|    |    |    +--- androidx.loader:loader:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.8.7
|    |    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0
|    |    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    |    \--- androidx.arch.core:core-common:2.2.0 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|    |    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.8.7 (*)
|    |    |    +--- androidx.activity:activity:1.2.4 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 -> 2.8.7 (*)
|    |    |    +--- androidx.savedstate:savedstate:1.1.0 -> 1.2.1 (*)
|    |    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.4.1 (*)
|    |    \--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    +--- androidx.appcompat:appcompat-resources:1.7.0 (*)
|    +--- androidx.autofill:autofill:1.1.0
|    +--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    \--- androidx.interpolator:interpolator:1.0.0 (*)
|    +--- androidx.tracing:tracing:1.1.0 -> 1.2.0
|    +--- com.facebook.fbjni:fbjni:0.7.0
|    +--- com.facebook.fresco:fresco:3.6.0
|    |    +--- com.facebook.fresco:fbcore:3.6.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:drawee:3.6.0
|    |    |    +--- com.facebook.fresco:ui-core:3.6.0
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:imagepipeline:3.6.0
|    |    |    \--- com.facebook.fresco:imagepipeline-base:3.6.0
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0
|    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0
|    |    +--- com.facebook.fresco:memory-type-native:3.6.0
|    |    +--- com.facebook.fresco:memory-type-java:3.6.0
|    |    +--- com.facebook.fresco:nativeimagefilters:3.6.0
|    |    +--- com.facebook.fresco:nativeimagetranscoder:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:imagepipeline-okhttp3:3.6.0
|    |    +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.2
|    |    |    +--- com.squareup.okio:okio:2.8.0 -> 2.9.0
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.10 -> 2.0.21
|    |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:middleware:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:ui-common:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.infer.annotation:infer-annotation:0.18.0
|    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    \--- org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72
|    +--- com.facebook.soloader:soloader:0.12.1
|    |    +--- com.facebook.soloader:annotation:0.12.1
|    |    \--- com.facebook.soloader:nativeloader:0.12.1
|    +--- com.facebook.yoga:proguard-annotations:1.19.0
|    +--- com.google.code.findbugs:jsr305:3.0.2
|    +--- com.squareup.okhttp3:okhttp-urlconnection:4.9.2
|    |    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.0
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0
|    |              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|    +--- com.squareup.okio:okio:2.9.0 (*)
|    +--- javax.inject:javax.inject:1
|    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- com.facebook.react:hermes-android -> 0.79.3
+--- org.jetbrains.kotlin:kotlin-stdlib:{strictly 2.0.21} -> 2.0.21 (c)
+--- com.facebook.react:react-android:{strictly 0.79.3} -> 0.79.3 (c)
+--- com.facebook.react:hermes-android:{strictly 0.79.3} -> 0.79.3 (c)
+--- org.jetbrains:annotations:{strictly 23.0.0} -> 23.0.0 (c)
+--- androidx.appcompat:appcompat:{strictly 1.6.1} -> 1.6.1 (c)
+--- androidx.appcompat:appcompat-resources:{strictly 1.7.0} -> 1.7.0 (c)
+--- androidx.autofill:autofill:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.swiperefreshlayout:swiperefreshlayout:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.tracing:tracing:{strictly 1.2.0} -> 1.2.0 (c)
+--- com.facebook.fbjni:fbjni:{strictly 0.7.0} -> 0.7.0 (c)
+--- com.facebook.fresco:fresco:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline-okhttp3:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:middleware:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:ui-common:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.infer.annotation:infer-annotation:{strictly 0.18.0} -> 0.18.0 (c)
+--- com.facebook.soloader:soloader:{strictly 0.12.1} -> 0.12.1 (c)
+--- com.facebook.yoga:proguard-annotations:{strictly 1.19.0} -> 1.19.0 (c)
+--- com.google.code.findbugs:jsr305:{strictly 3.0.2} -> 3.0.2 (c)
+--- com.squareup.okhttp3:okhttp-urlconnection:{strictly 4.9.2} -> 4.9.2 (c)
+--- com.squareup.okhttp3:okhttp:{strictly 4.9.2} -> 4.9.2 (c)
+--- com.squareup.okio:okio:{strictly 2.9.0} -> 2.9.0 (c)
+--- javax.inject:javax.inject:{strictly 1} -> 1 (c)
+--- androidx.activity:activity:{strictly 1.6.0} -> 1.6.0 (c)
+--- androidx.annotation:annotation:{strictly 1.8.1} -> 1.8.1 (c)
+--- androidx.core:core:{strictly 1.13.1} -> 1.13.1 (c)
+--- androidx.cursoradapter:cursoradapter:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.drawerlayout:drawerlayout:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.fragment:fragment:{strictly 1.3.6} -> 1.3.6 (c)
+--- androidx.savedstate:savedstate:{strictly 1.2.1} -> 1.2.1 (c)
+--- androidx.vectordrawable:vectordrawable:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.vectordrawable:vectordrawable-animated:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.interpolator:interpolator:{strictly 1.0.0} -> 1.0.0 (c)
+--- com.facebook.fresco:fbcore:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:drawee:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline-native:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:memory-type-ashmem:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:memory-type-native:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:memory-type-java:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:nativeimagefilters:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:nativeimagetranscoder:{strictly 3.6.0} -> 3.6.0 (c)
+--- org.jetbrains.kotlin:kotlin-annotations-jvm:{strictly 1.3.72} -> 1.3.72 (c)
+--- com.facebook.soloader:annotation:{strictly 0.12.1} -> 0.12.1 (c)
+--- com.facebook.soloader:nativeloader:{strictly 0.12.1} -> 0.12.1 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:{strictly 1.8.0} -> 1.8.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-common:{strictly 2.0.21} -> 2.0.21 (c)
+--- androidx.lifecycle:lifecycle-runtime:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-viewmodel:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-viewmodel-savedstate:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.annotation:annotation-jvm:{strictly 1.8.1} -> 1.8.1 (c)
+--- androidx.annotation:annotation-experimental:{strictly 1.4.1} -> 1.4.1 (c)
+--- androidx.versionedparcelable:versionedparcelable:{strictly 1.1.1} -> 1.1.1 (c)
+--- androidx.customview:customview:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.collection:collection:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.viewpager:viewpager:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.loader:loader:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core:{strictly 2.8.7} -> 2.8.7 (c)
+--- com.facebook.fresco:ui-core:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline-base:{strictly 3.6.0} -> 3.6.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:{strictly 1.8.0} -> 1.8.0 (c)
+--- androidx.lifecycle:lifecycle-runtime-android:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-viewmodel-android:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.core:core-ktx:{strictly 1.13.1} -> 1.13.1 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:{strictly 1.9.0} -> 1.9.0 (c)
+--- androidx.lifecycle:lifecycle-livedata:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-common:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.arch.core:core-common:{strictly 2.2.0} -> 2.2.0 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core:{strictly 1.9.0} -> 1.9.0 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:{strictly 1.9.0} -> 1.9.0 (c)
+--- androidx.arch.core:core-runtime:{strictly 2.2.0} -> 2.2.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core-ktx:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-common-jvm:{strictly 2.8.7} -> 2.8.7 (c)
\--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:{strictly 1.9.0} -> 1.9.0 (c)

releaseCompileOnly - Compile only dependencies for 'release' sources. (n)
No dependencies

releaseCompileOnlyApi - Compile only API dependencies for 'release' sources. (n)
No dependencies

releaseCompileOnlyDependenciesMetadata
No dependencies

releaseImplementation - Implementation only dependencies for 'release' sources. (n)
No dependencies

releaseImplementationDependenciesMetadata
No dependencies

releaseIntransitiveDependenciesMetadata
No dependencies

releaseReverseMetadataValues - Metadata Values dependencies for the base Split
No dependencies

releaseRuntimeClasspath - Runtime classpath of '/release'.
+--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
|    +--- org.jetbrains:annotations:13.0 -> 23.0.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 (c)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 (c)
+--- project :react-native-voice_voice
|    +--- com.android.support:appcompat-v7:28.0.0
|    |    +--- com.android.support:support-annotations:28.0.0
|    |    +--- com.android.support:support-compat:28.0.0
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:collections:28.0.0
|    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    +--- android.arch.lifecycle:runtime:1.1.1
|    |    |    |    +--- android.arch.lifecycle:common:1.1.1
|    |    |    |    |    \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    |    +--- android.arch.core:common:1.1.1
|    |    |    |    |    \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    |    \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    \--- com.android.support:versionedparcelable:28.0.0
|    |    |         +--- com.android.support:support-annotations:28.0.0
|    |    |         \--- com.android.support:collections:28.0.0 (*)
|    |    +--- com.android.support:collections:28.0.0 (*)
|    |    +--- com.android.support:cursoradapter:28.0.0
|    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    +--- com.android.support:support-core-utils:28.0.0
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    +--- com.android.support:documentfile:28.0.0
|    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:loader:28.0.0
|    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    +--- android.arch.lifecycle:livedata:1.1.1
|    |    |    |    |    +--- android.arch.core:runtime:1.1.1
|    |    |    |    |    |    +--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    |    |    |    \--- android.arch.core:common:1.1.1 (*)
|    |    |    |    |    +--- android.arch.lifecycle:livedata-core:1.1.1
|    |    |    |    |    |    +--- android.arch.lifecycle:common:1.1.1 (*)
|    |    |    |    |    |    +--- android.arch.core:common:1.1.1 (*)
|    |    |    |    |    |    \--- android.arch.core:runtime:1.1.1 (*)
|    |    |    |    |    \--- android.arch.core:common:1.1.1 (*)
|    |    |    |    \--- android.arch.lifecycle:viewmodel:1.1.1
|    |    |    |         \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    +--- com.android.support:localbroadcastmanager:28.0.0
|    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    \--- com.android.support:print:28.0.0
|    |    |         \--- com.android.support:support-annotations:28.0.0
|    |    +--- com.android.support:support-fragment:28.0.0
|    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    +--- com.android.support:support-core-ui:28.0.0
|    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    +--- com.android.support:support-core-utils:28.0.0 (*)
|    |    |    |    +--- com.android.support:customview:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    \--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    +--- com.android.support:viewpager:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:coordinatorlayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:drawerlayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:slidingpanelayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:interpolator:28.0.0
|    |    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    |    +--- com.android.support:swiperefreshlayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:interpolator:28.0.0 (*)
|    |    |    |    +--- com.android.support:asynclayoutinflater:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    \--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    \--- com.android.support:cursoradapter:28.0.0 (*)
|    |    |    +--- com.android.support:support-core-utils:28.0.0 (*)
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:loader:28.0.0 (*)
|    |    |    \--- android.arch.lifecycle:viewmodel:1.1.1 (*)
|    |    +--- com.android.support:support-vector-drawable:28.0.0
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    \--- com.android.support:support-compat:28.0.0 (*)
|    |    \--- com.android.support:animated-vector-drawable:28.0.0
|    |         +--- com.android.support:support-vector-drawable:28.0.0 (*)
|    |         \--- com.android.support:support-core-ui:28.0.0 (*)
|    \--- com.facebook.react:react-native:+ -> com.facebook.react:react-android:0.79.3
|         +--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1
|         |    +--- androidx.activity:activity:1.6.0
|         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1
|         |    |    |    \--- androidx.annotation:annotation-jvm:1.8.1
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|         |    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    +--- androidx.core:core:1.8.0 -> 1.13.1
|         |    |    |    +--- androidx.annotation:annotation:1.6.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.annotation:annotation-experimental:1.4.0 -> 1.4.1
|         |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    |    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    \--- com.google.guava:listenablefuture:1.0
|         |    |    |    +--- androidx.interpolator:interpolator:1.0.0
|         |    |    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 -> 2.8.7
|         |    |    |    |    \--- androidx.lifecycle:lifecycle-runtime-android:2.8.7
|         |    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|         |    |    |    |         +--- androidx.arch.core:core-common:2.2.0
|         |    |    |    |         |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |         +--- androidx.arch.core:core-runtime:2.2.0
|         |    |    |    |         |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |         |    \--- androidx.arch.core:core-common:2.2.0 (*)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7
|         |    |    |    |         |    \--- androidx.lifecycle:lifecycle-common-jvm:2.8.7
|         |    |    |    |         |         +--- androidx.annotation:annotation:1.8.1 (*)
|         |    |    |    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0
|         |    |    |    |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0
|         |    |    |    |         |         |         +--- org.jetbrains:annotations:23.0.0
|         |    |    |    |         |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0
|         |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (c)
|         |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (c)
|         |    |    |    |         |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0 (c)
|         |    |    |    |         |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |         |         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |         +--- androidx.profileinstaller:profileinstaller:1.3.1
|         |    |    |    |         |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    |    |         |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|         |    |    |    |         |    +--- androidx.startup:startup-runtime:1.1.1
|         |    |    |    |         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |         |    |    \--- androidx.tracing:tracing:1.0.0 -> 1.2.0
|         |    |    |    |         |    |         +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    |    |         |    |         \--- androidx.tracing:tracing-ktx:1.2.0 (c)
|         |    |    |    |         |    \--- com.google.guava:listenablefuture:1.0
|         |    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0
|         |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (*)
|         |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0 (*)
|         |    |    |    |         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    \--- androidx.core:core-ktx:1.13.1 (c)
|         |    |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-android:2.8.7
|         |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|         |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|         |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|         |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1 -> 2.8.7
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.13.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    +--- androidx.core:core:1.13.1 (*)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    \--- androidx.core:core:1.13.1 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7
|         |    |    |    |    +--- androidx.arch.core:core-common:2.2.0 (*)
|         |    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (*)
|         |    |    |    +--- androidx.savedstate:savedstate:1.2.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    +--- androidx.arch.core:core-common:2.1.0 -> 2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 -> 2.8.7 (*)
|         |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|         |    |    +--- androidx.tracing:tracing:1.0.0 -> 1.2.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    +--- androidx.annotation:annotation:1.3.0 -> 1.8.1 (*)
|         |    +--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0
|         |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    |    +--- androidx.core:core:1.6.0 -> 1.13.1 (*)
|         |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|         |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|         |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|         |    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|         |    |    |    +--- androidx.interpolator:interpolator:1.0.0 (*)
|         |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|         |    |    \--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1 (c)
|         |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    +--- androidx.core:core:1.9.0 -> 1.13.1 (*)
|         |    +--- androidx.core:core-ktx:1.8.0 -> 1.13.1 (*)
|         |    +--- androidx.cursoradapter:cursoradapter:1.0.0
|         |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    +--- androidx.drawerlayout:drawerlayout:1.0.0
|         |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    |    \--- androidx.customview:customview:1.0.0
|         |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |         \--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    +--- androidx.emoji2:emoji2:1.2.0
|         |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    +--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.core:core:1.3.0 -> 1.13.1 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-process:2.4.1 -> 2.8.7
|         |    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (*)
|         |    |    |    +--- androidx.startup:startup-runtime:1.1.1 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    \--- androidx.startup:startup-runtime:1.0.0 -> 1.1.1 (*)
|         |    +--- androidx.emoji2:emoji2-views-helper:1.2.0
|         |    |    +--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.core:core:1.3.0 -> 1.13.1 (*)
|         |    |    \--- androidx.emoji2:emoji2:1.2.0 (*)
|         |    +--- androidx.fragment:fragment:1.3.6
|         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|         |    |    +--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.viewpager:viewpager:1.0.0
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    |    |    \--- androidx.customview:customview:1.0.0 (*)
|         |    |    +--- androidx.loader:loader:1.0.0
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.8.7
|         |    |    |    |    +--- androidx.arch.core:core-common:2.2.0 (*)
|         |    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|         |    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.8.7 (*)
|         |    |    +--- androidx.activity:activity:1.2.4 -> 1.6.0 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 -> 2.8.7 (*)
|         |    |    +--- androidx.savedstate:savedstate:1.1.0 -> 1.2.1 (*)
|         |    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.4.1 (*)
|         |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7 (*)
|         |    +--- androidx.resourceinspection:resourceinspection-annotation:1.0.1
|         |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    \--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0 (c)
|         +--- androidx.appcompat:appcompat-resources:1.7.0 (*)
|         +--- androidx.autofill:autofill:1.1.0
|         |    \--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|         +--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|         |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|         |    \--- androidx.interpolator:interpolator:1.0.0 (*)
|         +--- androidx.tracing:tracing:1.1.0 -> 1.2.0 (*)
|         +--- com.facebook.fbjni:fbjni:0.7.0
|         |    \--- com.facebook.soloader:nativeloader:0.10.5 -> 0.12.1
|         +--- com.facebook.fresco:fresco:3.6.0
|         |    +--- com.facebook.fresco:soloader:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0
|         |    |    |    +--- androidx.core:core:1.13.1 (*)
|         |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    \--- com.facebook.soloader:soloader:0.11.0 -> 0.12.1
|         |    |         +--- com.facebook.soloader:annotation:0.12.1
|         |    |         \--- com.facebook.soloader:nativeloader:0.12.1
|         |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    +--- com.facebook.fresco:ui-common:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:ui-core:3.6.0
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:middleware:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:ui-common:3.6.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    +--- com.facebook.fresco:drawee:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:imagepipeline:3.6.0
|         |    |    |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    |    +--- com.facebook.soloader:annotation:0.11.0 -> 0.12.1
|         |    |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:urimod:3.6.0
|         |    |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    |    +--- com.facebook.fresco:ui-core:3.6.0 (*)
|         |    |    |    |    +--- com.facebook.fresco:vito-source:3.6.0
|         |    |    |    |    |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    \--- com.facebook.fresco:imagepipeline-base:3.6.0
|         |    |    |         +--- com.facebook.infer.annotation:infer-annotation:0.18.0
|         |    |    |         |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|         |    |    |         |    \--- org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72
|         |    |    |         +--- com.facebook.soloader:annotation:0.11.0 -> 0.12.1
|         |    |    |         +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    |         +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |         +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0
|         |    |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    \--- com.facebook.soloader:soloader:0.11.0 -> 0.12.1 (*)
|         |    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    \--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-native:3.6.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    |    |    \--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    +--- com.facebook.fresco:memory-type-java:3.6.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    |    \--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:ui-common:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:ui-core:3.6.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-java:3.6.0 (*)
|         |    +--- com.facebook.fresco:nativeimagefilters:3.6.0
|         |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-native:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-java:3.6.0 (*)
|         |    |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    \--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    +--- com.facebook.fresco:nativeimagetranscoder:3.6.0
|         |    |    +--- com.facebook.fresco:imagepipeline-base:3.6.0 (*)
|         |    |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    \--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         +--- com.facebook.fresco:imagepipeline-okhttp3:3.6.0
|         |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-java:3.6.0 (*)
|         |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.2
|         |    |    +--- com.squareup.okio:okio:2.8.0 -> 2.9.0
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|         |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.10 -> 2.0.21
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         +--- com.facebook.fresco:middleware:3.6.0 (*)
|         +--- com.facebook.fresco:ui-common:3.6.0 (*)
|         +--- com.facebook.infer.annotation:infer-annotation:0.18.0 (*)
|         +--- com.facebook.soloader:soloader:0.12.1 (*)
|         +--- com.facebook.yoga:proguard-annotations:1.19.0
|         +--- com.google.code.findbugs:jsr305:3.0.2
|         +--- com.squareup.okhttp3:okhttp-urlconnection:4.9.2
|         |    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.0
|         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|         |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0
|         |              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|         +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|         +--- com.squareup.okio:okio:2.9.0 (*)
|         +--- javax.inject:javax.inject:1
|         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- project :react-native-image-picker
|    +--- com.facebook.react:react-native:+ -> com.facebook.react:react-android:0.79.3 (*)
|    +--- androidx.core:core:1.3.1 -> 1.13.1 (*)
|    \--- androidx.exifinterface:exifinterface:1.3.3
|         \--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
+--- project :react-native-permissions
|    \--- com.facebook.react:react-native:+ -> com.facebook.react:react-android:0.79.3 (*)
+--- project :react-native-vision-camera
|    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|    +--- com.facebook.react:react-android:+ -> 0.79.3 (*)
|    +--- androidx.camera:camera-core:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.annotation:annotation-experimental:1.4.1 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    +--- androidx.concurrent:concurrent-futures-ktx:1.1.0
|    |    |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.71 -> 2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.3.4 -> 1.9.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- androidx.exifinterface:exifinterface:1.3.2 -> 1.3.3 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.1.0 -> 2.8.7 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata:2.1.0 -> 2.8.7 (*)
|    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-camera2:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    +--- androidx.concurrent:concurrent-futures-ktx:1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.1.0 -> 2.8.7 (*)
|    |    +--- androidx.tracing:tracing-ktx:1.2.0
|    |    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 -> 2.0.21 (*)
|    |    |    \--- androidx.tracing:tracing:1.2.0 (c)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-video:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-view:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.annotation:annotation-experimental:1.4.1 (*)
|    |    +--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (*)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.3.2 -> 1.13.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.0.0 -> 2.8.7 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-extensions:1.5.0-alpha03
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (*)
|    +--- com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1
|    |    +--- com.google.android.datatransport:transport-api:2.2.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    +--- com.google.android.datatransport:transport-backend-cct:2.3.3
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |    +--- com.google.android.datatransport:transport-runtime:2.2.5 -> 2.2.6
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |    |    \--- javax.inject:javax.inject:1
|    |    |    +--- com.google.firebase:firebase-encoders:16.1.0
|    |    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    \--- com.google.firebase:firebase-encoders-json:17.1.0
|    |    |         +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |         \--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    +--- com.google.android.datatransport:transport-runtime:2.2.6 (*)
|    |    +--- com.google.android.gms:play-services-base:18.5.0
|    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    |    |    +--- com.google.android.gms:play-services-basement:18.4.0
|    |    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|    |    |    \--- com.google.android.gms:play-services-tasks:18.2.0
|    |    |         \--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-tasks:18.2.0 (*)
|    |    +--- com.google.android.odml:image:1.0.0-beta1
|    |    +--- com.google.firebase:firebase-components:16.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    \--- com.google.firebase:firebase-annotations:16.0.0
|    |    +--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    +--- com.google.firebase:firebase-encoders-json:17.1.0 (*)
|    |    +--- com.google.mlkit:barcode-scanning-common:17.0.0
|    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    |    \--- com.google.mlkit:vision-common:17.0.0 -> 17.3.0
|    |    |         +--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3 (*)
|    |    |         +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |         +--- com.google.android.datatransport:transport-backend-cct:2.3.3 (*)
|    |    |         +--- com.google.android.datatransport:transport-runtime:2.2.6 (*)
|    |    |         +--- com.google.android.gms:play-services-base:18.1.0 -> 18.5.0 (*)
|    |    |         +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.4.0 (*)
|    |    |         +--- com.google.android.gms:play-services-tasks:18.0.2 -> 18.2.0 (*)
|    |    |         +--- com.google.android.odml:image:1.0.0-beta1
|    |    |         +--- com.google.firebase:firebase-components:16.1.0 (*)
|    |    |         +--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    |         +--- com.google.firebase:firebase-encoders-json:17.1.0 (*)
|    |    |         \--- com.google.mlkit:common:18.6.0 -> 18.11.0
|    |    |              +--- androidx.appcompat:appcompat:1.6.1 (*)
|    |    |              +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |              +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |              +--- com.google.android.datatransport:transport-backend-cct:2.3.3 (*)
|    |    |              +--- com.google.android.datatransport:transport-runtime:2.2.6 (*)
|    |    |              +--- com.google.android.gms:play-services-base:18.5.0 (*)
|    |    |              +--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    |              +--- com.google.android.gms:play-services-tasks:18.2.0 (*)
|    |    |              +--- com.google.firebase:firebase-components:16.1.0 (*)
|    |    |              +--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    |              \--- com.google.firebase:firebase-encoders-json:17.1.0 (*)
|    |    +--- com.google.mlkit:common:18.11.0 (*)
|    |    +--- com.google.mlkit:vision-common:17.3.0 (*)
|    |    \--- com.google.mlkit:vision-interfaces:16.3.0
|    |         +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.4.0 (*)
|    |         \--- com.google.android.gms:play-services-tasks:18.0.2 -> 18.2.0 (*)
|    \--- project :react-native-worklets-core
|         +--- com.facebook.react:react-android -> 0.79.3 (*)
|         \--- com.facebook.react:hermes-android -> 0.79.3
|              +--- com.facebook.fbjni:fbjni:0.7.0 (*)
|              +--- com.facebook.yoga:proguard-annotations:1.19.0
|              \--- androidx.annotation:annotation:1.6.0 -> 1.8.1 (*)
+--- project :react-native-worklets-core (*)
+--- com.facebook.react:react-android -> 0.79.3 (*)
\--- com.facebook.react:hermes-android -> 0.79.3 (*)

releaseRuntimeElements - Runtime elements for release (n)
No dependencies

releaseRuntimeOnly - Runtime only dependencies for 'release' sources. (n)
No dependencies

releaseUnitTestAnnotationProcessorClasspath - Resolved configuration for annotation-processor for variant: releaseUnitTest
No dependencies

releaseUnitTestApi (n)
No dependencies

releaseUnitTestApiDependenciesMetadata
No dependencies

releaseUnitTestCompilationApi - API dependencies for '/releaseUnitTest'. (n)
No dependencies

releaseUnitTestCompilationCompileOnly - Compile only dependencies for '/releaseUnitTest'. (n)
No dependencies

releaseUnitTestCompilationImplementation - Implementation only dependencies for '/releaseUnitTest'. (n)
No dependencies

releaseUnitTestCompilationRuntimeOnly - Runtime only dependencies for '/releaseUnitTest'. (n)
No dependencies

releaseUnitTestCompileClasspath - Compile classpath for '/releaseUnitTest'.
+--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
|    +--- org.jetbrains:annotations:13.0 -> 23.0.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 (c)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 (c)
+--- project :react-native-voice_voice
+--- project :react-native-image-picker
+--- project :react-native-permissions
+--- project :react-native-vision-camera
|    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- project :react-native-worklets-core
+--- com.facebook.react:react-android -> 0.79.3
|    +--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1
|    |    +--- androidx.activity:activity:1.6.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1
|    |    |    |    \--- androidx.annotation:annotation-jvm:1.8.1
|    |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    |    +--- androidx.core:core:1.8.0 -> 1.13.1
|    |    |    |    +--- androidx.annotation:annotation:1.6.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.annotation:annotation-experimental:1.4.0 -> 1.4.1
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 -> 2.8.7
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-runtime-android:2.8.7
|    |    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|    |    |    |    |         +--- androidx.arch.core:core-common:2.2.0
|    |    |    |    |         |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7
|    |    |    |    |         |    \--- androidx.lifecycle:lifecycle-common-jvm:2.8.7
|    |    |    |    |         |         +--- androidx.annotation:annotation:1.8.1 (*)
|    |    |    |    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0
|    |    |    |    |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0
|    |    |    |    |         |         |         +--- org.jetbrains:annotations:23.0.0
|    |    |    |    |         |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0
|    |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (c)
|    |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0 (c)
|    |    |    |    |         |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (c)
|    |    |    |    |         |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |         |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0
|    |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (*)
|    |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0 (*)
|    |    |    |    |         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    |    |    |         \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-android:2.8.7
|    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1 -> 2.8.7
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.13.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    +--- androidx.core:core:1.13.1 (*)
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (*)
|    |    |    |    +--- androidx.savedstate:savedstate:1.2.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|    |    +--- androidx.annotation:annotation:1.3.0 -> 1.8.1 (*)
|    |    +--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0
|    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.6.0 -> 1.13.1 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |    |    |    +--- androidx.interpolator:interpolator:1.0.0
|    |    |    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    \--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1 (c)
|    |    +--- androidx.core:core:1.9.0 -> 1.13.1 (*)
|    |    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |         \--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    +--- androidx.fragment:fragment:1.3.6
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.customview:customview:1.0.0 (*)
|    |    |    +--- androidx.loader:loader:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.8.7
|    |    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0
|    |    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    |    |    \--- androidx.arch.core:core-common:2.2.0 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|    |    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.8.7 (*)
|    |    |    +--- androidx.activity:activity:1.2.4 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 -> 2.8.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 -> 2.8.7 (*)
|    |    |    +--- androidx.savedstate:savedstate:1.1.0 -> 1.2.1 (*)
|    |    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.4.1 (*)
|    |    \--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    +--- androidx.appcompat:appcompat-resources:1.7.0 (*)
|    +--- androidx.autofill:autofill:1.1.0
|    +--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    \--- androidx.interpolator:interpolator:1.0.0 (*)
|    +--- androidx.tracing:tracing:1.1.0 -> 1.2.0
|    +--- com.facebook.fbjni:fbjni:0.7.0
|    +--- com.facebook.fresco:fresco:3.6.0
|    |    +--- com.facebook.fresco:fbcore:3.6.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:drawee:3.6.0
|    |    |    +--- com.facebook.fresco:ui-core:3.6.0
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:imagepipeline:3.6.0
|    |    |    \--- com.facebook.fresco:imagepipeline-base:3.6.0
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0
|    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0
|    |    +--- com.facebook.fresco:memory-type-native:3.6.0
|    |    +--- com.facebook.fresco:memory-type-java:3.6.0
|    |    +--- com.facebook.fresco:nativeimagefilters:3.6.0
|    |    +--- com.facebook.fresco:nativeimagetranscoder:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:imagepipeline-okhttp3:3.6.0
|    |    +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.2
|    |    |    +--- com.squareup.okio:okio:2.8.0 -> 2.9.0
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.10 -> 2.0.21
|    |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:middleware:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.fresco:ui-common:3.6.0
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|    +--- com.facebook.infer.annotation:infer-annotation:0.18.0
|    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    \--- org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72
|    +--- com.facebook.soloader:soloader:0.12.1
|    |    +--- com.facebook.soloader:annotation:0.12.1
|    |    \--- com.facebook.soloader:nativeloader:0.12.1
|    +--- com.facebook.yoga:proguard-annotations:1.19.0
|    +--- com.google.code.findbugs:jsr305:3.0.2
|    +--- com.squareup.okhttp3:okhttp-urlconnection:4.9.2
|    |    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.0
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0
|    |              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|    +--- com.squareup.okio:okio:2.9.0 (*)
|    +--- javax.inject:javax.inject:1
|    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- com.facebook.react:hermes-android -> 0.79.3
+--- project :app (*)
+--- org.jetbrains.kotlin:kotlin-stdlib:{strictly 2.0.21} -> 2.0.21 (c)
+--- com.facebook.react:react-android:{strictly 0.79.3} -> 0.79.3 (c)
+--- com.facebook.react:hermes-android:{strictly 0.79.3} -> 0.79.3 (c)
+--- org.jetbrains:annotations:{strictly 23.0.0} -> 23.0.0 (c)
+--- androidx.appcompat:appcompat:{strictly 1.6.1} -> 1.6.1 (c)
+--- androidx.appcompat:appcompat-resources:{strictly 1.7.0} -> 1.7.0 (c)
+--- androidx.autofill:autofill:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.swiperefreshlayout:swiperefreshlayout:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.tracing:tracing:{strictly 1.2.0} -> 1.2.0 (c)
+--- com.facebook.fbjni:fbjni:{strictly 0.7.0} -> 0.7.0 (c)
+--- com.facebook.fresco:fresco:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline-okhttp3:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:middleware:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:ui-common:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.infer.annotation:infer-annotation:{strictly 0.18.0} -> 0.18.0 (c)
+--- com.facebook.soloader:soloader:{strictly 0.12.1} -> 0.12.1 (c)
+--- com.facebook.yoga:proguard-annotations:{strictly 1.19.0} -> 1.19.0 (c)
+--- com.google.code.findbugs:jsr305:{strictly 3.0.2} -> 3.0.2 (c)
+--- com.squareup.okhttp3:okhttp-urlconnection:{strictly 4.9.2} -> 4.9.2 (c)
+--- com.squareup.okhttp3:okhttp:{strictly 4.9.2} -> 4.9.2 (c)
+--- com.squareup.okio:okio:{strictly 2.9.0} -> 2.9.0 (c)
+--- javax.inject:javax.inject:{strictly 1} -> 1 (c)
+--- androidx.activity:activity:{strictly 1.6.0} -> 1.6.0 (c)
+--- androidx.annotation:annotation:{strictly 1.8.1} -> 1.8.1 (c)
+--- androidx.core:core:{strictly 1.13.1} -> 1.13.1 (c)
+--- androidx.cursoradapter:cursoradapter:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.drawerlayout:drawerlayout:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.fragment:fragment:{strictly 1.3.6} -> 1.3.6 (c)
+--- androidx.savedstate:savedstate:{strictly 1.2.1} -> 1.2.1 (c)
+--- androidx.vectordrawable:vectordrawable:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.vectordrawable:vectordrawable-animated:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.interpolator:interpolator:{strictly 1.0.0} -> 1.0.0 (c)
+--- com.facebook.fresco:fbcore:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:drawee:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline-native:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:memory-type-ashmem:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:memory-type-native:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:memory-type-java:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:nativeimagefilters:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:nativeimagetranscoder:{strictly 3.6.0} -> 3.6.0 (c)
+--- org.jetbrains.kotlin:kotlin-annotations-jvm:{strictly 1.3.72} -> 1.3.72 (c)
+--- com.facebook.soloader:annotation:{strictly 0.12.1} -> 0.12.1 (c)
+--- com.facebook.soloader:nativeloader:{strictly 0.12.1} -> 0.12.1 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:{strictly 1.8.0} -> 1.8.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-common:{strictly 2.0.21} -> 2.0.21 (c)
+--- androidx.lifecycle:lifecycle-runtime:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-viewmodel:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-viewmodel-savedstate:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.annotation:annotation-jvm:{strictly 1.8.1} -> 1.8.1 (c)
+--- androidx.annotation:annotation-experimental:{strictly 1.4.1} -> 1.4.1 (c)
+--- androidx.versionedparcelable:versionedparcelable:{strictly 1.1.1} -> 1.1.1 (c)
+--- androidx.customview:customview:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.collection:collection:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.viewpager:viewpager:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.loader:loader:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core:{strictly 2.8.7} -> 2.8.7 (c)
+--- com.facebook.fresco:ui-core:{strictly 3.6.0} -> 3.6.0 (c)
+--- com.facebook.fresco:imagepipeline-base:{strictly 3.6.0} -> 3.6.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:{strictly 1.8.0} -> 1.8.0 (c)
+--- androidx.lifecycle:lifecycle-runtime-android:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-viewmodel-android:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.core:core-ktx:{strictly 1.13.1} -> 1.13.1 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:{strictly 1.9.0} -> 1.9.0 (c)
+--- androidx.lifecycle:lifecycle-livedata:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-common:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.arch.core:core-common:{strictly 2.2.0} -> 2.2.0 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core:{strictly 1.9.0} -> 1.9.0 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:{strictly 1.9.0} -> 1.9.0 (c)
+--- androidx.arch.core:core-runtime:{strictly 2.2.0} -> 2.2.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core-ktx:{strictly 2.8.7} -> 2.8.7 (c)
+--- androidx.lifecycle:lifecycle-common-jvm:{strictly 2.8.7} -> 2.8.7 (c)
\--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:{strictly 1.9.0} -> 1.9.0 (c)

releaseUnitTestCompileOnly (n)
No dependencies

releaseUnitTestCompileOnlyDependenciesMetadata
No dependencies

releaseUnitTestImplementation (n)
No dependencies

releaseUnitTestImplementationDependenciesMetadata
No dependencies

releaseUnitTestIntransitiveDependenciesMetadata
No dependencies

releaseUnitTestRuntimeClasspath - Runtime classpath of '/releaseUnitTest'.
+--- project :app (*)
+--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21
|    +--- org.jetbrains:annotations:13.0 -> 23.0.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 (c)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 (c)
+--- project :react-native-voice_voice
|    +--- com.android.support:appcompat-v7:28.0.0
|    |    +--- com.android.support:support-annotations:28.0.0
|    |    +--- com.android.support:support-compat:28.0.0
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:collections:28.0.0
|    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    +--- android.arch.lifecycle:runtime:1.1.1
|    |    |    |    +--- android.arch.lifecycle:common:1.1.1
|    |    |    |    |    \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    |    +--- android.arch.core:common:1.1.1
|    |    |    |    |    \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    |    \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    \--- com.android.support:versionedparcelable:28.0.0
|    |    |         +--- com.android.support:support-annotations:28.0.0
|    |    |         \--- com.android.support:collections:28.0.0 (*)
|    |    +--- com.android.support:collections:28.0.0 (*)
|    |    +--- com.android.support:cursoradapter:28.0.0
|    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    +--- com.android.support:support-core-utils:28.0.0
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    +--- com.android.support:documentfile:28.0.0
|    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:loader:28.0.0
|    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    +--- android.arch.lifecycle:livedata:1.1.1
|    |    |    |    |    +--- android.arch.core:runtime:1.1.1
|    |    |    |    |    |    +--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    |    |    |    \--- android.arch.core:common:1.1.1 (*)
|    |    |    |    |    +--- android.arch.lifecycle:livedata-core:1.1.1
|    |    |    |    |    |    +--- android.arch.lifecycle:common:1.1.1 (*)
|    |    |    |    |    |    +--- android.arch.core:common:1.1.1 (*)
|    |    |    |    |    |    \--- android.arch.core:runtime:1.1.1 (*)
|    |    |    |    |    \--- android.arch.core:common:1.1.1 (*)
|    |    |    |    \--- android.arch.lifecycle:viewmodel:1.1.1
|    |    |    |         \--- com.android.support:support-annotations:26.1.0 -> 28.0.0
|    |    |    +--- com.android.support:localbroadcastmanager:28.0.0
|    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    \--- com.android.support:print:28.0.0
|    |    |         \--- com.android.support:support-annotations:28.0.0
|    |    +--- com.android.support:support-fragment:28.0.0
|    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    +--- com.android.support:support-core-ui:28.0.0
|    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    +--- com.android.support:support-core-utils:28.0.0 (*)
|    |    |    |    +--- com.android.support:customview:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    \--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    +--- com.android.support:viewpager:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:coordinatorlayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:drawerlayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:slidingpanelayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:customview:28.0.0 (*)
|    |    |    |    +--- com.android.support:interpolator:28.0.0
|    |    |    |    |    \--- com.android.support:support-annotations:28.0.0
|    |    |    |    +--- com.android.support:swiperefreshlayout:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    +--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    |    \--- com.android.support:interpolator:28.0.0 (*)
|    |    |    |    +--- com.android.support:asynclayoutinflater:28.0.0
|    |    |    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    |    |    \--- com.android.support:support-compat:28.0.0 (*)
|    |    |    |    \--- com.android.support:cursoradapter:28.0.0 (*)
|    |    |    +--- com.android.support:support-core-utils:28.0.0 (*)
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    +--- com.android.support:loader:28.0.0 (*)
|    |    |    \--- android.arch.lifecycle:viewmodel:1.1.1 (*)
|    |    +--- com.android.support:support-vector-drawable:28.0.0
|    |    |    +--- com.android.support:support-annotations:28.0.0
|    |    |    \--- com.android.support:support-compat:28.0.0 (*)
|    |    \--- com.android.support:animated-vector-drawable:28.0.0
|    |         +--- com.android.support:support-vector-drawable:28.0.0 (*)
|    |         \--- com.android.support:support-core-ui:28.0.0 (*)
|    \--- com.facebook.react:react-native:+ -> com.facebook.react:react-android:0.79.3
|         +--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1
|         |    +--- androidx.activity:activity:1.6.0
|         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1
|         |    |    |    \--- androidx.annotation:annotation-jvm:1.8.1
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|         |    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    +--- androidx.core:core:1.8.0 -> 1.13.1
|         |    |    |    +--- androidx.annotation:annotation:1.6.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.annotation:annotation-experimental:1.4.0 -> 1.4.1
|         |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    |    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    \--- com.google.guava:listenablefuture:1.0
|         |    |    |    +--- androidx.interpolator:interpolator:1.0.0
|         |    |    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 -> 2.8.7
|         |    |    |    |    \--- androidx.lifecycle:lifecycle-runtime-android:2.8.7
|         |    |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|         |    |    |    |         +--- androidx.arch.core:core-common:2.2.0
|         |    |    |    |         |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |         +--- androidx.arch.core:core-runtime:2.2.0
|         |    |    |    |         |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |         |    \--- androidx.arch.core:core-common:2.2.0 (*)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7
|         |    |    |    |         |    \--- androidx.lifecycle:lifecycle-common-jvm:2.8.7
|         |    |    |    |         |         +--- androidx.annotation:annotation:1.8.1 (*)
|         |    |    |    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0
|         |    |    |    |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0
|         |    |    |    |         |         |         +--- org.jetbrains:annotations:23.0.0
|         |    |    |    |         |         |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0
|         |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (c)
|         |    |    |    |         |         |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (c)
|         |    |    |    |         |         |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0 (c)
|         |    |    |    |         |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |         |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |         |         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |         +--- androidx.profileinstaller:profileinstaller:1.3.1
|         |    |    |    |         |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    |    |         |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|         |    |    |    |         |    +--- androidx.startup:startup-runtime:1.1.1
|         |    |    |    |         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |         |    |    \--- androidx.tracing:tracing:1.0.0 -> 1.2.0
|         |    |    |    |         |    |         +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    |    |         |    |         \--- androidx.tracing:tracing-ktx:1.2.0 (c)
|         |    |    |    |         |    \--- com.google.guava:listenablefuture:1.0
|         |    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0
|         |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0 (*)
|         |    |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.9.0 (*)
|         |    |    |    |         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    \--- androidx.core:core-ktx:1.13.1 (c)
|         |    |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-android:2.8.7
|         |    |    |         +--- androidx.annotation:annotation:1.8.0 -> 1.8.1 (*)
|         |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|         |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|         |    |    |         +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1 -> 2.8.7
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.13.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    +--- androidx.core:core:1.13.1 (*)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    \--- androidx.core:core:1.13.1 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7
|         |    |    |    |    +--- androidx.arch.core:core-common:2.2.0 (*)
|         |    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (*)
|         |    |    |    +--- androidx.savedstate:savedstate:1.2.1
|         |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    |    +--- androidx.arch.core:core-common:2.1.0 -> 2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 -> 2.8.7 (*)
|         |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 2.0.21 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|         |    |    +--- androidx.tracing:tracing:1.0.0 -> 1.2.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    +--- androidx.annotation:annotation:1.3.0 -> 1.8.1 (*)
|         |    +--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0
|         |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    |    +--- androidx.core:core:1.6.0 -> 1.13.1 (*)
|         |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|         |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|         |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|         |    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|         |    |    |    +--- androidx.interpolator:interpolator:1.0.0 (*)
|         |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|         |    |    \--- androidx.appcompat:appcompat:1.7.0 -> 1.6.1 (c)
|         |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         |    +--- androidx.core:core:1.9.0 -> 1.13.1 (*)
|         |    +--- androidx.core:core-ktx:1.8.0 -> 1.13.1 (*)
|         |    +--- androidx.cursoradapter:cursoradapter:1.0.0
|         |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    +--- androidx.drawerlayout:drawerlayout:1.0.0
|         |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    |    \--- androidx.customview:customview:1.0.0
|         |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |         \--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    +--- androidx.emoji2:emoji2:1.2.0
|         |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    +--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.core:core:1.3.0 -> 1.13.1 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-process:2.4.1 -> 2.8.7
|         |    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (*)
|         |    |    |    +--- androidx.startup:startup-runtime:1.1.1 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    \--- androidx.startup:startup-runtime:1.0.0 -> 1.1.1 (*)
|         |    +--- androidx.emoji2:emoji2-views-helper:1.2.0
|         |    |    +--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.core:core:1.3.0 -> 1.13.1 (*)
|         |    |    \--- androidx.emoji2:emoji2:1.2.0 (*)
|         |    +--- androidx.fragment:fragment:1.3.6
|         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|         |    |    +--- androidx.collection:collection:1.1.0 (*)
|         |    |    +--- androidx.viewpager:viewpager:1.0.0
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    |    |    \--- androidx.customview:customview:1.0.0 (*)
|         |    |    +--- androidx.loader:loader:1.0.0
|         |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.8.1 (*)
|         |    |    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|         |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.8.7
|         |    |    |    |    +--- androidx.arch.core:core-common:2.2.0 (*)
|         |    |    |    |    +--- androidx.arch.core:core-runtime:2.2.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (*)
|         |    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|         |    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3 -> 1.9.0 (*)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.8.7 (c)
|         |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.8.7 (c)
|         |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7 (c)
|         |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.8.7 (*)
|         |    |    +--- androidx.activity:activity:1.2.4 -> 1.6.0 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 -> 2.8.7 (*)
|         |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 -> 2.8.7 (*)
|         |    |    +--- androidx.savedstate:savedstate:1.1.0 -> 1.2.1 (*)
|         |    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.4.1 (*)
|         |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.8.7 (*)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.8.7 (*)
|         |    +--- androidx.resourceinspection:resourceinspection-annotation:1.0.1
|         |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 2.0.21 (*)
|         |    \--- androidx.appcompat:appcompat-resources:1.6.1 -> 1.7.0 (c)
|         +--- androidx.appcompat:appcompat-resources:1.7.0 (*)
|         +--- androidx.autofill:autofill:1.1.0
|         |    \--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|         +--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|         |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|         |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|         |    \--- androidx.interpolator:interpolator:1.0.0 (*)
|         +--- androidx.tracing:tracing:1.1.0 -> 1.2.0 (*)
|         +--- com.facebook.fbjni:fbjni:0.7.0
|         |    \--- com.facebook.soloader:nativeloader:0.10.5 -> 0.12.1
|         +--- com.facebook.fresco:fresco:3.6.0
|         |    +--- com.facebook.fresco:soloader:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0
|         |    |    |    +--- androidx.core:core:1.13.1 (*)
|         |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    \--- com.facebook.soloader:soloader:0.11.0 -> 0.12.1
|         |    |         +--- com.facebook.soloader:annotation:0.12.1
|         |    |         \--- com.facebook.soloader:nativeloader:0.12.1
|         |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    +--- com.facebook.fresco:ui-common:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:ui-core:3.6.0
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:middleware:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:ui-common:3.6.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    +--- com.facebook.fresco:drawee:3.6.0
|         |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:imagepipeline:3.6.0
|         |    |    |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    |    +--- com.facebook.soloader:annotation:0.11.0 -> 0.12.1
|         |    |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:urimod:3.6.0
|         |    |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    |    +--- com.facebook.fresco:ui-core:3.6.0 (*)
|         |    |    |    |    +--- com.facebook.fresco:vito-source:3.6.0
|         |    |    |    |    |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    |    \--- com.facebook.fresco:imagepipeline-base:3.6.0
|         |    |    |         +--- com.facebook.infer.annotation:infer-annotation:0.18.0
|         |    |    |         |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|         |    |    |         |    \--- org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72
|         |    |    |         +--- com.facebook.soloader:annotation:0.11.0 -> 0.12.1
|         |    |    |         +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    |         +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |         +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0
|         |    |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    \--- com.facebook.soloader:soloader:0.11.0 -> 0.12.1 (*)
|         |    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    \--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-native:3.6.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    |    |    \--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    +--- com.facebook.fresco:memory-type-java:3.6.0
|         |    |    |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    |    \--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:ui-common:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:ui-core:3.6.0 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-java:3.6.0 (*)
|         |    +--- com.facebook.fresco:nativeimagefilters:3.6.0
|         |    |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-native:3.6.0 (*)
|         |    |    +--- com.facebook.fresco:memory-type-java:3.6.0 (*)
|         |    |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    \--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    +--- com.facebook.fresco:nativeimagetranscoder:3.6.0
|         |    |    +--- com.facebook.fresco:imagepipeline-base:3.6.0 (*)
|         |    |    +--- com.facebook.soloader:nativeloader:0.11.0 -> 0.12.1
|         |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|         |    |    \--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         +--- com.facebook.fresco:imagepipeline-okhttp3:3.6.0
|         |    +--- com.facebook.fresco:fbcore:3.6.0 (*)
|         |    +--- com.facebook.fresco:imagepipeline:3.6.0 (*)
|         |    +--- com.facebook.fresco:imagepipeline-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-ashmem:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-native:3.6.0 (*)
|         |    +--- com.facebook.fresco:memory-type-java:3.6.0 (*)
|         |    +--- com.facebook.fresco:middleware:3.6.0 (*)
|         |    +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.2
|         |    |    +--- com.squareup.okio:okio:2.8.0 -> 2.9.0
|         |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|         |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.10 -> 2.0.21
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 2.0.21 (*)
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.0 -> 2.0.21 (*)
|         +--- com.facebook.fresco:middleware:3.6.0 (*)
|         +--- com.facebook.fresco:ui-common:3.6.0 (*)
|         +--- com.facebook.infer.annotation:infer-annotation:0.18.0 (*)
|         +--- com.facebook.soloader:soloader:0.12.1 (*)
|         +--- com.facebook.yoga:proguard-annotations:1.19.0
|         +--- com.google.code.findbugs:jsr305:3.0.2
|         +--- com.squareup.okhttp3:okhttp-urlconnection:4.9.2
|         |    +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.0
|         |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|         |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0
|         |              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.0 -> 2.0.21 (*)
|         +--- com.squareup.okhttp3:okhttp:4.9.2 (*)
|         +--- com.squareup.okio:okio:2.9.0 (*)
|         +--- javax.inject:javax.inject:1
|         \--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
+--- project :react-native-image-picker
|    +--- com.facebook.react:react-native:+ -> com.facebook.react:react-android:0.79.3 (*)
|    +--- androidx.core:core:1.3.1 -> 1.13.1 (*)
|    \--- androidx.exifinterface:exifinterface:1.3.3
|         \--- androidx.annotation:annotation:1.2.0 -> 1.8.1 (*)
+--- project :react-native-permissions
|    \--- com.facebook.react:react-native:+ -> com.facebook.react:react-android:0.79.3 (*)
+--- project :react-native-vision-camera
|    +--- org.jetbrains.kotlin:kotlin-stdlib:2.0.21 (*)
|    +--- com.facebook.react:react-android:+ -> 0.79.3 (*)
|    +--- androidx.camera:camera-core:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.annotation:annotation-experimental:1.4.1 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    +--- androidx.concurrent:concurrent-futures-ktx:1.1.0
|    |    |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.71 -> 2.0.21 (*)
|    |    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.3.4 -> 1.9.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- androidx.exifinterface:exifinterface:1.3.2 -> 1.3.3 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.1.0 -> 2.8.7 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata:2.1.0 -> 2.8.7 (*)
|    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.22 -> 2.0.21 (*)
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-camera2:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.1.0 (*)
|    |    +--- androidx.concurrent:concurrent-futures-ktx:1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.1.0 -> 2.8.7 (*)
|    |    +--- androidx.tracing:tracing-ktx:1.2.0
|    |    |    +--- androidx.tracing:tracing:1.2.0 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 -> 2.0.21 (*)
|    |    |    \--- androidx.tracing:tracing:1.2.0 (c)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3 -> 1.9.0 (*)
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-video:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.13.1 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-view:1.5.0-alpha03
|    |    +--- androidx.annotation:annotation:1.8.1 (*)
|    |    +--- androidx.annotation:annotation-experimental:1.4.1 (*)
|    |    +--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (*)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.3.2 -> 1.13.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.0.0 -> 2.8.7 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-extensions:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    +--- androidx.camera:camera-extensions:1.5.0-alpha03
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    |    +--- com.google.guava:listenablefuture:1.0
|    |    +--- androidx.camera:camera-camera2:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-core:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-lifecycle:1.5.0-alpha03 (c)
|    |    +--- androidx.camera:camera-video:1.5.0-alpha03 (c)
|    |    \--- androidx.camera:camera-view:1.5.0-alpha03 (c)
|    +--- androidx.lifecycle:lifecycle-common:2.8.7 (*)
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0 (*)
|    +--- com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1
|    |    +--- com.google.android.datatransport:transport-api:2.2.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    +--- com.google.android.datatransport:transport-backend-cct:2.3.3
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |    +--- com.google.android.datatransport:transport-runtime:2.2.5 -> 2.2.6
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    |    +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |    |    \--- javax.inject:javax.inject:1
|    |    |    +--- com.google.firebase:firebase-encoders:16.1.0
|    |    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    \--- com.google.firebase:firebase-encoders-json:17.1.0
|    |    |         +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |         \--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    +--- com.google.android.datatransport:transport-runtime:2.2.6 (*)
|    |    +--- com.google.android.gms:play-services-base:18.5.0
|    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    |    |    +--- com.google.android.gms:play-services-basement:18.4.0
|    |    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    |    +--- androidx.core:core:1.2.0 -> 1.13.1 (*)
|    |    |    |    \--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|    |    |    \--- com.google.android.gms:play-services-tasks:18.2.0
|    |    |         \--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-tasks:18.2.0 (*)
|    |    +--- com.google.android.odml:image:1.0.0-beta1
|    |    +--- com.google.firebase:firebase-components:16.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.8.1 (*)
|    |    |    \--- com.google.firebase:firebase-annotations:16.0.0
|    |    +--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    +--- com.google.firebase:firebase-encoders-json:17.1.0 (*)
|    |    +--- com.google.mlkit:barcode-scanning-common:17.0.0
|    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    |    \--- com.google.mlkit:vision-common:17.0.0 -> 17.3.0
|    |    |         +--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3 (*)
|    |    |         +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |         +--- com.google.android.datatransport:transport-backend-cct:2.3.3 (*)
|    |    |         +--- com.google.android.datatransport:transport-runtime:2.2.6 (*)
|    |    |         +--- com.google.android.gms:play-services-base:18.1.0 -> 18.5.0 (*)
|    |    |         +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.4.0 (*)
|    |    |         +--- com.google.android.gms:play-services-tasks:18.0.2 -> 18.2.0 (*)
|    |    |         +--- com.google.android.odml:image:1.0.0-beta1
|    |    |         +--- com.google.firebase:firebase-components:16.1.0 (*)
|    |    |         +--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    |         +--- com.google.firebase:firebase-encoders-json:17.1.0 (*)
|    |    |         \--- com.google.mlkit:common:18.6.0 -> 18.11.0
|    |    |              +--- androidx.appcompat:appcompat:1.6.1 (*)
|    |    |              +--- androidx.core:core:1.0.0 -> 1.13.1 (*)
|    |    |              +--- com.google.android.datatransport:transport-api:2.2.1 (*)
|    |    |              +--- com.google.android.datatransport:transport-backend-cct:2.3.3 (*)
|    |    |              +--- com.google.android.datatransport:transport-runtime:2.2.6 (*)
|    |    |              +--- com.google.android.gms:play-services-base:18.5.0 (*)
|    |    |              +--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    |              +--- com.google.android.gms:play-services-tasks:18.2.0 (*)
|    |    |              +--- com.google.firebase:firebase-components:16.1.0 (*)
|    |    |              +--- com.google.firebase:firebase-encoders:16.1.0 (*)
|    |    |              \--- com.google.firebase:firebase-encoders-json:17.1.0 (*)
|    |    +--- com.google.mlkit:common:18.11.0 (*)
|    |    +--- com.google.mlkit:vision-common:17.3.0 (*)
|    |    \--- com.google.mlkit:vision-interfaces:16.3.0
|    |         +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.4.0 (*)
|    |         \--- com.google.android.gms:play-services-tasks:18.0.2 -> 18.2.0 (*)
|    \--- project :react-native-worklets-core
|         +--- com.facebook.react:react-android -> 0.79.3 (*)
|         \--- com.facebook.react:hermes-android -> 0.79.3
|              +--- com.facebook.fbjni:fbjni:0.7.0 (*)
|              +--- com.facebook.yoga:proguard-annotations:1.19.0
|              \--- androidx.annotation:annotation:1.6.0 -> 1.8.1 (*)
+--- project :react-native-worklets-core (*)
+--- com.facebook.react:react-android -> 0.79.3 (*)
\--- com.facebook.react:hermes-android -> 0.79.3 (*)

releaseUnitTestRuntimeOnly (n)
No dependencies

releaseWearApp - Link to a wear app to embed for object 'release'. (n)
No dependencies

releaseWearBundling - Resolved Configuration for wear app bundling for variant: release
No dependencies

runtimeOnly - Runtime only dependencies for 'main' sources. (n)
No dependencies

testAnnotationProcessor - Classpath for the annotation processor for 'test'. (n)
No dependencies

testApi (n)
No dependencies

testApiDependenciesMetadata
No dependencies

testCompileOnly - Compile only dependencies for 'test' sources. (n)
No dependencies

testCompileOnlyDependenciesMetadata
No dependencies

testDebugAnnotationProcessor - Classpath for the annotation processor for 'testDebug'. (n)
No dependencies

testDebugApi (n)
No dependencies

testDebugApiDependenciesMetadata
No dependencies

testDebugCompileOnly - Compile only dependencies for 'testDebug' sources. (n)
No dependencies

testDebugCompileOnlyDependenciesMetadata
No dependencies

testDebugImplementation - Implementation only dependencies for 'testDebug' sources. (n)
No dependencies

testDebugImplementationDependenciesMetadata
No dependencies

testDebugIntransitiveDependenciesMetadata
No dependencies

testDebugRuntimeOnly - Runtime only dependencies for 'testDebug' sources. (n)
No dependencies

testDebugWearApp - Link to a wear app to embed for object 'testDebug'. (n)
No dependencies

testFixturesAnnotationProcessor - Classpath for the annotation processor for 'testFixtures'. (n)
No dependencies

testFixturesApi - API dependencies for 'testFixtures' sources. (n)
No dependencies

testFixturesApiDependenciesMetadata
No dependencies

testFixturesCompileOnly - Compile only dependencies for 'testFixtures' sources. (n)
No dependencies

testFixturesCompileOnlyApi - Compile only API dependencies for 'testFixtures' sources. (n)
No dependencies

testFixturesCompileOnlyDependenciesMetadata
No dependencies

testFixturesDebugAnnotationProcessor - Classpath for the annotation processor for 'testFixturesDebug'. (n)
No dependencies

testFixturesDebugApi - API dependencies for 'testFixturesDebug' sources. (n)
No dependencies

testFixturesDebugApiDependenciesMetadata
No dependencies

testFixturesDebugCompileOnly - Compile only dependencies for 'testFixturesDebug' sources. (n)
No dependencies

testFixturesDebugCompileOnlyApi - Compile only API dependencies for 'testFixturesDebug' sources. (n)
No dependencies

testFixturesDebugCompileOnlyDependenciesMetadata
No dependencies

testFixturesDebugImplementation - Implementation only dependencies for 'testFixturesDebug' sources. (n)
No dependencies

testFixturesDebugImplementationDependenciesMetadata
No dependencies

testFixturesDebugIntransitiveDependenciesMetadata
No dependencies

testFixturesDebugRuntimeOnly - Runtime only dependencies for 'testFixturesDebug' sources. (n)
No dependencies

testFixturesDebugWearApp - Link to a wear app to embed for object 'testFixturesDebug'. (n)
No dependencies

testFixturesImplementation - Implementation only dependencies for 'testFixtures' sources. (n)
No dependencies

testFixturesImplementationDependenciesMetadata
No dependencies

testFixturesIntransitiveDependenciesMetadata
No dependencies

testFixturesReleaseAnnotationProcessor - Classpath for the annotation processor for 'testFixturesRelease'. (n)
No dependencies

testFixturesReleaseApi - API dependencies for 'testFixturesRelease' sources. (n)
No dependencies

testFixturesReleaseApiDependenciesMetadata
No dependencies

testFixturesReleaseCompileOnly - Compile only dependencies for 'testFixturesRelease' sources. (n)
No dependencies

testFixturesReleaseCompileOnlyApi - Compile only API dependencies for 'testFixturesRelease' sources. (n)
No dependencies

testFixturesReleaseCompileOnlyDependenciesMetadata
No dependencies

testFixturesReleaseImplementation - Implementation only dependencies for 'testFixturesRelease' sources. (n)
No dependencies

testFixturesReleaseImplementationDependenciesMetadata
No dependencies

testFixturesReleaseIntransitiveDependenciesMetadata
No dependencies

testFixturesReleaseRuntimeOnly - Runtime only dependencies for 'testFixturesRelease' sources. (n)
No dependencies

testFixturesReleaseWearApp - Link to a wear app to embed for object 'testFixturesRelease'. (n)
No dependencies

testFixturesRuntimeOnly - Runtime only dependencies for 'testFixtures' sources. (n)
No dependencies

testFixturesWearApp - Link to a wear app to embed for object 'testFixtures'. (n)
No dependencies

testImplementation - Implementation only dependencies for 'test' sources. (n)
No dependencies

testImplementationDependenciesMetadata
No dependencies

testIntransitiveDependenciesMetadata
No dependencies

testReleaseAnnotationProcessor - Classpath for the annotation processor for 'testRelease'. (n)
No dependencies

testReleaseApi (n)
No dependencies

testReleaseApiDependenciesMetadata
No dependencies

testReleaseCompileOnly - Compile only dependencies for 'testRelease' sources. (n)
No dependencies

testReleaseCompileOnlyDependenciesMetadata
No dependencies

testReleaseImplementation - Implementation only dependencies for 'testRelease' sources. (n)
No dependencies

testReleaseImplementationDependenciesMetadata
No dependencies

testReleaseIntransitiveDependenciesMetadata
No dependencies

testReleaseRuntimeOnly - Runtime only dependencies for 'testRelease' sources. (n)
No dependencies

testReleaseWearApp - Link to a wear app to embed for object 'testRelease'. (n)
No dependencies

testRuntimeOnly - Runtime only dependencies for 'test' sources. (n)
No dependencies

testWearApp - Link to a wear app to embed for object 'test'. (n)
No dependencies

wearApp - Link to a wear app to embed for object 'main'. (n)
No dependencies

(c) - A dependency constraint, not a dependency. The dependency affected by the constraint occurs elsewhere in the tree.
(*) - Indicates repeated occurrences of a transitive dependency subtree. Gradle expands transitive dependency subtrees only once per project; repeat occurrences only display the root of the subtree, followed by this annotation.

(n) - A dependency or dependency configuration that cannot be resolved.

A web-based, searchable dependency report is available by adding the --scan option.

[Incubating] Problems report is available at: file:///Users/<USER>/Documents/project/gongzhimall/mobile/android/build/reports/problems/problems-report.html

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.13/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD SUCCESSFUL in 1m 28s
11 actionable tasks: 1 executed, 10 up-to-date
