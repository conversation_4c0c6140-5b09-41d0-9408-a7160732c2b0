pluginManagement { includeBuild("../node_modules/@react-native/gradle-plugin") }
plugins { id("com.facebook.react.settings") }
extensions.configure(com.facebook.react.ReactSettingsExtension){ ex ->
    ex.autolinkLibrariesFromCommand(["/opt/homebrew/bin/npx", "react-native", "config", "--platform", "android"])
}
rootProject.name = 'GongZhiMallMobile'
include ':app'
includeBuild('../node_modules/@react-native/gradle-plugin')

include ':react-native-worklets-core'
project(':react-native-worklets-core').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-worklets-core/android')
