# 公职猫移动端 (GongZhiMall Mobile)

一款专为体制内工作人员设计的智能日程管理和文档助手应用。

## 📱 项目概述

公职猫移动端是基于React Native开发的跨平台应用，采用"本地为主，云端辅助"的架构理念，确保用户隐私数据100%本地存储，通过规则引擎和轻量级AI实现离线智能功能。

### 核心功能

- **📅 智能日程管理**：支持个人日程和领导日程的统一管理
- **📄 文档智能识别**：OCR识别、语音转文字、智能分类
- **🔒 隐私保护**：本地加密存储，U盘级私密保护
- **🤖 AI助手**：秘书般的智能理解和贴心服务
- **📱 移动优先**：专为移动设备优化的交互体验

## 🛠 技术栈

### 核心框架

- **React Native**: 0.79+ (跨平台移动开发)
- **TypeScript**: 类型安全的JavaScript超集
- **React Navigation**: 原生导航体验

### UI/UX技术

- **react-native-vector-icons**: 矢量图标库 (主要使用Ionicons)
- **react-native-haptic-feedback**: 触觉反馈系统
- **react-native-safe-area-context**: 安全区域处理
- **react-native-gesture-handler**: 手势交互

### 数据处理

- **date-fns**: 日期处理库 (支持中文本地化)
- **SQLite**: 本地数据库存储
- **better-sqlite3-multiple-ciphers**: 数据库加密

### 开发工具

- **Metro**: JavaScript打包工具
- **Jest**: 单元测试框架
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化

## 🚀 快速开始

### 环境要求

确保已完成 [React Native环境配置](https://reactnative.dev/docs/set-up-your-environment)。

**必需环境**:

- Node.js 18+
- iOS: Xcode 14+ (macOS)
- Android: Android Studio + Android SDK

### 安装依赖

```bash
# 安装Node.js依赖
yarn install

# iOS依赖安装 (仅首次或更新原生依赖时)
cd ios && pod install && cd ..
```

### 运行应用

#### 启动Metro服务器

```bash
yarn start
```

#### 运行iOS应用

```bash
yarn ios
```

#### 运行Android应用

```bash
yarn android
```

### 重新加载应用

- **iOS**: 在模拟器中按 `Cmd ⌘ + R`
- **Android**: 按 `R` 键两次或 `Ctrl + M` (Windows/Linux) / `Cmd ⌘ + M` (macOS) 打开开发菜单

## 📁 项目结构

```
mobile/
├── src/                          # 源代码目录
│   ├── screens/                  # 页面组件
│   │   ├── HomeScreen.tsx        # 首页
│   │   ├── ScheduleScreen.tsx    # 日程管理主页
│   │   ├── DayDetailScreen.tsx   # 日程详情页
│   │   ├── AddScheduleScreen.tsx # 添加日程页
│   │   └── FullScreenMonthScreen.tsx # 全屏月历
│   ├── components/               # 通用组件
│   ├── utils/                    # 工具函数
│   │   └── calendar.ts           # 日历相关工具
│   ├── types/                    # TypeScript类型定义
│   └── navigation/               # 导航配置
├── ios/                          # iOS原生代码
├── android/                      # Android原生代码
├── docs/                         # 项目文档
└── __tests__/                    # 测试文件
```

## 🎨 设计系统

项目采用统一的设计系统，详见 [`docs/03_design/设计系统规范.md`](../docs/03_design/设计系统规范.md)。

### 主题特色

- **亮色主题**: 专业清晰的浅色设计
- **橙色品牌**: #FF8C00 温暖的主品牌色
- **触觉反馈**: 三级触觉反馈体系 (Light/Medium/Heavy)
- **响应式设计**: 适配不同屏幕尺寸的动态布局

### 核心组件

- **半日色块**: 通过颜色直观显示日程角色 (主办/协办/参与)
- **响应式网格**: 8格自适应网格布局 (1月视图 + 7日视图)
- **迷你日历**: 完整月份显示的紧凑日历组件
- **触觉交互**: 全面的触觉反馈增强用户体验

## ⚙️ 配置说明

### iOS配置

#### 字体配置

项目使用 `react-native-vector-icons`，需要在 `ios/GongZhiMallMobile/Info.plist` 中配置字体：

```xml
<key>UIAppFonts</key>
<array>
    <string>AntDesign.ttf</string>
    <string>Entypo.ttf</string>
    <string>EvilIcons.ttf</string>
    <string>Feather.ttf</string>
    <string>FontAwesome.ttf</string>
    <string>Foundation.ttf</string>
    <string>Ionicons.ttf</string>
    <string>MaterialIcons.ttf</string>
    <string>MaterialCommunityIcons.ttf</string>
    <string>SimpleLineIcons.ttf</string>
    <string>Octicons.ttf</string>
    <string>Zocial.ttf</string>
    <string>FontAwesome5_Brands.ttf</string>
    <string>FontAwesome5_Regular.ttf</string>
    <string>FontAwesome5_Solid.ttf</string>
</array>
```

#### 触觉反馈

iOS原生支持丰富的触觉反馈，通过 `react-native-haptic-feedback` 实现：

- `impactLight`: 轻微反馈 (日期点击、标签切换)
- `impactMedium`: 中等反馈 (手势滑动、重要按钮)
- `impactHeavy`: 强烈反馈 (长按操作、重要确认)

### Android配置

Android平台自动处理大部分配置，但需要注意：

- 触觉反馈使用Android Vibration API
- 字体文件自动链接到APK中
- 权限配置在 `android/app/src/main/AndroidManifest.xml`

## 🧪 测试

```bash
# 运行单元测试
yarn test

# 运行测试并监听文件变化
yarn test --watch

# 生成测试覆盖率报告
yarn test --coverage
```

## 📦 构建发布

### iOS构建

```bash
# 在Xcode中选择Generic iOS Device
# Product -> Archive -> Distribute App
```

### Android构建

```bash
# 生成发布APK
cd android && ./gradlew assembleRelease

# 生成发布AAB (推荐用于Play Store)
cd android && ./gradlew bundleRelease
```

## 🔧 开发工具

### 调试工具

- **Flipper**: React Native官方调试工具
- **React DevTools**: 组件层级调试
- **Redux DevTools**: 状态管理调试 (如果使用Redux)

### 代码质量

```bash
# 运行ESLint检查
yarn lint

# 自动修复ESLint问题
yarn lint --fix

# 运行Prettier格式化
yarn format
```

## 🐛 故障排除

### 常见问题

#### iOS图标不显示

确保已正确配置Info.plist中的字体列表，并运行：

```bash
cd ios && pod install && cd ..
yarn ios
```

#### Android构建失败

清理构建缓存：

```bash
cd android && ./gradlew clean && cd ..
yarn android
```

#### Metro缓存问题

清理Metro缓存：

```bash
yarn start --reset-cache
```

### 更多帮助

如遇到其他问题，请参考：

- [React Native故障排除指南](https://reactnative.dev/docs/troubleshooting)
- [项目技术设计文档](../docs/04_architecture/技术设计文档.md)

## 📚 相关文档

- [设计系统规范](../docs/03_design/设计系统规范.md)
- [技术设计文档](../docs/04_architecture/技术设计文档.md)
- [产品需求文档](../docs/02_requirements/)
- [项目总体文档](../docs/README.md)

## 🤝 开发规范

### 代码风格

- 使用TypeScript进行类型安全开发
- 遵循ESLint和Prettier配置
- 组件使用函数式组件 + Hooks
- 状态管理优先使用React内置状态

### 提交规范

```bash
# 功能开发
git commit -m "feat: 添加日程管理响应式网格布局"

# 问题修复
git commit -m "fix: 修复iOS图标显示问题"

# 文档更新
git commit -m "docs: 更新移动端README文档"

# 样式调整
git commit -m "style: 优化日程卡片间距和颜色"
```

### 性能优化

- 使用React.memo优化组件重渲染
- 合理使用useMemo和useCallback
- 图片资源使用WebP格式 (Android) 和优化的PNG (iOS)
- 避免在render中创建新对象和函数

---

## 📄 许可证

本项目为公职猫团队内部开发项目，保留所有权利。

## 📞 联系我们

如有技术问题或建议，请联系开发团队。

---

**最后更新**: 2024-12-13
**版本**: 1.0.0
**React Native版本**: 0.79+
