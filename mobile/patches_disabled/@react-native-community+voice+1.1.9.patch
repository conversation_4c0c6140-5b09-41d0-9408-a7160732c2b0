diff --git a/node_modules/@react-native-community/voice/android/src/main/java/com/wenkesj/voice/VoiceModule.java b/node_modules/@react-native-community/voice/android/src/main/java/com/wenkesj/voice/VoiceModule.java
index d2d701d..b64f083 100644
--- a/node_modules/@react-native-community/voice/android/src/main/java/com/wenkesj/voice/VoiceModule.java
+++ b/node_modules/@react-native-community/voice/android/src/main/java/com/wenkesj/voice/VoiceModule.java
@@ -397,4 +397,14 @@ public class VoiceModule extends ReactContextBaseJavaModule implements Recogniti
     }
     return message;
   }
+
+  @ReactMethod
+  public void addListener(String eventName) {
+    // Keep: Required for RN built in Event Emitter Calls.
+  }
+
+  @ReactMethod
+  public void removeListeners(Integer count) {
+    // Keep: Required for RN built in Event Emitter Calls.
+  }
 }
