{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"jsx": "react", "lib": ["es6"], "moduleResolution": "bundler", "noEmit": true, "strict": true, "strictPropertyInitialization": false, "target": "esnext", "experimentalDecorators": true, "emitDecoratorMetadata": true, "typeRoots": ["./node_modules/@types", "./src/types"], "types": ["node", "jest"]}, "include": ["src/**/*", "src/types/declarations.d.ts"], "exclude": []}