# RNVisionOCR 原生模块 Xcode 集成步骤

## 🎯 目标
将 RNVisionOCR.h 和 RNVisionOCR.m 文件添加到 Xcode 项目中，解决"原生模块未找到"错误。

## 📋 详细步骤

### 1. 打开 Xcode 项目
```bash
cd mobile/ios
open GongZhiMallMobile.xcworkspace
```

### 2. 在 Xcode 中添加文件
1. **右键点击** 左侧项目导航器中的 `GongZhiMallMobile` 文件夹（蓝色图标）
2. 选择 **"Add Files to 'GongZhiMallMobile'"**
3. 导航到 `GongZhiMallMobile` 文件夹
4. **选择以下两个文件**：
   - `RNVisionOCR.h`
   - `RNVisionOCR.m`
5. 确保勾选：
   - ✅ **"Copy items if needed"**
   - ✅ **"Create groups"** (不是 "Create folder references")
   - ✅ **"Add to target: GongZhiMallMobile"**
6. 点击 **"Add"**

### 3. 验证文件已添加
1. 在项目导航器中确认可以看到：
   - `RNVisionOCR.h` (头文件图标)
   - `RNVisionOCR.m` (实现文件图标)
2. 点击 `RNVisionOCR.m` 文件
3. 在右侧 **"File Inspector"** 中确认：
   - ✅ **"Target Membership"** 中 `GongZhiMallMobile` 已勾选

### 4. 检查编译设置
1. 点击项目根节点 `GongZhiMallMobile` (最顶层)
2. 选择 **"Build Phases"** 标签
3. 展开 **"Compile Sources"**
4. 确认 `RNVisionOCR.m` 在列表中

### 5. 添加 Vision 框架依赖
1. 在 **"Build Phases"** 中找到 **"Link Binary With Libraries"**
2. 点击 **"+"** 按钮
3. 搜索并添加 **"Vision.framework"**
4. 确保 **"Status"** 为 **"Required"**

### 6. 清理并重新编译
```bash
# 在终端中执行
cd mobile/ios
rm -rf build/
cd ..
npx react-native run-ios --device
```

## 🔍 验证步骤

### 编译成功后，在应用中测试：
1. 打开 Vision OCR 测试界面
2. 点击 **"快速验证"** 按钮
3. 应该看到成功的验证结果，而不是"原生模块未找到"错误

## 🚨 常见问题

### 问题1：文件添加后仍然找不到模块
**解决方案**：
- 确保文件的 Target Membership 正确
- 清理项目：Product → Clean Build Folder
- 重新编译

### 问题2：编译错误
**解决方案**：
- 检查 Vision.framework 是否正确添加
- 确保 iOS 部署目标 ≥ 13.0

### 问题3：模块导入错误
**解决方案**：
- 确保 RNVisionOCR.m 中的 `RCT_EXPORT_MODULE();` 存在
- 检查头文件中的接口声明

## 📱 测试验证

成功集成后，快速验证应该返回类似结果：
```json
{
  "engine": "ios_vision",
  "available": true,
  "supportedLanguages": ["zh-Hans", "zh-Hant", "en", "ja", "ko"],
  "version": "iOS 13.0+"
}
```

## 🎉 完成标志

- ✅ 文件在 Xcode 项目中可见
- ✅ 编译无错误
- ✅ 快速验证返回成功结果
- ✅ 不再出现"原生模块未找到"错误 