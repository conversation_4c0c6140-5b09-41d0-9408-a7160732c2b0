#!/bin/bash

# iOS Assets 构建时打包脚本
# 类似Android机制，在构建时将 mobile/src/assets 打包到 iOS app bundle
# 这样iOS和Android保持一致的assets处理方式

set -e

echo "🚀 开始构建时打包 Assets 文件到 iOS Bundle..."

# 定义路径
PROJECT_ROOT="$SRCROOT/../.."
SRC_ASSETS_DIR="$PROJECT_ROOT/mobile/src/assets"
DEST_ASSETS_DIR="$BUILT_PRODUCTS_DIR/$PRODUCT_NAME.app/assets"

# 调试信息
echo "🔍 调试信息:"
echo "  PROJECT_ROOT: $PROJECT_ROOT"
echo "  SRC_ASSETS_DIR: $SRC_ASSETS_DIR"
echo "  DEST_ASSETS_DIR: $DEST_ASSETS_DIR"
echo "  SRCROOT: $SRCROOT"
echo "  BUILT_PRODUCTS_DIR: $BUILT_PRODUCTS_DIR"
echo "  PRODUCT_NAME: $PRODUCT_NAME"

# 检查源目录是否存在
if [ ! -d "$SRC_ASSETS_DIR" ]; then
    echo "❌ 错误: 源 assets 目录不存在: $SRC_ASSETS_DIR"
    echo "🔍 检查目录结构:"
    ls -la "$PROJECT_ROOT/mobile/src/" 2>/dev/null || echo "  mobile/src 目录不存在"
    exit 1
fi

# 创建目标目录
echo "📁 创建目标目录: $DEST_ASSETS_DIR"
mkdir -p "$DEST_ASSETS_DIR"

# 复制HTML文件
echo "📋 复制HTML文件..."
find "$SRC_ASSETS_DIR" -maxdepth 1 -name "*.html" -exec cp {} "$DEST_ASSETS_DIR/" \;

# 复制libs目录 - 智能选择必要文件
echo "📋 复制libs目录..."
if [ -d "$SRC_ASSETS_DIR/libs" ]; then
    mkdir -p "$DEST_ASSETS_DIR/libs"

    # 定义必要的库文件（基于实际存在的文件）
    REQUIRED_LIBS=(
        "jszip.min.js"
        "opentype.min.js"
        "x2js.js"
        "easyjbig2.js"
        "easyofd.js"
        "docx-preview.min.js"
        "d3.min.js"
    )

    # 只复制必要的库文件
    for lib in "${REQUIRED_LIBS[@]}"; do
        if [ -f "$SRC_ASSETS_DIR/libs/$lib" ]; then
            cp "$SRC_ASSETS_DIR/libs/$lib" "$DEST_ASSETS_DIR/libs/"
            echo "  ✅ 复制必要库: $lib"
        else
            echo "  ⚠️  缺少库文件: $lib"
        fi
    done

    # 排除不再需要的PPT相关库
    EXCLUDED_LIBS=(
        "jquery-1.11.3.min.js"
        "jszip-utils.min.js"
        "nv.d3.min.js"
        "divs2slides.js"
        "pptxjs.js"
    )

    echo "  📝 已排除PPT相关库文件，减少安装包大小"
else
    echo "  ⚠️  libs目录不存在，跳过库文件复制"
fi

# 清理不必要的文件
echo "🧹 清理不必要的文件..."
find "$DEST_ASSETS_DIR" -name ".DS_Store" -delete 2>/dev/null || true
find "$DEST_ASSETS_DIR" -name "*.tmp" -delete 2>/dev/null || true
find "$DEST_ASSETS_DIR" -name "Thumbs.db" -delete 2>/dev/null || true

# 文件大小监控和优化建议
echo "📊 检查复制文件大小..."
if [ -d "$DEST_ASSETS_DIR" ]; then
    TOTAL_SIZE=$(find "$DEST_ASSETS_DIR" -type f -exec stat -f%z {} \; | awk '{sum+=$1} END {print sum}')
    TOTAL_SIZE_MB=$((TOTAL_SIZE / 1024 / 1024))

    # 计算节省的空间（相比之前的PPT库）
    SAVED_SIZE_MB=1  # 大约节省了1MB的PPT相关库
    echo "💾 相比之前版本节省了约 ${SAVED_SIZE_MB}MB 空间"

    if [ $TOTAL_SIZE_MB -gt 5 ]; then
        echo "⚠️  警告: Assets文件总大小 ${TOTAL_SIZE_MB}MB 超过5MB，建议检查是否有不必要的文件"

        # 显示最大的文件
        echo "📋 最大的文件:"
        find "$DEST_ASSETS_DIR" -type f -exec stat -f"%z %N" {} \; | sort -nr | head -5 | while read size file; do
            size_kb=$((size / 1024))
            echo "  📄 $(basename "$file"): ${size_kb}KB"
        done
    else
        echo "✅ Assets文件大小合理: ${TOTAL_SIZE_MB}MB"
    fi

    # 列出复制的文件
    echo "✅ 复制完成! 已复制的文件:"
    find "$DEST_ASSETS_DIR" -type f | while read file; do
        SIZE=$(stat -f%z "$file")
        SIZE_KB=$((SIZE / 1024))
        echo "  📄 $(basename "$file") (${SIZE_KB}KB)"
    done

    echo "🎉 Assets 复制完成! 总大小: ${TOTAL_SIZE_MB}MB"
else
    echo "❌ 错误: 目标目录创建失败"
    exit 1
fi
