require_relative '../node_modules/react-native/scripts/react_native_pods'
platform :ios, '15.1'

# Enable Hermes JS engine for better performance, required for Frame Processors
ENV['USE_HERMES'] = '1'
# Enable VisionCamera Frame Processors
ENV['VC_ENABLE_FRAME_PROCESSORS'] = '1'

prepare_react_native_project!

# react-native-permissions configuration
def setup_permissions(permissions)
  # ... (existing permission setup logic can remain if needed)
end

target 'GongZhiMallMobile' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  # WatermelonDB dependency
  pod 'simdjson', path: '../node_modules/@nozbe/simdjson', modular_headers: true
  
  post_install do |installer|
    react_native_post_install(installer)
    __apply_Xcode_12_5_M1_post_install_workaround(installer)
  end
end
