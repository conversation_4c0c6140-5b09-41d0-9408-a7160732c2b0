/**
 * 公职猫移动端应用
 * @format
 */

import 'react-native-gesture-handler';
import React, { useState, useEffect, useRef } from 'react';
import { BackHandler } from 'react-native';
import { NavigationContainer, useNavigation, NavigationContainerRef } from '@react-navigation/native';
import { DataConsistencyService } from './src/services/DataConsistencyService';
// import { DataExportService } from './src/services/DataExportService'; // 暂时禁用
import { userDeviceManager } from './src/utils/UserDeviceManager';
import { DeviceRegistrationService } from './src/services/DeviceRegistrationService';
import { getWeChatBindingService, getWeChatSyncService, getWeChatPushService } from './src/services/WeChatServiceRegistry';
import PushTokenSyncUtil from './src/utils/pushTokenSync';
import {
  createNativeStackNavigator,
  NativeStackNavigationProp,
} from '@react-navigation/native-stack';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import SplashScreen from './src/components/SplashScreen';
import HomeScreen from './src/screens/HomeScreen';
import ExploreScreen from './src/screens/ExploreScreen';
import ScheduleScreen from './src/screens/ScheduleScreen';
import FullScreenMonthScreen from './src/screens/FullScreenMonthScreen';
import DayDetailScreen from './src/screens/DayDetailScreen';
import AllStickyNotesScreen from './src/screens/AllStickyNotesScreen';
import { StickyNote } from './src/screens/AllStickyNotesScreen';
import TaskCreationScreen from './src/screens/TaskCreationScreen';
import CameraScreen from './src/screens/CameraScreen';
import CaptureReviewScreen from './src/screens/CaptureReviewScreen';
import { useCameraStore } from './src/stores/cameraStore';
import CaptureActionSheet from './src/components/InputPanel/CaptureActionSheet';
import CreationScreen from './src/screens/CreationScreen';
import CustomImagePicker from './src/components/CustomImagePicker';
import { useGlobalImagePickerStore } from './src/stores/globalImagePickerStore';
import ChatScreen from './src/screens/ChatScreen';
import WebViewScreen from './src/screens/WebViewScreen';
import FilePreviewScreen from './src/screens/FilePreviewScreen';
import SimpleWeChatBindingScreen from './src/screens/SimpleWeChatBindingScreen';
import PrivacyPolicyDetailScreen from './src/screens/PrivacyPolicyDetailScreen';
import { PrivacyPolicyModal } from './src/components/PrivacyPolicyModal';
import { PrivacyPolicyManager } from './src/utils/privacyPolicyManager';

// --- Global Data and Types ---
export type Role = 'host' | 'assist' | 'attend' | null;

export type ScheduleItem = {
  id: string;
  date: string; // YYYY-MM-DD
  time: string;
  title: string;
  role: Role;
};

export type ScheduleData = { [key: string]: ScheduleItem[] };

const INITIAL_SCHEDULE_DATA: ScheduleData = {
  '2025-06-23': [
    { id: '1', date: '2025-06-23', time: '09:00 - 10:00', title: '全区数字化改革推进会', role: 'host' },
    { id: '2', date: '2025-06-23', time: '10:30 - 12:00', title: '研究XX公司上市材料', role: 'assist' },
  ],
  '2025-06-24': [
    { id: '3', date: '2025-06-24', time: '14:00 - 15:30', title: '接待省厅调研组一行', role: 'attend' },
  ],
  '2025-06-25': [
      { id: '4', date: '2025-06-25', time: '16:00 - 16:30', title: '与晓阳同志谈心谈话', role: null },
  ],
};

export type RootStackParamList = {
  Splash: undefined;
  Home: undefined;
  Explore: undefined;
  Schedule: undefined;
  FullScreenMonth: undefined;
  DayDetail: { date: string };
  AllStickyNotes: { notes: StickyNote[] };
  TaskCreation: { sessionId: string };
  CameraScreen: undefined;
  CaptureReviewScreen: undefined;
  CreationScreen: { sessionId?: string; initialTab?: 'note' | 'schedule' | 'notebook'; context?: any };
  Chat: undefined;
  WebViewScreen: { url: string };
  FilePreviewScreen: { fileData: { uri: string; name: string; size: number; type: string; originalUri?: string; } };
  SimpleWeChatBinding: undefined;
  PrivacyPolicyDetail: undefined;
};

type AppNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const Stack = createNativeStackNavigator<RootStackParamList>();

// --- App Context ---
export const AppContext = React.createContext({
  scheduleData: INITIAL_SCHEDULE_DATA as ScheduleData,
  addScheduleItem: (_item: Omit<ScheduleItem, 'id'>) => {},
});

// --- Navigation Listener ---
const NavigationListener = () => {
  const navigation = useNavigation<AppNavigationProp>();
  const { navigationTarget, clearNavigationTarget } = useCameraStore();

  useEffect(() => {
    if (navigationTarget) {
      const { destination, params } = navigationTarget;
      if (destination === 'CreationScreen') {
        navigation.navigate('CreationScreen', params);
      }
      clearNavigationTarget();
    }
  }, [navigationTarget, navigation, clearNavigationTarget]);

  return null; // This component does not render anything
};

// --- App Component ---
function App(): React.ReactElement {
  const [showSplash, setShowSplash] = useState(true);
  const [scheduleData, setScheduleData] = useState<ScheduleData>(INITIAL_SCHEDULE_DATA);
  const [privacyCheckComplete, setPrivacyCheckComplete] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const navigationRef = useRef<NavigationContainerRef<RootStackParamList>>(null);

  const {
    isActionSheetVisible,
    hideActionSheet,
    handleActionSheetOption,
  } = useCameraStore();

  const {
    isVisible: isPickerVisible,
    options: pickerOptions,
    closePicker,
  } = useGlobalImagePickerStore();

  const handleSplashFinish = () => {
    // This is now handled by navigation, but we keep the logic for the splash screen's own duration
    setShowSplash(false);
  };

  // 🔒 隐私政策检查
  useEffect(() => {
    const checkPrivacyPolicy = async () => {
      try {
        const hasAgreed = await PrivacyPolicyManager.hasUserAgreedToCurrentPolicy();
        if (hasAgreed) {
          setPrivacyCheckComplete(true);
        } else {
          // 🔧 修复：如果未同意，显示隐私政策弹框
          setShowPrivacyModal(true);
        }
      } catch (error) {
        console.error('[App] 隐私政策检查失败:', error);
        // 🔧 修复：出错时显示隐私政策弹框，确保合规
        setShowPrivacyModal(true);
      }
    };

    // 在启动屏结束后检查隐私政策
    if (!showSplash) {
      checkPrivacyPolicy();
    }
  }, [showSplash]);

  // 处理隐私政策同意
  const handlePrivacyAgree = () => {
    setShowPrivacyModal(false);
    setPrivacyCheckComplete(true);
    console.log('[App] 用户已同意隐私政策');
  };

  // 处理隐私政策拒绝
  const handlePrivacyDisagree = () => {
    //直接退出
    BackHandler.exitApp();
  };

  // 处理查看完整隐私政策
  const handleViewFullPolicy = () => {
    console.log('[App] handleViewFullPolicy called');
    // 🔧 修复：临时隐藏弹框以便导航，但不改变隐私政策检查状态
    setShowPrivacyModal(false);

    // 使用setTimeout确保弹框关闭后再导航
    setTimeout(() => {
      if (navigationRef.current) {
        console.log('[App] Navigating to PrivacyPolicyDetail');
        navigationRef.current.navigate('PrivacyPolicyDetail');
      } else {
        console.log('[App] Navigation ref is null');
      }
    }, 100);
  };

  // 🔧 微信服务初始化函数
  const initializeWeChatServices = async () => {
    try {
      console.log('[App] 开始初始化微信服务...');

      // 1. 初始化JPush推送服务
      const pushService = getWeChatPushService();
      const pushInitialized = await pushService.initialize();
      if (pushInitialized) {
        console.log('[App] JPush推送服务初始化成功');
      } else {
        console.warn('[App] JPush推送服务初始化失败，但不阻止应用启动');
      }

      // 2. 检查微信绑定状态
      const bindingService = getWeChatBindingService();
      const canSync = await bindingService.canSync();

      if (canSync) {
        console.log('[App] 检测到微信已绑定，启动消息同步服务');

        // 3. 启动消息同步服务
        const syncService = getWeChatSyncService();
        const syncStarted = await syncService.startSync();

        if (syncStarted) {
          console.log('[App] 微信消息同步服务启动成功');
        } else {
          console.warn('[App] 微信消息同步服务启动失败');
        }
      } else {
        console.log('[App] 微信未绑定，跳过消息同步服务启动');
      }

    } catch (error) {
      console.error('[App] 微信服务初始化失败:', error);
      // 不阻止应用启动，只记录错误
    }
  };

  // 🔧 应用启动时的数据一致性检查和设备注册
  useEffect(() => {
    const performStartupChecks = async () => {
      try {
        console.log('[App] 开始启动时检查...');

        // 1. 数据一致性检查
        console.log('[App] 执行数据一致性检查...');
        await DataConsistencyService.performStartupCheck();
        console.log('[App] 数据一致性检查完成');

        // 2. 设备注册和UUID初始化
        console.log('[App] 初始化设备注册...');
        const deviceRegistrationService = DeviceRegistrationService.getInstance();
        const registrationResult = await deviceRegistrationService.initializeDeviceRegistration();

        if (registrationResult.success) {
          console.log('[App] 设备注册成功:', registrationResult.deviceInfo?.deviceId);

          // 获取并显示用户UUID（用于微信绑定）
          const userUuid = await userDeviceManager.getUserUuid();
          console.log('[App] 用户UUID:', userUuid);
          console.log('[App] 微信转发绑定需要此UUID');
        } else {
          console.warn('[App] 设备注册失败:', registrationResult.error);
          // 不阻止应用启动，但记录错误
        }

        // 3. 初始化微信相关服务
        console.log('[App] 初始化微信服务...');
        await initializeWeChatServices();
        console.log('[App] 微信服务初始化完成');

        // 4. 检查并同步push_token（延迟执行，确保JPush初始化完成）
        setTimeout(async () => {
          try {
            console.log('[App] 开始检查push_token同步状态...');

            const syncResult = await PushTokenSyncUtil.checkAndSyncPushToken();
            console.log('[App] push_token同步检查结果:', syncResult);

            if (!syncResult.success && syncResult.message.includes('JPush注册ID不存在')) {
              // 如果JPush注册ID不存在，尝试强制刷新
              console.log('[App] 尝试强制刷新JPush注册ID...');
              const refreshResult = await PushTokenSyncUtil.forceRefreshAndSync();
              console.log('[App] 强制刷新结果:', refreshResult);
            }
          } catch (error) {
            console.error('[App] push_token同步检查失败:', error);
          }
        }, 3000); // 3秒后执行，确保JPush有足够时间初始化

      } catch (error) {
        console.error('[App] 启动时检查失败:', error);
        // 不阻止应用启动，只记录错误
      }
    };

    // 延迟执行，避免影响应用启动速度
    const timer = setTimeout(performStartupChecks, 2000);

    return () => clearTimeout(timer);
  }, []);

  const addScheduleItem = (item: Omit<ScheduleItem, 'id'>) => {
    setScheduleData(prevData => {
      const newData = { ...prevData };
      const dateKey = item.date;
      const newId = `${dateKey}-${(newData[dateKey]?.length || 0) + 1}`;
      const newItem: ScheduleItem = { ...item, id: newId };

      if (!newData[dateKey]) {
        newData[dateKey] = [];
      }
      newData[dateKey].push(newItem);
      // Sort by time
      newData[dateKey].sort((a, b) => a.time.localeCompare(b.time));
      return newData;
    });
  };

  if (showSplash) {
    return <SplashScreen onFinish={handleSplashFinish} />;
  }

  // 如果隐私政策检查未完成，显示隐私政策弹框
  if (!privacyCheckComplete) {
    return (
      <SafeAreaProvider>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <NavigationContainer
            ref={navigationRef}
            onStateChange={(state) => {
              // 🔧 监听导航状态变化，当从隐私政策详情页返回时重新显示弹框
              const currentRoute = state?.routes[state.index];
              if (currentRoute?.name === 'Home' && !showPrivacyModal && !privacyCheckComplete) {
                console.log('[App] 从隐私政策详情页返回，重新显示弹框');
                setShowPrivacyModal(true);
              }
            }}
          >
            <Stack.Navigator
              initialRouteName="Home"
              screenOptions={{
                headerShown: false,
              }}
            >
              <Stack.Screen name="Home" component={HomeScreen} />
              <Stack.Screen name="PrivacyPolicyDetail" component={PrivacyPolicyDetailScreen} />
            </Stack.Navigator>

            {/* 🔧 修复：根据状态控制弹框显示 */}
            <PrivacyPolicyModal
              visible={showPrivacyModal}
              onAgree={handlePrivacyAgree}
              onDisagree={handlePrivacyDisagree}
              onViewFullPolicy={handleViewFullPolicy}
            />
          </NavigationContainer>
        </GestureHandlerRootView>
      </SafeAreaProvider>
    );
  }

  return (
    <SafeAreaProvider>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <AppContext.Provider value={{ scheduleData, addScheduleItem }}>
          <NavigationContainer ref={navigationRef}>
              <NavigationListener />
              <Stack.Navigator
                initialRouteName="Home"
                screenOptions={{
                  headerShown: false, // We use custom headers in each screen
                }}
              >
                <Stack.Group>
                  <Stack.Screen name="Home" component={HomeScreen} />
                  <Stack.Screen name="Explore" component={ExploreScreen} />
                  <Stack.Screen name="Schedule" component={ScheduleScreen} />
                  <Stack.Screen name="AllStickyNotes" component={AllStickyNotesScreen} />
                  <Stack.Screen name="TaskCreation" component={TaskCreationScreen} />
                  <Stack.Screen name="CameraScreen" component={CameraScreen} options={{ headerShown: false }} />
                  <Stack.Screen name="CreationScreen" component={CreationScreen} />
                  <Stack.Screen name="Chat" component={ChatScreen} />
                  <Stack.Screen name="WebViewScreen" component={WebViewScreen} />
                  <Stack.Screen name="FilePreviewScreen" component={FilePreviewScreen} />
                  <Stack.Screen name="SimpleWeChatBinding" component={SimpleWeChatBindingScreen} />
                  <Stack.Screen name="PrivacyPolicyDetail" component={PrivacyPolicyDetailScreen} />
                </Stack.Group>
                <Stack.Group screenOptions={{ presentation: 'modal' }}>
                  <Stack.Screen name="CaptureReviewScreen" component={CaptureReviewScreen} options={{ headerShown: false }} />
                  <Stack.Screen
                    name="FullScreenMonth"
                    component={FullScreenMonthScreen}
                    options={{ headerShown: false }}
                  />
                  <Stack.Screen
                    name="DayDetail"
                    component={DayDetailScreen}
                    options={{ headerShown: false }}
                  />
                </Stack.Group>
              </Stack.Navigator>

              <CaptureActionSheet
                isVisible={isActionSheetVisible}
                onClose={hideActionSheet}
                onOptionSelect={handleActionSheetOption}
              />

              <CustomImagePicker
                isVisible={isPickerVisible}
                onClose={closePicker}
                onSelect={(uris) => {
                  pickerOptions.onSelect?.(uris);
                  closePicker();
                }}
                selectionLimit={pickerOptions.selectionLimit}
                mode={pickerOptions.mode}
              />

          </NavigationContainer>
        </AppContext.Provider>
      </GestureHandlerRootView>
    </SafeAreaProvider>
  );
}

export default App;
