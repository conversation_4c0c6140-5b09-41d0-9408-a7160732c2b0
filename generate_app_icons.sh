#!/bin/bash

set -e

ICON=appicon.png

# 安卓 mipmap
convert $ICON -resize 48x48   mobile/android/app/src/main/res/mipmap-mdpi/ic_launcher.png
convert $ICON -resize 72x72   mobile/android/app/src/main/res/mipmap-hdpi/ic_launcher.png
convert $ICON -resize 96x96   mobile/android/app/src/main/res/mipmap-xhdpi/ic_launcher.png
convert $ICON -resize 144x144 mobile/android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png
convert $ICON -resize 192x192 mobile/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png

# 安卓圆形图标（如有需要，可取消注释）
convert $ICON -resize 48x48   mobile/android/app/src/main/res/mipmap-mdpi/ic_launcher_round.png
convert $ICON -resize 72x72   mobile/android/app/src/main/res/mipmap-hdpi/ic_launcher_round.png
convert $ICON -resize 96x96   mobile/android/app/src/main/res/mipmap-xhdpi/ic_launcher_round.png
convert $ICON -resize 144x144 mobile/android/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png
convert $ICON -resize 192x192 mobile/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png

# 桌面端 Electron
mkdir -p desktop/src/assets/icons
convert $ICON -resize 256x256 desktop/src/assets/icons/icon.ico
convert $ICON -resize 512x512 desktop/src/assets/icons/icon.png

# macOS icns（需在macOS下执行）
mkdir -p icon.iconset
sips -z 16 16     $ICON --out icon.iconset/icon_16x16.png
sips -z 32 32     $ICON --out icon.iconset/<EMAIL>
sips -z 32 32     $ICON --out icon.iconset/icon_32x32.png
sips -z 64 64     $ICON --out icon.iconset/<EMAIL>
sips -z 128 128   $ICON --out icon.iconset/icon_128x128.png
sips -z 256 256   $ICON --out icon.iconset/<EMAIL>
sips -z 256 256   $ICON --out icon.iconset/icon_256x256.png
sips -z 512 512   $ICON --out icon.iconset/<EMAIL>
sips -z 512 512   $ICON --out icon.iconset/icon_512x512.png
cp $ICON icon.iconset/<EMAIL>
iconutil -c icns icon.iconset
mv icon.icns desktop/src/assets/icons/icon.icns
rm -rf icon.iconset

# Web端
convert $ICON -resize 512x512 admin-web/public/logo512.png
convert $ICON -resize 192x192 admin-web/public/logo192.png
convert $ICON -resize 48x48 admin-web/public/favicon.ico

echo "✅ 全平台App图标已批量生成并替换完成！"

# 第二步：赋予执行权限
# chmod +x generate_app_icons.sh

# 第三步：执行脚本
# ./generate_app_icons.sh
