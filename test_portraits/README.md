# 证件照测试图片文件夹说明

## 📁 文件夹结构说明

### 主要分类:
- **male/**: 男性人像图片
  - child/: 男童 (6-12岁)
  - teenager/: 男性青少年 (13-18岁)  
  - young_adult/: 男性青年 (19-35岁)
  - middle_aged/: 男性中年 (36-55岁)
  - elderly/: 男性老年 (55岁以上)

- **female/**: 女性人像图片
  - child/: 女童 (6-12岁)
  - teenager/: 女性青少年 (13-18岁)
  - young_adult/: 女性青年 (19-35岁)
  - middle_aged/: 女性中年 (36-55岁)
  - elderly/: 女性老年 (55岁以上)

### 特殊情况:
- **edge_cases/**: 边缘测试用例
  - glasses/: 戴眼镜的人像
  - beard/: 有胡须的人像
  - complex_background/: 复杂背景的人像

## 📋 下载目标
每个子文件夹建议放置5-8张测试图片，总计约50-80张。

## 📝 命名规范
文件命名格式: `{类别}_{年龄段}_{序号}.jpg`
示例: `male_young_adult_01.jpg`

## 🔍 图片要求
- 分辨率: 至少1080p
- 格式: JPG或PNG
- 人像清晰，正面或微侧面
- 背景简洁优先
- 光照均匀

## 📖 详细下载指南
请参考项目根目录的 `快速下载指南.md` 文件。
