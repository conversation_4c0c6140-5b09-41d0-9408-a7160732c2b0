{"folders": [{"name": "🏠 项目根目录", "path": "."}, {"name": "📱 移动端 (React Native)", "path": "./mobile/GongZhiMallMobile"}, {"name": "🖥️ 桌面端 (Electron)", "path": "./desktop"}, {"name": "📋 任务管理", "path": "./.taskmaster"}], "settings": {"typescript.preferences.includePackageJsonAutoImports": "on", "editor.formatOnSave": true, "files.associations": {"*.tsx": "typescriptreact", "*.jsx": "javascriptreact"}, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "search.exclude": {"**/node_modules": true, "**/ios/build": true, "**/android/build": true, "**/android/.gradle": true, "**/.expo": true}, "terminal.integrated.cwd": "${workspaceFolder}", "terminal.integrated.defaultProfile.osx": "zsh", "terminal.integrated.profiles.osx": {"zsh": {"path": "/bin/zsh", "args": ["-l"]}}}, "extensions": {"recommendations": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-eslint", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-json"]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "启动移动端开发服务器", "type": "shell", "command": "cd mobile/GongZhiMallMobile && yarn start", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}}, {"label": "启动桌面端应用", "type": "shell", "command": "cd desktop && yarn dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}}, {"label": "查看任务列表", "type": "shell", "command": "npx task-master list", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}}]}}