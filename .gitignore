# ================================
# 操作系统文件
# ================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ================================
# 编辑器和IDE
# ================================
.vscode/
.idea/
*.swp
*.swo
*~

# ================================
# 敏感配置文件
# ================================
# 环境变量文件
.env
.env.local
.env.production
.env.development
backend/wechat/.env
backend/wechat/.env.production
backend/wechat/.env.local

# 华为云配置文件
mobile/android/app/agconnect-services.json
mobile/android/app/agconnect-services-prod.json

# 证书和密钥文件
*.p12
*.cer
*.pem
*.key
**/证书/**
mobile/ios/证书/

# ================================
# Node.js 依赖
# ================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# ================================
# 环境变量和敏感配置
# ================================
# 环境文件
.env
.env.local
.env.*.local
.env.development.local
.env.test.local
.env.production.local

# 但保留模板文件
!.env.example
!.env.*.example

# 密钥文件
*.pem
*.key
*.crt
*.p12
*.pfx
secrets/
config/secrets.*
private/

# ================================
# 构建输出
# ================================
dist/
build/
out/
.next/
.nuxt/

# ================================
# 日志文件
# ================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ================================
# 测试覆盖率
# ================================
coverage/
*.lcov
.nyc_output/

# ================================
# 缓存
# ================================
.cache/
.parcel-cache/
.npm/
.eslintcache
.stylelintcache

# ================================
# 数据库
# ================================
*.db
*.sqlite
*.sqlite3
database.json

# ================================
# 文件上传
# ================================
uploads/
public/uploads/
storage/
temp/
tmp/

# ================================
# React Native
# ================================
.expo/
.expo-shared/
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
web-build/

# ================================
# Electron
# ================================
dist_electron/
release/
app/dist/

# ================================
# 包管理器
# ================================
package-lock.json
yarn.lock
.pnp
.pnp.js

# ================================
# TypeScript
# ================================
*.tsbuildinfo

# ================================
# 本地开发
# ================================
.local/
.temp/
.development/

# ================================
# Docker
# ================================
.dockerignore

# ================================
# 备份文件
# ================================
*.backup
*.bak
*.tmp

# ================================
# Task Master 特定
# ================================
# Task Master 生成的临时文件
scripts/task-complexity-report.json
.task-master-cache/

# ================================
# 项目特定忽略
# ================================
# 设计文件的工作文件
界面设计效果示意/工作文件/
*.sketch
*.fig
*.psd

# 文档的临时文件
需求/临时/
技术方案/草稿/

# 公职猫项目特定
# 用户数据和测试数据
test_data/
user_uploads/
mock_data/

# Added by Claude Task Master
# Logs
logs
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/

# 权限管理和安全相关文件
.access-control/
.permissions/
.team-config/
*.pem
*.key
*.crt
secrets/
.env.outsourcing.*

# 外包团队临时文件
outsourcing-temp/
external-work/

# 访问日志
access.log
audit.log mobile/ios/.xcode.env
document-edit-mcp/
