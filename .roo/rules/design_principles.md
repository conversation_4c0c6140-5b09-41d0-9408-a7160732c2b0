---
description: 项目核心设计原则与架构思想
globs: "**/*"
alwaysApply: true
---

# 核心设计原则

## 🤖 Agent思维：API/服务即工具 (API/Service as a Tool)

**核心原则**：在设计任何可被调用的接口或服务时，必须超越为UI提供数据的传统思路，要将其视为未来AI Agent可以调用的独立、可靠的"工具"。

此原则适用于：
- **后端**：所有对外暴露的RESTful API或GraphQL端点。
- **桌面端/移动端**：内部模块间的服务接口、可供本地Agent调用的函数或方法。

### 设计要求

- **原子化 (Atomicity)**
  - `✅` 每个接口/服务应执行一个单一、明确的动作（如 `create_document`, `send_notification`, `query_status`）。
  - `❌` 避免设计一个包办所有事情的"瑞士军刀"式接口，这会增加Agent的理解和使用难度。

- **场景化 (Scenario-Based)**
  - `✅` 命名和参数应清晰反映其业务场景（如 `approve_leave_request(requestId, approverId)`）。
  - `❌` 避免使用模糊、通用的命名（如 `update_data(payload)`）。

- **自包含与无状态 (Self-Contained & Stateless)**
  - `✅` 每个调用都应包含执行其功能所需的所有信息。Agent不应该依赖于前一个调用的上下文或会话状态。
  - `❌` 避免需要按特定顺序调用才能工作的接口链。

- **明确的响应 (Unambiguous Response)**
  - `✅` 成功或失败的响应必须清晰、明确，并提供机器可读的错误码和信息，便于Agent理解并决定下一步操作。
  - `❌` 返回模糊的成功信息或笼统的错误信息。

- **安全性与幂等性 (Secure & Idempotent)**
  - `✅` 每个接口都必须经过严格的认证和授权检查。对于可能被重复调用的写操作（如创建订单），应设计为幂等的，避免重复执行产生副作用。
  - `❌` 假设调用方（Agent）总是可信的，或忽略重复调用的影响。

**最终目的**：为AI Agent构建一个稳定、可靠的"工具箱"（Toolbox），使其能够通过灵活组合这些"工具"来自动化完成复杂任务。这是我们产品核心价值和未来扩展性的基石。
