#!/bin/bash
# 敏感信息脱敏验证脚本

set -e

echo "🔍 公职猫敏感信息脱敏验证"
echo "=========================="

# 定义敏感信息列表
SENSITIVE_PATTERNS=(
    "AKID0tmEXfhYCdOxLVSCl7FAuR1Ulofs7D8q"
    "Zt2zwgLuNeiYCest78wYxBMK8jBQUMUe"
    "wechat@gongzhimall123"
    "ww857dc7bfe97b085b"
    "eQs0lmS8uUgpyDM74XvHHwWuAddq1n_qhdNsBZoB13I"
    "bd2c958b83dc679759a25664"
    "59a9af913b1d2dd85f4cbc24"
    "gongzhimallEnterprise2025"
    "zlb6q9HgvrGOYlgMR9HNmMNIHo9dJgrQVmbs7G2DJcv"
    "gongzhimall-app-2025"
    "gongzhimall_binding_secret_2025"
    "gongzhimall-file-encryption-2025"
    "6bea22ca7c5993c747ab3a58de0caf06"
    "YBvT4DCD"
    "DGDPGuTVSC8I42PxuLbXGLMQO6PeGEmEa8ttmJmuMLh"
    "3EE7EDA9105812501120F8BD93BD313AF48DF44A0AFB3EED75E400D0D9DD0D1C"
    "DQEDAFEaaiB7OHKPaIXff4LVLu75l/qAp0lOqVUAIqrwD6zWHn9VesLQ+SrrCg+iAD+WhoO7/EgURfR0COEZp5gCaufi3FXdkz7SOw=="
)

# 检查文件列表
CHECK_FILES=(
    "backend/wechat/config/env.template"
    "mobile/src/config/appConfig.ts"
    "mobile/src/config/wechatBindingConfig.ts"
    "mobile/env.example"
)

# 检查目录列表
CHECK_DIRS=(
    "backend/wechat"
    "mobile/src"
    "docs"
)

echo "📋 检查敏感信息模式: ${#SENSITIVE_PATTERNS[@]} 个"
echo "📁 检查文件: ${#CHECK_FILES[@]} 个"
echo "📂 检查目录: ${#CHECK_DIRS[@]} 个"
echo ""

# 初始化结果
FOUND_ISSUES=0
TOTAL_CHECKS=0

# 检查指定文件
echo "🔍 检查指定文件中的敏感信息..."
for file in "${CHECK_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  检查文件: $file"
        for pattern in "${SENSITIVE_PATTERNS[@]}"; do
            TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
            if grep -q "$pattern" "$file" 2>/dev/null; then
                echo "    ❌ 发现敏感信息: $pattern"
                FOUND_ISSUES=$((FOUND_ISSUES + 1))
            fi
        done
    else
        echo "  ⚠️  文件不存在: $file"
    fi
done

echo ""

# 检查目录中的所有文件
echo "🔍 检查目录中的敏感信息..."
for dir in "${CHECK_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "  检查目录: $dir"
        for pattern in "${SENSITIVE_PATTERNS[@]}"; do
            TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
            # 查找包含敏感信息的文件
            FOUND_FILES=$(find "$dir" -type f \( -name "*.js" -o -name "*.ts" -o -name "*.json" -o -name "*.md" -o -name "*.sh" \) -exec grep -l "$pattern" {} \; 2>/dev/null || true)
            if [ -n "$FOUND_FILES" ]; then
                echo "    ❌ 发现敏感信息 '$pattern' 在文件:"
                echo "$FOUND_FILES" | sed 's/^/      /'
                FOUND_ISSUES=$((FOUND_ISSUES + 1))
            fi
        done
    else
        echo "  ⚠️  目录不存在: $dir"
    fi
done

echo ""

# 检查华为云配置文件
echo "🔍 检查华为云配置文件..."
HUAWEI_CONFIG_FILE="mobile/android/app/agconnect-services.json"
if [ -f "$HUAWEI_CONFIG_FILE" ]; then
    echo "  ❌ 华为云配置文件仍存在: $HUAWEI_CONFIG_FILE"
    echo "    此文件包含敏感信息，应该被移除或移出版本控制"
    FOUND_ISSUES=$((FOUND_ISSUES + 1))
else
    echo "  ✅ 华为云配置文件已正确移除"
fi

# 检查模板文件是否存在
HUAWEI_TEMPLATE_FILE="mobile/android/app/agconnect-services.template.json"
if [ -f "$HUAWEI_TEMPLATE_FILE" ]; then
    echo "  ✅ 华为云配置模板文件存在: $HUAWEI_TEMPLATE_FILE"
    # 检查模板文件是否包含敏感信息
    for pattern in "${SENSITIVE_PATTERNS[@]}"; do
        if grep -q "$pattern" "$HUAWEI_TEMPLATE_FILE" 2>/dev/null; then
            echo "    ❌ 模板文件包含敏感信息: $pattern"
            FOUND_ISSUES=$((FOUND_ISSUES + 1))
        fi
    done
else
    echo "  ⚠️  华为云配置模板文件不存在: $HUAWEI_TEMPLATE_FILE"
fi

echo ""

# 检查.gitignore配置
echo "🔍 检查.gitignore配置..."
GITIGNORE_PATTERNS=(
    ".env"
    "agconnect-services.json"
    "*.p12"
    "*.cer"
    "*.pem"
    "*.key"
)

for pattern in "${GITIGNORE_PATTERNS[@]}"; do
    if grep -q "$pattern" .gitignore 2>/dev/null; then
        echo "  ✅ .gitignore包含: $pattern"
    else
        echo "  ❌ .gitignore缺少: $pattern"
        FOUND_ISSUES=$((FOUND_ISSUES + 1))
    fi
done

echo ""

# 检查环境变量使用情况
echo "🔍 检查环境变量使用情况..."
ENV_VAR_PATTERNS=(
    "process.env.WECHAT_CORP_ID"
    "process.env.WECHAT_CORP_SECRET"
    "process.env.JPUSH_APP_KEY"
    "process.env.TENCENT_SECRET_ID"
)

for pattern in "${ENV_VAR_PATTERNS[@]}"; do
    FOUND_FILES=$(find . -name "*.js" -o -name "*.ts" | grep -v node_modules | xargs grep -l "$pattern" 2>/dev/null || true)
    if [ -n "$FOUND_FILES" ]; then
        echo "  ✅ 环境变量使用: $pattern"
    else
        echo "  ⚠️  未找到环境变量使用: $pattern"
    fi
done

echo ""

# 生成报告
echo "📊 脱敏验证报告"
echo "================"
echo "总检查项: $TOTAL_CHECKS"
echo "发现问题: $FOUND_ISSUES"

if [ $FOUND_ISSUES -eq 0 ]; then
    echo ""
    echo "🎉 恭喜！脱敏验证通过"
    echo "✅ 未发现敏感信息泄露"
    echo "✅ 配置文件已正确脱敏"
    echo "✅ .gitignore配置正确"
    echo ""
    echo "可以安全提交代码到版本控制系统"
    exit 0
else
    echo ""
    echo "❌ 脱敏验证失败"
    echo "发现 $FOUND_ISSUES 个问题需要修复"
    echo ""
    echo "请修复上述问题后重新运行验证"
    exit 1
fi
