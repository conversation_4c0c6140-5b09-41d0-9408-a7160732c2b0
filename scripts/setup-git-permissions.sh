#!/bin/bash

# 公职猫项目Git权限管理配置脚本
# 用于在Gitee上配置分支保护和访问权限

echo "🔒 公职猫项目Git权限管理配置"
echo "================================"

# 检查是否在正确的仓库中
if [ ! -d ".git" ]; then
    echo "❌ 错误: 请在Git仓库根目录中运行此脚本"
    exit 1
fi

# 检查远程仓库
REMOTE_URL=$(git remote get-url origin)
if [[ ! "$REMOTE_URL" == *"gitee.com"* ]]; then
    echo "⚠️  警告: 当前仓库不是Gitee仓库，某些功能可能不可用"
fi

echo "📋 当前仓库信息:"
echo "远程地址: $REMOTE_URL"
echo "当前分支: $(git branch --show-current)"
echo ""

# 1. 配置Git基本安全设置
echo "🔧 配置Git基本安全设置..."

# 启用签名验证
git config receive.fsckObjects true
git config receive.denyNonFastForwards true
git config transfer.fsckObjects true

# 配置提交签名
read -p "是否配置GPG签名? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "请确保已经设置了GPG密钥，然后运行:"
    echo "git config --global user.signingkey <YOUR-GPG-KEY-ID>"
    echo "git config --global commit.gpgsign true"
fi

# 2. 创建分支保护配置文件
echo "📝 创建分支保护配置..."

cat > .gitee/branch-protection.yml << 'EOF'
# Gitee分支保护配置
# 需要在Gitee仓库设置中手动配置

master:
  protection_level: "high"
  required_reviews: 2
  dismiss_stale_reviews: true
  require_code_owner_reviews: true
  required_status_checks:
    - "ci/build"
    - "ci/test"
    - "security/scan"
  restrictions:
    push_allowlist: []  # 禁止直接推送
    merge_allowlist: ["project-manager", "lead-developer"]

develop:
  protection_level: "medium"
  required_reviews: 1
  dismiss_stale_reviews: true
  required_status_checks:
    - "ci/build"
    - "ci/test"
  restrictions:
    push_allowlist: []  # 禁止直接推送
    merge_allowlist: ["core-developers"]

feature/*:
  protection_level: "low"
  required_reviews: 0
  required_status_checks:
    - "ci/build"
  restrictions:
    push_allowlist: ["core-developers", "platform-developers"]
    merge_allowlist: ["core-developers"]

outsourcing/*:
  protection_level: "medium"
  required_reviews: 1
  required_status_checks:
    - "ci/build"
    - "ci/test"
    - "security/scan"
  restrictions:
    push_allowlist: ["outsourcing-teams"]
    merge_allowlist: ["core-developers"]
EOF

# 3. 创建团队权限配置
echo "👥 创建团队权限配置..."

cat > .gitee/team-permissions.yml << 'EOF'
# 团队权限配置
# 需要在Gitee组织设置中配置

teams:
  core-developers:
    members: ["project-manager", "lead-developer", "senior-developer"]
    permissions:
      - "admin"
      - "push"
      - "pull"
      - "maintain"
    access_paths: ["*"]
    
  platform-developers:
    android-team:
      permissions: ["push", "pull"]
      access_paths: 
        - "mobile/android/**"
        - "mobile/src/**"
        - "docs/03_design/移动端/**"
    
    ios-team:
      permissions: ["push", "pull"]
      access_paths:
        - "mobile/ios/**"
        - "mobile/src/**"
        - "docs/03_design/移动端/**"
    
    desktop-team:
      permissions: ["push", "pull"]
      access_paths:
        - "desktop/**"
        - "shared/**"
        - "docs/03_design/桌面端/**"
  
  outsourcing-teams:
    permissions: ["push", "pull"]
    restrictions:
      - "no-backend-access"
      - "no-crypto-access"
      - "no-env-files"
    access_paths:
      - "mobile/android/**"  # 仅示例，实际按分配调整
    forbidden_paths:
      - "backend/**"
      - "shared/crypto/**"
      - ".env*"
      - "secrets/**"

  designers:
    permissions: ["pull"]
    access_paths:
      - "docs/03_design/**"
      - "mobile/src/styles/**"
      - "desktop/src/styles/**"
EOF

# 4. 创建访问控制脚本
echo "🛡️  创建访问控制脚本..."

cat > scripts/check-permissions.sh << 'EOF'
#!/bin/bash

# 检查用户是否有权限访问特定路径
check_path_permission() {
    local user=$1
    local path=$2
    local team_config=".gitee/team-permissions.yml"
    
    echo "检查用户 $user 对路径 $path 的访问权限..."
    
    # 这里需要根据实际的权限配置进行检查
    # 实际实现需要解析YAML文件并检查权限
    
    echo "权限检查完成"
}

# 记录访问日志
log_access() {
    local user=$1
    local action=$2
    local path=$3
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "[$timestamp] User: $user, Action: $action, Path: $path" >> access.log
}

# 导出函数供其他脚本使用
export -f check_path_permission
export -f log_access
EOF

chmod +x scripts/check-permissions.sh

# 5. 创建外包团队设置脚本
echo "🤝 创建外包团队设置脚本..."

cat > scripts/setup-outsourcing-repo.sh << 'EOF'
#!/bin/bash

# 为外包团队设置受限访问的仓库

setup_outsourcing_access() {
    local team_name=$1
    local allowed_paths=$2
    
    echo "为外包团队 $team_name 设置访问权限..."
    
    # 创建外包专用分支
    git checkout -b "outsourcing/$team_name/setup"
    
    # 配置sparse-checkout
    git config core.sparseCheckout true
    echo "$allowed_paths" > .git/info/sparse-checkout
    
    # 重新读取工作树
    git read-tree -m -u HEAD
    
    echo "外包团队 $team_name 的访问权限配置完成"
    echo "允许访问的路径: $allowed_paths"
}

# 示例用法
if [ "$1" = "android" ]; then
    setup_outsourcing_access "android-team" "mobile/android/\nmobile/src/\ndocs/03_design/移动端/"
elif [ "$1" = "ios" ]; then
    setup_outsourcing_access "ios-team" "mobile/ios/\nmobile/src/\ndocs/03_design/移动端/"
else
    echo "用法: $0 [android|ios]"
    echo "或者: setup_outsourcing_access <team_name> <allowed_paths>"
fi
EOF

chmod +x scripts/setup-outsourcing-repo.sh

# 6. 创建权限审计脚本
echo "📊 创建权限审计脚本..."

cat > scripts/audit-permissions.sh << 'EOF'
#!/bin/bash

# Git权限审计脚本

echo "🔍 Git权限审计报告"
echo "==================="
echo "生成时间: $(date)"
echo ""

# 检查分支保护状态
echo "📋 分支保护状态:"
git branch -r | while read branch; do
    echo "  $branch"
done
echo ""

# 检查最近的提交记录
echo "📝 最近10次提交记录:"
git log --oneline -10 --pretty=format:"%h %an %ad %s" --date=short
echo ""

# 检查访问日志
if [ -f "access.log" ]; then
    echo "📊 最近访问记录:"
    tail -20 access.log
else
    echo "📊 访问日志文件不存在"
fi
echo ""

# 检查敏感文件
echo "🔒 敏感文件检查:"
find . -name "*.env*" -o -name "*.key" -o -name "*.pem" -o -name "secrets" | while read file; do
    if [ -f "$file" ]; then
        echo "  ⚠️  发现敏感文件: $file"
    fi
done
echo ""

# 检查大文件
echo "📦 大文件检查 (>1MB):"
find . -type f -size +1M | head -10
echo ""

echo "审计完成"
EOF

chmod +x scripts/audit-permissions.sh

# 7. 创建Git hooks
echo "🪝 设置Git hooks..."

mkdir -p .git/hooks

# pre-commit hook - 检查敏感信息
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash

# 检查是否提交了敏感信息
echo "🔍 检查敏感信息..."

# 检查是否包含真实的API密钥（OpenAI/OpenRouter格式）
if git diff --cached --name-only | xargs grep -l "sk-[a-zA-Z0-9]\{20,\}" 2>/dev/null; then
    echo "❌ 错误: 检测到可能的API密钥，请移除后再提交"
    exit 1
fi

# 检查是否包含密码
if git diff --cached --name-only | xargs grep -l "password.*=" 2>/dev/null; then
    echo "⚠️  警告: 检测到可能的密码，请确认是否安全"
fi

# 检查.env文件
if git diff --cached --name-only | grep -q "\.env$"; then
    echo "❌ 错误: 不能提交.env文件"
    exit 1
fi

echo "✅ 安全检查通过"
EOF

chmod +x .git/hooks/pre-commit

# pre-push hook - 权限检查
cat > .git/hooks/pre-push << 'EOF'
#!/bin/bash

# 推送前权限检查
echo "🔒 推送前权限检查..."

# 获取当前分支
current_branch=$(git branch --show-current)

# 检查是否推送到保护分支
if [[ "$current_branch" == "master" || "$current_branch" == "main" ]]; then
    echo "❌ 错误: 不能直接推送到主分支，请使用Pull Request"
    exit 1
fi

if [[ "$current_branch" == "develop" ]]; then
    echo "❌ 错误: 不能直接推送到develop分支，请使用Pull Request"
    exit 1
fi

echo "✅ 权限检查通过"
EOF

chmod +x .git/hooks/pre-push

echo ""
echo "✅ Git权限管理配置完成!"
echo ""
echo "📋 下一步操作:"
echo "1. 在Gitee仓库设置中配置分支保护规则"
echo "2. 在Gitee组织中创建相应的团队"
echo "3. 为团队成员分配适当的权限"
echo "4. 运行 'scripts/audit-permissions.sh' 进行权限审计"
echo ""
echo "📁 创建的配置文件:"
echo "  - .gitee/branch-protection.yml"
echo "  - .gitee/team-permissions.yml"
echo "  - scripts/check-permissions.sh"
echo "  - scripts/setup-outsourcing-repo.sh"
echo "  - scripts/audit-permissions.sh"
echo "  - .git/hooks/pre-commit"
echo "  - .git/hooks/pre-push"
echo ""
echo "🔗 Gitee配置指南:"
echo "  访问: https://gitee.com/your-org/gongzhimall/settings"
echo "  配置分支保护、团队权限和访问控制"
EOF 