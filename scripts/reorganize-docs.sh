#!/bin/bash

# 公职猫项目文档重组脚本
# 用途：清理根目录散乱文件，按功能分类整理

echo "🚀 开始公职猫项目文档重组..."

# 第零步：修复docs目录命名冲突
echo "🔧 修复docs目录命名冲突..."
if [ -d "docs/01_product" ] && [ -d "docs/01_strategy" ]; then
    mv docs/01_product docs/01_b_product
    echo "  ✅ 目录 'docs/01_product' 已重命名为 'docs/01_b_product'"
fi

# 第一步：创建必要目录
echo "📁 创建目录结构..."
mkdir -p docs/00_project_management
mkdir -p docs/00_project_management/archive
mkdir -p mobile/assets/ai_models

# 第二步：移动项目管理文档
echo "📋 移动项目管理文档..."
if [ -f "CHANGELOG.md" ]; then
    mv CHANGELOG.md docs/00_project_management/
    echo "  ✅ 移动 CHANGELOG.md"
fi

if [ -f "CONTRIBUTING.md" ]; then
    mv CONTRIBUTING.md docs/00_project_management/
    echo "  ✅ 移动 CONTRIBUTING.md"
fi

if [ -f "UPDATE_SUMMARY.md" ]; then
    mv UPDATE_SUMMARY.md docs/00_project_management/
    echo "  ✅ 移动 UPDATE_SUMMARY.md"
fi

# 第三步：移动测试相关文件
echo "🧪 移动测试相关文件..."
if [ -f "chi_sim.traineddata" ]; then
    mv chi_sim.traineddata mobile/assets/ai_models/
    echo "  ✅ 移动 chi_sim.traineddata"
fi

if [ -f "eng.traineddata" ]; then
    mv eng.traineddata mobile/assets/ai_models/
    echo "  ✅ 移动 eng.traineddata"
fi

if [ -f "test-ocr-real.js" ]; then
    mv test-ocr-real.js mobile/scripts/
    echo "  ✅ 移动 test-ocr-real.js"
fi

if [ -f "ocr-test-results.json" ]; then
    mv ocr-test-results.json test_reports/
    echo "  ✅ 移动 ocr-test-results.json"
fi

if [ -f "ocr-test-report.json" ]; then
    mv ocr-test-report.json test_reports/
    echo "  ✅ 移动 ocr-test-report.json"
fi

if [ -f "OCR_VERIFICATION_REPORT.md" ]; then
    mv OCR_VERIFICATION_REPORT.md test_reports/
    echo "  ✅ 移动 OCR_VERIFICATION_REPORT.md"
fi

# 第四步：处理临时文件
echo "🗂️ 处理临时文件..."
if [ -f "TaskMaster_重新规划提示词.md" ]; then
    mv "TaskMaster_重新规划提示词.md" docs/00_project_management/archive/
    echo "  ✅ 归档 TaskMaster_重新规划提示词.md"
fi

# 第五步：移动TaskMaster文档
echo "📖 移动TaskMaster文档..."
if [ -f "README-task-master.md" ]; then
    mv README-task-master.md .taskmaster/
    echo "  ✅ 移动 README-task-master.md"
fi

echo ""
echo "✨ 文档重组完成！"
echo ""
echo "📊 重组结果："
echo "  🔧 修复了 'docs/' 目录命名冲突"
echo "  📁 项目管理文档 → docs/00_project_management/"
echo "  🧪 测试文件 → test_reports/ 和 mobile/assets/ai_models/"
echo "  📖 TaskMaster文档 → .taskmaster/"
echo "  🗂️ 临时文件 → docs/00_project_management/archive/"
echo ""
echo "🎯 根目录现在更加整洁！" 