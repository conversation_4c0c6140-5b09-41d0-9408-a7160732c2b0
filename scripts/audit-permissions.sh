#!/bin/bash

# Git权限审计脚本

echo "🔍 Git权限审计报告"
echo "==================="
echo "生成时间: $(date)"
echo ""

# 检查分支保护状态
echo "📋 分支保护状态:"
git branch -r | while read branch; do
    echo "  $branch"
done
echo ""

# 检查最近的提交记录
echo "📝 最近10次提交记录:"
git log --oneline -10 --pretty=format:"%h %an %ad %s" --date=short
echo ""

# 检查访问日志
if [ -f "access.log" ]; then
    echo "📊 最近访问记录:"
    tail -20 access.log
else
    echo "📊 访问日志文件不存在"
fi
echo ""

# 检查敏感文件
echo "🔒 敏感文件检查:"
find . -name "*.env*" -o -name "*.key" -o -name "*.pem" -o -name "secrets" | while read file; do
    if [ -f "$file" ]; then
        echo "  ⚠️  发现敏感文件: $file"
    fi
done
echo ""

# 检查大文件
echo "📦 大文件检查 (>1MB):"
find . -type f -size +1M | head -10
echo ""

echo "审计完成"
