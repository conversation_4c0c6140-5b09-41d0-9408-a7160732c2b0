#!/bin/bash

# 为外包团队设置受限访问的仓库

setup_outsourcing_access() {
    local team_name=$1
    local allowed_paths=$2
    
    echo "为外包团队 $team_name 设置访问权限..."
    
    # 创建外包专用分支
    git checkout -b "outsourcing/$team_name/setup"
    
    # 配置sparse-checkout
    git config core.sparseCheckout true
    echo "$allowed_paths" > .git/info/sparse-checkout
    
    # 重新读取工作树
    git read-tree -m -u HEAD
    
    echo "外包团队 $team_name 的访问权限配置完成"
    echo "允许访问的路径: $allowed_paths"
}

# 示例用法
if [ "$1" = "android" ]; then
    setup_outsourcing_access "android-team" "mobile/android/\nmobile/src/\ndocs/03_design/移动端/"
elif [ "$1" = "ios" ]; then
    setup_outsourcing_access "ios-team" "mobile/ios/\nmobile/src/\ndocs/03_design/移动端/"
else
    echo "用法: $0 [android|ios]"
    echo "或者: setup_outsourcing_access <team_name> <allowed_paths>"
fi
