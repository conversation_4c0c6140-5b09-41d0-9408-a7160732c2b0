#!/usr/bin/env python3
"""
证件照处理功能测试图片下载脚本

此脚本用于为公职猫项目的证件照处理功能下载测试用的人像图片。
下载的图片将用于验证MediaPipe Selfie Segmentation的人像抠图效果。

功能特点：
- 下载12张高质量人像图片（男女各6张，分青年和中年两个年龄段）
- 图片分辨率1080px，适合证件照处理测试
- 按性别和年龄段分类存储
- 生成详细的下载报告和元数据

使用方法：
1. 设置UNSPLASH_ACCESS_KEY环境变量
2. 运行: python download_sample_portraits.py

相关任务：TaskMaster Task #41 - 证件照处理模块实现
创建日期：2025-01-14
"""

import os
import requests
import json
from pathlib import Path
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimplePortraitDownloader:
    def __init__(self):
        # Unsplash API配置
        self.unsplash_access_key = "*******************************************"
        
        # 基础配置
        self.base_dir = Path("test_portraits")
        
        # 预选的高质量人像图片ID（来自Unsplash）
        self.sample_images = {
            "male": {
                "young_adult": [
                    {"id": "WNoLnJo7tS8", "description": "Professional Asian man portrait"},
                    {"id": "d2MSDujJl2g", "description": "Young Asian businessman"},
                    {"id": "rDEOVtE7vOs", "description": "Asian man headshot"}
                ],
                "middle_aged": [
                    {"id": "EGVccebWodM", "description": "Middle-aged Asian man with glasses"},
                    {"id": "gG70fyu3qsg", "description": "Mature Asian businessman"},
                    {"id": "bqe0J0b26RQ", "description": "Professional middle-aged man"}
                ]
            },
            "female": {
                "young_adult": [
                    {"id": "ANNsvl-6AG0", "description": "Young Asian woman professional"},
                    {"id": "tLKOj6cNwe0", "description": "Asian woman with glasses"},
                    {"id": "__ng3cRXmFE", "description": "Professional Asian woman"}
                ],
                "middle_aged": [
                    {"id": "B4TjXnI0Y2c", "description": "Mature Asian woman"},
                    {"id": "N2IJ31xZ_ks", "description": "Middle-aged professional woman"},
                    {"id": "ILip77SbmOE", "description": "Asian businesswoman"}
                ]
            }
        }

    def setup_directories(self):
        """创建必要的目录结构"""
        logger.info("设置目录结构...")
        
        for gender in self.sample_images:
            for age_group in self.sample_images[gender]:
                dir_path = self.base_dir / gender / age_group
                dir_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建目录: {dir_path}")

    def get_image_info(self, image_id: str) -> dict:
        """获取图片信息"""
        url = f"https://api.unsplash.com/photos/{image_id}"
        headers = {"Authorization": f"Client-ID {self.unsplash_access_key}"}
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取图片信息失败 {image_id}: {e}")
            return None

    def download_image(self, image_info: dict, target_path: Path) -> bool:
        """下载图片"""
        try:
            # 获取高质量图片URL
            download_url = image_info["urls"]["regular"]  # 1080px宽度
            
            # 下载图片
            response = requests.get(download_url, timeout=15)
            response.raise_for_status()
            
            # 保存图片
            with open(target_path, 'wb') as f:
                f.write(response.content)
            
            # 触发Unsplash下载统计
            download_trigger_url = image_info["links"]["download_location"]
            headers = {"Authorization": f"Client-ID {self.unsplash_access_key}"}
            requests.get(download_trigger_url, headers=headers, timeout=5)
            
            logger.info(f"✅ 图片下载成功: {target_path}")
            return True
            
        except Exception as e:
            logger.error(f"图片下载失败: {e}")
            return False

    def download_category(self, gender: str, age_group: str):
        """下载特定分类的图片"""
        logger.info(f"开始下载分类: {gender}/{age_group}")
        
        target_dir = self.base_dir / gender / age_group
        images = self.sample_images[gender][age_group]
        
        for i, image_data in enumerate(images):
            image_id = image_data["id"]
            description = image_data["description"]
            
            # 检查是否已存在
            filename = f"{gender}_{age_group}_{i+1:02d}.jpg"
            target_path = target_dir / filename
            
            if target_path.exists():
                logger.info(f"图片已存在，跳过: {filename}")
                continue
            
            logger.info(f"下载图片: {image_id} - {description}")
            
            # 获取图片信息
            image_info = self.get_image_info(image_id)
            if not image_info:
                continue
            
            # 下载图片
            if self.download_image(image_info, target_path):
                # 保存元数据
                metadata = {
                    "unsplash_id": image_id,
                    "description": description,
                    "photographer": image_info["user"]["name"],
                    "download_time": datetime.now().isoformat(),
                    "category": f"{gender}/{age_group}"
                }
                
                metadata_path = target_path.with_suffix('.json')
                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)

    def generate_report(self):
        """生成下载报告"""
        report = {
            "download_summary": {
                "download_time": datetime.now().isoformat(),
                "total_categories": len(self.sample_images) * 2,  # male/female * 2 age groups
                "images_per_category": 3
            },
            "category_breakdown": {}
        }
        
        total_downloaded = 0
        
        # 统计各分类下载情况
        for gender in self.sample_images:
            report["category_breakdown"][gender] = {}
            for age_group in self.sample_images[gender]:
                target_dir = self.base_dir / gender / age_group
                image_count = len(list(target_dir.glob("*.jpg")))
                report["category_breakdown"][gender][age_group] = image_count
                total_downloaded += image_count
        
        report["download_summary"]["total_downloaded"] = total_downloaded
        
        # 保存报告
        report_path = self.base_dir / "download_report_simple.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        logger.info("=" * 50)
        logger.info("下载完成报告")
        logger.info("=" * 50)
        logger.info(f"总下载图片: {total_downloaded}")
        logger.info(f"目标图片数: {len(self.sample_images) * 2 * 3}")
        logger.info(f"详细报告已保存至: {report_path}")

    def run(self):
        """运行主程序"""
        logger.info("开始证件照测试图片下载任务（简化版本）...")
        
        # 设置目录
        self.setup_directories()
        
        try:
            # 下载所有分类
            for gender in self.sample_images:
                for age_group in self.sample_images[gender]:
                    self.download_category(gender, age_group)
            
            # 生成报告
            self.generate_report()
            
        except KeyboardInterrupt:
            logger.info("用户中断下载")
        except Exception as e:
            logger.error(f"下载过程中出现错误: {e}")
        finally:
            self.generate_report()

if __name__ == "__main__":
    downloader = SimplePortraitDownloader()
    downloader.run() 