---
description: 环境配置管理和部署规范
globs: "**/.env*", "**/config/**/*", "**/docker/**/*"
alwaysApply: true
---

# 环境配置管理与部署规范

## 🌍 环境分离原则

### 环境层级定义
```
开发环境 (development) - 本地开发
├── 测试环境 (testing) - 自动化测试
├── 预生产环境 (staging) - 生产环境镜像
└── 生产环境 (production) - 线上服务
```

### 配置文件结构
```
项目根目录/
├── .env.local              # 本地开发配置（不提交）
├── .env.example            # 配置模板（提交）
├── .env.test               # 测试环境配置
├── .env.staging            # 预生产环境配置
├── .env.production         # 生产环境配置
├── config/
│   ├── development.js      # 开发环境具体配置
│   ├── testing.js          # 测试环境具体配置
│   ├── staging.js          # 预生产环境具体配置
│   └── production.js       # 生产环境具体配置
└── docker/
    ├── development.yml     # 开发环境Docker配置
    ├── staging.yml         # 预生产环境Docker配置
    └── production.yml      # 生产环境Docker配置
```

## 🔐 环境变量管理

### 敏感信息分类
```typescript
// ✅ 环境变量分类标准
interface EnvironmentVariables {
  // 🔴 高敏感：绝不能暴露
  secrets: {
    DATABASE_PASSWORD: string
    JWT_SECRET: string
    API_KEYS: string
    PRIVATE_KEYS: string
  }
  
  // 🟡 中敏感：谨慎处理
  configuration: {
    DATABASE_URL: string
    REDIS_URL: string
    SMTP_CONFIG: string
  }
  
  // 🟢 低敏感：可以暴露
  public: {
    APP_NAME: string
    VERSION: string
    NODE_ENV: string
    PORT: number
  }
}
```

### 环境变量命名规范
```bash
# ✅ 命名规范示例
# 格式：[模块]_[功能]_[属性]
DATABASE_URL="postgresql://..."
DATABASE_MAX_CONNECTIONS=10
DATABASE_TIMEOUT=30000

JWT_SECRET="..."
JWT_EXPIRES_IN="7d"
JWT_ALGORITHM="HS256"

REDIS_URL="redis://..."
REDIS_PREFIX="gongzhimall:"
REDIS_TTL=3600

SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="..."
SMTP_PASS="..."

# 应用配置
APP_NAME="公职猫"
APP_VERSION="1.0.0"
APP_ENV="development"
APP_PORT=3000
APP_DEBUG=true

# 前端配置
FRONTEND_URL="http://localhost:3000"
ADMIN_URL="http://localhost:3001"
```

### 不同环境的配置策略

#### 开发环境 (.env.local)
```bash
# 🔧 开发环境配置
NODE_ENV=development
APP_DEBUG=true
APP_PORT=3000

# 本地数据库
DATABASE_URL="postgresql://dev:dev123@localhost:5432/gongzhimall_dev"
REDIS_URL="redis://localhost:6379"

# 开发API密钥（测试用）
JWT_SECRET="dev_jwt_secret_change_in_prod"
ANTHROPIC_API_KEY="sk-..."

# 文件存储（本地）
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=10485760

# 邮件（测试）
SMTP_HOST="smtp.mailtrap.io"
SMTP_PORT=2525
SMTP_USER="test_user"
SMTP_PASS="test_pass"

# 日志级别
LOG_LEVEL=debug
LOG_TO_FILE=false

# 热重载
HOT_RELOAD=true
WATCH_FILES=true
```

#### 测试环境 (.env.test)
```bash
# 🧪 测试环境配置
NODE_ENV=test
APP_DEBUG=false
APP_PORT=3001

# 测试数据库（独立）
DATABASE_URL="postgresql://test:test123@localhost:5432/gongzhimall_test"
REDIS_URL="redis://localhost:6380"

# 测试用密钥
JWT_SECRET="test_jwt_secret"
JWT_EXPIRES_IN="1h"

# 禁用外部服务
SKIP_EMAIL=true
SKIP_SMS=true
SKIP_UPLOAD=true

# 测试专用配置
TEST_TIMEOUT=30000
CLEAR_DB_BEFORE_TESTS=true
MOCK_EXTERNAL_APIS=true

# 日志设置
LOG_LEVEL=error
LOG_TO_FILE=false
```

#### 预生产环境 (.env.staging)
```bash
# 🎭 预生产环境配置
NODE_ENV=staging
APP_DEBUG=false
APP_PORT=3000

# 预生产数据库
DATABASE_URL="${STAGING_DATABASE_URL}"
DATABASE_MAX_CONNECTIONS=20
DATABASE_TIMEOUT=10000

# 预生产Redis
REDIS_URL="${STAGING_REDIS_URL}"
REDIS_PREFIX="staging:"

# 生产级密钥
JWT_SECRET="${STAGING_JWT_SECRET}"
JWT_EXPIRES_IN="7d"

# 云存储配置
UPLOAD_PROVIDER="aliyun"
ALIYUN_ACCESS_KEY="${STAGING_ALIYUN_ACCESS_KEY}"
ALIYUN_SECRET_KEY="${STAGING_ALIYUN_SECRET_KEY}"

# 监控配置
ENABLE_MONITORING=true
SENTRY_DSN="${STAGING_SENTRY_DSN}"

# 日志配置
LOG_LEVEL=info
LOG_TO_FILE=true
LOG_ROTATION=daily
```

#### 生产环境 (.env.production)
```bash
# 🚀 生产环境配置
NODE_ENV=production
APP_DEBUG=false
APP_PORT=3000

# 生产数据库（高可用）
DATABASE_URL="${PROD_DATABASE_URL}"
DATABASE_MAX_CONNECTIONS=50
DATABASE_TIMEOUT=5000
DATABASE_SSL=true

# 生产Redis集群
REDIS_URL="${PROD_REDIS_CLUSTER_URL}"
REDIS_PREFIX="prod:"
REDIS_CLUSTER=true

# 安全密钥（强密钥）
JWT_SECRET="${PROD_JWT_SECRET}"
JWT_EXPIRES_IN="7d"
JWT_ALGORITHM="HS256"

# 云服务配置
UPLOAD_PROVIDER="aliyun"
ALIYUN_ACCESS_KEY="${PROD_ALIYUN_ACCESS_KEY}"
ALIYUN_SECRET_KEY="${PROD_ALIYUN_SECRET_KEY}"
CDN_URL="${PROD_CDN_URL}"

# 邮件服务
SMTP_HOST="${PROD_SMTP_HOST}"
SMTP_PORT=587
SMTP_USER="${PROD_SMTP_USER}"
SMTP_PASS="${PROD_SMTP_PASS}"
SMTP_SECURE=true

# 性能配置
ENABLE_CACHE=true
CACHE_TTL=3600
ENABLE_COMPRESSION=true
RATE_LIMIT_MAX=1000

# 监控与日志
ENABLE_MONITORING=true
SENTRY_DSN="${PROD_SENTRY_DSN}"
LOG_LEVEL=warn
LOG_TO_FILE=true
LOG_ROTATION=daily
LOG_RETENTION_DAYS=30

# 安全配置
CORS_ORIGINS="${PROD_CORS_ORIGINS}"
HELMET_ENABLED=true
RATE_LIMIT_STRICT=true
```

## 🐳 Docker 配置管理

### 开发环境 Docker (docker/development.yml)
```yaml
version: '3.8'
services:
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - ../:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: gongzhimall_dev
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: dev123
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data

volumes:
  postgres_dev_data:
  redis_dev_data:
```

### 生产环境 Docker (docker/production.yml)
```yaml
version: '3.8'
services:
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile.prod
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - ../.env.production
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./postgres-init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_prod_data:/data
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_prod_data:
  redis_prod_data:
```

## 📝 配置文件动态管理

### 配置加载器 (config/index.ts)
```typescript
// ✅ 动态配置加载器
import dotenv from 'dotenv'
import path from 'path'

// 根据环境加载对应的配置文件
const env = process.env.NODE_ENV || 'development'
const envFile = `.env.${env}`

// 加载环境配置
dotenv.config({ path: path.resolve(process.cwd(), envFile) })
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') })

interface AppConfig {
  env: string
  port: number
  debug: boolean
  database: DatabaseConfig
  redis: RedisConfig
  jwt: JWTConfig
  upload: UploadConfig
  email: EmailConfig
  logging: LoggingConfig
  monitoring: MonitoringConfig
}

interface DatabaseConfig {
  url: string
  maxConnections: number
  timeout: number
  ssl: boolean
}

interface RedisConfig {
  url: string
  prefix: string
  cluster: boolean
}

const config: AppConfig = {
  env,
  port: parseInt(process.env.APP_PORT || '3000'),
  debug: process.env.APP_DEBUG === 'true',
  
  database: {
    url: process.env.DATABASE_URL!,
    maxConnections: parseInt(process.env.DATABASE_MAX_CONNECTIONS || '10'),
    timeout: parseInt(process.env.DATABASE_TIMEOUT || '30000'),
    ssl: process.env.DATABASE_SSL === 'true'
  },
  
  redis: {
    url: process.env.REDIS_URL!,
    prefix: process.env.REDIS_PREFIX || 'app:',
    cluster: process.env.REDIS_CLUSTER === 'true'
  },
  
  jwt: {
    secret: process.env.JWT_SECRET!,
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    algorithm: process.env.JWT_ALGORITHM || 'HS256'
  },
  
  upload: {
    provider: process.env.UPLOAD_PROVIDER || 'local',
    maxSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'),
    allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,gif,pdf').split(',')
  },
  
  email: {
    host: process.env.SMTP_HOST!,
    port: parseInt(process.env.SMTP_PORT || '587'),
    user: process.env.SMTP_USER!,
    pass: process.env.SMTP_PASS!,
    secure: process.env.SMTP_SECURE === 'true'
  },
  
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    toFile: process.env.LOG_TO_FILE === 'true',
    rotation: process.env.LOG_ROTATION || 'daily'
  },
  
  monitoring: {
    enabled: process.env.ENABLE_MONITORING === 'true',
    sentryDsn: process.env.SENTRY_DSN
  }
}

// 配置验证
const validateConfig = (config: AppConfig) => {
  const required = [
    'DATABASE_URL',
    'JWT_SECRET',
    'REDIS_URL'
  ]
  
  const missing = required.filter(key => !process.env[key])
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
  
  // 生产环境额外检查
  if (config.env === 'production') {
    const prodRequired = [
      'SENTRY_DSN',
      'SMTP_HOST',
      'SMTP_USER',
      'SMTP_PASS'
    ]
    
    const prodMissing = prodRequired.filter(key => !process.env[key])
    
    if (prodMissing.length > 0) {
      console.warn(`Missing production environment variables: ${prodMissing.join(', ')}`)
    }
  }
}

validateConfig(config)

export default config
```

## 🚀 部署配置

### CI/CD 配置 (.github/workflows/deploy.yml)
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    environment: test
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test123
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 18
        cache: 'yarn'
    
    - name: Install dependencies
      run: yarn install --frozen-lockfile
    
    - name: Run tests
      run: yarn test
      env:
        DATABASE_URL: postgresql://postgres:test123@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test_secret
  
  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Docker image
      run: |
        docker build -f docker/Dockerfile.prod -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} .
        docker tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
    
    - name: Push to registry
      run: |
        echo ${{ secrets.GITHUB_TOKEN }} | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin
        docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
  
  deploy:
    needs: build
    runs-on: ubuntu-latest
    environment: production
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to production
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.PROD_SERVER_HOST }}
        username: ${{ secrets.PROD_SERVER_USER }}
        key: ${{ secrets.PROD_SERVER_KEY }}
        script: |
          cd /opt/gongzhimall
          docker-compose -f docker/production.yml down
          docker-compose -f docker/production.yml pull
          docker-compose -f docker/production.yml up -d
          docker system prune -f
```

### 健康检查配置
```typescript
// ✅ 健康检查端点
import { Request, Response } from 'express'
import prisma from '../lib/prisma'
import redis from '../lib/redis'

export const healthCheck = async (req: Request, res: Response) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    checks: {
      database: 'unknown',
      redis: 'unknown',
      memory: 'unknown'
    }
  }
  
  try {
    // 数据库检查
    await prisma.$queryRaw`SELECT 1`
    health.checks.database = 'ok'
  } catch (error) {
    health.checks.database = 'error'
    health.status = 'error'
  }
  
  try {
    // Redis检查
    await redis.ping()
    health.checks.redis = 'ok'
  } catch (error) {
    health.checks.redis = 'error'
    health.status = 'error'
  }
  
  // 内存使用检查
  const memUsage = process.memoryUsage()
  const memUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024)
  health.checks.memory = `${memUsedMB}MB`
  
  if (memUsedMB > 512) {
    health.status = 'warning'
  }
  
  const statusCode = health.status === 'ok' ? 200 : 
                    health.status === 'warning' ? 200 : 503
  
  res.status(statusCode).json(health)
}
```

## 🔒 安全最佳实践

### 敏感信息保护
```bash
# ✅ .gitignore 配置
# 环境文件
.env
.env.local
.env.*.local

# 但保留模板文件
!.env.example
!.env.*.example

# 密钥文件
*.pem
*.key
*.crt
secrets/

# 配置文件中的敏感部分
config/secrets.js
```

### 环境变量验证中间件
```typescript
// ✅ 环境验证中间件
const validateEnvironment = () => {
  const errors: string[] = []
  
  // 检查必需的环境变量
  const required = [
    'NODE_ENV',
    'DATABASE_URL',
    'JWT_SECRET',
    'REDIS_URL'
  ]
  
  required.forEach(variable => {
    if (!process.env[variable]) {
      errors.push(`Missing required environment variable: ${variable}`)
    }
  })
  
  // 检查JWT密钥强度
  if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
    errors.push('JWT_SECRET must be at least 32 characters long')
  }
  
  // 生产环境额外检查
  if (process.env.NODE_ENV === 'production') {
    if (process.env.JWT_SECRET === 'your_jwt_secret_here') {
      errors.push('JWT_SECRET must be changed from default value in production')
    }
    
    if (!process.env.DATABASE_URL?.includes('ssl=true')) {
      console.warn('Warning: Database SSL should be enabled in production')
    }
  }
  
  if (errors.length > 0) {
    console.error('Environment validation failed:')
    errors.forEach(error => console.error(`- ${error}`))
    process.exit(1)
  }
  
  console.log(`✅ Environment validation passed for ${process.env.NODE_ENV}`)
}

export default validateEnvironment
```

记住：**配置即代码，安全第一，环境隔离。**
