---
description: 
globs: 
alwaysApply: true
---
---
description: 防止重复造轮子和过度设计
globs: "**/*"
alwaysApply: true
---

# 代码质量控制与防重复造轮子规则

## 🎯 核心目标

- 防止重复实现已有功能
- 避免过度设计和过度工程
- 保持代码简洁和可维护性
- 优先使用现有解决方案

## 🔍 重复造轮子检查

### 在实现新功能前必须检查：

1. **现有代码库**
   - 搜索相似的函数或组件
   - 检查是否已有解决方案
   - 优先扩展现有代码而不是重写

2. **技术栈内置功能**
   - React Native内置组件和API
   - Electron原生功能
   - JavaScript/TypeScript标准库

3. **项目依赖**
   - 已安装的npm包功能
   - 第三方库的API
   - 工具库函数（如lodash、moment等）

### 常见重复造轮子场景：

```typescript
// ❌ 不要重新实现已有功能
// 如果已有日期处理库
const formatDate = (date) => { /* 自己实现 */ }

// ✅ 使用现有解决方案
import { format } from 'date-fns'
const formattedDate = format(date, 'yyyy-MM-dd')

// ❌ 不要重新实现状态管理
// 如果项目已有Redux或Zustand
const MyCustomStateManager = { /* 自己实现 */ }

// ✅ 使用项目已有的状态管理
import { useStore } from './store'
```

## 🚫 过度设计检查清单

### 设计模式过度使用
- **禁止**：为简单功能引入复杂设计模式
- **禁止**：在小型组件中使用观察者模式
- **禁止**：为单一用途创建抽象工厂
- **禁止**：过度使用装饰器模式

### 抽象层级控制
```typescript
// ❌ 过度抽象 - 为简单的API调用创建多层抽象
class ApiAbstractFactory {
  createApiClient() { /* 复杂实现 */ }
}
class ApiClientBuilder {
  build() { /* 更多抽象 */ }
}

// ✅ 简单直接 - 直接使用fetch或axios
const fetchUser = async (id: string) => {
  return fetch(`/api/users/${id}`).then(res => res.json())
}
```

### 配置过度化
```typescript
// ❌ 过度配置
interface ButtonConfig {
  size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'
  variant: 'primary' | 'secondary' | 'tertiary' | 'quaternary'
  theme: 'light' | 'dark' | 'auto' | 'system' | 'custom'
  animation: 'fade' | 'slide' | 'bounce' | 'scale' | 'none'
  // ... 20+ 配置项
}

// ✅ 简单够用
interface ButtonProps {
  size?: 'small' | 'medium' | 'large'
  variant?: 'primary' | 'secondary'
  disabled?: boolean
}
```

## 📚 技术栈优先级

### React Native 开发
1. **优先使用React Native内置组件**
   - View, Text, ScrollView, FlatList
   - Image, TextInput, TouchableOpacity
   - 原生API：Dimensions, Platform, Linking

2. **常用第三方库检查顺序**
   ```
   导航: @react-navigation/native
   状态管理: Redux Toolkit / Zustand
   网络请求: React Query + Axios
   UI组件: Native Base / React Native Elements
   图标: react-native-vector-icons
   ```

### Electron 开发
1. **优先使用Electron内置API**
   - BrowserWindow, Menu, Tray
   - ipcMain, ipcRenderer
   - app, shell, clipboard

2. **Web技术优先级**
   ```
   React 18+ 内置功能
   CSS-in-JS: styled-components
   状态管理: Redux Toolkit
   路由: React Router
   ```

## 🛠 实现检查流程

### 1. 功能需求分析
```
开始实现前问自己：
- 这个功能是否已经存在？
- 现有解决方案能否满足80%的需求？
- 修改现有代码比重写更简单吗？
```

### 2. 技术方案选择
```
选择技术方案时：
- 最简单能工作的方案是什么？
- 是否需要考虑未来扩展？（通常答案是否）
- 团队其他成员能理解这个方案吗？
```

### 3. 代码审查标准
```typescript
// 审查要点：
// ✅ 代码是否解决了具体问题？
// ✅ 是否使用了已有的工具/库？
// ✅ 是否遵循了项目已有的模式？
// ✅ 是否可以被其他开发者理解？
// ❌ 是否引入了不必要的复杂性？
// ❌ 是否重复实现了已有功能？
```

## 🔄 重构指导原则

### 何时重构
- 代码重复超过3次
- 功能需求明确变更
- 性能问题需要解决
- 安全漏洞需要修复

### 何时不重构
- "看起来不够优雅"
- "可能未来会用到"
- "这样更符合设计模式"
- "为了代码一致性"

## 📋 工具和库的选择原则

### 添加新依赖前检查：
1. **必要性**：解决实际问题还是"nice to have"？
2. **维护状态**：库是否活跃维护？
3. **包大小**：对应用体积的影响
4. **学习成本**：团队学习时间成本
5. **替代方案**：是否有更轻量的解决方案？

### 推荐的工具选择策略：
```
小而专一 > 大而全能
成熟稳定 > 新潮先进
社区支持 > 个人项目
文档完善 > 功能强大
```

## 🏆 开源库选型实践标准

> 基于公职猫项目AI规则引擎重构的实践经验总结

### 选型评估矩阵
```typescript
interface LibraryEvaluation {
  // P0 - 必须满足的条件
  compatibility: {
    reactNative: boolean        // React Native兼容性
    typescript: boolean         // TypeScript支持
    platform: 'ios' | 'android' | 'both'
  }
  
  // P1 - 重要考虑因素
  community: {
    weeklyDownloads: number     // 周下载量 >100k 优先
    githubStars: number         // GitHub Stars >1k 优先
    lastUpdate: Date           // 最近更新 <6个月 优先
    issues: number             // 未解决问题数量
  }
  
  // P2 - 加分项
  quality: {
    documentation: 'excellent' | 'good' | 'poor'
    testCoverage: number       // 测试覆盖率
    bundleSize: number         // 包大小影响
    performance: 'high' | 'medium' | 'low'
  }
}
```

### 实践验证案例

#### ✅ 成功案例：AI文本处理库选型
```typescript
// 时间识别：chrono-node
// ✓ 优势：544k+周下载量，React Native兼容，中文支持优秀
// ✓ 实践：支持"明天下午3点"等中文时间表达式
// ✓ 降级：失败时回退到正则表达式

// 中文分词：@node-rs/jieba  
// ✓ 优势：基于Rust的高性能实现，内存效率高
// ✓ 实践：提高政务关键词识别准确率30%+
// ✓ 备选：nodejieba作为降级方案

// 拼音转换：react-native-quick-pinyin
// ✓ 优势：轻量级，专注单一功能
// ✓ 实践：支持政务人员姓名智能索引
```

#### ❌ 避免的选择模式：
```typescript
// 过度追求新技术
❌ 选择最新发布但社区较小的库
❌ 选择功能强大但文档不全的库  
❌ 选择单一维护者的个人项目
❌ 选择包大小过大影响应用性能的库
```

### 技术债务控制策略

#### 渐进式重构原则
```typescript
// ✅ 保持API兼容性的重构示例
// 第一步：创建新的实现，保持旧接口
const legacyRuleEngine = (text: string) => { /* 旧实现 */ }
const enhancedRuleEngine = (text: string) => { /* 新实现 */ }

// 第二步：添加特性开关
const useEnhancedEngine = config.features.enhancedAI ?? false

// 第三步：逐步迁移和验证
const analyzeText = (text: string) => {
  return useEnhancedEngine 
    ? enhancedRuleEngine(text)
    : legacyRuleEngine(text)
}

// 第四步：完全替换（确认稳定后）
// const analyzeText = enhancedRuleEngine
```

#### 风险控制机制
1. **多级降级策略**：新库 → 备选库 → 基础实现 → 错误处理
2. **A/B测试验证**：新旧实现并行运行，对比结果准确性
3. **监控和回滚**：异常情况下快速回退到稳定版本
4. **测试驱动重构**：先写测试用例，确保重构不破坏功能

### 质量保证清单

#### 集成前验证
- [ ] **兼容性测试**：iOS/Android真机测试通过
- [ ] **性能基准**：对比旧实现，性能不显著下降
- [ ] **内存占用**：监控内存泄漏和峰值使用量
- [ ] **错误处理**：异常情况下的降级策略有效
- [ ] **文档更新**：API变更和使用说明同步更新

#### 集成后监控
- [ ] **准确性对比**：新旧实现结果一致性 >95%
- [ ] **响应时间**：处理时间在可接受范围内
- [ ] **错误率**：异常情况 <1%
- [ ] **用户反馈**：实际使用中的问题收集

### 团队协作规范

#### 技术决策流程
1. **问题识别**：明确当前实现的具体问题
2. **方案调研**：对比3-5个候选解决方案
3. **POC验证**：核心功能原型验证
4. **团队评审**：技术方案团队讨论
5. **分阶段实施**：渐进式迁移和验证
6. **经验总结**：记录决策过程和经验教训

记住这次实践的核心经验：**技术选型要平衡稳定性、性能和开发效率，永远准备好降级方案。**

## ⚡ 性能考虑

### 避免过早优化
```typescript
// ❌ 过早优化 - 在没有性能问题时添加复杂缓存
const memoizedExpensiveCalculation = useMemo(() => {
  return someSimpleCalculation()
}, [dependency])

// ✅ 按需优化 - 只在确实有性能问题时优化
const result = someSimpleCalculation()
```

### 合理使用优化技术
- React.memo：仅用于确实昂贵的组件
- useMemo/useCallback：仅用于复杂计算或避免重新渲染
- 代码分割：仅用于大型应用的路由级别

## 📖 最佳实践示例

### 好的实践：
```typescript
// 简单、直接、可理解
const UserProfile = ({ userId }: { userId: string }) => {
  const { data: user, isLoading } = useQuery({
    queryKey: ['user', userId],
    queryFn: () => fetchUser(userId)
  })

  if (isLoading) return <LoadingSpinner />
  if (!user) return <ErrorMessage />
  
  return (
    <View>
      <Text>{user.name}</Text>
      <Text>{user.email}</Text>
    </View>
  )
}
```

### 避免的实践：
```typescript
// 过度设计、难以理解
class UserProfileFactory {
  static create(config: UserProfileConfig): IUserProfile {
    return new UserProfileBuilder()
      .withId(config.id)
      .withRenderer(config.renderer)
      .withStateManager(config.stateManager)
      .build()
  }
}
```

记住：**简单是终极的复杂，最好的代码是没有代码。**
