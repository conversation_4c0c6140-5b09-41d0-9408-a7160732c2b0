---
description: 后端开发规范和最佳实践
globs: "backend/**/*", "server/**/*", "api/**/*"
alwaysApply: true
---

# 后端开发规范与最佳实践

## 🏗 技术架构原则

### Node.js + Express 核心规范
```typescript
// ✅ 推荐的项目结构
backend/
├── src/
│   ├── controllers/     # 控制器层
│   ├── models/         # 数据模型
│   ├── routes/         # 路由定义
│   ├── middleware/     # 中间件
│   ├── services/       # 业务逻辑
│   ├── utils/          # 工具函数
│   ├── config/         # 配置文件
│   └── types/          # TypeScript类型定义
├── tests/              # 测试文件
├── docs/               # API文档
└── .env.example        # 环境变量模板
```

### 依赖选择标准
```json
{
  "推荐技术栈": {
    "框架": "Express.js",
    "数据库ORM": "Prisma",
    "验证": "Joi 或 Zod",
    "认证": "jsonwebtoken + bcrypt",
    "文件上传": "multer",
    "日志": "winston",
    "测试": "Jest + Supertest"
  }
}
```

## 📋 API设计规范

### RESTful API设计
```typescript
// ✅ 标准REST路由结构
GET    /api/users           # 获取用户列表
GET    /api/users/:id       # 获取单个用户
POST   /api/users           # 创建用户
PUT    /api/users/:id       # 更新用户（完整）
PATCH  /api/users/:id       # 更新用户（部分）
DELETE /api/users/:id       # 删除用户

// ✅ 嵌套资源
GET    /api/users/:id/orders     # 获取用户的订单
POST   /api/users/:id/orders     # 为用户创建订单
```

**重要**：所有API设计都必须遵循项目中定义的[核心设计原则](mdc:.cursor/rules/design_principles.mdc)，特别是关于"Agent思维：API/服务即工具"的规范。

### 响应格式标准化
```typescript
// ✅ 统一的API响应格式
interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: {
    code: string
    message: string
    details?: any
  }
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// 成功响应示例
const successResponse = {
  success: true,
  data: { id: 1, name: "用户名" },
  message: "获取成功"
}

// 错误响应示例
const errorResponse = {
  success: false,
  error: {
    code: "USER_NOT_FOUND",
    message: "用户不存在"
  }
}
```

### 请求验证规范
```typescript
// ✅ 使用Joi进行请求验证
import Joi from 'joi'

const createUserSchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional()
})

// 验证中间件
const validateRequest = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body)
    if (error) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: error.details[0].message
        }
      })
    }
    next()
  }
}
```

## 🔐 安全性规范

### 认证与授权
```typescript
// ✅ JWT Token处理
const generateToken = (payload: any) => {
  return jwt.sign(payload, process.env.JWT_SECRET!, {
    expiresIn: '7d'
  })
}

// ✅ 认证中间件
const authenticateToken = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({
      success: false,
      error: { code: 'UNAUTHORIZED', message: '缺少访问令牌' }
    })
  }

  jwt.verify(token, process.env.JWT_SECRET!, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        error: { code: 'FORBIDDEN', message: '令牌无效' }
      })
    }
    req.user = user
    next()
  })
}
```

### 数据安全
```typescript
// ✅ 密码加密
import bcrypt from 'bcrypt'

const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 10
  return bcrypt.hash(password, saltRounds)
}

const comparePassword = async (password: string, hash: string): Promise<boolean> => {
  return bcrypt.compare(password, hash)
}

// ✅ 敏感数据过滤
const sanitizeUser = (user: any) => {
  const { password, ...safeUser } = user
  return safeUser
}
```

### 请求安全中间件
```typescript
// ✅ 安全中间件配置
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'
import cors from 'cors'

// CORS配置
const corsOptions = {
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}

// 请求限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: '请求过于频繁，请稍后再试'
    }
  }
})

app.use(helmet())
app.use(cors(corsOptions))
app.use(limiter)
```

## 🗄 数据库操作规范

### Prisma 最佳实践
```typescript
// ✅ 数据库服务层模式
class UserService {
  async createUser(userData: CreateUserDto) {
    try {
      return await prisma.user.create({
        data: {
          ...userData,
          password: await hashPassword(userData.password)
        },
        select: {
          id: true,
          name: true,
          email: true,
          createdAt: true
        }
      })
    } catch (error) {
      if (error.code === 'P2002') {
        throw new Error('邮箱已存在')
      }
      throw error
    }
  }

  async getUserWithOrders(userId: number) {
    return prisma.user.findUnique({
      where: { id: userId },
      include: {
        orders: {
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    })
  }
}
```

### 数据库事务处理
```typescript
// ✅ 事务操作示例
const transferMoney = async (fromUserId: number, toUserId: number, amount: number) => {
  return prisma.$transaction(async (tx) => {
    // 检查发送方余额
    const fromUser = await tx.user.findUnique({
      where: { id: fromUserId }
    })
    
    if (fromUser.balance < amount) {
      throw new Error('余额不足')
    }

    // 扣款
    await tx.user.update({
      where: { id: fromUserId },
      data: { balance: { decrement: amount } }
    })

    // 收款
    await tx.user.update({
      where: { id: toUserId },
      data: { balance: { increment: amount } }
    })

    // 记录交易
    await tx.transaction.create({
      data: {
        fromUserId,
        toUserId,
        amount,
        type: 'TRANSFER'
      }
    })
  })
}
```

## 📝 错误处理规范

### 全局错误处理
```typescript
// ✅ 统一错误处理中间件
const errorHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('API Error:', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip
  })

  // Prisma错误处理
  if (err.name === 'PrismaClientKnownRequestError') {
    return res.status(400).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: '数据库操作失败'
      }
    })
  }

  // 验证错误
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: err.message
      }
    })
  }

  // 默认服务器错误
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: '服务器内部错误'
    }
  })
}
```

### 自定义错误类
```typescript
// ✅ 业务错误类
class BusinessError extends Error {
  constructor(
    public code: string,
    public message: string,
    public statusCode: number = 400
  ) {
    super(message)
    this.name = 'BusinessError'
  }
}

// 使用示例
const findUser = async (id: number) => {
  const user = await prisma.user.findUnique({ where: { id } })
  if (!user) {
    throw new BusinessError('USER_NOT_FOUND', '用户不存在', 404)
  }
  return user
}
```

## 📊 日志与监控

### 日志配置
```typescript
// ✅ Winston日志配置
import winston from 'winston'

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ]
})

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }))
}
```

### 请求日志中间件
```typescript
// ✅ 请求日志记录
const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now()
  
  res.on('finish', () => {
    const duration = Date.now() - start
    logger.info('HTTP Request', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    })
  })
  
  next()
}
```

## 🧪 测试规范

### API测试结构
```typescript
// ✅ 测试文件示例
import request from 'supertest'
import app from '../src/app'

describe('User API', () => {
  let authToken: string
  let userId: number

  beforeAll(async () => {
    // 测试前准备
    const response = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'testpass123'
      })
    
    authToken = response.body.data.token
  })

  afterAll(async () => {
    // 清理测试数据
    await prisma.user.deleteMany({
      where: { email: { endsWith: '@test.com' } }
    })
  })

  describe('POST /api/users', () => {
    it('应该成功创建新用户', async () => {
      const userData = {
        name: '测试用户',
        email: '<EMAIL>',
        password: 'password123'
      }

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send(userData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.email).toBe(userData.email)
      expect(response.body.data.password).toBeUndefined()
      
      userId = response.body.data.id
    })

    it('应该拒绝无效的邮箱格式', async () => {
      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: '测试用户',
          email: 'invalid-email',
          password: 'password123'
        })
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })
  })
})
```

## 🔧 环境配置规范

### 环境变量管理
```bash
# ✅ .env.example 模板
NODE_ENV=development
PORT=3000

# 数据库
DATABASE_URL="postgresql://username:password@localhost:5432/dbname"

# JWT密钥
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=7d

# API密钥
ANTHROPIC_API_KEY=your_anthropic_key
PERPLEXITY_API_KEY=your_perplexity_key

# 前端地址
FRONTEND_URL=http://localhost:3000

# 文件存储
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880

# 日志配置
LOG_LEVEL=info

# 邮件服务
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
```

### 配置文件结构
```typescript
// ✅ config/index.ts
interface Config {
  port: number
  database: {
    url: string
  }
  jwt: {
    secret: string
    expiresIn: string
  }
  upload: {
    dir: string
    maxSize: number
  }
}

const config: Config = {
  port: parseInt(process.env.PORT || '3000'),
  database: {
    url: process.env.DATABASE_URL!
  },
  jwt: {
    secret: process.env.JWT_SECRET!,
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  },
  upload: {
    dir: process.env.UPLOAD_DIR || 'uploads',
    maxSize: parseInt(process.env.MAX_FILE_SIZE || '5242880')
  }
}

export default config
```

## 🚀 部署配置

### Docker配置
```dockerfile
# ✅ Dockerfile
FROM base AS builder
WORKDIR /app
COPY . .
# 安装所有依赖，包括开发依赖，用于构建
RUN yarn install --frozen-lockfile
# 运行构建脚本
RUN yarn build

# --- 生产环境 ---
FROM base
WORKDIR /app
COPY --from=builder /app/package.json /app/yarn.lock ./
# 只安装生产依赖
RUN yarn install --production --frozen-lockfile
COPY --from=builder /app/dist ./dist
# 暴露端口
EXPOSE 8080
# 启动应用
CMD ["yarn", "start"]
```

### PM2配置
```json
{
  "apps": [{
    "name": "gongzhimall-api",
    "script": "dist/index.js",
    "instances": "max",
    "exec_mode": "cluster",
    "env": {
      "NODE_ENV": "production",
      "PORT": 3000
    },
    "log_file": "logs/pm2.log",
    "out_file": "logs/pm2-out.log",
    "error_file": "logs/pm2-error.log",
    "log_date_format": "YYYY-MM-DD HH:mm:ss"
  }]
}
```

记住：**安全第一，性能第二，可维护性第三。**
