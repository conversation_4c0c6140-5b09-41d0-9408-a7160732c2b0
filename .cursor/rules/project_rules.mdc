---
description: 项目级规则约束和开发工作流程
globs: "**/*"
alwaysApply: true
---
# 项目级规则约束

## 🎯 规则应用优先级

### 1. 🔒 强制性规则（最高优先级）
这些规则必须严格遵守，任何违反都必须立即停止：

- **范围控制**：只能修改与当前任务直接相关的文件
- **文件创建限制**：绝对禁止创建未经明确要求的文件
- **需求边界**：严格限制在用户明确要求的范围内

### 2. 🛠 技术规范规则（高优先级）
技术实现必须遵循的标准：

- **重复造轮子检查**：优先使用现有解决方案
- **过度设计控制**：保持简单直接的实现
- **后端开发规范**：API设计、安全性、数据库操作标准

### 3. 📋 流程管理规则（中等优先级）
开发流程和项目管理：

- **TaskMaster工作流**：任务驱动的开发模式
- **环境配置管理**：多环境部署和配置分离
- **代码质量保证**：测试、文档、代码审查

## 📝 TaskMaster工作流最佳实践

> 基于AI规则引擎重构项目的实践经验总结

### 任务状态管理标准

#### 状态定义和转换规则
```typescript
type TaskStatus = 'pending' | 'in-progress' | 'review' | 'done' | 'deferred' | 'cancelled'

// 标准状态转换流程
const statusTransition = {
  pending: ['in-progress', 'deferred', 'cancelled'],
  'in-progress': ['review', 'pending', 'cancelled'],
  review: ['done', 'in-progress', 'pending'],
  done: [], // 终态，不可逆转
  deferred: ['pending', 'cancelled'],
  cancelled: ['pending'] // 可恢复
}
```

#### 实践经验：何时更新任务状态
```typescript
// ✅ 及时状态更新的最佳实践
{
  "开始工作时": "pending → in-progress",
  "完成主要功能": "in-progress → review", 
  "测试全部通过": "review → done",
  "发现技术债务": "保持主任务状态，子任务标记deferred",
  "遇到阻塞问题": "in-progress → pending，记录阻塞原因"
}

// ❌ 避免的状态管理错误
{
  "长期停留in-progress": "超过3天未更新状态",
  "跳过review阶段": "直接从in-progress到done",
  "状态与实际不符": "代码已完成但状态仍是pending"
}
```

### 子任务分解策略

#### 有效的子任务粒度控制
```typescript
interface SubtaskBestPractices {
  // ✅ 合适的子任务特征
  goodSubtask: {
    timeEstimate: "0.5-2天完成",
    scope: "单一功能或组件",
    testable: "独立可测试",
    deliverable: "可独立交付价值"
  }
  
  // ❌ 避免的子任务模式  
  badSubtask: {
    tooLarge: "需要超过3天完成",
    tooSmall: "30分钟内完成的修改",
    dependent: "必须与其他子任务同时完成",
    vague: "目标不明确或范围模糊"
  }
}
```

#### 实践案例：AI功能模块分解
```typescript
// ✅ 有效的分解示例 - Task 29.2
const aiModuleBreakdown = {
  "29.2.1": "集成chrono-node时间识别库",
  "29.2.2": "集成@node-rs/jieba中文分词",  
  "29.2.3": "建立政务专用词典",
  "29.2.4": "实现多级降级策略",
  "29.2.5": "编写完整测试用例覆盖"
}

// 每个子任务：
// - 可独立完成和测试
// - 有明确的完成标准  
// - 对整体功能有贡献
// - 时间可控（1-2天）
```

### 依赖关系管理

#### 依赖识别和处理原则
```typescript
// 依赖类型分类
interface DependencyTypes {
  technical: {
    example: "Task B需要Task A提供的API接口",
    handling: "严格按顺序执行，不可并行"
  },
  
  logical: {
    example: "界面设计需要在功能实现之前确定",
    handling: "优先级排序，可部分并行"
  },
  
  resource: {
    example: "需要同一个开发者完成的相关任务",
    handling: "合理安排时间，避免上下文切换"
  }
}
```

#### 依赖冲突解决策略
1. **识别阻塞链**：找出关键路径上的依赖关系
2. **优先级重排**：调整任务优先级，优先解除阻塞  
3. **并行策略**：识别可以并行进行的独立任务
4. **风险缓解**：为关键依赖准备备选方案

### 进度跟踪和报告

#### 有效的进度更新实践
```typescript
// ✅ 高质量的任务更新示例
const goodTaskUpdate = {
  timestamp: "2025-06-12T14:18:49.438Z",
  summary: "开源库集成重构工作完成",
  achievements: [
    "成功替代手写规则引擎",
    "测试覆盖率100%，所有9个测试用例通过",
    "技术方案验证：证明开源库技术栈可行性"
  ],
  technicalDetails: [
    "集成chrono-node进行智能时间识别",
    "使用@node-rs/jieba进行高性能中文分词", 
    "建立政务领域专用词典"
  ],
  nextSteps: "推进29.3（Android ONNX Runtime集成）",
  blockers: [] // 无阻塞问题
}
```

### 质量控制集成

#### TaskMaster与代码质量的联动
```typescript
// 质量门禁检查点
const qualityGates = {
  beforeReview: [
    "所有相关测试用例通过",
    "代码覆盖率满足要求", 
    "TypeScript类型检查无错误",
    "符合项目代码规范"
  ],
  
  beforeDone: [
    "功能验收测试通过",
    "性能指标满足预期",
    "文档更新同步完成",
    "没有引入新的技术债务"
  ]
}
```

### 迭代节奏控制

#### 周迭代价值交付策略
```typescript
interface WeeklyDelivery {
  week1: {
    goal: "基础功能可用",
    deliverable: "核心API和基础测试",
    demoable: "功能原型演示"
  },
  
  week2: {
    goal: "生产环境就绪", 
    deliverable: "完整功能和集成测试",
    demoable: "用户体验演示"
  },
  
  weekly_review: {
    retrospective: "分析本周完成情况",
    adjustment: "调整下周任务优先级",
    documentation: "更新项目文档和规则"
  }
}
```

记住TaskMaster实践的核心：**任务驱动，状态透明，价值导向，持续改进。**

## 🚫 常见违规行为警告

### 立即停止的行为标识
如果AI开始说以下语句，必须立即停止：
- "顺便优化一下..."
- "我还添加了..."
- "为了更好的实践..."
- "这样更安全..."
- "让我创建一个配置文件..."
- "我来重构一下这部分代码..."

### 违规行为检查清单
每次开始编码前，检查：
- [ ] 是否明确理解了用户的具体需求？
- [ ] 计划修改的文件是否都与任务直接相关？
- [ ] 是否准备创建任何新文件？（需要明确理由）
- [ ] 是否准备重构或优化现有代码？（需要明确要求）
- [ ] 是否准备添加额外的功能或配置？（需要明确要求）

## ✅ 正确的工作流程

### 开始任何工作前
```
1. 📖 明确理解：准确理解用户要求什么
2. 🎯 确定范围：识别完成任务的最小必要更改
3. 📝 列出计划：明确需要修改的文件和具体更改
4. ✅ 获得确认：向用户确认理解是否正确
```

### 执行过程中
```
1. 🔍 持续检查：每次修改前确认是否直接解决用户问题
2. 🛑 及时停止：发现需要额外更改时立即询问用户
3. 📋 记录变更：清楚记录所有修改和决策理由
```

### 完成后
```
1. 🔍 审查更改：确保没有超出范围的修改
2. 🧹 清理代码：移除任何不必要的更改
3. 📊 总结结果：向用户报告完成的具体内容
```

## 🎯 项目特定规则

### 公职猫项目规则
- **技术栈限制**：React Native + Electron + Node.js + PostgreSQL
- **架构模式**：前后端分离，移动端与桌面端共享组件
- **开发模式**：TaskMaster驱动的任务式开发

### 🎯 用户价值导向强制性规则（新增）
**所有开发活动必须严格遵循以下用户价值导向原则：**

1. **颜值优先原则**
   - UI/UX设计任务永远优先于技术债务
   - 产品"颜值"直接影响投资人和种子用户的第一印象
   - 界面必须体现"高级感"和"简单亲切"的交互

2. **周迭代价值交付**
   - 每周必须交付对用户可见的价值增量
   - 技术改进必须与用户可感知的改进挂钩
   - 禁止纯技术优化占用迭代周期

3. **产品灵魂体现**
   - 所有界面设计必须体现"U盘级私密，秘书般懂你"
   - 交互设计要让用户感觉产品"懂行"和"可信"
   - 避免冷冰冰的技术产品感觉

4. **投资人价值可见**
   - 任何开发成果都要考虑如何向投资人展示价值
   - 优先开发可演示的功能而非底层技术
   - 重视产品的商业价值传达

### 代码组织原则
```
mobile/          # React Native移动端
desktop/         # Electron桌面端
backend/         # Node.js后端API
shared/          # 共享组件和逻辑
```

## 🤖 自动维护机制

### 📋 自动更新触发条件
- 当检测到 `.cursor/rules/` 目录下有新增、删除或重命名的 `.mdc` 文件时
- 当现有规则文件的描述或用途发生重大变化时
- 当项目结构或技术栈发生变化影响规则分类时
- 当用户明确要求更新规则索引时

### ⚡ 自动维护指令
**当AI检测到规则文件变化时，应该：**
1. 立即扫描 `.cursor/rules/` 目录
2. 对比当前索引与实际文件的差异
3. 自动更新"规则文件索引"部分
4. 保持其他内容不变
5. 在文件末尾添加更新时间戳

## 🔄 规则更新机制

### 自动化维护
- **自动扫描**：脚本自动扫描 `.cursor/rules/` 目录
- **自动分类**：根据预设分类自动组织规则文件
- **时间戳**：自动添加更新时间戳
- **运行方式**：`yarn update-rules-index`

### 何时更新规则
- 发现新的常见错误模式
- 项目技术栈或架构变更
- 开发流程优化需要
- 团队规范标准化

### 更新流程
1. **识别问题**：记录具体的违规行为或需要改进的地方
2. **分析影响**：评估问题的影响范围和频率
3. **设计规则**：制定具体、可执行的规则条目
4. **测试验证**：在实际开发中验证规则有效性
5. **文档更新**：运行 `yarn update-rules-index` 自动更新索引

### 规则质量标准
- **具体可执行**：有明确的行为指导
- **易于理解**：开发者能快速理解和应用
- **实例充足**：提供足够的正反面例子
- **与时俱进**：随项目发展持续更新

## 📖 使用指南

### 新团队成员
1. 阅读规则索引文件了解整体框架
2. 重点学习scope_control.mdc和code_quality.mdc
3. 根据工作内容选择相关规则深入学习
4. 在实际工作中应用规则并反馈问题

### 日常开发
1. 开始工作前快速浏览相关规则
2. 遇到问题时查阅对应规则文件
3. 发现规则遗漏或不当时及时反馈
4. 定期回顾规则执行效果

### 项目管理
1. 将规则执行情况纳入代码审查
2. 定期组织规则学习和讨论
3. 收集和分析规则违反情况
4. 持续优化和更新规则体系

## 🎖 最佳实践总结

记住这些核心原则：
- **做得少但做得对，比做得多但做错了要好得多**
- **简单是终极的复杂，最好的代码是没有代码**
- **任务驱动，需求明确，范围受控**
- **优先复用，避免重造，保持简洁**
- **安全第一，性能第二，可维护性第三**
- **配置即代码，安全第一，环境隔离**

---

*这套规则体系旨在提高开发效率，减少错误，确保代码质量。请严格遵守，共同维护良好的开发环境。*
