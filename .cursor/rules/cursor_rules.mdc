---
description: Guidelines for creating and maintaining Cursor rules to ensure consistency and effectiveness.
globs: .cursor/rules/*.mdc
alwaysApply: true
---
---
description: Cursor Rules基础结构规范
globs: "**/*.mdc"
alwaysApply: true
---

# Cursor Rules 编写规范

## 📋 规则文件结构

### 必需的文件头
每个规则文件（.mdc）必须包含以下头部：
```markdown
---
description: Clear, one-line description of what the rule enforces
globs: path/to/files/*.ext, other/path/**/*
alwaysApply: boolean
---
```

### 文件命名
- 使用小写字母和下划线
- 名称应清晰表达规则的用途
- 例如：`backend_rules.mdc`, `code_quality.mdc`

## 📝 内容编写规范

### 文件引用
- 使用 `[filename](mdc:path/to/file)` 语法引用其他文件
- 例如：`[prisma.mdc](mdc:.cursor/rules/prisma.mdc)`
- 代码文件：`[schema.prisma](mdc:prisma/schema.prisma)`

### 代码示例
```typescript
// ✅ DO: 展示正确的做法
const goodExample = true;

// ❌ DON'T: 展示错误的模式
const badExample = false;
```

### 内容组织
1. **概述**：开始时提供高层次的概述
2. **具体规则**：列出具体、可执行的要求
3. **示例**：提供实际的代码示例
4. **引用**：适当引用其他规则或实际代码
5. **维护说明**：如何保持规则更新

## ✅ 最佳实践

### 编写原则
- 保持简洁：每条规则应该简单明了
- 可执行性：规则应该是具体可执行的
- 实例导向：优先使用实际代码作为示例
- 交叉引用：适当引用其他相关规则

### 格式规范
- 使用Markdown格式
- 使用emoji增加可读性
- 使用缩进保持层次结构
- 使用代码块展示示例

### 维护建议
- 定期更新过时的规则
- 添加新发现的模式
- 保持示例代码的时效性
- 确保所有引用都是有效的

## 🔄 规则文件模板

```markdown
---
description: 规则的简短描述
globs: "相关文件匹配模式"
alwaysApply: true/false
---

# 规则标题

## 🎯 核心原则
- 主要原则1
- 主要原则2

## 📋 具体要求
### 要求类别1
- 详细要求
- 示例代码

### 要求类别2
- 详细要求
- 示例代码

## ✅ 最佳实践
- 实践1
- 实践2

## ⚠️ 常见问题
- 问题1及解决方案
- 问题2及解决方案
```

## 📚 相关规则文件

更多具体规则请参考：
- **[project_rules.mdc](mdc:.cursor/rules/project_rules.mdc)** - 项目级规则约束和开发工作流程
- **[design_principles.mdc](mdc:.cursor/rules/design_principles.mdc)** - 项目核心设计原则与架构思想
- **[scope_control.mdc](mdc:.cursor/rules/scope_control.mdc)** - 防止AI超出需求范围和乱改代码
- **[code_quality.mdc](mdc:.cursor/rules/code_quality.mdc)** - 防止重复造轮子和过度设计
- **[self_improve.mdc](mdc:.cursor/rules/self_improve.mdc)** - 规则自我改进机制 