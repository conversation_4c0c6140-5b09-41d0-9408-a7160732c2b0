---
description: Guidelines for continuously improving Cursor rules based on emerging code patterns and best practices.
globs: **/*
alwaysApply: true
---

---
description: 规则自我改进机制和文档自动维护
globs: "**/*"
alwaysApply: true
---

# 规则自我改进机制和文档自动维护

## 🔄 文档自动维护规则

### README文件索引自动更新
- **触发条件**：
  - 检测到目录下有新增、删除或重命名的`.md`文件
  - 文件内容发生重大变化影响描述
  - 用户明确要求更新文档索引

- **自动更新范围**：
  - `界面设计效果示意/README.md` - 界面文件索引
  - `.cursor/rules/README.mdc` - 规则文件索引
  - 其他包含文件列表的README文档

- **更新流程**：
  1. **扫描目录**：自动扫描相关目录下的文件
  2. **读取描述**：提取文件头部的描述信息
  3. **智能分类**：根据文件名和内容自动归类
  4. **更新索引**：自动更新文件列表部分
  5. **保持结构**：保持现有的文档结构和格式
  6. **添加时间戳**：记录更新时间和原因

- **实施要求**：
  - 每次修改文件结构时，主动检查相关README是否需要更新
  - 发现文件列表与实际不符时，立即更新
  - 保持链接的有效性和描述的准确性
  - 维护正确的编号顺序和分类

### 界面设计文档特殊规则
- **编号维护**：当添加新界面文件时，自动调整后续编号
- **总数更新**：自动更新界面总数统计
- **逻辑顺序**：按使用频率和用户流程调整界面顺序
- **链接验证**：确保所有链接指向实际存在的文件

## 🎯 规则改进机制

- **Rule Improvement Triggers:**
  - New code patterns not covered by existing rules
  - Repeated similar implementations across files
  - Common error patterns that could be prevented
  - New libraries or tools being used consistently
  - Emerging best practices in the codebase

- **Analysis Process:**
  - Compare new code with existing rules
  - Identify patterns that should be standardized
  - Look for references to external documentation
  - Check for consistent error handling patterns
  - Monitor test patterns and coverage

- **Rule Updates:**
  - **Add New Rules When:**
    - A new technology/pattern is used in 3+ files
    - Common bugs could be prevented by a rule
    - Code reviews repeatedly mention the same feedback
    - New security or performance patterns emerge

  - **Modify Existing Rules When:**
    - Better examples exist in the codebase
    - Additional edge cases are discovered
    - Related rules have been updated
    - Implementation details have changed

- **Example Pattern Recognition:**
  ```typescript
  // If you see repeated patterns like:
  const data = await prisma.user.findMany({
    select: { id: true, email: true },
    where: { status: 'ACTIVE' }
  });
  
  // Consider adding to [prisma.mdc](mdc:.cursor/rules/prisma.mdc):
  // - Standard select fields
  // - Common where conditions
  // - Performance optimization patterns
  ```

- **Rule Quality Checks:**
  - Rules should be actionable and specific
  - Examples should come from actual code
  - References should be up to date
  - Patterns should be consistently enforced

- **Continuous Improvement:**
  - Monitor code review comments
  - Track common development questions
  - Update rules after major refactors
  - Add links to relevant documentation
  - Cross-reference related rules

- **Rule Deprecation:**
  - Mark outdated patterns as deprecated
  - Remove rules that no longer apply
  - Update references to deprecated rules
  - Document migration paths for old patterns

- **Documentation Updates:**
  - Keep examples synchronized with code
  - Update references to external docs
  - Maintain links between related rules
  - Document breaking changes

Follow [cursor_rules.mdc](mdc:.cursor/rules/cursor_rules.mdc) for proper rule formatting and structure.