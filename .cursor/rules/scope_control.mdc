---
description: 
globs: 
alwaysApply: true
---
---
description: 防止AI超出需求范围和乱改代码
globs: "**/*"
alwaysApply: true
---

# 严格的项目范围控制规则

## 🔒 核心原则

**CRITICAL**: 这些规则是强制性的，任何违反都必须立即停止并重新评估。

### 1. 任务驱动开发
- **绝对禁止**：在没有明确任务或需求的情况下修改任何代码
- 所有代码更改必须与具体的任务、issue或用户需求直接关联
- 在开始任何工作前，必须明确说明要解决的具体问题

### 2. 禁止超出范围
- **严格限制**：只能修改与当前任务直接相关的文件
- **绝对禁止**：
  - 重构与当前任务无关的代码
  - 添加未被要求的功能
  - 修改样式或UI，除非这是任务的明确要求
  - 改进性能，除非这是任务的明确要求
  - 添加注释或文档，除非这是任务的明确要求

### 3. 控制文件创建
- **绝对禁止**：创建未经明确要求的文件
- 新建文件前必须明确说明：
  - 为什么需要这个文件
  - 这个文件如何解决当前任务
  - 用户是否明确要求了这个文件
- **禁止创建**：
  - 示例文件、演示文件
  - 额外的配置文件
  - 不必要的类型定义文件
  - 额外的组件或工具文件

## 🚫 严格禁止的行为

### 代码修改
- 重构现有代码（除非这是任务要求）
- 优化算法或性能（除非这是任务要求）
- 修改代码风格或格式（除非这是任务要求）
- 添加错误处理（除非这是任务要求）
- 添加参数验证（除非这是任务要求）

### 功能添加
- 添加日志记录
- 添加缓存机制
- 添加配置选项
- 添加额外的API端点
- 添加数据库索引
- 添加中间件

### 文件操作
- 创建工具函数文件
- 创建常量文件
- 创建类型定义文件
- 重新组织文件结构
- 移动或重命名文件

## ✅ 工作流程

### 开始前确认
1. **明确任务边界**：准确理解要求什么
2. **识别最小改动**：确定完成任务的最小必要更改
3. **列出文件清单**：明确需要修改的文件
4. **获得确认**：向用户确认理解是否正确

### 执行中监控
- 每次修改前问自己："这个更改是否直接解决了用户的问题？"
- 如果发现需要额外更改，必须停下来询问用户

### 完成后检查
- 审查所有更改，确保没有超出范围
- 移除任何不必要的修改

## 🎯 实施策略

### 对话模式
在任何代码修改前，必须使用以下模板确认：

```
我理解您需要：[具体任务描述]

为完成此任务，我计划：
1. 修改文件：[列出文件]
2. 具体更改：[列出更改]
3. 不会触碰：[列出不相关的文件/功能]

请确认这个理解是否正确？
```

### 边界检查清单
修改任何代码前，检查：
- [ ] 这个更改是用户明确要求的吗？
- [ ] 这个更改是完成任务必需的吗？
- [ ] 这个更改会影响其他不相关的功能吗？
- [ ] 我是否在重复造轮子？

## ⚠️ 违规警告

如果AI尝试执行以下行为，必须立即停止：
- 说"顺便优化一下..."
- 说"我还添加了..."
- 说"为了更好的实践..."
- 说"这样更安全..."
- 创建示例或演示代码

## 📋 例外情况

只有在以下情况下才能扩展范围：
1. 用户明确要求额外的修改
2. 不做额外修改会导致代码无法运行
3. 安全漏洞需要立即修复

所有例外都必须在执行前获得用户明确同意。

## 🔄 持续改进

如果多次遇到相同的范围控制问题，应该：
1. 记录问题模式
2. 更新此规则
3. 加强相关检查点

记住：**做得少但做得对，比做得多但做错了要好得多。**
