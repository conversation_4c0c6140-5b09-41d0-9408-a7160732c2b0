{"mcpServers": {"taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"OPENROUTER_API_KEY": "${OPENROUTER_API_KEY}", "ANTHROPIC_API_KEY": "${OPENROUTER_API_KEY}", "PERPLEXITY_API_KEY": "${OPENROUTER_API_KEY}", "OPENAI_API_KEY": "${OPENROUTER_API_KEY}", "GOOGLE_API_KEY": "${OPENROUTER_API_KEY}", "MISTRAL_API_KEY": "${OPENROUTER_API_KEY}", "XAI_API_KEY": "${OPENROUTER_API_KEY}", "AZURE_OPENAI_API_KEY": "${OPENROUTER_API_KEY}"}}, "time-mcp": {"command": "npx", "args": ["-y", "time-mcp"]}, "figma-mcp": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=*********************************************", "--st<PERSON>"]}, "XcodeBuildMCP": {"command": "npx", "args": ["-y", "xcodebuildmcp@latest"]}}}