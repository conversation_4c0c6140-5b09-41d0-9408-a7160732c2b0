{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "javascript.suggest.autoImports": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true, "source.organizeImports": true}, "files.exclude": {"**/node_modules": true, "**/ios/build": true, "**/android/build": true, "**/android/.gradle": true, "**/.git": false}, "search.exclude": {"**/node_modules": true, "**/ios/build": true, "**/android/build": true, "**/android/.gradle": true}, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "react-native.packager.port": 8081, "react-native.android.runArguments.simulator": "--simulator", "react-native.ios.runArguments.simulator": "--simulator", "eslint.workingDirectories": ["mobile/GongZhiMallMobile", "desktop"], "typescript.preferences.importModuleSpecifier": "relative", "terminal.integrated.cwd": "${workspaceFolder}", "terminal.integrated.defaultProfile.osx": "zsh", "terminal.integrated.profiles.osx": {"zsh": {"path": "/bin/zsh", "args": ["-l"]}}}