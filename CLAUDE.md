# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

公职猫 (GongZhiMall) is a multi-platform productivity suite for government workers, featuring a "local-first, cloud-assisted" hybrid architecture with four AI layers (rule engine → local AI → cloud AI → professional knowledge base).

## Architecture

### Multi-Platform Structure
- **Mobile** (`mobile/`) - React Native 0.79.3 + TypeScript, primary user interface
- **Desktop** (`desktop/`) - Electron + React + TypeScript, document processing and large screen experience  
- **Backend API** (`backend/`) - Node.js + Express, core business logic
- **WeChat Service** (`backend/wechat/`) - Enterprise WeChat integration service
- **Admin Web** (`admin-web/`) - React management dashboard

### Key Technical Stack
- **Mobile**: React Native + Zustand + WatermelonDB + SQLite
- **Desktop**: Electron + better-sqlite3-multiple-ciphers (AES-256 encryption)
- **Backend**: Node.js + Express + MySQL (cloud) + SQLite (local)
- **State Management**: Zustand for mobile, React Context for web

## Development Commands

### Mobile Development
```bash
cd mobile
yarn install
yarn start          # Start Metro server
yarn ios            # Launch iOS simulator
yarn android        # Launch Android emulator
yarn test           # Run mobile tests
```

### Desktop Development
```bash
cd desktop
yarn install
yarn dev            # Start Electron in development mode
yarn test           # Run desktop tests
```

### Backend Services
```bash
# Main backend API (port 3001)
cd backend
yarn install
yarn dev

# WeChat forwarding service (port 3000)
cd backend/wechat
yarn install
yarn dev
yarn test           # Run backend integration tests

# Admin web (port 3002)
cd admin-web
yarn install
yarn start
```

### Production Environment Setup
```bash
./scripts/setup-production-env.sh    # Setup production environment variables
./scripts/audit-permissions.sh       # Run security permission audit
```

## Key Services Architecture

### WeChat Service Layer (`backend/wechat/service/`)
- **WebhookService** - Enterprise WeChat webhook processing
- **MessageProcessService** - Message classification and processing
- **UserBindingService** - User binding management 
- **MessageSyncService** - Cross-device message synchronization
- **MediaDownloadService** - Media file download and processing

### Mobile Services (`mobile/src/services/`)
- **OCRService** - Text recognition (iOS Vision + Android MLKit)
- **AIBridge** - Four-layer AI architecture coordination
- **VoiceCommandParser** - Voice command processing
- **SignatureService** - Digital signature and encryption

## Configuration Management

### Environment Variables
Projects use layered configuration management:

**Mobile** (`mobile/.env`):
- `JPUSH_APP_KEY` - JPush notification key
- `WECHAT_CORP_ID` - Enterprise WeChat corp ID
- `WECHAT_BINDING_API_BASE_URL` - WeChat binding API endpoint

**WeChat Backend** (`backend/wechat/.env`):
- `WECHAT_CORP_ID`, `WECHAT_CORP_SECRET`, `WECHAT_TOKEN` - Enterprise WeChat config
- `MYSQL_HOST`, `MYSQL_USER`, `MYSQL_DATABASE` - Database config
- `JPUSH_APP_KEY`, `JPUSH_MASTER_SECRET` - Push notification config

## Database Architecture

### Multi-Database Strategy
- **Desktop**: better-sqlite3-multiple-ciphers with AES-256 encryption
- **Mobile**: WatermelonDB + SQLite with system-level secure storage
- **Backend**: MySQL for cloud data, SQLite for local caching

### Key Tables
- `users` - User account management
- `messages` - Cross-platform message sync
- `files` - File metadata and references
- `system_logs` - Partitioned by year for compliance

## Security Features

### Data Encryption
- Desktop uses AFS-256 encryption via better-sqlite3-multiple-ciphers
- Mobile leverages system keychain/keystore for sensitive data
- Cross-device sync uses WebRTC P2P with end-to-end encryption

### Privacy Design
- 100% local data storage as primary architecture
- Cloud sync is optional and user-controlled
- Anonymous telemetry only, no personal data collection

## Testing Strategy

### Test Locations
- `mobile/src/services/__tests__/` - Mobile service unit tests
- `backend/wechat/test-files/` - Backend integration tests  
- `desktop/tests/` - Desktop functionality tests

### Test Commands
```bash
yarn test                          # Run all tests
cd mobile && yarn test             # Mobile tests only
cd backend/wechat && yarn test     # Backend integration tests
cd desktop && yarn test            # Desktop tests only
```

## Development Workflow

### Cursor Rules Integration
The project includes comprehensive Cursor development rules:
- `.cursor/rules/project_rules.mdc` - Project-level standards
- `.cursor/rules/dev_workflow.mdc` - Development workflow
- `.cursor/rules/backend_rules.mdc` - Backend development standards
- `.cursor/rules/code_quality.mdc` - Code quality guidelines

### Git Workflow
- Main branch: `master`
- Development branch: `develop` 
- Feature branches: `feature/feature-name`
- Commit format: `type: description` (e.g., `feat: add OCR service`)

## Cross-Platform Sync Architecture

### Sync Strategies
- **WebRTC P2P**: Office-home cross-network sync
- **mDNS LAN**: Same WiFi device collaboration  
- **Bluetooth/USB**: Offline data transfer

### Four-Layer AI Architecture
1. **Rule Engine** (fallback): Keyword matching + pattern recognition
2. **Local AI** (lightweight): ONNX Runtime + quantized models
3. **Cloud AI** (optional): Alibaba Cloud Bailian/Tencent Cloud Hunyuan
4. **Professional Knowledge Base**: Government-specific dictionaries

## Key Dependencies

### Mobile Critical Dependencies
- `react-native-vision-camera` - Camera integration
- `@node-rs/jieba` - Chinese word segmentation
- `chrono-node` - Time/date recognition
- `react-native-permissions` - Permission management
- `jpush-react-native` - Push notifications

### Backend Critical Dependencies
- `better-sqlite3-multiple-ciphers` - Encrypted database
- `mysql2` - MySQL connectivity
- `express` - Web framework
- `node-cron` - Scheduled tasks

## Important Notes

- Always check environment variables are properly configured before starting services
- Mobile app requires native dependencies - run `npx react-native doctor` to verify setup
- Desktop encryption requires secure key storage - ensure Electron safeStorage is available
- WeChat service requires valid enterprise WeChat credentials for webhook processing
- All file uploads go through OCR processing pipeline for content analysis