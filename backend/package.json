{"name": "gongzhimall-backend", "version": "1.0.0", "description": "公职猫后端API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["gongzhimall", "api", "backend"], "author": "<EMAIL>", "license": "PRIVATE", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "sqlite3": "^5.1.7"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.3"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["wechat/**/*.js", "!wechat/**/*.test.js"], "testMatch": ["**/wechat/**/*.test.js"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}