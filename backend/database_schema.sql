-- 公职猫 (GongZhiMall) 数据库设计
-- 版本: 1.0
-- 创建日期: 2025-06-09
-- 描述: 支持"U盘级私密"的个人AI助理数据库设计

-- ============================================================================
-- 1. 用户档案管理模块
-- ============================================================================

-- 用户基础信息表
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_uuid TEXT UNIQUE NOT NULL,           -- 用于多端同步的唯一标识
    username TEXT,                            -- 用户名（加密存储）
    employee_id TEXT,                         -- 工号（加密存储）
    department TEXT,                          -- 部门（加密存储）
    position TEXT,                            -- 职务（加密存储）
    phone TEXT,                               -- 手机号（加密存储）
    email TEXT,                               -- 邮箱（加密存储）
    avatar_path TEXT,                         -- 头像文件路径（加密存储）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    sync_version INTEGER DEFAULT 1,          -- 同步版本号
    is_active INTEGER DEFAULT 1              -- 账户状态
);

-- 个人档案文件表
CREATE TABLE user_documents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    document_type TEXT NOT NULL,              -- 文档类型: id_card_front, id_card_back, photo, certificate, resume, etc.
    file_name TEXT,                           -- 原始文件名（加密存储）
    file_path TEXT,                           -- 文件存储路径（加密存储）
    file_size INTEGER,                        -- 文件大小（字节）
    file_hash TEXT,                           -- 文件SHA256哈希值，用于完整性校验
    mime_type TEXT,                           -- 文件MIME类型
    metadata TEXT,                            -- JSON格式的元数据（加密存储）
    description TEXT,                         -- 文档描述（加密存储）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    sync_version INTEGER DEFAULT 1,
    is_deleted INTEGER DEFAULT 0,            -- 软删除标记
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 用户偏好设置表
CREATE TABLE user_preferences (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    preference_category TEXT NOT NULL,        -- 偏好分类: ui, ai, notification, security
    preference_key TEXT NOT NULL,            -- 偏好键名
    preference_value TEXT,                    -- 偏好值（加密存储）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, preference_category, preference_key)
);

-- ============================================================================
-- 2. 文档管理模块
-- ============================================================================

-- 导入文档表
CREATE TABLE documents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    title TEXT,                               -- 文档标题（加密存储）
    file_name TEXT,                           -- 原始文件名（加密存储）
    file_path TEXT,                           -- 文件存储路径（加密存储）
    file_size INTEGER,                        -- 文件大小
    file_hash TEXT,                           -- 文件哈希值
    mime_type TEXT,                           -- 文件类型
    document_category TEXT,                   -- 文档分类: meeting, report, notice, plan, etc.
    source_type TEXT,                         -- 来源类型: upload, ocr, import
    extracted_text TEXT,                      -- 提取的文本内容（用于全文检索）
    summary TEXT,                             -- AI生成的摘要（加密存储）
    tags TEXT,                                -- 标签，JSON数组格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    sync_version INTEGER DEFAULT 1,
    is_deleted INTEGER DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 文档全文检索表（使用FTS5）
CREATE VIRTUAL TABLE documents_fts USING fts5(
    document_id,
    title,
    content,
    tags,
    content='documents',
    content_rowid='id'
);

-- 文档分类规则表
CREATE TABLE document_classification_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,
    keywords TEXT NOT NULL,                   -- 关键词，JSON数组格式
    category TEXT NOT NULL,                   -- 分类结果
    confidence_threshold REAL DEFAULT 0.6,   -- 置信度阈值
    is_active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- 3. 任务管理模块
-- ============================================================================

-- 任务表
CREATE TABLE tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    task_uuid TEXT UNIQUE NOT NULL,          -- 任务唯一标识
    title TEXT,                               -- 任务标题（加密存储）
    description TEXT,                         -- 任务描述（加密存储）
    user_role TEXT NOT NULL,                 -- 用户角色: host(主办), assistant(协办), participant(参与)
    task_type TEXT,                           -- 任务类型: meeting, document, event, etc.
    priority INTEGER DEFAULT 2,              -- 优先级: 1=高, 2=中, 3=低
    status TEXT DEFAULT 'pending',           -- 状态: pending, in_progress, completed, cancelled
    start_date DATETIME,                      -- 开始时间
    due_date DATETIME,                        -- 截止时间
    completion_date DATETIME,                 -- 完成时间
    source_document_id INTEGER,              -- 来源文档ID
    parent_task_id INTEGER,                  -- 父任务ID（用于子任务）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    sync_version INTEGER DEFAULT 1,
    is_deleted INTEGER DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (source_document_id) REFERENCES documents(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_task_id) REFERENCES tasks(id) ON DELETE CASCADE
);

-- 任务步骤表（用于主办任务的详细步骤拆解）
CREATE TABLE task_steps (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    step_order INTEGER NOT NULL,             -- 步骤顺序
    step_title TEXT,                          -- 步骤标题（加密存储）
    step_description TEXT,                    -- 步骤描述（加密存储）
    status TEXT DEFAULT 'pending',           -- 步骤状态
    due_date DATETIME,                        -- 步骤截止时间
    completed_at DATETIME,                    -- 完成时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
);

-- 任务材料清单表
CREATE TABLE task_materials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    material_name TEXT,                       -- 材料名称（加密存储）
    material_description TEXT,               -- 材料描述（加密存储）
    is_required INTEGER DEFAULT 1,           -- 是否必需
    status TEXT DEFAULT 'pending',           -- 状态: pending, prepared, submitted
    document_id INTEGER,                      -- 关联的文档ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL
);

-- ============================================================================
-- 4. 日程管理模块
-- ============================================================================

-- 日程事件表
CREATE TABLE calendar_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    event_uuid TEXT UNIQUE NOT NULL,         -- 事件唯一标识
    title TEXT,                               -- 事件标题（加密存储）
    description TEXT,                         -- 事件描述（加密存储）
    location TEXT,                            -- 地点（加密存储）
    start_time DATETIME NOT NULL,            -- 开始时间
    end_time DATETIME,                        -- 结束时间
    is_all_day INTEGER DEFAULT 0,            -- 是否全天事件
    event_type TEXT,                          -- 事件类型: meeting, deadline, reminder
    related_task_id INTEGER,                 -- 关联任务ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    sync_version INTEGER DEFAULT 1,
    is_deleted INTEGER DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (related_task_id) REFERENCES tasks(id) ON DELETE SET NULL
);

-- 提醒设置表
CREATE TABLE reminders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    target_type TEXT NOT NULL,               -- 提醒目标类型: task, event
    target_id INTEGER NOT NULL,              -- 目标ID
    reminder_time DATETIME NOT NULL,         -- 提醒时间
    reminder_type TEXT NOT NULL,             -- 提醒类型: notification, email, sms
    message TEXT,                             -- 提醒消息（加密存储）
    status TEXT DEFAULT 'pending',           -- 状态: pending, sent, cancelled
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- ============================================================================
-- 5. 规则引擎模块
-- ============================================================================

-- 角色识别规则表
CREATE TABLE role_identification_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,
    keywords TEXT NOT NULL,                   -- 关键词，JSON数组格式
    context_patterns TEXT,                    -- 上下文模式，JSON格式
    role_result TEXT NOT NULL,               -- 角色结果: host, assistant, participant
    confidence_score REAL DEFAULT 0.8,       -- 置信度分数
    priority INTEGER DEFAULT 1,              -- 规则优先级
    is_active INTEGER DEFAULT 1,
    usage_count INTEGER DEFAULT 0,           -- 使用次数统计
    success_count INTEGER DEFAULT 0,         -- 成功识别次数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 关键词词典表
CREATE TABLE keyword_dictionary (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    category TEXT NOT NULL,                  -- 词典分类: role, task_type, priority, etc.
    keyword TEXT NOT NULL,                   -- 关键词
    weight REAL DEFAULT 1.0,                 -- 权重
    synonyms TEXT,                            -- 同义词，JSON数组格式
    is_active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 用户学习记录表（记录用户的角色选择偏好）
CREATE TABLE user_learning_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    input_text TEXT,                          -- 输入文本（加密存储）
    predicted_role TEXT,                      -- AI预测的角色
    actual_role TEXT,                         -- 用户实际选择的角色
    context_info TEXT,                        -- 上下文信息，JSON格式
    feedback_score INTEGER,                  -- 用户反馈分数 1-5
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- ============================================================================
-- 6. 多端同步模块
-- ============================================================================

-- 同步日志表
CREATE TABLE sync_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    device_id TEXT NOT NULL,                 -- 设备唯一标识
    device_type TEXT NOT NULL,               -- 设备类型: mobile, desktop
    sync_type TEXT NOT NULL,                 -- 同步类型: full, incremental
    table_name TEXT NOT NULL,                -- 同步的表名
    record_id INTEGER NOT NULL,              -- 记录ID
    operation TEXT NOT NULL,                 -- 操作类型: insert, update, delete
    sync_status TEXT DEFAULT 'pending',      -- 同步状态: pending, success, failed, conflict
    conflict_resolution TEXT,                -- 冲突解决方案
    sync_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    error_message TEXT,                       -- 错误信息
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 设备注册表
CREATE TABLE registered_devices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    device_id TEXT UNIQUE NOT NULL,          -- 设备唯一标识
    device_name TEXT,                         -- 设备名称（加密存储）
    device_type TEXT NOT NULL,               -- 设备类型
    platform TEXT,                           -- 平台: ios, android, windows, linux
    last_sync_time DATETIME,                 -- 最后同步时间
    is_active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 冲突解决记录表
CREATE TABLE conflict_resolutions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    table_name TEXT NOT NULL,
    record_id INTEGER NOT NULL,
    conflict_type TEXT NOT NULL,             -- 冲突类型: version, content, delete
    local_data TEXT,                          -- 本地数据，JSON格式
    remote_data TEXT,                         -- 远程数据，JSON格式
    resolution_strategy TEXT,                -- 解决策略: local_wins, remote_wins, merge, manual
    resolved_data TEXT,                       -- 解决后的数据，JSON格式
    resolved_at DATETIME,
    resolved_by TEXT,                         -- 解决方式: auto, user
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);


-- ============================================================================
-- 7. 系统配置和日志
-- ============================================================================

-- 系统配置表
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key TEXT UNIQUE NOT NULL,
    config_value TEXT,
    config_type TEXT DEFAULT 'string',       -- 配置类型: string, integer, boolean, json
    description TEXT,
    is_encrypted INTEGER DEFAULT 0,          -- 是否加密存储
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 操作日志表
CREATE TABLE operation_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    operation_type TEXT NOT NULL,            -- 操作类型: login, logout, create, update, delete
    table_name TEXT,                          -- 操作的表名
    record_id INTEGER,                        -- 操作的记录ID
    operation_details TEXT,                   -- 操作详情，JSON格式
    ip_address TEXT,                          -- IP地址
    user_agent TEXT,                          -- 用户代理
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- ============================================================================
-- 8. 索引创建
-- ============================================================================

-- 用户表索引
CREATE INDEX idx_users_uuid ON users(user_uuid);
CREATE INDEX idx_users_employee_id ON users(employee_id);

-- 文档表索引
CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_documents_category ON documents(document_category);
CREATE INDEX idx_documents_created_at ON documents(created_at);
CREATE INDEX idx_documents_hash ON documents(file_hash);

-- 任务表索引
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_tasks_uuid ON tasks(task_uuid);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
CREATE INDEX idx_tasks_role ON tasks(user_role);

-- 日程表索引
CREATE INDEX idx_calendar_events_user_id ON calendar_events(user_id);
CREATE INDEX idx_calendar_events_start_time ON calendar_events(start_time);
CREATE INDEX idx_calendar_events_task_id ON calendar_events(related_task_id);

-- 提醒表索引
CREATE INDEX idx_reminders_user_id ON reminders(user_id);
CREATE INDEX idx_reminders_time ON reminders(reminder_time);
CREATE INDEX idx_reminders_status ON reminders(status);

-- 同步日志索引
CREATE INDEX idx_sync_logs_user_id ON sync_logs(user_id);
CREATE INDEX idx_sync_logs_device_id ON sync_logs(device_id);
CREATE INDEX idx_sync_logs_timestamp ON sync_logs(sync_timestamp);

-- ============================================================================
-- 9. 触发器（用于自动更新时间戳和同步版本）
-- ============================================================================

-- 用户表更新触发器
CREATE TRIGGER update_users_timestamp 
    AFTER UPDATE ON users
    FOR EACH ROW
BEGIN
    UPDATE users SET 
        updated_at = CURRENT_TIMESTAMP,
        sync_version = sync_version + 1
    WHERE id = NEW.id;
END;

-- 文档表更新触发器
CREATE TRIGGER update_documents_timestamp 
    AFTER UPDATE ON documents
    FOR EACH ROW
BEGIN
    UPDATE documents SET 
        updated_at = CURRENT_TIMESTAMP,
        sync_version = sync_version + 1
    WHERE id = NEW.id;
END;

-- 任务表更新触发器
CREATE TRIGGER update_tasks_timestamp 
    AFTER UPDATE ON tasks
    FOR EACH ROW
BEGIN
    UPDATE tasks SET 
        updated_at = CURRENT_TIMESTAMP,
        sync_version = sync_version + 1
    WHERE id = NEW.id;
END;

-- 日程表更新触发器
CREATE TRIGGER update_calendar_events_timestamp 
    AFTER UPDATE ON calendar_events
    FOR EACH ROW
BEGIN
    UPDATE calendar_events SET 
        updated_at = CURRENT_TIMESTAMP,
        sync_version = sync_version + 1
    WHERE id = NEW.id;
END;

-- ============================================================================
-- 10. 初始化数据
-- ============================================================================

-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('app_version', '1.0.0', 'string', '应用版本号'),
('encryption_enabled', 'true', 'boolean', '是否启用加密'),
('sync_enabled', 'true', 'boolean', '是否启用同步'),
('max_file_size', '52428800', 'integer', '最大文件大小（50MB）'),
('supported_file_types', '["pdf","doc","docx","xls","xlsx","jpg","jpeg","png","txt"]', 'json', '支持的文件类型'),
('ai_model_config', '{"provider":"openrouter","model":"deepseek/deepseek-r1-0528-qwen3-8b:free"}', 'json', 'AI模型配置');

-- 插入默认关键词词典
INSERT INTO keyword_dictionary (category, keyword, weight) VALUES
('role_host', '主办', 1.0),
('role_host', '负责', 0.9),
('role_host', '牵头', 0.9),
('role_host', '组织', 0.8),
('role_assistant', '协办', 1.0),
('role_assistant', '配合', 0.9),
('role_assistant', '协助', 0.9),
('role_assistant', '支持', 0.7),
('role_participant', '参与', 1.0),
('role_participant', '参加', 0.9),
('role_participant', '出席', 0.8),
('task_type_meeting', '会议', 1.0),
('task_type_meeting', '座谈', 0.9),
('task_type_meeting', '研讨', 0.9),
('task_type_document', '文件', 1.0),
('task_type_document', '报告', 0.9),
('task_type_document', '材料', 0.8),
('priority_high', '紧急', 1.0),
('priority_high', '重要', 0.9),
('priority_high', '优先', 0.8);

-- 插入默认角色识别规则
INSERT INTO role_identification_rules (rule_name, keywords, role_result, confidence_score) VALUES
('主办关键词识别', '["主办","负责","牵头","组织"]', 'host', 0.9),
('协办关键词识别', '["协办","配合","协助","支持"]', 'assistant', 0.8),
('参与关键词识别', '["参与","参加","出席"]', 'participant', 0.7);

-- 插入默认文档分类规则
INSERT INTO document_classification_rules (rule_name, keywords, category) VALUES
('会议通知识别', '["会议","座谈","研讨","讨论"]', 'meeting'),
('工作报告识别', '["报告","总结","汇报"]', 'report'),
('通知公告识别', '["通知","公告","通告"]', 'notice'),
('工作计划识别', '["计划","方案","安排"]', 'plan'); 