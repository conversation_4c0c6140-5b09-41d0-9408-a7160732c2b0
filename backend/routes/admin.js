const express = require('express');
const router = express.Router();

// 获取系统概览数据
router.get('/dashboard', (req, res) => {
  res.json({
    message: '管理后台首页数据接口',
    data: {
      deviceStats: {
        total: 1234,
        todayNew: 23,
        active: 892
      },
      usage: {
        mobileActive: 856,
        desktopActive: 378,
        documentsProcessed: 2456
      },
      systemStatus: {
        api: 'healthy',
        database: 'healthy',
        ai: 'healthy',
        storage: 78
      }
    },
    status: 'mock data'
  });
});

// 获取设备管理数据
router.get('/devices', (req, res) => {
  res.json({
    message: '设备管理数据接口',
    status: 'pending implementation'
  });
});

// 获取系统监控数据
router.get('/monitoring', (req, res) => {
  res.json({
    message: '系统监控数据接口',
    status: 'pending implementation'
  });
});

// 获取用户统计数据
router.get('/users/stats', (req, res) => {
  res.json({
    message: '用户统计数据接口',
    status: 'pending implementation'
  });
});

// 获取使用趋势数据
router.get('/trends', (req, res) => {
  res.json({
    message: '使用趋势数据接口',
    status: 'pending implementation'
  });
});

module.exports = router; 