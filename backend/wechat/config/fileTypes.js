/**
 * 统一文件类型配置
 * 作为服务端和移动端的文件类型判断标准
 */

// 文件类型分类
const FILE_CATEGORIES = {
  IMAGE: 'image',
  VIDEO: 'video', 
  AUDIO: 'voice',
  DOCUMENT: 'file',
  ARCHIVE: 'file'
};

// 完整的文件类型映射表
const FILE_TYPE_MAPPING = {
  // ==================== 图片文件 ====================
  'jpg': { 
    category: FILE_CATEGORIES.IMAGE, 
    mimeType: 'image/jpeg',
    description: 'JPEG图片',
    icon: '🖼️'
  },
  'jpeg': { 
    category: FILE_CATEGORIES.IMAGE, 
    mimeType: 'image/jpeg',
    description: 'JPEG图片',
    icon: '🖼️'
  },
  'png': { 
    category: FILE_CATEGORIES.IMAGE, 
    mimeType: 'image/png',
    description: 'PNG图片',
    icon: '🖼️'
  },
  'gif': { 
    category: FILE_CATEGORIES.IMAGE, 
    mimeType: 'image/gif',
    description: 'GIF图片',
    icon: '🖼️'
  },
  'webp': { 
    category: FILE_CATEGORIES.IMAGE, 
    mimeType: 'image/webp',
    description: 'WebP图片',
    icon: '🖼️'
  },
  'bmp': { 
    category: FILE_CATEGORIES.IMAGE, 
    mimeType: 'image/bmp',
    description: 'BMP图片',
    icon: '🖼️'
  },
  'svg': { 
    category: FILE_CATEGORIES.IMAGE, 
    mimeType: 'image/svg+xml',
    description: 'SVG图片',
    icon: '🖼️'
  },

  // ==================== 视频文件 ====================
  'mp4': { 
    category: FILE_CATEGORIES.VIDEO, 
    mimeType: 'video/mp4',
    description: 'MP4视频',
    icon: '🎬'
  },
  'avi': { 
    category: FILE_CATEGORIES.VIDEO, 
    mimeType: 'video/avi',
    description: 'AVI视频',
    icon: '🎬'
  },
  'mov': { 
    category: FILE_CATEGORIES.VIDEO, 
    mimeType: 'video/mov',
    description: 'MOV视频',
    icon: '🎬'
  },
  'wmv': { 
    category: FILE_CATEGORIES.VIDEO, 
    mimeType: 'video/wmv',
    description: 'WMV视频',
    icon: '🎬'
  },
  'flv': { 
    category: FILE_CATEGORIES.VIDEO, 
    mimeType: 'video/flv',
    description: 'FLV视频',
    icon: '🎬'
  },
  'webm': { 
    category: FILE_CATEGORIES.VIDEO, 
    mimeType: 'video/webm',
    description: 'WebM视频',
    icon: '🎬'
  },

  // ==================== 音频文件 ====================
  'mp3': { 
    category: FILE_CATEGORIES.AUDIO, 
    mimeType: 'audio/mpeg', // 统一使用标准MIME类型
    description: 'MP3音频',
    icon: '🎵'
  },
  'amr': { 
    category: FILE_CATEGORIES.AUDIO, 
    mimeType: 'audio/amr',
    description: 'AMR音频',
    icon: '🎵'
  },
  'wav': { 
    category: FILE_CATEGORIES.AUDIO, 
    mimeType: 'audio/wav',
    description: 'WAV音频',
    icon: '🎵'
  },
  'aac': { 
    category: FILE_CATEGORIES.AUDIO, 
    mimeType: 'audio/aac',
    description: 'AAC音频',
    icon: '🎵'
  },
  'm4a': { 
    category: FILE_CATEGORIES.AUDIO, 
    mimeType: 'audio/m4a',
    description: 'M4A音频',
    icon: '🎵'
  },
  'ogg': { 
    category: FILE_CATEGORIES.AUDIO, 
    mimeType: 'audio/ogg',
    description: 'OGG音频',
    icon: '🎵'
  },

  // ==================== 文档文件 ====================
  'pdf': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/pdf',
    description: 'PDF文档',
    icon: '📄'
  },
  'ofd': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/ofd',
    description: 'OFD文档',
    icon: '📋'
  },
  
  // Microsoft Office
  'doc': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/msword',
    description: 'Word文档',
    icon: '📝'
  },
  'docx': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    description: 'Word文档',
    icon: '📝'
  },
  'docm': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/vnd.ms-word.document.macroEnabled.12',
    description: 'Word文档(宏)',
    icon: '📝'
  },
  'xls': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/vnd.ms-excel',
    description: 'Excel表格',
    icon: '📊'
  },
  'xlsx': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    description: 'Excel表格',
    icon: '📊'
  },
  'xlsm': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/vnd.ms-excel.sheet.macroEnabled.12',
    description: 'Excel表格(宏)',
    icon: '📊'
  },
  'ppt': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/vnd.ms-powerpoint',
    description: 'PowerPoint演示',
    icon: '📽️'
  },
  'pptx': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    description: 'PowerPoint演示',
    icon: '📽️'
  },
  'pptm': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
    description: 'PowerPoint演示(宏)',
    icon: '📽️'
  },

  // WPS Office
  'wps': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/wps-office.wps',
    description: 'WPS文字',
    icon: '📝'
  },
  'et': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/wps-office.et',
    description: 'WPS表格',
    icon: '📊'
  },
  'dps': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/wps-office.dps',
    description: 'WPS演示',
    icon: '📽️'
  },

  // 文本文件
  'txt': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'text/plain',
    description: '纯文本',
    icon: '📃'
  },
  'rtf': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/rtf',
    description: '富文本',
    icon: '📃'
  },
  'csv': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'text/csv',
    description: 'CSV数据',
    icon: '📊'
  },
  'md': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'text/markdown',
    description: 'Markdown文档',
    icon: '📝'
  },
  'json': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/json',
    description: 'JSON数据',
    icon: '📄'
  },
  'xml': { 
    category: FILE_CATEGORIES.DOCUMENT, 
    mimeType: 'application/xml',
    description: 'XML文档',
    icon: '📄'
  },

  // ==================== 压缩文件 ====================
  'zip': { 
    category: FILE_CATEGORIES.ARCHIVE, 
    mimeType: 'application/zip',
    description: 'ZIP压缩包',
    icon: '📦'
  },
  'rar': { 
    category: FILE_CATEGORIES.ARCHIVE, 
    mimeType: 'application/x-rar-compressed',
    description: 'RAR压缩包',
    icon: '📦'
  },
  '7z': { 
    category: FILE_CATEGORIES.ARCHIVE, 
    mimeType: 'application/x-7z-compressed',
    description: '7Z压缩包',
    icon: '📦'
  }
};

// MIME类型到扩展名的映射
const MIME_TO_EXTENSION = {
  // 图片格式
  'image/jpeg': 'jpg',
  'image/jpg': 'jpg',
  'image/png': 'png',
  'image/gif': 'gif',
  'image/webp': 'webp',
  'image/bmp': 'bmp',
  'image/tiff': 'tiff',
  'image/svg+xml': 'svg',

  // 音频格式
  'audio/amr': 'amr',
  'audio/mp3': 'mp3',
  'audio/mpeg': 'mp3',
  'audio/wav': 'wav',
  'audio/ogg': 'ogg',
  'audio/aac': 'aac',
  'audio/m4a': 'm4a',

  // 视频格式
  'video/mp4': 'mp4',
  'video/avi': 'avi',
  'video/mov': 'mov',
  'video/wmv': 'wmv',
  'video/flv': 'flv',
  'video/webm': 'webm',

  // 文档格式
  'application/pdf': 'pdf',
  'application/pdfa': 'pdf',
  'application/ofd': 'ofd',
  'application/vnd.ofd': 'ofd',
  'application/msword': 'doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
  'application/vnd.ms-word.document.macroEnabled.12': 'docm',
  'application/vnd.ms-excel': 'xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
  'application/vnd.ms-excel.sheet.macroEnabled.12': 'xlsm',
  'application/vnd.ms-powerpoint': 'ppt',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
  'application/vnd.ms-powerpoint.presentation.macroEnabled.12': 'pptm',
  
  // WPS Office
  'application/wps-office.wps': 'wps',
  'application/wps-office.et': 'et',
  'application/wps-office.dps': 'dps',

  // OpenDocument 格式
  'application/vnd.oasis.opendocument.text': 'odt',
  'application/vnd.oasis.opendocument.spreadsheet': 'ods',
  'application/vnd.oasis.opendocument.presentation': 'odp',
  'application/vnd.oasis.opendocument.graphics': 'odg',
  'application/vnd.oasis.opendocument.formula': 'odf',
  'application/vnd.oasis.opendocument.chart': 'odc',
  'application/vnd.oasis.opendocument.database': 'odb',
  'application/vnd.oasis.opendocument.image': 'odi',

  // 文本格式
  'text/plain': 'txt',
  'text/html': 'html',
  'text/css': 'css',
  'text/javascript': 'js',
  'text/csv': 'csv',
  'text/markdown': 'md',
  'text/rtf': 'rtf',
  'application/rtf': 'rtf',
  'application/json': 'json',
  'application/xml': 'xml',
  'text/xml': 'xml',

  // 压缩格式
  'application/zip': 'zip',
  'application/x-rar-compressed': 'rar',
  'application/x-7z-compressed': '7z',

  // 其他格式
  'application/octet-stream': 'bin'
};

/**
 * 根据文件名检测文件类型
 * @param {string} fileName 文件名
 * @returns {Object} 文件类型信息
 */
const detectFileTypeFromName = (fileName) => {
  try {
    if (!fileName) {
      return {
        category: FILE_CATEGORIES.DOCUMENT,
        extension: 'bin',
        mimeType: 'application/octet-stream',
        description: '未知文件',
        icon: '📁'
      };
    }

    // 提取文件扩展名
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    
    // 查找文件类型信息
    const typeInfo = FILE_TYPE_MAPPING[extension];
    
    if (typeInfo) {
      return {
        category: typeInfo.category,
        extension: extension,
        mimeType: typeInfo.mimeType,
        description: typeInfo.description,
        icon: typeInfo.icon
      };
    }

    // 默认为文档类型
    return {
      category: FILE_CATEGORIES.DOCUMENT,
      extension: extension || 'bin',
      mimeType: 'application/octet-stream',
      description: `${extension.toUpperCase()}文件`,
      icon: '📁'
    };
  } catch (error) {
    console.error('文件类型检测失败:', error);
    return {
      category: FILE_CATEGORIES.DOCUMENT,
      extension: 'bin',
      mimeType: 'application/octet-stream',
      description: '未知文件',
      icon: '📁'
    };
  }
};

/**
 * 根据MIME类型获取文件扩展名
 * @param {string} mimeType MIME类型
 * @returns {string} 文件扩展名
 */
const getExtensionFromMimeType = (mimeType) => {
  return MIME_TO_EXTENSION[mimeType] || 'bin';
};

/**
 * 检查是否为图片文件
 * @param {string} fileName 文件名
 * @param {string} mimeType MIME类型
 * @returns {boolean}
 */
const isImageFile = (fileName, mimeType) => {
  const typeInfo = detectFileTypeFromName(fileName);
  return typeInfo.category === FILE_CATEGORIES.IMAGE || 
         (mimeType && mimeType.startsWith('image/'));
};

/**
 * 检查是否为视频文件
 * @param {string} fileName 文件名
 * @param {string} mimeType MIME类型
 * @returns {boolean}
 */
const isVideoFile = (fileName, mimeType) => {
  const typeInfo = detectFileTypeFromName(fileName);
  return typeInfo.category === FILE_CATEGORIES.VIDEO || 
         (mimeType && mimeType.startsWith('video/'));
};

/**
 * 检查是否为音频文件
 * @param {string} fileName 文件名
 * @param {string} mimeType MIME类型
 * @returns {boolean}
 */
const isAudioFile = (fileName, mimeType) => {
  const typeInfo = detectFileTypeFromName(fileName);
  return typeInfo.category === FILE_CATEGORIES.AUDIO || 
         (mimeType && mimeType.startsWith('audio/'));
};

/**
 * 检查是否为文档文件
 * @param {string} fileName 文件名
 * @param {string} mimeType MIME类型
 * @returns {boolean}
 */
const isDocumentFile = (fileName, mimeType) => {
  const typeInfo = detectFileTypeFromName(fileName);
  return typeInfo.category === FILE_CATEGORIES.DOCUMENT;
};

module.exports = {
  FILE_CATEGORIES,
  FILE_TYPE_MAPPING,
  MIME_TO_EXTENSION,
  detectFileTypeFromName,
  getExtensionFromMimeType,
  isImageFile,
  isVideoFile,
  isAudioFile,
  isDocumentFile
}; 