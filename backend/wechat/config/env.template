# 公职猫微信转发服务环境变量配置模板
# 复制此文件为 .env 并填写实际值
# 注意：此文件为模板，请勿在此文件中填写真实的敏感信息


# ==================== 腾讯云配置 ====================
# 腾讯云API密钥（用于自动化部署）
TENCENT_SECRET_ID=YOUR_TENCENT_SECRET_ID
TENCENT_SECRET_KEY=YOUR_TENCENT_SECRET_KEY

# ==================== 服务器配置 ====================
# 服务器端口配置
PORT=3000

# 服务器域名（用于生成下载链接）
SERVER_DOMAIN=wechat.api.gongzhimall.com

# 文件存储配置
FILE_STORAGE_PATH=/var/www/cache
MAX_FILE_SIZE=104857600  # 100MB

# 文件清理配置
FILE_CLEANUP_INTERVAL=3600000  # 1小时（毫秒）
FILE_MAX_AGE_DAYS=3  # 文件最大保存天数

# ==================== 数据库配置 ====================
# 数据库连接信息（云服务器本地MySQL分支）
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=wechat_user
MYSQL_PASSWORD=YOUR_MYSQL_PASSWORD
MYSQL_DATABASE=gongzhimall_wechat

# 数据库连接池配置
MYSQL_CONNECTION_LIMIT=10
MYSQL_QUEUE_LIMIT=0

# ==================== 企业微信配置 ====================
# 企业微信基础信息（公职猫企业微信应用）
WECHAT_CORP_ID=YOUR_WECHAT_CORP_ID
WECHAT_CORP_SECRET=YOUR_WECHAT_CORP_SECRET
WECHAT_AGENT_ID=YOUR_WECHAT_AGENT_ID

# 企业微信API基础URL（支持不同环境）
WECHAT_API_BASE=https://qyapi.weixin.qq.com

# 企业微信Webhook配置（公职猫转发服务专用）
WECHAT_TOKEN=YOUR_WECHAT_TOKEN
WECHAT_ENCODING_AES_KEY=YOUR_WECHAT_ENCODING_AES_KEY

# 企业微信API配置
WECHAT_API_TIMEOUT=30000  # 30秒超时
WECHAT_API_RETRY_COUNT=3  # 重试次数

# 企业微信客服配置
WECHAT_DEFAULT_OPEN_KFID=YOUR_WECHAT_DEFAULT_OPEN_KFID  # 默认客服账号ID
WECHAT_BINDING_SECRET=YOUR_WECHAT_BINDING_SECRET  # 绑定加密密钥

# ==================== 推送服务配置 ====================
# 极光推送（JPush）- 公职猫专用应用
JPUSH_APP_KEY=YOUR_JPUSH_APP_KEY
JPUSH_MASTER_SECRET=YOUR_JPUSH_MASTER_SECRET

# ==================== 应用配置 ====================
# 运行环境
NODE_ENV=production

# 应用密钥（用于JWT和数据加密）
TOKEN_SECRET=YOUR_TOKEN_SECRET
WECHAT_BINDING_SECRET=YOUR_WECHAT_BINDING_SECRET  # 统一绑定加密密钥

# 文件加密密钥（用于文件加密存储）
FILE_ENCRYPTION_KEY=YOUR_FILE_ENCRYPTION_KEY

# 下载令牌配置
DOWNLOAD_TOKEN_EXPIRES_HOURS=24  # 下载令牌有效期（小时）

# ==================== 安全配置 ====================
# CORS配置
CORS_ORIGIN=*  # 生产环境应设置为具体域名

# 请求限制
RATE_LIMIT_MAX=100  # 每15分钟最大请求数
RATE_LIMIT_WINDOW_MS=900000  # 15分钟窗口期

# ==================== 日志配置 ====================
# 日志级别：DEBUG, INFO, WARN, ERROR
LOG_LEVEL=INFO

# 是否启用数据库日志记录
ENABLE_DB_LOGGING=true

# 调试模式
DEBUG=false

# 日志文件配置
LOG_FILE_PATH=/var/log/wechat-service
LOG_FILE_MAX_SIZE=10485760  # 10MB
LOG_FILE_MAX_FILES=5

# ==================== 监控配置 ====================
# 健康检查配置
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health

# 性能监控
ENABLE_PERFORMANCE_MONITORING=true

# ==================== Docker配置 ====================
# Docker容器配置（宝塔面板使用）
DOCKER_CONTAINER_NAME=gongzhimall-wechat
DOCKER_RESTART_POLICY=unless-stopped

# ==================== 配置说明 ====================
# 1. 必需配置：TENCENT_*, MYSQL_*, WECHAT_*, JPUSH_*, PORT
# 2. 推送服务：仅使用极光推送，适配国内环境
# 3. 系统配置：NODE_ENV, LOG_LEVEL, DEBUG 等
# 4. 文件配置：FILE_STORAGE_PATH, MAX_FILE_SIZE 等
# 5. 部署方式：支持宝塔面板部署
# 6. 下载链接：过期后自动重新生成，用户无感知
# 7. 安全提醒：请勿在此模板文件中填写真实的敏感信息
# 8. 生产环境：请通过环境变量或安全配置文件设置敏感信息
# 9. 轻量服务器需要确保文件存储目录的读写权限
# 10. 建议在生产环境中使用环境变量管理工具进行配置管理