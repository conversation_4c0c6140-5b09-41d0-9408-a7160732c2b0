/**
 * 公职猫微信转发服务 PM2 生态系统配置
 * 用于生产环境的进程管理和部署配置
 */

module.exports = {
  apps: [
    {
      // 应用基本配置
      name: 'gongzhimall-wechat',
      script: 'index.js',
      cwd: '/www/wwwroot/wechat.api.gongzhimall.com',
      
      // 进程配置
      instances: 1,
      exec_mode: 'fork',
      
      // 环境配置
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 3001
      },
      
      // 日志配置
      log_file: '/var/log/wechat-service/combined.log',
      out_file: '/var/log/wechat-service/out.log',
      error_file: '/var/log/wechat-service/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 进程管理
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // 性能配置
      node_args: '--max-old-space-size=512',
      
      // 健康检查
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // 进程信号处理
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // 忽略监听的文件/目录
      ignore_watch: [
        'node_modules',
        'logs',
        'cache',
        '.git',
        '*.log'
      ],
      
      // 环境变量文件
      env_file: '.env',
      
      // 实例配置
      instance_var: 'INSTANCE_ID',
      
      // 错误处理
      error_file: '/var/log/wechat-service/error.log',
      combine_logs: true,
      
      // 时间配置
      time: true,
      
      // 进程标题
      name: 'gongzhimall-wechat-service'
    }
  ],

  // 部署配置
  deploy: {
    // 生产环境部署
    production: {
      user: 'root',
      host: 'wechat.api.gongzhimall.com',
      ref: 'origin/main',
      path: '/www/wwwroot/wechat.api.gongzhimall.com',
      'pre-deploy-local': '',
      'post-deploy': 'cd backend/wechat && npm install --production && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'mkdir -p /var/log/wechat-service && mkdir -p /var/www/cache',
      'post-setup': 'ls -la',
      env: {
        NODE_ENV: 'production'
      }
    },
    
    // 测试环境部署
    staging: {
      user: 'root',
      host: 'staging.wechat.api.gongzhimall.com',
      ref: 'origin/develop',
      path: '/www/wwwroot/staging-wechat',
      'post-deploy': 'cd backend/wechat && npm install && pm2 reload ecosystem.config.js --env staging',
      'pre-setup': 'mkdir -p /var/log/staging-wechat && mkdir -p /var/www/staging-cache',
      env: {
        NODE_ENV: 'staging'
      }
    }
  }
};
