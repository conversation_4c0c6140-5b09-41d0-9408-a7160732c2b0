{"name": "gongzhimall-wechat-server", "version": "1.0.0", "description": "公职猫微信转发服务 - 腾讯云轻量应用服务器版本", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "node --test", "validate": "node -c index.js && node -c api/controller.js && node -c data/database.js && node -c service/wechatApi.js && node -c service/pushService.js && node -c service/deviceManager.js && node -c api/errorHandler.js", "verify-push": "node test-files/verify-jpush-config.js", "test-end-to-end": "node test-files/test-end-to-end.js", "test-push": "node test-files/test-push-notifications.js", "test-file-detection": "node test-files/test-file-type-detection.js", "test-services": "node test-files/test-refactored-services.js", "test-architecture": "node test-files/test-new-architecture.js", "verify-binding": "node test-files/verify-binding-encryption.js", "cleanup-files": "node scripts/cleanup-files.js", "cleanup-database": "node scripts/cleanup-database.js", "docker:build": "docker build -t gongzhimall-wechat .", "docker:run": "docker-compose up -d", "docker:stop": "docker-compose down"}, "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.0.0", "express": "^4.18.2", "express-rate-limit": "^6.11.2", "form-data": "^4.0.0", "helmet": "^7.0.0", "jpush-sdk": "^3.5.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.2", "node-cron": "^3.0.2", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.15.0"}, "keywords": ["wechat", "lightweight-server", "tencent-cloud", "gongzhimall", "enterprise-wechat", "push-notification", "multi-platform"], "author": "芝麻巧匠", "license": "private", "repository": {"type": "git", "url": "https://gitee.com/gongzhimall/gongzhimall"}, "homepage": "https://www.gongzhimall.com", "bugs": {"url": "https://gitee.com/gongzhimall/gongzhimall/issues"}}