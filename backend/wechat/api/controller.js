// 微信转发功能控制器
// 处理HTTP请求和响应逻辑

const weChatService = require('../service/service');

/**
 * 【开启接收】企业微信Webhook URL验证（GET请求）
 * 根据官方文档：企业微信会发送GET请求来验证URL的有效性
 * 需要：1.Urldecode处理 2.验证签名 3.解密echostr 4.返回明文(不能加引号、bom头、\r\n符)
 */
const verifyWebhookUrl = async (req, res) => {
  try {
    console.log('🔍 【开启接收】企业微信URL验证请求');
    console.log('原始query参数:', req.query);
    
    // 第一步：对收到的请求做Urldecode处理（Express已经自动处理了URL解码）
    let { msg_signature, timestamp, nonce, echostr } = req.query;
    
    // 手动进行URL解码（以防Express没有完全解码）
    if (msg_signature) msg_signature = decodeURIComponent(msg_signature);
    if (timestamp) timestamp = decodeURIComponent(timestamp);
    if (nonce) nonce = decodeURIComponent(nonce);
    if (echostr) echostr = decodeURIComponent(echostr);
    
    console.log('解码后参数:', {
      msg_signature: msg_signature ? msg_signature.substring(0, 10) + '...' : 'undefined',
      timestamp,
      nonce,
      echostr: echostr ? echostr.substring(0, 10) + '...' : 'undefined'
    });
    
    // 验证必需参数
    if (!msg_signature || !timestamp || !nonce || !echostr) {
      console.error('❌ URL验证失败：参数不完整');
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Access-Control-Allow-Origin': '*'
        },
        body: '参数不完整'
      };
    }
    
    // 第三步：验证签名并解密echostr
    const result = await weChatService.verifyWebhookUrl(msg_signature, timestamp, nonce, echostr);
    
    if (result && result.success) {
      console.log('✅ 完整result对象:', result);
      console.log('✅ result.echostr字段:', result.echostr);
      console.log('✅ result.echostr类型:', typeof result.echostr);
      
      // 修复：正确处理echostr字段
      let echostrValue;
      if (typeof result.echostr === 'string') {
        // 如果echostr直接是字符串
        echostrValue = result.echostr;
      } else if (result.echostr && typeof result.echostr === 'object' && result.echostr.echostr) {
        // 如果echostr是对象且包含echostr字段
        echostrValue = result.echostr.echostr;
      } else {
        console.error('❌ 无效的echostr格式:', result.echostr);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'text/plain; charset=utf-8',
            'Access-Control-Allow-Origin': '*'
          },
          body: '解密结果格式错误'
        };
      }
      
      console.log('✅ 提取的echostr值:', echostrValue);
      console.log('✅ 提取的echostr类型:', typeof echostrValue);
      
      // 腾讯云函数返回格式：根据官方文档要求返回明文消息内容(不能加引号，不能带bom头，不能带\r\n符)
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Access-Control-Allow-Origin': '*'
        },
        body: echostrValue
      };
    } else {
      console.error('❌ URL验证失败:', result ? result.message : '未知错误');
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Access-Control-Allow-Origin': '*'
        },
        body: 'URL验证失败'
      };
    }
  } catch (error) {
    console.error('【开启接收】URL验证异常:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Access-Control-Allow-Origin': '*'
      },
      body: '验证异常'
    };
  }
};

/**
 * 【使用接收】处理企业微信消息推送（POST请求）
 * 这是企业微信配置成功后的第二步：实际接收和处理消息
 * 企业微信会发送POST请求推送消息到我们的服务器
 */
const handleWeChatMessage = async (req, res) => {
  try {
    console.log('📨 【使用接收】企业微信消息推送');

    const { msg_signature, timestamp, nonce } = req.query;
    const messageData = req.body;

    console.log('消息参数:', {
      msg_signature: msg_signature ? msg_signature.substring(0, 10) + '...' : 'undefined',
      timestamp,
      nonce,
      hasBody: !!messageData,
      bodyType: typeof messageData,
      bodyKeys: messageData ? Object.keys(messageData) : 'no body'
    });

    // 详细记录消息体内容（用于调试）
    if (messageData) {
      console.log('消息体详情:', JSON.stringify(messageData, null, 2));
    }
    
    // 验证消息签名
    const verifyResult = await weChatService.verifyMessageSignature(
      msg_signature,
      timestamp,
      nonce,
      messageData
    );
    
    if (!verifyResult.success) {
      console.error('❌ 【使用接收】消息签名验证失败:', verifyResult.message);
      return res.status(403).send('消息签名验证失败');
    }
    
    // 处理消息内容
    const processResult = await weChatService.processWeChatMessage(messageData);
    
    if (processResult.success) {
      console.log('✅ 【使用接收】消息处理成功');
      res.send('success');
    } else {
      console.log('ℹ️ 【使用接收】消息已忽略:', processResult.message);
      res.send('success'); // 即使忽略也要返回success
    }
  } catch (error) {
    console.error('【使用接收】消息处理异常:', error);
    res.status(500).send('消息处理异常');
  }
};


/**
 * 根据用户UUID查询绑定状态
 * GET /api/bind/status?user_uuid={user_uuid}
 */
const getBindingByUserUuid = async (req, res) => {
  try {
    const { user_uuid } = req.query;
    
    if (!user_uuid) {
      return res.status(400).json({
        success: false,
        error: '缺少user_uuid参数'
      });
    }
    
    console.log('查询用户绑定状态:', user_uuid);
    
    const binding = await weChatService.getBindingByUserUuid(user_uuid);

    // 检查用户是否真正绑定（is_bound为true或binding_status为active）
    if (binding && (binding.is_bound === 1 || binding.is_bound === true || binding.binding_status === 'active')) {
      res.json({
        success: true,
        bound: true,
        status: 'success',
        data: {
          user_uuid: binding.user_uuid,
          external_userid: binding.external_userid,
          binding_status: binding.binding_status,
          binding_time: binding.binding_time
        }
      });
    } else {
      res.json({
        success: false,
        bound: false,
        message: '用户未绑定微信'
      });
    }
  } catch (error) {
    console.error('查询绑定状态失败:', error);
    res.status(500).json({
      success: false,
      error: '查询绑定状态失败',
      details: error.message
    });
  }
};



/**
 * 一键绑定链接生成接口
 * POST /api/bind/one-click-link
 * Body: { user_uuid: string }
 */
const generateOneClickBindingLink = async (req, res) => {
  try {
    const { user_uuid } = req.body;

    console.log('🚀 一键绑定链接生成请求:', { user_uuid });
    console.log('🔍 [DEBUG] 接收到的用户UUID:', user_uuid);
    console.log('🔍 [DEBUG] UUID类型:', typeof user_uuid);
    console.log('🔍 [DEBUG] UUID长度:', user_uuid ? user_uuid.length : 'undefined');
    console.log('🔍 [DEBUG] 请求体完整内容:', JSON.stringify(req.body, null, 2));

    // 验证参数
    if (!user_uuid) {
      return res.status(400).json({
        success: false,
        error: '缺少用户UUID参数'
      });
    }

    // 验证参数格式
    if (typeof user_uuid !== 'string') {
      return res.status(400).json({
        success: false,
        error: '用户UUID格式错误'
      });
    }

    // 检查用户是否已绑定
    const existingBinding = await weChatService.getBindingByUserUuid(user_uuid);
    if (existingBinding && existingBinding.binding_status === 'active') {
      return res.status(409).json({
        success: false,
        message: '用户已经绑定过微信账号',
        already_bound: true
      });
    }

    // 生成一键绑定链接（使用统一场景值'binding'）
    const linkResult = await weChatService.generateWeChatBindingLink(
      user_uuid,
      'binding', // 统一场景值
      null // 使用动态获取的客服账号
    );

    if (linkResult.success) {
      console.log('🔍 [DEBUG] 返回给APP的链接:', linkResult.binding_url);
      console.log('🔍 [DEBUG] 链接中的scene_param:', linkResult.scene_param);

      const responseData = {
        success: true,
        binding_url: linkResult.binding_url,
        instructions: '点击链接将自动跳转到微信客服完成绑定'
      };

      console.log('🔍 [DEBUG] 完整响应数据:', JSON.stringify(responseData, null, 2));
      res.json(responseData);
    } else {
      res.status(500).json({
        success: false,
        error: linkResult.message
      });
    }

  } catch (error) {
    console.error('生成一键绑定链接异常:', error);
    res.status(500).json({
      success: false,
      error: '生成一键绑定链接失败'
    });
  }
};

/**
 * 解除用户绑定
 * DELETE /api/bind/unbind
 * Body: { user_uuid: string }
 */
const unbindUser = async (req, res) => {
  try {
    const { user_uuid } = req.body;

    console.log('🔓 解除绑定请求:', { user_uuid });

    // 验证参数
    if (!user_uuid) {
      return res.status(400).json({
        success: false,
        error: '缺少用户UUID参数'
      });
    }

    // 验证参数格式
    if (typeof user_uuid !== 'string') {
      return res.status(400).json({
        success: false,
        error: '用户UUID格式错误'
      });
    }

    // 检查用户是否已绑定
    const existingBinding = await weChatService.getBindingByUserUuid(user_uuid);
    if (!existingBinding || existingBinding.binding_status !== 'active') {
      return res.status(404).json({
        success: false,
        message: '用户未绑定微信账号',
        not_bound: true
      });
    }

    // 执行解绑操作
    const unbindResult = await weChatService.unbindUser(user_uuid);

    if (unbindResult.success) {
      console.log('✅ 用户解绑成功:', user_uuid);
      res.json({
        success: true,
        message: '解除绑定成功'
      });
    } else {
      console.error('❌ 用户解绑失败:', unbindResult.message);
      res.status(500).json({
        success: false,
        error: unbindResult.message || '解除绑定失败'
      });
    }

  } catch (error) {
    console.error('解除绑定异常:', error);
    res.status(500).json({
      success: false,
      error: '解除绑定失败'
    });
  }
};

/**
 * 增量同步消息接口
 * GET /api/sync/messages?user_uuid={user_uuid}&device_id={device_id}&since_id={last_synced_id}&limit=100
 */
const syncMessages = async (req, res) => {
  try {
    const { user_uuid, device_id, since_id = 0, limit = 100 } = req.query;
    
    console.log('📱 增量同步请求:', { user_uuid, device_id, since_id, limit });
    
    // 验证参数
    if (!user_uuid || !device_id) {
      return res.status(400).json({
        success: false,
        error: '缺少用户或设备标识',
        details: {
          user_uuid: user_uuid ? 'valid' : 'missing',
          device_id: device_id ? 'valid' : 'missing'
        }
      });
    }
    
    // 验证参数格式
    if (typeof user_uuid !== 'string' || typeof device_id !== 'string') {
      return res.status(400).json({
        success: false,
        error: '用户或设备标识格式错误'
      });
    }
    
    // 获取增量消息
    const syncResult = await weChatService.getIncrementalMessages(
      user_uuid,
      device_id,
      parseInt(since_id),
      parseInt(limit)
    );
    
    if (syncResult.success) {
      res.json({
        success: true,
        data: {
          messages: syncResult.messages,
          has_more: syncResult.has_more,
          next_since_id: syncResult.next_since_id
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: syncResult.message
      });
    }
  } catch (error) {
    console.error('增量同步异常:', error);
    res.status(500).json({
      success: false,
      error: '增量同步失败'
    });
  }
};

/**
 * 更新设备同步状态
 * POST /api/sync/update-status
 */
const updateSyncStatus = async (req, res) => {
  try {
    const { user_uuid, device_id, last_synced_id } = req.body;
    
    console.log('📱 更新同步状态:', { user_uuid, device_id, last_synced_id });
    
    if (!user_uuid || !device_id || last_synced_id === undefined) {
      return res.status(400).json({
        success: false,
        error: '参数不完整',
        details: {
          user_uuid: user_uuid ? 'valid' : 'missing',
          device_id: device_id ? 'valid' : 'missing',
          last_synced_id: last_synced_id !== undefined ? 'valid' : 'missing'
        }
      });
    }
    
    // 验证参数格式
    if (typeof user_uuid !== 'string' || typeof device_id !== 'string' || isNaN(parseInt(last_synced_id))) {
      return res.status(400).json({
        success: false,
        error: '参数格式错误'
      });
    }
    
    // 更新设备同步状态
    const updateResult = await weChatService.updateDeviceSyncStatus(
      user_uuid,
      device_id,
      parseInt(last_synced_id)
    );
    
    if (updateResult.success) {
    res.json({
        success: true,
        message: '同步状态更新成功'
      });
    } else {
      res.status(500).json({
        success: false,
        error: updateResult.message
      });
    }
  } catch (error) {
    console.error('更新同步状态异常:', error);
    res.status(500).json({
      success: false,
      error: '更新同步状态失败'
    });
  }
};

/**
 * 注册设备
 * POST /api/sync/register-device
 */
const registerDevice = async (req, res) => {
  try {
    const { user_uuid, device_id, device_name, platform, app_version, push_token } = req.body;
    
    console.log('📱 注册设备:', { user_uuid, device_id, platform });
    
    if (!user_uuid || !device_id || !platform) {
      return res.status(400).json({
        success: false,
        error: '参数不完整'
      });
    }
    
    // 注册设备
    const registerResult = await weChatService.registerDevice({
      user_uuid,
      device_id,
      device_name,
      platform,
      app_version,
      push_token
    });
    
    if (registerResult.success) {
    res.json({
        success: true,
        message: '设备注册成功',
        data: registerResult.device
      });
    } else {
      res.status(500).json({
        success: false,
        error: registerResult.message
      });
    }
  } catch (error) {
    console.error('设备注册异常:', error);
    res.status(500).json({
      success: false,
      error: '设备注册失败'
    });
  }
};



module.exports = {
  verifyWebhookUrl,      // 【开启接收】URL验证
  handleWeChatMessage,    // 【使用接收】消息处理
  syncMessages,          // 增量同步消息
  updateSyncStatus,      // 更新设备同步状态
  registerDevice,        // 注册设备


  // 新的绑定查询方法
  getBindingByUserUuid,  // 根据用户UUID查询绑定状态
  generateOneClickBindingLink, // 一键绑定链接生成
  unbindUser,            // 解除用户绑定

};