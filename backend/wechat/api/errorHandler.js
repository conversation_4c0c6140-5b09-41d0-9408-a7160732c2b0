// 错误处理和日志记录模块（适配腾讯云轻量应用服务器环境）
// 提供统一的错误处理、数据库日志记录和监控功能

const mysql = require('mysql2/promise');

// 日志级别定义
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

// 当前日志级别
const CURRENT_LOG_LEVEL = LOG_LEVELS[process.env.LOG_LEVEL?.toUpperCase()] || LOG_LEVELS.INFO;

// 轻量服务器环境配置
const SERVER_CONFIG = {
  enabled: true,
  request_id: process.env.REQUEST_ID || null, // 服务器RequestId
  service_name: process.env.SERVICE_NAME || 'wechat-forward',
  memory_limit: process.env.MEMORY_LIMIT || 512,
  timeout: process.env.TIMEOUT || 30
};

// 数据库连接（复用现有连接）
let dbConnection = null;

/**
 * 获取数据库连接
 * @returns {Promise<Object>} 数据库连接
 */
const getDbConnection = async () => {
  if (!dbConnection) {
    // 生产环境数据库配置
    // 统一使用MYSQL_*格式
    const dbConfig = {
      host: process.env.MYSQL_HOST,
      port: parseInt(process.env.MYSQL_PORT || '3306'),
      user: process.env.MYSQL_USER,
      password: process.env.MYSQL_PASSWORD,
      database: process.env.MYSQL_DATABASE,
      charset: 'utf8mb4',
      timezone: '+08:00'
    };
    
    // 验证必要的配置
    if (!dbConfig.host || !dbConfig.user || !dbConfig.password || !dbConfig.database) {
      throw new Error('缺少必要的数据库配置环境变量: MYSQL_HOST, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DATABASE');
    }
    
    dbConnection = mysql.createConnection(dbConfig);
  }
  return dbConnection;
};

// 错误类型定义
class WeChatError extends Error {
  constructor(message, code, statusCode = 500, details = null) {
    super(message);
    this.name = 'WeChatError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}

class DatabaseError extends WeChatError {
  constructor(message, details = null) {
    super(message, 'DATABASE_ERROR', 500, details);
    this.name = 'DatabaseError';
  }
}

class ValidationError extends WeChatError {
  constructor(message, details = null) {
    super(message, 'VALIDATION_ERROR', 400, details);
    this.name = 'ValidationError';
  }
}

class AuthenticationError extends WeChatError {
  constructor(message, details = null) {
    super(message, 'AUTHENTICATION_ERROR', 401, details);
    this.name = 'AuthenticationError';
  }
}

class PushServiceError extends WeChatError {
  constructor(message, details = null) {
    super(message, 'PUSH_SERVICE_ERROR', 500, details);
    this.name = 'PushServiceError';
  }
}

class WeChatApiError extends WeChatError {
  constructor(message, details = null) {
    super(message, 'WECHAT_API_ERROR', 500, details);
    this.name = 'WeChatApiError';
  }
}

/**
 * 写入数据库日志
 * @param {string} level 日志级别
 * @param {string} message 日志消息
 * @param {Object} meta 元数据
 */
const writeDbLog = async (level, message, meta = {}) => {
  try {
    const connection = await getDbConnection();
    
    const logEntry = {
      log_level: level.toUpperCase(),
      message: message,
      module: meta.module || 'wechat-service',
      function_name: meta.function || SERVER_CONFIG.service_name,
      request_id: SERVER_CONFIG.request_id,
      user_uuid: meta.user_uuid || null,
      external_userid: meta.external_userid || null,
      error_code: meta.error_code || null,
      error_stack: meta.error_stack || null,
      metadata: JSON.stringify(meta),
      ip_address: meta.ip_address || null,
      user_agent: meta.user_agent || null,
      execution_time: meta.execution_time || null,
      memory_usage: meta.memory_usage || null
    };
    
    await connection.execute(`
      INSERT INTO system_logs (
        log_level, message, module, function_name, request_id, 
        user_uuid, external_userid, error_code, error_stack, 
        metadata, ip_address, user_agent, execution_time, memory_usage
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      logEntry.log_level, logEntry.message, logEntry.module, 
      logEntry.function_name, logEntry.request_id, logEntry.user_uuid,
      logEntry.external_userid, logEntry.error_code, logEntry.error_stack,
      logEntry.metadata, logEntry.ip_address, logEntry.user_agent,
      logEntry.execution_time, logEntry.memory_usage
    ]);
  } catch (error) {
    // 如果数据库日志写入失败，至少要在控制台输出
    console.error('写入数据库日志失败:', error.message);
    console.error('原始日志:', { level, message, meta });
  }
};

/**
 * 基础日志记录函数
 * @param {string} level 日志级别
 * @param {string} message 日志消息
 * @param {Object} meta 元数据
 */
const log = async (level, message, meta = {}) => {
  const levelValue = LOG_LEVELS[level.toUpperCase()];
  
  if (levelValue > CURRENT_LOG_LEVEL) return;
  
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] [${SERVER_CONFIG.service_name}] ${message}`;
  
  // 控制台输出（服务器日志）
  switch (level.toUpperCase()) {
    case 'ERROR':
      console.error(logMessage, meta);
      break;
    case 'WARN':
      console.warn(logMessage, meta);
      break;
    case 'INFO':
      console.log(logMessage, meta);
      break;
    case 'DEBUG':
      console.debug(logMessage, meta);
      break;
    default:
      console.log(logMessage, meta);
  }
  
  // 数据库日志（异步，不阻塞主流程）
  if (process.env.ENABLE_DB_LOGGING !== 'false') {
    setImmediate(() => {
      writeDbLog(level, message, {
        ...meta,
        timestamp,
        server_request_id: SERVER_CONFIG.request_id
      }).catch(error => {
        // 静默处理数据库日志写入失败，不影响主流程
        console.error('数据库日志写入失败:', error.message);
      });
    });
  }
};

/**
 * 错误日志
 * @param {string} message 错误消息
 * @param {Object} meta 元数据
 */
const logError = async (message, meta = {}) => {
  await log('ERROR', message, meta);
};

/**
 * 警告日志
 * @param {string} message 警告消息
 * @param {Object} meta 元数据
 */
const logWarn = async (message, meta = {}) => {
  await log('WARN', message, meta);
};

/**
 * 信息日志
 * @param {string} message 信息消息
 * @param {Object} meta 元数据
 */
const logInfo = async (message, meta = {}) => {
  await log('INFO', message, meta);
};

/**
 * 调试日志
 * @param {string} message 调试消息
 * @param {Object} meta 元数据
 */
const logDebug = async (message, meta = {}) => {
  await log('DEBUG', message, meta);
};

/**
 * 记录API请求
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {number} duration 处理时间（毫秒）
 */
const logApiRequest = async (req, res, duration) => {
  const meta = {
    module: 'api',
    function: req.route?.path || req.path,
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    execution_time: duration,
    ip_address: req.ip,
    user_agent: req.get('User-Agent'),
    user_uuid: req.user?.uuid || null,
    memory_usage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024)
  };
  
  if (res.statusCode >= 400) {
    await logError(`API请求失败: ${req.method} ${req.url}`, meta);
  } else {
    await logInfo(`API请求: ${req.method} ${req.url}`, meta);
  }
};

/**
 * 记录数据库操作
 * @param {string} operation 操作类型
 * @param {string} table 表名
 * @param {boolean} success 是否成功
 * @param {number} duration 耗时
 * @param {Object} meta 额外信息
 */
const logDatabaseOperation = async (operation, table, success, duration, meta = {}) => {
  const logData = {
    module: 'database',
    function: operation,
    operation,
    table,
    success,
    execution_time: duration,
    ...meta
  };
  
  if (success) {
    await logInfo(`数据库操作成功: ${operation} ${table}`, logData);
  } else {
    await logError(`数据库操作失败: ${operation} ${table}`, logData);
  }
};

/**
 * 记录推送操作
 * @param {string} provider 推送提供商
 * @param {number} deviceCount 设备数量
 * @param {boolean} success 是否成功
 * @param {Object} meta 额外信息
 */
const logPushOperation = async (provider, deviceCount, success, meta = {}) => {
  const logData = {
    module: 'push',
    function: 'sendNotification',
    provider,
    deviceCount,
    success,
    ...meta
  };
  
  if (success) {
    await logInfo(`推送成功: ${provider} 推送到${deviceCount}个设备`, logData);
  } else {
    await logError(`推送失败: ${provider} 推送到${deviceCount}个设备`, logData);
  }
};

/**
 * 记录企业微信API调用
 * @param {string} api API名称
 * @param {boolean} success 是否成功
 * @param {number} duration 耗时
 * @param {Object} meta 额外信息
 */
const logWeChatApiCall = async (api, success, duration, meta = {}) => {
  const logData = {
    module: 'wechat-api',
    function: api,
    api,
    success,
    execution_time: duration,
    ...meta
  };
  
  if (success) {
    await logInfo(`企业微信API调用成功: ${api}`, logData);
  } else {
    await logError(`企业微信API调用失败: ${api}`, logData);
  }
};

/**
 * 错误处理中间件
 * @param {Error} err 错误对象
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {Function} next 下一个中间件
 */
const errorHandlerMiddleware = async (err, req, res, next) => {
  // 记录错误
  await logError('请求处理异常', {
    module: 'middleware',
    function: 'errorHandler',
    error_code: err.code,
    error_stack: err.stack,
    url: req.url,
    method: req.method,
    body: req.body,
    params: req.params,
    query: req.query,
    user_uuid: req.user?.uuid || null,
    ip_address: req.ip,
    user_agent: req.get('User-Agent')
  });
  
  // 确定响应状态码
  let statusCode = 500;
  let errorCode = 'INTERNAL_SERVER_ERROR';
  let message = '服务器内部错误';
  
  if (err instanceof WeChatError) {
    statusCode = err.statusCode;
    errorCode = err.code;
    message = err.message;
  } else if (err.name === 'ValidationError') {
    statusCode = 400;
    errorCode = 'VALIDATION_ERROR';
    message = err.message;
  } else if (err.code === 'ECONNREFUSED') {
    statusCode = 503;
    errorCode = 'SERVICE_UNAVAILABLE';
    message = '服务暂时不可用';
  }
  
  // 构造错误响应
  const errorResponse = {
    success: false,
    error: {
      code: errorCode,
      message: message,
      timestamp: new Date().toISOString(),
      request_id: SERVER_CONFIG.request_id
    }
  };
  
  // 开发环境下包含详细错误信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.details = {
      stack: err.stack,
      originalMessage: err.message
    };
  }
  
  res.status(statusCode).json(errorResponse);
};

/**
 * 请求日志中间件
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {Function} next 下一个中间件
 */
const requestLoggerMiddleware = (req, res, next) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    logApiRequest(req, res, duration);
  });
  
  next();
};

/**
 * 包装异步函数以进行错误处理
 * @param {Function} fn 异步函数
 * @returns {Function} 包装后的函数
 */
const asyncWrapper = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 清理旧日志（定期任务）
 * @param {number} daysToKeep 保留天数
 */
const cleanupOldLogs = async (daysToKeep = 30) => {
  try {
    const connection = await getDbConnection();
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    const [result] = await connection.execute(
      'DELETE FROM system_logs WHERE created_at < ?',
      [cutoffDate]
    );
    
    if (result.affectedRows > 0) {
      await logInfo(`清理旧日志: 删除了${result.affectedRows}条记录`, {
        module: 'maintenance',
        function: 'cleanupOldLogs',
        days_to_keep: daysToKeep,
        cutoff_date: cutoffDate.toISOString()
      });
    }
  } catch (error) {
    await logError('清理旧日志失败', {
      module: 'maintenance',
      function: 'cleanupOldLogs',
      error_code: 'CLEANUP_FAILED',
      error_stack: error.stack
    });
  }
};

/**
 * 获取日志统计信息
 * @param {number} hours 统计最近几小时
 * @returns {Promise<Object>} 统计信息
 */
const getLogStats = async (hours = 24) => {
  try {
    const connection = await getDbConnection();
    
    const [stats] = await connection.execute(`
      SELECT 
        log_level,
        COUNT(*) as count,
        AVG(execution_time) as avg_execution_time
      FROM system_logs 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
      GROUP BY log_level
      ORDER BY count DESC
    `, [hours]);
    
    return {
      success: true,
      stats: stats,
      period_hours: hours
    };
  } catch (error) {
    await logError('获取日志统计失败', {
      module: 'monitoring',
      function: 'getLogStats',
      error_code: 'STATS_FAILED',
      error_stack: error.stack
    });
    
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 初始化错误处理器（适配轻量服务器）
 */
const initializeErrorHandler = async () => {
  // 处理未捕获的异常
  process.on('uncaughtException', async (error) => {
    await logError('未捕获的异常', {
      module: 'process',
      function: 'uncaughtException',
      error_code: 'UNCAUGHT_EXCEPTION',
      error_stack: error.stack
    });
    
    // 轻量服务器环境下，给一些时间写入日志后退出
    setTimeout(() => {
      process.exit(1);
    }, 1000);
  });
  
  // 处理未处理的Promise拒绝
  process.on('unhandledRejection', async (reason, promise) => {
    await logError('未处理的Promise拒绝', {
      module: 'process',
      function: 'unhandledRejection',
      error_code: 'UNHANDLED_REJECTION',
      error_stack: reason?.stack || null,
      reason: reason?.message || String(reason)
    });
  });
  
  await logInfo('错误处理器初始化完成（轻量服务器模式）', {
    module: 'system',
    function: 'initialize',
    server_service_name: SERVER_CONFIG.service_name,
    server_memory_limit: SERVER_CONFIG.memory_limit,
    server_timeout: SERVER_CONFIG.timeout
  });
};

/**
 * 服务器结束时的清理工作
 */
const cleanup = async () => {
  try {
    if (dbConnection) {
      await dbConnection.end();
      dbConnection = null;
    }
  } catch (error) {
    console.error('清理数据库连接失败:', error);
  }
};

module.exports = {
  // 错误类
  WeChatError,
  DatabaseError,
  ValidationError,
  AuthenticationError,
  PushServiceError,
  WeChatApiError,
  
  // 日志函数
  logError,
  logWarn,
  logInfo,
  logDebug,
  logApiRequest,
  logDatabaseOperation,
  logPushOperation,
  logWeChatApiCall,
  
  // 中间件
  errorHandlerMiddleware,
  requestLoggerMiddleware,
  asyncWrapper,
  
  // 初始化和清理
  initializeErrorHandler,
  cleanup,
  
  // 工具函数
  cleanupOldLogs,
  getLogStats
}; 