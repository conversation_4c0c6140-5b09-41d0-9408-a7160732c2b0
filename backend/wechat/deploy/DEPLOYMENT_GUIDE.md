# 公职猫微信转发服务 - 云服务器部署指南

## 📋 部署概述

本文档详细记录了云服务器部署的完整流程、关键注意事项和故障排除方法。基于实际部署经验总结，确保后续部署的可靠性和效率。

## 🎯 部署目标

- **云服务器**：wechat.api.gongzhimall.com
- **数据库**：MariaDB (云服务器本地)
- **服务**：Node.js Express应用
- **进程管理**：PM2
- **反向代理**：Nginx

## 🏗️ 系统架构

```
用户请求 → Nginx (443/80) → Node.js服务 (3000) → MariaDB
```

## 📦 部署前准备

### 1. 环境检查清单

- [X] 本地MySQL客户端已安装 (`brew install mysql-client`)
- [X] SSH密钥已配置，可无密码连接云服务器
- [X] 云服务器上已安装Node.js、PM2、MariaDB
- [ ] 域名解析正确指向云服务器IP

### 2. 关键配置文件

```bash
# 部署配置
deploy/deploy-config.json          # 服务器连接信息
deploy/production-deploy.sh        # 生产环境部署脚本
deploy/one-click-deploy.sh         # 一键部署脚本

# 数据库架构
data/schema/scripts/build-schema.js    # 架构构建脚本
data/schema/environments/production.sql # 生产环境配置
data/schema/build/schema.sql           # 生成的完整架构文件

# 环境配置
.env                                # 本地环境变量
config/env.template                  # 环境变量模板
```

## 🚀 完整部署流程

### 步骤1：构建数据库架构

```bash
# 进入项目目录
cd backend/wechat

# 构建生产环境架构
cd data/schema && node scripts/build-schema.js --env=production
cd ../..

# 验证生成的架构文件
grep -n "WHERE" data/schema/build/schema.sql
# 确保WHERE只在SELECT语句中，不在CREATE INDEX中
```

### 步骤2：上传代码到云服务器

```bash
# 使用rsync上传代码（排除不必要的文件）
rsync -avz --exclude='node_modules' \
           --exclude='.git' \
           --exclude='*.log' \
           --exclude='cache' \
           --exclude='logs' \
           ./ <EMAIL>:/www/wwwroot/wechat.api.gongzhimall.com/
```

### 步骤3：数据库初始化

```bash
# 连接到云服务器
ssh <EMAIL>

# 删除旧数据库（如果存在）
mysql -u wechat_user -pwechat@gongzhimall123 --socket=/var/lib/mysql/mysql.sock \
  -e 'DROP DATABASE IF EXISTS gongzhimall_wechat;'

# 创建新数据库
mysql -u wechat_user -pwechat@gongzhimall123 --socket=/var/lib/mysql/mysql.sock \
  -e 'CREATE DATABASE gongzhimall_wechat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;'

# 执行架构文件
mysql -u wechat_user -pwechat@gongzhimall123 --socket=/var/lib/mysql/mysql.sock \
  gongzhimall_wechat < /www/wwwroot/wechat.api.gongzhimall.com/data/schema/build/schema.sql

# 验证数据库创建
mysql -u wechat_user -pwechat@gongzhimall123 --socket=/var/lib/mysql/mysql.sock \
  -e 'USE gongzhimall_wechat; SHOW TABLES;'
```

### 步骤4：安装依赖并重启服务

```bash
# 在云服务器上安装依赖
cd /www/wwwroot/wechat.api.gongzhimall.com
npm install --production

# 重启PM2服务
pm2 restart gongzhimall-wechat-service

# 检查服务状态
pm2 status
pm2 logs gongzhimall-wechat-service --lines 10
```

### 步骤5：验证部署

```bash
# 检查服务端口
netstat -tlnp | grep :3000

# 测试本地健康检查
curl -s http://localhost:3000/health

# 测试外部访问
curl -s https://wechat.api.gongzhimall.com/health
```

## ⚠️ 关键注意事项

### 1. MariaDB兼容性问题

**问题**：MariaDB不支持带WHERE条件的CREATE INDEX语法

```sql
-- ❌ 不兼容的语法
CREATE INDEX IF NOT EXISTS idx_binding_status_active 
ON wechat_bindings(binding_status) 
WHERE binding_status = 'active';

-- ✅ 兼容的语法
CREATE INDEX IF NOT EXISTS idx_binding_status_active 
ON wechat_bindings(binding_status);
```

**解决方案**：

- 修改 `data/schema/environments/production.sql` 文件
- 移除所有CREATE INDEX中的WHERE条件
- 重新构建schema文件

### 2. 数据库连接配置

**本地开发**：

```bash
MYSQL_HOST=localhost
```

**生产部署**：

```bash
# 临时修改用于部署
MYSQL_HOST=wechat.api.gongzhimall.com
# 部署完成后恢复
MYSQL_HOST=localhost
```

### 3. SSH连接和文件传输

**确保SSH密钥配置**：

```bash
# 测试SSH连接
ssh <EMAIL> "echo '连接成功'"

# 使用rsync进行增量同步
rsync -avz --exclude='node_modules' ./ <EMAIL>:/www/wwwroot/wechat.api.gongzhimall.com/
```

### 4. 服务重启和日志监控

**PM2管理**：

```bash
# 查看服务状态
pm2 status

# 查看日志
pm2 logs gongzhimall-wechat-service

# 重启服务
pm2 restart gongzhimall-wechat-service

# 查看错误日志
tail -f /var/log/wechat-service/error.log
```

## 🔧 故障排除

### 1. 数据库连接失败

**症状**：`ER_PARSE_ERROR` 或连接超时

**排查步骤**：

```bash
# 检查MariaDB服务状态
systemctl status mysql

# 检查socket文件位置
find /var/lib/mysql -name '*.sock'

# 测试数据库连接
mysql -u wechat_user -pwechat@gongzhimall123 --socket=/var/lib/mysql/mysql.sock -e 'SHOW DATABASES;'
```

### 2. 服务启动失败

**症状**：PM2显示服务重启次数过多

**排查步骤**：

```bash
# 查看详细错误日志
pm2 logs gongzhimall-wechat-service --lines 50

# 检查端口占用
netstat -tlnp | grep :3000

# 手动启动测试
cd /www/wwwroot/wechat.api.gongzhimall.com
node index.js
```

### 3. 架构文件语法错误

**症状**：`You have an error in your SQL syntax`

**排查步骤**：

```bash
# 检查schema文件语法
grep -n "WHERE" data/schema/build/schema.sql

# 重新构建架构
cd data/schema && node scripts/build-schema.js --env=production

# 验证生成的SQL
head -20 data/schema/build/schema.sql
```

### 4. 外部访问失败

**症状**：curl返回空或连接失败

**排查步骤**：

```bash
# 检查nginx状态
systemctl status nginx
nginx -t

# 检查防火墙
iptables -L | grep :80
iptables -L | grep :443

# 检查域名解析
nslookup wechat.api.gongzhimall.com
```

## 📊 部署验证清单

### 功能验证

- [ ] 健康检查接口正常响应
- [ ] 数据库连接正常
- [ ] 文件缓存目录可写
- [ ] 日志文件正常生成
- [ ] PM2进程稳定运行

### 性能验证

- [ ] 服务启动时间 < 30秒
- [ ] 内存使用 < 100MB
- [ ] 响应时间 < 500ms
- [ ] 无内存泄漏

### 安全验证

- [ ] HTTPS证书有效
- [ ] 数据库用户权限正确
- [ ] 文件权限设置正确
- [ ] 日志不包含敏感信息

## 🔄 自动化部署脚本

### 一键部署脚本

```bash
#!/bin/bash
# deploy/auto-deploy.sh

set -e

echo "🚀 开始自动部署..."

# 1. 构建架构
echo "📋 构建数据库架构..."
cd data/schema && node scripts/build-schema.js --env=production
cd ../..

# 2. 上传代码
echo "📤 上传代码到云服务器..."
rsync -avz --exclude='node_modules' --exclude='.git' --exclude='*.log' \
  ./ <EMAIL>:/www/wwwroot/wechat.api.gongzhimall.com/

# 3. 数据库初始化
echo "🗄️ 初始化数据库..."
ssh <EMAIL> << 'EOF'
mysql -u wechat_user -pwechat@gongzhimall123 --socket=/var/lib/mysql/mysql.sock \
  -e 'DROP DATABASE IF EXISTS gongzhimall_wechat;'
mysql -u wechat_user -pwechat@gongzhimall123 --socket=/var/lib/mysql/mysql.sock \
  -e 'CREATE DATABASE gongzhimall_wechat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;'
mysql -u wechat_user -pwechat@gongzhimall123 --socket=/var/lib/mysql/mysql.sock \
  gongzhimall_wechat < /www/wwwroot/wechat.api.gongzhimall.com/data/schema/build/schema.sql
EOF

# 4. 重启服务
echo "🔄 重启服务..."
ssh <EMAIL> "cd /www/wwwroot/wechat.api.gongzhimall.com && npm install --production && pm2 restart gongzhimall-wechat-service"

# 5. 验证部署
echo "✅ 验证部署..."
sleep 10
curl -s https://wechat.api.gongzhimall.com/health

echo "🎉 部署完成！"
```

## 📝 部署记录模板

### 部署信息记录

```markdown
## 部署记录 - YYYY-MM-DD

### 部署环境
- 服务器：wechat.api.gongzhimall.com
- 数据库：MariaDB 10.x
- Node.js版本：18.x
- PM2版本：5.x

### 部署内容
- 代码版本：commit_hash
- 数据库架构：v1.1.0
- 配置文件：production

### 部署步骤
1. [x] 构建数据库架构
2. [x] 上传代码
3. [x] 初始化数据库
4. [x] 重启服务
5. [x] 验证部署

### 验证结果
- [x] 健康检查正常
- [x] 数据库连接正常
- [x] 服务稳定运行
- [x] 外部访问正常

### 注意事项
- 修复了MariaDB索引语法兼容性问题
- 确保所有WHERE条件只在SELECT语句中使用

### 下次部署提醒
- 检查schema文件是否有新的MariaDB不兼容语法
- 验证.env文件中的数据库连接配置
```

## 🎯 最佳实践总结

1. **始终先构建和验证架构文件**
2. **使用rsync进行增量同步，提高效率**
3. **在云服务器上直接执行数据库操作**
4. **部署后立即验证所有关键功能**
5. **保持详细的部署日志和故障记录**
6. **定期备份数据库和配置文件**
7. **监控服务性能和错误日志**

---

*本文档基于实际部署经验编写，持续更新中。*
