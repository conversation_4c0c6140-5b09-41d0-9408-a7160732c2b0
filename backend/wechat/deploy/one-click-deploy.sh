#!/bin/bash
# 公职猫微信转发服务 - 生产环境一键部署脚本（优化版）

set -e

echo "🚀 公职猫微信转发服务 - 生产环境部署"
echo "=================================="

# 检查运行环境
if [ "$USER" != "root" ]; then
    echo "❌ 请使用root用户执行部署脚本"
    exit 1
fi

# 检查环境变量文件
if [ ! -f "/etc/gongzhimall/production.env" ]; then
    echo "❌ 环境变量文件不存在: /etc/gongzhimall/production.env"
    echo "请先运行: bash scripts/setup-production-env.sh"
    exit 1
fi

# 设置变量
PROJECT_DIR="/var/www/gongzhimall"
SERVICE_DIR="$PROJECT_DIR/backend/wechat"
BACKUP_DIR="/var/backups/gongzhimall/$(date +%Y%m%d_%H%M%S)"

echo "📁 项目目录: $PROJECT_DIR"
echo "🔧 服务目录: $SERVICE_DIR"
echo "💾 备份目录: $BACKUP_DIR"

# 创建备份
echo "📦 创建备份..."
mkdir -p "$BACKUP_DIR"
if [ -d "$PROJECT_DIR" ]; then
    cp -r "$PROJECT_DIR" "$BACKUP_DIR/"
fi

# 停止现有服务
echo "⏹️  停止现有服务..."
pm2 stop gongzhimall-wechat || true
pm2 delete gongzhimall-wechat || true

# 清理旧部署
echo "🧹 清理旧部署..."
rm -rf "$PROJECT_DIR"

# 克隆最新代码
echo "📥 克隆最新代码..."
git clone https://github.com/your-repo/gongzhimall.git "$PROJECT_DIR"
cd "$SERVICE_DIR"

# 安装依赖
echo "📦 安装依赖..."
npm install --production

# 启动服务
echo "🚀 启动服务..."
pm2 start ecosystem.config.js --env production

# 健康检查
echo "🔍 健康检查..."
sleep 10
if curl -f http://localhost:3000/health; then
    echo "✅ 部署成功！服务正常运行"
    pm2 save
else
    echo "❌ 健康检查失败，正在回滚..."
    pm2 stop gongzhimall-wechat
    if [ -d "$BACKUP_DIR/gongzhimall" ]; then
        cp -r "$BACKUP_DIR/gongzhimall" "$PROJECT_DIR"
        cd "$SERVICE_DIR"
        pm2 start ecosystem.config.js --env production
    fi
    exit 1
fi

echo "🎉 部署完成！"
