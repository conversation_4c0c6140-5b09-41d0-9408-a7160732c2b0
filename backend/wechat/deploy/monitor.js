#!/usr/bin/env node

/**
 * 公职猫微信转发服务 - 监控和健康检查工具
 * 提供服务状态监控、性能检查、告警通知等功能
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ServiceMonitor {
    constructor() {
        this.scriptDir = __dirname;
        this.configFile = path.join(this.scriptDir, 'deploy-config.json');
        this.config = this.loadConfig();
        this.logFile = path.join(this.scriptDir, 'monitor.log');
    }

    // 加载配置
    loadConfig() {
        try {
            const configData = fs.readFileSync(this.configFile, 'utf8');
            return JSON.parse(configData);
        } catch (error) {
            console.error(`无法加载配置文件: ${error.message}`);
            process.exit(1);
        }
    }

    // 日志记录
    log(message, level = 'INFO') {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] [${level}] ${message}\n`;
        
        console.log(`[${level}] ${message}`);
        
        try {
            fs.appendFileSync(this.logFile, logEntry);
        } catch (error) {
            console.error(`日志写入失败: ${error.message}`);
        }
    }

    // HTTP请求
    async httpRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const isHttps = urlObj.protocol === 'https:';
            const client = isHttps ? https : http;
            
            const requestOptions = {
                hostname: urlObj.hostname,
                port: urlObj.port || (isHttps ? 443 : 80),
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                timeout: options.timeout || 10000,
                headers: options.headers || {}
            };

            const req = client.request(requestOptions, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data,
                        responseTime: Date.now() - startTime
                    });
                });
            });

            const startTime = Date.now();
            
            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('请求超时'));
            });

            if (options.data) {
                req.write(options.data);
            }
            
            req.end();
        });
    }

    // 健康检查
    async healthCheck(environment) {
        const env = this.config.environments[environment];
        if (!env) {
            throw new Error(`未找到环境配置: ${environment}`);
        }

        const protocol = env.ssl?.enabled ? 'https' : 'http';
        const healthUrl = `${protocol}://${env.server.host}${env.service.healthCheckUrl}`;
        
        try {
            this.log(`开始健康检查: ${healthUrl}`);
            const response = await this.httpRequest(healthUrl, { timeout: 5000 });
            
            if (response.statusCode === 200) {
                this.log(`✅ 健康检查通过 - 响应时间: ${response.responseTime}ms`);
                return {
                    status: 'healthy',
                    responseTime: response.responseTime,
                    statusCode: response.statusCode
                };
            } else {
                this.log(`❌ 健康检查失败 - HTTP ${response.statusCode}`, 'ERROR');
                return {
                    status: 'unhealthy',
                    responseTime: response.responseTime,
                    statusCode: response.statusCode,
                    error: `HTTP ${response.statusCode}`
                };
            }
        } catch (error) {
            this.log(`❌ 健康检查异常: ${error.message}`, 'ERROR');
            return {
                status: 'error',
                error: error.message
            };
        }
    }

    // 服务状态检查
    async serviceStatus(environment) {
        const env = this.config.environments[environment];
        if (!env) {
            throw new Error(`未找到环境配置: ${environment}`);
        }

        try {
            this.log(`检查服务状态: ${environment}`);
            
            // 检查进程状态
            let processStatus;
            if (env.service.processManager === 'pm2') {
                try {
                    const pm2Output = await this.sshCommand(environment, `pm2 jlist | jq '.[] | select(.name=="${env.service.name}")'`);
                    processStatus = pm2Output ? JSON.parse(pm2Output) : null;
                } catch (error) {
                    this.log(`PM2状态检查失败: ${error.message}`, 'WARNING');
                    processStatus = null;
                }
            } else {
                try {
                    const pidFile = `${env.server.deployPath}/service.pid`;
                    const pid = await this.sshCommand(environment, `cat ${pidFile} 2>/dev/null || echo ""`);
                    if (pid.trim()) {
                        const processCheck = await this.sshCommand(environment, `ps -p ${pid.trim()} -o pid,ppid,cmd --no-headers 2>/dev/null || echo ""`);
                        processStatus = processCheck.trim() ? { pid: pid.trim(), status: 'online' } : null;
                    }
                } catch (error) {
                    processStatus = null;
                }
            }

            // 检查端口监听
            let portStatus;
            try {
                const netstatOutput = await this.sshCommand(environment, `netstat -tlnp | grep :${env.service.port} || echo ""`);
                portStatus = netstatOutput.trim() ? 'listening' : 'not_listening';
            } catch (error) {
                portStatus = 'unknown';
            }

            // 检查磁盘空间
            let diskUsage;
            try {
                const dfOutput = await this.sshCommand(environment, `df -h ${env.server.deployPath} | tail -1 | awk '{print $5}' | sed 's/%//'`);
                diskUsage = parseInt(dfOutput.trim()) || 0;
            } catch (error) {
                diskUsage = 0;
            }

            // 检查内存使用
            let memoryUsage;
            try {
                const memOutput = await this.sshCommand(environment, `free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}'`);
                memoryUsage = parseFloat(memOutput.trim()) || 0;
            } catch (error) {
                memoryUsage = 0;
            }

            return {
                process: processStatus,
                port: portStatus,
                disk: diskUsage,
                memory: memoryUsage,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            this.log(`服务状态检查失败: ${error.message}`, 'ERROR');
            throw error;
        }
    }

    // SSH命令执行
    async sshCommand(environment, command) {
        const env = this.config.environments[environment];
        const sshCmd = `ssh ${env.server.user}@${env.server.host} "${command}"`;
        
        try {
            return execSync(sshCmd, { encoding: 'utf8', timeout: 30000 });
        } catch (error) {
            throw new Error(`SSH命令执行失败: ${error.message}`);
        }
    }

    // 性能监控
    async performanceCheck(environment) {
        this.log(`开始性能检查: ${environment}`);
        
        const results = {
            timestamp: new Date().toISOString(),
            environment: environment
        };

        try {
            // 健康检查
            const health = await this.healthCheck(environment);
            results.health = health;

            // 服务状态
            const status = await this.serviceStatus(environment);
            results.status = status;

            // API响应时间测试
            const env = this.config.environments[environment];
            const protocol = env.ssl?.enabled ? 'https' : 'http';
            const apiTests = [
                { name: 'health', url: `${protocol}://${env.server.host}/health` },
                { name: 'status', url: `${protocol}://${env.server.host}/api/status` }
            ];

            results.apiTests = {};
            for (const test of apiTests) {
                try {
                    const response = await this.httpRequest(test.url, { timeout: 5000 });
                    results.apiTests[test.name] = {
                        status: 'success',
                        responseTime: response.responseTime,
                        statusCode: response.statusCode
                    };
                } catch (error) {
                    results.apiTests[test.name] = {
                        status: 'error',
                        error: error.message
                    };
                }
            }

            // 性能评分
            results.score = this.calculatePerformanceScore(results);

            this.log(`性能检查完成 - 评分: ${results.score}/100`);
            return results;

        } catch (error) {
            this.log(`性能检查失败: ${error.message}`, 'ERROR');
            results.error = error.message;
            return results;
        }
    }

    // 计算性能评分
    calculatePerformanceScore(results) {
        let score = 100;

        // 健康检查 (30分)
        if (results.health?.status !== 'healthy') {
            score -= 30;
        } else if (results.health.responseTime > 1000) {
            score -= 10;
        }

        // 进程状态 (20分)
        if (!results.status?.process || results.status.process.status !== 'online') {
            score -= 20;
        }

        // 端口监听 (20分)
        if (results.status?.port !== 'listening') {
            score -= 20;
        }

        // 磁盘使用 (15分)
        if (results.status?.disk > 90) {
            score -= 15;
        } else if (results.status?.disk > 80) {
            score -= 8;
        }

        // 内存使用 (15分)
        if (results.status?.memory > 90) {
            score -= 15;
        } else if (results.status?.memory > 80) {
            score -= 8;
        }

        return Math.max(0, score);
    }

    // 告警检查
    async alertCheck(environment) {
        const results = await this.performanceCheck(environment);
        const alerts = [];

        // 检查告警条件
        if (results.health?.status !== 'healthy') {
            alerts.push({
                level: 'critical',
                message: '服务健康检查失败',
                details: results.health
            });
        }

        if (results.status?.disk > 90) {
            alerts.push({
                level: 'warning',
                message: `磁盘使用率过高: ${results.status.disk}%`,
                details: { disk: results.status.disk }
            });
        }

        if (results.status?.memory > 90) {
            alerts.push({
                level: 'warning',
                message: `内存使用率过高: ${results.status.memory}%`,
                details: { memory: results.status.memory }
            });
        }

        if (results.health?.responseTime > 5000) {
            alerts.push({
                level: 'warning',
                message: `响应时间过长: ${results.health.responseTime}ms`,
                details: { responseTime: results.health.responseTime }
            });
        }

        // 记录告警
        if (alerts.length > 0) {
            this.log(`发现 ${alerts.length} 个告警`, 'WARNING');
            alerts.forEach(alert => {
                this.log(`[${alert.level.toUpperCase()}] ${alert.message}`, 'WARNING');
            });
        } else {
            this.log('未发现告警，服务运行正常');
        }

        return {
            timestamp: new Date().toISOString(),
            environment: environment,
            alerts: alerts,
            performance: results
        };
    }

    // 生成监控报告
    generateReport(environment, results) {
        const report = {
            title: '公职猫微信转发服务监控报告',
            environment: environment,
            timestamp: new Date().toISOString(),
            summary: {
                status: results.health?.status || 'unknown',
                score: results.score || 0,
                alerts: results.alerts?.length || 0
            },
            details: results
        };

        // 保存报告
        const reportFile = path.join(this.scriptDir, `monitor-report-${environment}-${Date.now()}.json`);
        fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

        this.log(`监控报告已生成: ${reportFile}`);
        return report;
    }

    // 生成仪表板HTML
    generateDashboard(environment, results) {
        const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公职猫微信转发服务 - 监控仪表板</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .title { font-size: 24px; color: #333; margin-bottom: 10px; }
        .subtitle { color: #666; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .card-title { font-size: 18px; margin-bottom: 15px; color: #333; }
        .status-healthy { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .metric-label { color: #666; }
        .metric-value { font-weight: bold; }
        .progress-bar { width: 100%; height: 8px; background: #e9ecef; border-radius: 4px; overflow: hidden; }
        .progress-fill { height: 100%; transition: width 0.3s ease; }
        .progress-green { background: #28a745; }
        .progress-yellow { background: #ffc107; }
        .progress-red { background: #dc3545; }
        .alert { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .alert-critical { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .timestamp { font-size: 12px; color: #999; }
        .refresh-btn { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }
        .refresh-btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">公职猫微信转发服务监控仪表板</h1>
            <p class="subtitle">环境: ${environment} | 更新时间: ${new Date().toLocaleString('zh-CN')}</p>
            <button class="refresh-btn" onclick="location.reload()">刷新</button>
        </div>

        <div class="grid">
            <!-- 服务状态卡片 -->
            <div class="card">
                <h2 class="card-title">服务状态</h2>
                <div class="metric">
                    <span class="metric-label">健康状态:</span>
                    <span class="metric-value status-${results.health?.status === 'healthy' ? 'healthy' : 'error'}">
                        ${results.health?.status || 'unknown'}
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">响应时间:</span>
                    <span class="metric-value">${results.health?.responseTime || 'N/A'}ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">进程状态:</span>
                    <span class="metric-value">${results.status?.process?.status || 'unknown'}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">端口监听:</span>
                    <span class="metric-value">${results.status?.port || 'unknown'}</span>
                </div>
            </div>

            <!-- 性能指标卡片 -->
            <div class="card">
                <h2 class="card-title">性能指标</h2>
                <div class="metric">
                    <span class="metric-label">综合评分:</span>
                    <span class="metric-value">${results.score || 0}/100</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill progress-${results.score >= 80 ? 'green' : results.score >= 60 ? 'yellow' : 'red'}"
                         style="width: ${results.score || 0}%"></div>
                </div>

                <div class="metric">
                    <span class="metric-label">磁盘使用:</span>
                    <span class="metric-value">${results.status?.disk || 0}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill progress-${(results.status?.disk || 0) < 80 ? 'green' : (results.status?.disk || 0) < 90 ? 'yellow' : 'red'}"
                         style="width: ${results.status?.disk || 0}%"></div>
                </div>

                <div class="metric">
                    <span class="metric-label">内存使用:</span>
                    <span class="metric-value">${results.status?.memory || 0}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill progress-${(results.status?.memory || 0) < 80 ? 'green' : (results.status?.memory || 0) < 90 ? 'yellow' : 'red'}"
                         style="width: ${results.status?.memory || 0}%"></div>
                </div>
            </div>

            <!-- API测试卡片 -->
            <div class="card">
                <h2 class="card-title">API测试</h2>
                ${Object.entries(results.apiTests || {}).map(([name, test]) => `
                    <div class="metric">
                        <span class="metric-label">${name}:</span>
                        <span class="metric-value status-${test.status === 'success' ? 'healthy' : 'error'}">
                            ${test.status === 'success' ? `${test.responseTime}ms` : '失败'}
                        </span>
                    </div>
                `).join('')}
            </div>

            <!-- 告警信息卡片 -->
            <div class="card">
                <h2 class="card-title">告警信息</h2>
                ${results.alerts && results.alerts.length > 0 ?
                    results.alerts.map(alert => `
                        <div class="alert alert-${alert.level}">
                            <strong>[${alert.level.toUpperCase()}]</strong> ${alert.message}
                        </div>
                    `).join('') :
                    '<p style="color: #28a745;">✅ 无告警信息</p>'
                }
            </div>
        </div>

        <div class="timestamp">
            最后更新: ${new Date().toLocaleString('zh-CN')}
        </div>
    </div>

    <script>
        // 自动刷新 (每30秒)
        setTimeout(() => location.reload(), 30000);
    </script>
</body>
</html>`;

        const dashboardFile = path.join(this.scriptDir, `dashboard-${environment}.html`);
        fs.writeFileSync(dashboardFile, html);

        this.log(`监控仪表板已生成: ${dashboardFile}`);
        return dashboardFile;
    }

    // 持续监控
    async continuousMonitor(environment, interval = 60000) {
        this.log(`开始持续监控: ${environment}, 间隔: ${interval}ms`);
        
        const monitor = async () => {
            try {
                const results = await this.alertCheck(environment);
                
                // 如果有告警，生成报告
                if (results.alerts.length > 0) {
                    this.generateReport(environment, results);
                }
                
            } catch (error) {
                this.log(`监控检查失败: ${error.message}`, 'ERROR');
            }
        };

        // 立即执行一次
        await monitor();
        
        // 设置定时监控
        setInterval(monitor, interval);
        
        this.log('持续监控已启动，按 Ctrl+C 停止');
    }
}

// 命令行接口
async function main() {
    const args = process.argv.slice(2);
    const command = args[0] || 'health';
    const environment = args[1] || 'production';
    
    const monitor = new ServiceMonitor();
    
    try {
        switch (command) {
            case 'health':
                const health = await monitor.healthCheck(environment);
                console.log(JSON.stringify(health, null, 2));
                break;
                
            case 'status':
                const status = await monitor.serviceStatus(environment);
                console.log(JSON.stringify(status, null, 2));
                break;
                
            case 'performance':
                const perf = await monitor.performanceCheck(environment);
                console.log(JSON.stringify(perf, null, 2));
                break;
                
            case 'alert':
                const alerts = await monitor.alertCheck(environment);
                console.log(JSON.stringify(alerts, null, 2));
                break;
                
            case 'report':
                const results = await monitor.performanceCheck(environment);
                const report = monitor.generateReport(environment, results);
                console.log(JSON.stringify(report, null, 2));
                break;
                
            case 'watch':
                const interval = parseInt(args[2]) || 60000;
                await monitor.continuousMonitor(environment, interval);
                break;
                
            default:
                console.log(`
用法: node monitor.js <command> [environment] [options]

命令:
  health      - 健康检查
  status      - 服务状态
  performance - 性能检查
  alert       - 告警检查
  report      - 生成监控报告
  watch       - 持续监控 [间隔毫秒]

环境: production, staging

示例:
  node monitor.js health production
  node monitor.js watch production 30000
                `);
                break;
        }
    } catch (error) {
        console.error(`执行失败: ${error.message}`);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = ServiceMonitor;
