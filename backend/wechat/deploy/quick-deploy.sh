#!/bin/bash

# 公职猫微信转发服务 - 快速部署脚本
# 用于日常代码更新，不重新初始化数据库

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 配置变量
SERVER_HOST="wechat.api.gongzhimall.com"
SERVER_USER="root"
SERVER_PATH="/www/wwwroot/wechat.api.gongzhimall.com"
SERVICE_NAME="gongzhimall-wechat-service"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 主函数
main() {
    echo "🚀 快速部署 - 代码更新"
    echo "========================="
    
    # 1. 上传代码
    log_info "上传代码到云服务器..."
    rsync -avz --exclude='node_modules' \
               --exclude='.git' \
               --exclude='*.log' \
               --exclude='cache' \
               --exclude='logs' \
               ./ ${SERVER_USER}@${SERVER_HOST}:${SERVER_PATH}/
    
    # 2. 安装依赖并重启服务
    log_info "安装依赖并重启服务..."
    ssh ${SERVER_USER}@${SERVER_HOST} << EOF
        cd ${SERVER_PATH}
        npm install --production
        pm2 restart ${SERVICE_NAME}
        sleep 5
        pm2 status
EOF
    
    # 3. 验证部署
    log_info "验证部署..."
    sleep 10
    
    # 测试健康检查
    health_response=$(curl -s https://${SERVER_HOST}/health)
    if [[ "$health_response" =~ "status.*ok" ]]; then
        log_success "部署成功！"
        echo "📡 服务地址: https://${SERVER_HOST}"
        echo "🏥 健康检查: https://${SERVER_HOST}/health"
    else
        log_warning "健康检查失败，请手动检查服务状态"
        echo "响应: $health_response"
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 