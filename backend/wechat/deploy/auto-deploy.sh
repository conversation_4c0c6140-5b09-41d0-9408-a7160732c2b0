#!/bin/bash

# 公职猫微信转发服务 - 自动化部署脚本
# 基于实际部署经验编写，包含完整的错误处理和验证

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SERVER_HOST="wechat.api.gongzhimall.com"
SERVER_USER="root"
SERVER_PATH="/www/wwwroot/wechat.api.gongzhimall.com"
DB_USER="wechat_user"
DB_PASS="wechat@gongzhimall123"
DB_NAME="gongzhimall_wechat"
SERVICE_NAME="gongzhimall-wechat-service"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 错误处理函数
handle_error() {
    log_error "部署失败: $1"
    exit 1
}

# 检查前置条件
check_prerequisites() {
    log_info "检查部署前置条件..."
    
    # 检查是否在正确的目录
    if [[ ! -f "package.json" ]] || [[ ! -d "deploy" ]]; then
        handle_error "请在backend/wechat目录下运行此脚本"
    fi
    
    # 检查SSH连接
    log_info "测试SSH连接..."
    if ! ssh -o ConnectTimeout=10 ${SERVER_USER}@${SERVER_HOST} "echo 'SSH连接正常'" > /dev/null 2>&1; then
        handle_error "无法连接到云服务器，请检查SSH配置"
    fi
    
    # 检查MySQL客户端
    if ! command -v mysql &> /dev/null; then
        log_warning "MySQL客户端未安装，尝试安装..."
        if command -v brew &> /dev/null; then
            brew install mysql-client
            export PATH="/opt/homebrew/Cellar/mysql-client/9.3.0/bin:$PATH"
        else
            handle_error "请手动安装MySQL客户端"
        fi
    fi
    
    log_success "前置条件检查通过"
}

# 构建数据库架构
build_schema() {
    log_info "构建数据库架构..."
    
    cd data/schema
    
    # 检查构建脚本是否存在
    if [[ ! -f "scripts/build-schema.js" ]]; then
        handle_error "数据库架构构建脚本不存在"
    fi
    
    # 构建生产环境架构
    if ! node scripts/build-schema.js --env=production; then
        handle_error "数据库架构构建失败"
    fi
    
    # 验证生成的架构文件
    if grep -q "CREATE INDEX.*WHERE" build/schema.sql; then
        log_error "发现MariaDB不兼容的索引语法，请修复后重试"
        log_error "问题文件: data/schema/environments/production.sql"
        exit 1
    fi
    
    cd ../..
    log_success "数据库架构构建完成"
}

# 上传代码到云服务器
upload_code() {
    log_info "上传代码到云服务器..."
    
    # 创建临时排除文件列表
    cat > /tmp/rsync_exclude.txt << EOF
node_modules/
.git/
*.log
cache/
logs/
.env.backup
EOF
    
    # 使用rsync上传代码
    if ! rsync -avz --exclude-from=/tmp/rsync_exclude.txt \
        ./ ${SERVER_USER}@${SERVER_HOST}:${SERVER_PATH}/; then
        handle_error "代码上传失败"
    fi
    
    # 清理临时文件
    rm -f /tmp/rsync_exclude.txt
    
    log_success "代码上传完成"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 在云服务器上执行数据库操作
    ssh ${SERVER_USER}@${SERVER_HOST} << EOF
        set -e
        
        echo "删除旧数据库..."
        mysql -u ${DB_USER} -p${DB_PASS} --socket=/var/lib/mysql/mysql.sock \
            -e 'DROP DATABASE IF EXISTS ${DB_NAME};' || exit 1
        
        echo "创建新数据库..."
        mysql -u ${DB_USER} -p${DB_PASS} --socket=/var/lib/mysql/mysql.sock \
            -e 'CREATE DATABASE ${DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;' || exit 1
        
        echo "执行架构文件..."
        mysql -u ${DB_USER} -p${DB_PASS} --socket=/var/lib/mysql/mysql.sock \
            ${DB_NAME} < ${SERVER_PATH}/data/schema/build/schema.sql || exit 1
        
        echo "验证数据库创建..."
        TABLE_COUNT=\$(mysql -u ${DB_USER} -p${DB_PASS} --socket=/var/lib/mysql/mysql.sock \
            -e "USE ${DB_NAME}; SHOW TABLES;" | wc -l)
        
        if [ \$TABLE_COUNT -lt 10 ]; then
            echo "警告: 数据库表数量异常 (\$TABLE_COUNT)"
            exit 1
        fi
        
        echo "数据库初始化完成，共创建 \$TABLE_COUNT 个表"
EOF
    
    if [ $? -ne 0 ]; then
        handle_error "数据库初始化失败"
    fi
    
    log_success "数据库初始化完成"
}

# 安装依赖并重启服务
restart_service() {
    log_info "安装依赖并重启服务..."
    
    ssh ${SERVER_USER}@${SERVER_HOST} << EOF
        set -e
        
        cd ${SERVER_PATH}
        
        echo "安装依赖..."
        npm install --production || exit 1
        
        echo "检查PM2服务状态..."
        if ! pm2 list | grep -q "${SERVICE_NAME}"; then
            echo "服务未运行，启动服务..."
            pm2 start ecosystem.config.js --name "${SERVICE_NAME}" || exit 1
        else
            echo "重启服务..."
            pm2 restart ${SERVICE_NAME} || exit 1
        fi
        
        echo "等待服务启动..."
        sleep 5
        
        echo "检查服务状态..."
        if ! pm2 list | grep -q "${SERVICE_NAME}.*online"; then
            echo "服务启动失败，查看日志..."
            pm2 logs ${SERVICE_NAME} --lines 20
            exit 1
        fi
        
        echo "服务重启完成"
EOF
    
    if [ $? -ne 0 ]; then
        handle_error "服务重启失败"
    fi
    
    log_success "服务重启完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 等待服务完全启动
    sleep 10
    
    # 检查服务端口
    if ! ssh ${SERVER_USER}@${SERVER_HOST} "netstat -tlnp | grep :3000" > /dev/null 2>&1; then
        handle_error "服务端口未监听"
    fi
    
    # 测试本地健康检查
    local_health=$(ssh ${SERVER_USER}@${SERVER_HOST} "curl -s http://localhost:3000/health")
    if [[ ! "$local_health" =~ "status.*ok" ]]; then
        handle_error "本地健康检查失败: $local_health"
    fi
    
    # 测试外部访问
    external_health=$(curl -s https://${SERVER_HOST}/health)
    if [[ ! "$external_health" =~ "status.*ok" ]]; then
        log_warning "外部健康检查失败，可能是DNS或SSL问题"
        log_warning "响应: $external_health"
    else
        log_success "外部访问正常"
    fi
    
    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    log_info "显示部署信息..."
    
    ssh ${SERVER_USER}@${SERVER_HOST} << EOF
        echo "=== 服务状态 ==="
        pm2 status
        
        echo -e "\n=== 服务日志 (最近10行) ==="
        pm2 logs ${SERVICE_NAME} --lines 10
        
        echo -e "\n=== 数据库表 ==="
        mysql -u ${DB_USER} -p${DB_PASS} --socket=/var/lib/mysql/mysql.sock \
            -e "USE ${DB_NAME}; SHOW TABLES;" | tail -n +2
        
        echo -e "\n=== 系统资源 ==="
        free -h
        df -h /www/wwwroot
EOF
}

# 主函数
main() {
    echo "🚀 公职猫微信转发服务 - 自动化部署"
    echo "====================================="
    echo "服务器: ${SERVER_HOST}"
    echo "数据库: ${DB_NAME}"
    echo "服务名: ${SERVICE_NAME}"
    echo "====================================="
    echo ""
    
    # 执行部署步骤
    check_prerequisites
    build_schema
    upload_code
    init_database
    restart_service
    verify_deployment
    show_deployment_info
    
    echo ""
    echo "🎉 部署完成！"
    echo "📡 服务地址: https://${SERVER_HOST}"
    echo "🏥 健康检查: https://${SERVER_HOST}/health"
    echo "📊 监控地址: https://${SERVER_HOST}/api/wechat/webhook"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 