#!/bin/bash
# 公职猫微信转发服务 - 环境变量配置脚本（简化版）

set -e

echo "🔧 公职猫环境变量配置"
echo "===================="

# 检查运行环境
if [ "$USER" != "root" ]; then
    echo "❌ 请使用root用户执行此脚本"
    exit 1
fi

echo "⚠️  警告：此脚本将设置生产环境敏感配置"
echo "请确认您在腾讯云生产服务器上执行 (输入 'YES' 确认):"
read confirmation

if [ "$confirmation" != "YES" ]; then
    echo "操作已取消"
    exit 1
fi

# 创建安全目录
echo "📁 创建配置目录..."
mkdir -p /etc/gongzhimall
mkdir -p /var/log/gongzhimall
mkdir -p /var/www/cache
mkdir -p /var/backups/gongzhimall

# 设置目录权限
chmod 700 /etc/gongzhimall
chmod 755 /var/log/gongzhimall
chmod 755 /var/www/cache
chmod 755 /var/backups/gongzhimall

echo "🔐 请输入生产环境配置信息："

# 交互式输入敏感信息
echo "请输入腾讯云Secret ID:"
read -r TENCENT_SECRET_ID

echo "请输入腾讯云Secret Key:"
read -s TENCENT_SECRET_KEY

echo "请输入MySQL密码:"
read -s MYSQL_PASSWORD

echo "请输入企业微信Corp ID:"
read -r WECHAT_CORP_ID

echo "请输入企业微信Corp Secret:"
read -s WECHAT_CORP_SECRET

echo "请输入企业微信Agent ID:"
read -r WECHAT_AGENT_ID

echo "请输入极光推送App Key:"
read -r JPUSH_APP_KEY

echo "请输入极光推送Master Secret:"
read -s JPUSH_MASTER_SECRET

# 创建生产环境变量文件
echo "📝 创建环境变量文件..."
cat > /etc/gongzhimall/production.env << EOF
# 公职猫微信转发服务 - 生产环境配置
NODE_ENV=production

# 腾讯云配置
TENCENT_SECRET_ID=$TENCENT_SECRET_ID
TENCENT_SECRET_KEY=$TENCENT_SECRET_KEY

# 服务器配置
PORT=3000
SERVER_DOMAIN=wechat.api.gongzhimall.com
FILE_STORAGE_PATH=/var/www/cache
MAX_FILE_SIZE=104857600

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=wechat_user
MYSQL_PASSWORD=$MYSQL_PASSWORD
MYSQL_DATABASE=gongzhimall_wechat

# 企业微信配置
WECHAT_CORP_ID=$WECHAT_CORP_ID
WECHAT_CORP_SECRET=$WECHAT_CORP_SECRET
WECHAT_AGENT_ID=$WECHAT_AGENT_ID
WECHAT_API_BASE=https://qyapi.weixin.qq.com

# 极光推送配置
JPUSH_APP_KEY=$JPUSH_APP_KEY
JPUSH_MASTER_SECRET=$JPUSH_MASTER_SECRET

# 应用配置
TOKEN_SECRET=gongzhimall-app-2025
FILE_ENCRYPTION_KEY=gongzhimall-file-encryption-2025

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=/var/log/gongzhimall
EOF

# 设置文件权限
chmod 600 /etc/gongzhimall/production.env
chown root:root /etc/gongzhimall/production.env

echo "✅ 环境变量配置完成！"
echo "配置文件: /etc/gongzhimall/production.env"
echo ""
echo "⚠️  重要提醒："
echo "1. 环境变量文件包含敏感信息，请妥善保管"
echo "2. 定期备份配置文件"
echo "3. 现在可以运行部署脚本: bash one-click-deploy.sh"
