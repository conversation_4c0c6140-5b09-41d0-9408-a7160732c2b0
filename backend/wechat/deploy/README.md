# 部署文件说明

本目录包含公职猫微信转发服务的所有部署相关文件和脚本。

## 📁 文件结构

```
deploy/
├── README.md                    # 本文件
├── DEPLOYMENT_GUIDE.md          # 完整部署指南
├── DEPLOYMENT_RECORD.md         # 部署记录
├── auto-deploy.sh              # 自动化部署脚本
├── quick-deploy.sh             # 快速部署脚本
├── production-deploy.sh         # 生产环境部署脚本
├── one-click-deploy.sh         # 一键部署脚本
└── deploy-config.json          # 部署配置文件
```

## 🚀 快速开始

### 完整部署（推荐）

```bash
# 在backend/wechat目录下执行
./deploy/auto-deploy.sh
```

### 快速更新

```bash
# 仅更新代码，不重新初始化数据库
./deploy/quick-deploy.sh
```

## 📋 脚本说明

### 1. auto-deploy.sh - 自动化部署脚本

**功能**: 完整的自动化部署流程，包含数据库初始化
**特点**: 
- 自动检查前置条件
- 构建数据库架构
- 上传代码到云服务器
- 初始化数据库
- 重启服务
- 验证部署结果

**使用场景**: 
- 首次部署
- 重大更新
- 数据库结构变更

### 2. quick-deploy.sh - 快速部署脚本

**功能**: 日常代码更新部署
**特点**: 
- 不重新初始化数据库
- 快速上传代码
- 重启服务
- 基本验证

**使用场景**: 
- 代码bug修复
- 功能优化
- 配置更新

### 3. production-deploy.sh - 生产环境部署脚本

**功能**: 原有的生产环境部署脚本
**特点**: 
- 包含监控和日志
- 详细的错误处理
- 环境检查

### 4. one-click-deploy.sh - 一键部署脚本

**功能**: 简化的一键部署
**特点**: 
- 简单的部署流程
- 基本的错误处理

## 📖 文档说明

### 1. DEPLOYMENT_GUIDE.md - 完整部署指南

**内容**: 
- 详细的部署流程
- 关键注意事项
- 故障排除方法
- 最佳实践总结

**用途**: 
- 新团队成员培训
- 部署问题排查
- 部署流程标准化

### 2. DEPLOYMENT_RECORD.md - 部署记录

**内容**: 
- 本次部署的详细记录
- 遇到的问题和解决方案
- 经验总结
- 下次部署提醒

**用途**: 
- 部署历史记录
- 经验沉淀
- 问题追踪

## ⚠️ 重要注意事项

### 1. MariaDB兼容性

- 确保所有SQL语法与MariaDB兼容
- 避免使用PostgreSQL特有的功能
- 特别注意CREATE INDEX的WHERE条件

### 2. 环境配置

- 部署前检查.env文件配置
- 确保SSH连接正常
- 验证云服务器环境

### 3. 数据库操作

- 生产环境谨慎执行DROP DATABASE
- 建议先备份重要数据
- 验证数据库连接和权限

## 🔧 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查SSH密钥配置
   - 验证服务器地址和用户名

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证用户名和密码
   - 确认socket文件路径

3. **服务启动失败**
   - 查看PM2日志
   - 检查端口占用
   - 验证依赖安装

### 获取帮助

1. 查看 `DEPLOYMENT_GUIDE.md` 中的故障排除部分
2. 检查 `DEPLOYMENT_RECORD.md` 中的经验总结
3. 查看服务日志: `pm2 logs gongzhimall-wechat-service`

## 📞 联系信息

如有部署相关问题，请参考：
- 部署指南: `DEPLOYMENT_GUIDE.md`
- 部署记录: `DEPLOYMENT_RECORD.md`
- 项目文档: 项目根目录的README.md

---

*最后更新: 2025-07-27*
*版本: v1.1.0*
