#!/bin/bash

# 公职猫微信转发服务 - 生产环境部署脚本
# 包含数据库清理、环境验证、代码部署的完整流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

# 显示横幅
show_banner() {
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                    公职猫微信转发服务                          ║${NC}"
    echo -e "${CYAN}║                   生产环境完整部署工具                         ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  🗄️  数据库清理  ⚙️  环境验证  🚀 代码部署  📊 健康检查        ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 检查依赖
check_dependencies() {
    log "🔍 检查部署依赖..."
    
    local missing_tools=()
    
    # 检查必需工具
    command -v node >/dev/null 2>&1 || missing_tools+=("node")
    command -v npm >/dev/null 2>&1 || missing_tools+=("npm")
    command -v mysql >/dev/null 2>&1 || missing_tools+=("mysql")
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        error "缺少必需工具: ${missing_tools[*]}"
    fi
    
    # 检查Node.js版本
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        error "Node.js版本过低: $(node -v)，需要18+版本"
    fi
    
    log "✅ 依赖检查通过"
}

# 检查环境变量文件
check_env_file() {
    log "📋 检查环境变量配置..."
    
    local env_file="$PROJECT_DIR/.env"
    
    if [ ! -f "$env_file" ]; then
        warning "未找到 .env 文件，从模板创建..."
        
        if [ -f "$PROJECT_DIR/config/env.template" ]; then
            cp "$PROJECT_DIR/config/env.template" "$env_file"
            info "已从模板创建 .env 文件"
            echo ""
            echo -e "${YELLOW}⚠️  请编辑 .env 文件并填写正确的配置值${NC}"
            echo "文件位置: $env_file"
            echo ""
            read -p "编辑完成后按回车继续..." -r
        else
            error "未找到环境变量模板文件"
        fi
    fi
    
    log "✅ 环境变量文件检查完成"
}

# 验证环境变量
validate_environment() {
    log "🔍 验证环境变量配置..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "scripts/validate-production-env.js" ]; then
        if node scripts/validate-production-env.js; then
            success "环境变量验证通过"
        else
            error "环境变量验证失败，请检查配置"
        fi
    else
        warning "未找到环境变量验证脚本，跳过验证"
    fi
}

# 清理和初始化数据库
clean_and_init_database() {
    log "🗄️  清理和初始化数据库..."
    
    cd "$PROJECT_DIR"
    
    echo ""
    echo -e "${YELLOW}⚠️  警告: 此操作将删除现有数据库并重新创建${NC}"
    echo "数据库名称: gongzhimall_wechat"
    echo "这将清除所有现有数据！"
    echo ""
    
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        info "数据库清理已取消"
        return 0
    fi
    
    if [ -f "scripts/clean-and-init-database.js" ]; then
        if node scripts/clean-and-init-database.js; then
            success "数据库清理和初始化完成"
        else
            error "数据库初始化失败"
        fi
    else
        error "未找到数据库初始化脚本"
    fi
}

# 安装依赖
install_dependencies() {
    log "📦 安装项目依赖..."
    
    cd "$PROJECT_DIR"
    
    if [ -f "package.json" ]; then
        npm install --production
        success "依赖安装完成"
    else
        error "未找到 package.json 文件"
    fi
}

# 构建项目
build_project() {
    log "🔨 构建项目..."
    
    cd "$PROJECT_DIR"
    
    # 检查是否有构建脚本
    if npm run | grep -q "build"; then
        npm run build
        success "项目构建完成"
    else
        info "无需构建，跳过构建步骤"
    fi
}

# 启动服务
start_service() {
    log "🚀 启动微信转发服务..."
    
    cd "$PROJECT_DIR"
    
    # 检查PM2是否安装
    if ! command -v pm2 >/dev/null 2>&1; then
        info "安装PM2..."
        npm install -g pm2
    fi
    
    # 停止现有服务
    pm2 stop gongzhimall-wechat 2>/dev/null || true
    pm2 delete gongzhimall-wechat 2>/dev/null || true
    
    # 启动服务
    if [ -f "ecosystem.config.js" ]; then
        pm2 start ecosystem.config.js --env production
    else
        pm2 start index.js --name gongzhimall-wechat --env production
    fi
    
    # 保存PM2配置
    pm2 save
    pm2 startup
    
    success "服务启动完成"
}

# 健康检查
health_check() {
    log "🏥 执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    local health_url="http://localhost:3000/health"
    
    while [ $attempt -le $max_attempts ]; do
        info "健康检查尝试 $attempt/$max_attempts..."
        
        if curl -f -s "$health_url" >/dev/null 2>&1; then
            success "健康检查通过"
            return 0
        fi
        
        sleep 2
        ((attempt++))
    done
    
    error "健康检查失败，服务可能未正常启动"
}

# 显示部署结果
show_deployment_result() {
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    success "🎉 生产环境部署完成！"
    echo ""
    echo -e "${BLUE}📊 部署统计:${NC}"
    echo "  环境: production"
    echo "  耗时: ${duration}秒"
    echo "  状态: 成功"
    echo ""
    
    echo -e "${YELLOW}📝 部署信息:${NC}"
    echo "  服务名称: gongzhimall-wechat"
    echo "  运行端口: 3000"
    echo "  数据库: localhost:3306/gongzhimall_wechat"
    echo "  健康检查: http://localhost:3000/health"
    echo ""
    
    echo -e "${BLUE}🔧 常用管理命令:${NC}"
    echo "  查看状态: pm2 status"
    echo "  查看日志: pm2 logs gongzhimall-wechat"
    echo "  重启服务: pm2 restart gongzhimall-wechat"
    echo "  停止服务: pm2 stop gongzhimall-wechat"
    echo ""
    
    echo -e "${GREEN}✨ 微信转发服务已在生产环境成功部署！${NC}"
}

# 错误处理
handle_error() {
    local exit_code=$?
    echo ""
    error "部署过程中发生错误 (退出码: $exit_code)"
    echo ""
    echo -e "${YELLOW}🔧 故障排除建议:${NC}"
    echo "1. 检查数据库连接和权限"
    echo "2. 验证环境变量配置"
    echo "3. 查看详细错误日志"
    echo "4. 检查端口是否被占用"
    echo ""
    exit $exit_code
}

# 主函数
main() {
    # 设置错误处理
    trap 'handle_error' ERR
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 显示横幅
    show_banner
    
    # 确认部署
    echo -e "${YELLOW}📋 生产环境部署确认:${NC}"
    echo "  项目: 公职猫微信转发服务"
    echo "  环境: production"
    echo "  数据库: 将清理并重新初始化"
    echo "  时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    
    read -p "确认开始生产环境部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        info "部署已取消"
        exit 0
    fi
    
    # 执行部署流程
    check_dependencies
    check_env_file
    validate_environment
    clean_and_init_database
    install_dependencies
    build_project
    start_service
    health_check
    show_deployment_result
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
