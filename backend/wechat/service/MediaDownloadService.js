/**
 * 媒体下载服务
 * 专门处理媒体文件下载和管理
 */

const wechatApi = require('./wechatApi');
const { logInfo, logError } = require('../api/errorHandler');
const fileService = require('./fileService');

/**
 * 获取企业微信访问令牌
 */
const getAccessToken = async () => {
  try {
    return await wechatApi.getAccessToken();
  } catch (error) {
    logError('获取access_token失败:', error);
    throw error;
  }
};

/**
 * 获取媒体文件信息（不下载文件内容）
 */
const getMediaFileInfo = async (mediaId) => {
  try {
    const result = await wechatApi.getMediaInfo(mediaId);
    if (result.success) {
      return {
        success: true,
        mediaId,
        contentType: result.contentType,
        size: result.size,
        fileName: result.fileName
      };
    } else {
      return {
        success: false,
        message: result.message
      };
    }
  } catch (error) {
    logError('获取媒体文件信息失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 验证媒体文件是否可下载
 */
const validateMediaAccess = async (mediaId, userUuid) => {
  try {
    // 检查媒体ID格式
    if (!mediaId || typeof mediaId !== 'string') {
      return {
        success: false,
        message: '无效的媒体ID'
      };
    }
    
    // 检查用户权限（这里可以添加更多的权限验证逻辑）
    if (!userUuid) {
      return {
        success: false,
        message: '用户身份验证失败'
      };
    }
    
    // 尝试获取媒体文件信息来验证访问权限
    const infoResult = await getMediaFileInfo(mediaId);
    if (!infoResult.success) {
      return {
        success: false,
        message: '媒体文件不存在或已过期'
      };
    }
    
    return {
      success: true,
      message: '媒体文件访问验证通过',
      fileInfo: infoResult
    };
  } catch (error) {
    logError('验证媒体文件访问权限失败:', error);
    return {
      success: false,
      message: '访问权限验证失败'
    };
  }
};

/**
 * 下载微信媒体文件（安全链路）
 */
const downloadWeChatMediaFile = async (mediaId, userUuid, messageId = null, originalFileName = null) => {
  try {
    // messageId 可选，便于日志和缓存唯一性
    const msgId = messageId || mediaId;
    const result = await fileService.downloadWeChatMediaAndCache(mediaId, msgId, originalFileName, userUuid);
    if (!result.success) {
      throw new Error('下载媒体文件失败: ' + result.message);
    }
    return result;
  } catch (error) {
    logError('下载微信媒体文件失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 批量下载媒体文件（安全链路）
 */
const batchDownloadMediaFiles = async (mediaIds, userUuid) => {
  try {
    logInfo(`📥 开始批量下载 ${mediaIds.length} 个媒体文件`);
    const results = [];
    const concurrency = 3; // 并发下载数量限制
    
    // 分批处理，避免同时下载过多文件
    for (let i = 0; i < mediaIds.length; i += concurrency) {
      const batch = mediaIds.slice(i, i + concurrency);
      const batchPromises = batch.map(async (mediaId, index) => {
        const messageId = `${mediaId}_${Date.now()}_${index}`;
        try {
          const result = await fileService.downloadWeChatMediaAndCache(mediaId, messageId, null, userUuid);
          return {
            mediaId,
            success: result.success,
            message: result.message || '下载成功',
            fileInfo: result.success ? result : null
          };
        } catch (error) {
          logError(`批量下载失败 mediaId=${mediaId}:`, error);
          return {
            mediaId,
            success: false,
            message: error.message
          };
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // 批次间延迟，避免API限流
      if (i + concurrency < mediaIds.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    const successCount = results.filter(r => r.success).length;
    logInfo(`✅ 批量下载完成: ${successCount}/${mediaIds.length} 成功`);
    
    return {
      success: true,
      totalCount: mediaIds.length,
      successCount,
      failedCount: mediaIds.length - successCount,
      results
    };
  } catch (error) {
    logError('批量下载媒体文件失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 预热下载常用媒体文件
 */
const preloadMediaFiles = async (mediaIds, userUuid) => {
  try {
    logInfo(`🔥 开始预热下载 ${mediaIds.length} 个媒体文件`);
    
    // 预热下载使用较低的并发数，避免影响正常业务
    const concurrency = 2;
    const results = [];
    
    for (let i = 0; i < mediaIds.length; i += concurrency) {
      const batch = mediaIds.slice(i, i + concurrency);
      const batchPromises = batch.map(async (mediaId, index) => {
        const messageId = `preload_${mediaId}_${Date.now()}_${index}`;
        try {
          const result = await fileService.downloadWeChatMediaAndCache(mediaId, messageId, null, userUuid);
          return {
            mediaId,
            success: result.success,
            message: result.message || '预热成功',
            fileInfo: result.success ? result : null
          };
        } catch (error) {
          logError(`预热下载失败 mediaId=${mediaId}:`, error);
          return {
            mediaId,
            success: false,
            message: error.message
          };
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // 预热下载间隔更长，避免影响性能
      if (i + concurrency < mediaIds.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    const successCount = results.filter(r => r.success).length;
    logInfo(`🔥 预热下载完成: ${successCount}/${mediaIds.length} 成功`);
    
    return {
      success: true,
      totalCount: mediaIds.length,
      successCount,
      failedCount: mediaIds.length - successCount,
      results
    };
  } catch (error) {
    logError('预热下载媒体文件失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 清理过期的媒体文件缓存
 */
const cleanupExpiredMediaCache = async () => {
  try {
    logInfo('🧹 开始清理过期媒体文件缓存');
    
    // 这里可以添加清理本地缓存文件的逻辑
    // 例如删除超过72小时的临时文件
    
    const cleanupResult = await fileService.cleanupExpiredFiles();
    
    if (cleanupResult.success) {
      logInfo(`✅ 媒体文件缓存清理完成，删除了 ${cleanupResult.deletedCount} 个文件`);
      return {
        success: true,
        deletedCount: cleanupResult.deletedCount
      };
    } else {
      return {
        success: false,
        message: '缓存清理失败'
      };
    }
  } catch (error) {
    logError('清理媒体文件缓存失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 获取媒体下载统计信息
 */
const getDownloadStats = async (userUuid, timeRange = '24h') => {
  try {
    const db = require('../data/database');
    
    // 根据时间范围计算开始时间
    let startTime;
    switch (timeRange) {
      case '1h':
        startTime = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case '24h':
        startTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }
    
    const stats = await db.getMediaDownloadStats(userUuid, startTime);
    
    return {
      success: true,
      stats: {
        totalDownloads: stats.total_downloads || 0,
        totalSize: stats.total_size || 0,
        averageSize: stats.average_size || 0,
        timeRange,
        startTime: startTime.toISOString()
      }
    };
  } catch (error) {
    logError('获取下载统计信息失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

module.exports = {
  getAccessToken,
  downloadWeChatMediaFile,
  batchDownloadMediaFiles,
  getMediaFileInfo,
  validateMediaAccess,
  cleanupExpiredMediaCache,
  getDownloadStats,
  preloadMediaFiles
};
