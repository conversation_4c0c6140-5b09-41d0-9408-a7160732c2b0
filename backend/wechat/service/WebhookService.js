/**
 * Webhook处理服务
 * 专门处理企业微信Webhook验证和消息解密
 */

const crypto = require('crypto');
const { logInfo, logError } = require('../api/errorHandler');

// 企业微信配置 - 生产环境必须从环境变量获取
const CORP_TOKEN = process.env.WECHAT_TOKEN;
const CORP_ENCODING_AES_KEY = process.env.WECHAT_ENCODING_AES_KEY;
const CORP_ID = process.env.WECHAT_CORP_ID;

// 安全的配置验证 - 不直接抛出错误，而是返回错误信息
const validateConfig = () => {
  const missing = [];
  if (!CORP_TOKEN) missing.push('WECHAT_TOKEN');
  if (!CORP_ENCODING_AES_KEY) missing.push('WECHAT_ENCODING_AES_KEY');
  if (!CORP_ID) missing.push('WECHAT_CORP_ID');
  
  if (missing.length > 0) {
    // 使用同步的console.error而不是异步的logError，避免模块加载时的异步问题
    console.error('❌ 缺少必要的企业微信配置环境变量:', missing.join(', '));
    return {
      valid: false,
      missing: missing,
      message: `缺少必要的企业微信配置环境变量: ${missing.join(', ')}`
    };
  }
  
  // 使用同步的console.log而不是异步的logInfo，避免模块加载时的异步问题
  console.log('✅ 企业微信配置验证通过');
  return { valid: true };
};

// 延迟配置验证 - 不在模块加载时执行，而是在实际使用时执行
let configValidation = null;

// 获取配置验证结果的函数
const getConfigValidation = () => {
  if (configValidation === null) {
    configValidation = validateConfig();
  }
  return configValidation;
};

/**
 * 【开启接收】企业微信Webhook URL验证
 * 这是企业微信配置webhook时的第一步：验证URL的有效性
 * 企业微信发送GET请求，我们需要验证签名并返回解密后的echostr
 */
const verifyWebhookUrl = async (msg_signature, timestamp, nonce, echostr) => {
  try {
    logInfo('🔍 【开启接收】开始URL验证流程');
    
    // 首先检查配置是否有效
    const configValidation = getConfigValidation();
    if (!configValidation.valid) {
      logError('❌ 配置验证失败:', configValidation.message);
      return {
        success: false,
        message: '服务配置错误: ' + configValidation.message
      };
    }
    
    // 验证必需参数
    if (!msg_signature || !timestamp || !nonce || !echostr) {
      logError('❌ URL验证参数不完整');
      return {
        success: false,
        message: 'URL验证参数不完整'
      };
    }
    
    // 第一步：验证签名
    const signatureValid = verifyUrlSignature(msg_signature, timestamp, nonce, echostr);
    if (!signatureValid) {
      logError('❌ URL验证签名不正确');
      return {
        success: false,
        message: 'URL验证签名不正确'
      };
    }
    
    // 第二步：解密echostr
    const decryptResult = decryptEchostr(echostr);
    if (!decryptResult || !decryptResult.success) {
      logError('❌ echostr解密失败:', decryptResult ? decryptResult.message : '未知错误');
      return {
        success: false,
        message: decryptResult ? decryptResult.message : 'echostr解密失败'
      };
    }
    
    logInfo('✅ 【开启接收】URL验证成功');
    return {
      success: true,
      echostr: decryptResult.echostr
    };
  } catch (error) {
    logError('【开启接收】URL验证异常:', error);
    return {
      success: false,
      message: 'URL验证异常: ' + error.message
    };
  }
};

/**
 * 验证URL签名
 */
const verifyUrlSignature = (msg_signature, timestamp, nonce, echostr) => {
  try {
    // 1. 将token、timestamp、nonce、echostr按字典序排序
    const sortedArray = [CORP_TOKEN, timestamp, nonce, echostr].sort();
    
    // 2. 将排序后的参数拼接成一个字符串
    const sortedString = sortedArray.join('');
    
    // 3. 对拼接后的字符串进行sha1加密
    const hash = crypto.createHash('sha1');
    hash.update(sortedString);
    const signature = hash.digest('hex');
    
    // 4. 将加密后的字符串与msg_signature对比
    const isValid = signature === msg_signature;
    
    logInfo('URL签名验证:', {
      expected: signature,
      received: msg_signature,
      valid: isValid
    });
    
    return isValid;
  } catch (error) {
    logError('URL签名验证异常:', error);
    return false;
  }
};

/**
 * 解密echostr
 */
const decryptEchostr = (echostr) => {
  try {
    logInfo('🔓 开始解密echostr');
    
    // 使用AES-256-CBC解密
    const aesKey = Buffer.from(CORP_ENCODING_AES_KEY + '=', 'base64');
    const encrypted = Buffer.from(echostr, 'base64');
    
    const decipher = crypto.createDecipheriv('aes-256-cbc', aesKey, aesKey.slice(0, 16));
    decipher.setAutoPadding(false);
    
    let decrypted = decipher.update(encrypted);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    
    // 去除PKCS7填充
    const padLength = decrypted[decrypted.length - 1];
    decrypted = decrypted.slice(0, decrypted.length - padLength);
    
    // 解析结构：random(16B) + msg_len(4B) + msg + corp_id
    const msgLen = decrypted.readUInt32BE(16);
    const msg = decrypted.slice(20, 20 + msgLen).toString('utf8');
    const corpId = decrypted.slice(20 + msgLen).toString('utf8');
    
    logInfo('解密echostr结果:', {
      msgLen,
      corpId,
      msgPreview: msg.substring(0, 50) + '...'
    });
    
    // 验证corp_id
    if (corpId !== CORP_ID) {
      logError('❌ echostr中的corp_id验证失败:', { expected: CORP_ID, actual: corpId });
      return {
        success: false,
        message: 'corp_id验证失败'
      };
    }
    
    return {
      success: true,
      echostr: msg
    };
  } catch (error) {
    logError('echostr解密异常:', error);
    return {
      success: false,
      message: 'echostr解密失败: ' + error.message
    };
  }
};

/**
 * 【使用接收】验证消息签名
 * 这是企业微信配置成功后，接收实际消息时的签名验证
 */
const verifyMessageSignature = async (msg_signature, timestamp, nonce, messageData) => {
  try {
    logInfo('🔍 【使用接收】验证消息签名...');
    
    // 获取加密消息体
    const encrypt = messageData.Encrypt || messageData.encrypt;
    if (!encrypt) {
      logError('❌ 消息体中缺少Encrypt字段');
      return {
        success: false,
        message: '消息体格式错误：缺少Encrypt字段'
      };
    }
    
    // 验证签名
    const signatureValid = verifyUrlSignature(msg_signature, timestamp, nonce, encrypt);
    if (!signatureValid) {
      logError('❌ 消息签名验证失败');
      return {
        success: false,
        message: '消息签名验证失败'
      };
    }
    
    logInfo('✅ 【使用接收】消息签名验证成功');
    return {
      success: true,
      message: '消息签名验证成功'
    };
  } catch (error) {
    logError('【使用接收】消息签名验证异常:', error);
    return {
      success: false,
      message: '消息签名验证异常: ' + error.message
    };
  }
};

/**
 * 解密消息内容
 */
const decryptMessage = (messageData) => {
  try {
    logInfo('🔓 开始解密企业微信消息');

    const encrypt = messageData.Encrypt || messageData.encrypt;
    if (!encrypt) {
      logError('❌ 消息体中缺少Encrypt字段');
      logError('消息体结构:', Object.keys(messageData));
      return null;
    }

    logInfo('加密消息长度:', encrypt.length);

    // 使用与echostr相同的解密逻辑
    const aesKey = Buffer.from(CORP_ENCODING_AES_KEY + '=', 'base64');
    const encrypted = Buffer.from(encrypt, 'base64');

    const decipher = crypto.createDecipheriv('aes-256-cbc', aesKey, aesKey.slice(0, 16));
    decipher.setAutoPadding(false);

    let decrypted = decipher.update(encrypted);
    decrypted = Buffer.concat([decrypted, decipher.final()]);

    // 去除PKCS7填充
    const padLength = decrypted[decrypted.length - 1];
    decrypted = decrypted.slice(0, decrypted.length - padLength);

    // 解析结构：random(16B) + msg_len(4B) + msg + corp_id
    const msgLen = decrypted.readUInt32BE(16);
    const msg = decrypted.slice(20, 20 + msgLen).toString('utf8');
    const corpId = decrypted.slice(20 + msgLen).toString('utf8');

    logInfo('解密消息长度:', msgLen);
    logInfo('解密消息内容:', msg.substring(0, 100) + '...');
    logInfo('消息corp_id:', corpId);

    // 验证corp_id
    if (corpId !== CORP_ID) {
      logError('❌ 消息corp_id验证失败:', { expected: CORP_ID, actual: corpId });
      return null;
    }

    // 解析消息内容（可能是XML或JSON格式）
    let parsedMessage;
    if (msg.trim().startsWith('<xml>')) {
      // XML格式消息，需要进一步解析
      logInfo('解密后的消息是XML格式，进行XML解析');
      parsedMessage = parseDecryptedXML(msg);
    } else {
      // JSON格式消息
      try {
        parsedMessage = JSON.parse(msg);
        logInfo('解密后的消息是JSON格式');
      } catch (parseError) {
        logError('JSON解析失败，尝试作为纯文本处理:', parseError);
        parsedMessage = { content: msg };
      }
    }

    return parsedMessage;
  } catch (error) {
    logError('【使用接收】消息解密异常:', error);
    return null;
  }
};

/**
 * 解析解密后的XML消息
 */
const parseDecryptedXML = (xmlString) => {
  try {
    // 定义完整的结果对象，包含所有可能的字段
    const result = {
      ToUserName: '',
      FromUserName: '',
      CreateTime: 0,
      MsgType: '',
      MsgId: '',
      Content: '',
      MediaId: '',
      Event: '',
      Token: '',
      OpenKfId: '',
      // 位置消息字段
      Location_X: 0,
      Location_Y: 0,
      Scale: 0,
      Label: '',
      // 链接消息字段
      Title: '',
      Description: '',
      Url: '',
      PicUrl: '',
      // 文件消息字段
      FileName: '',
      FileSize: 0
    };

    // 提取基本字段
    const toUserMatch = xmlString.match(/<ToUserName><!\[CDATA\[(.*?)\]\]><\/ToUserName>/);
    if (toUserMatch) result.ToUserName = toUserMatch[1];

    const fromUserMatch = xmlString.match(/<FromUserName><!\[CDATA\[(.*?)\]\]><\/FromUserName>/);
    if (fromUserMatch) {
      result.FromUserName = fromUserMatch[1];
    } else {
      // 尝试不带CDATA的格式
      const fromUserMatch2 = xmlString.match(/<FromUserName>(.*?)<\/FromUserName>/);
      if (fromUserMatch2) result.FromUserName = fromUserMatch2[1];
    }

    const createTimeMatch = xmlString.match(/<CreateTime>(\d+)<\/CreateTime>/);
    if (createTimeMatch) result.CreateTime = parseInt(createTimeMatch[1]);

    const msgTypeMatch = xmlString.match(/<MsgType><!\[CDATA\[(.*?)\]\]><\/MsgType>/);
    if (msgTypeMatch) result.MsgType = msgTypeMatch[1];

    const msgIdMatch = xmlString.match(/<MsgId>(\d+)<\/MsgId>/);
    if (msgIdMatch) result.MsgId = msgIdMatch[1];

    // 根据消息类型提取特定字段
    if (result.MsgType === 'text') {
      const contentMatch = xmlString.match(/<Content><!\[CDATA\[(.*?)\]\]><\/Content>/);
      if (contentMatch) result.Content = contentMatch[1];
    }

    // 提取媒体ID（图片、语音、视频、文件）
    const mediaIdMatch = xmlString.match(/<MediaId><!\[CDATA\[(.*?)\]\]><\/MediaId>/);
    if (mediaIdMatch) result.MediaId = mediaIdMatch[1];

    // 提取位置信息字段
    if (result.MsgType === 'location') {
      const locationXMatch = xmlString.match(/<Location_X>(.*?)<\/Location_X>/);
      if (locationXMatch) result.Location_X = parseFloat(locationXMatch[1]);

      const locationYMatch = xmlString.match(/<Location_Y>(.*?)<\/Location_Y>/);
      if (locationYMatch) result.Location_Y = parseFloat(locationYMatch[1]);

      const scaleMatch = xmlString.match(/<Scale>(\d+)<\/Scale>/);
      if (scaleMatch) result.Scale = parseInt(scaleMatch[1]);

      const labelMatch = xmlString.match(/<Label><!\[CDATA\[(.*?)\]\]><\/Label>/);
      if (labelMatch) result.Label = labelMatch[1];
    }

    // 提取链接消息字段
    if (result.MsgType === 'link') {
      const titleMatch = xmlString.match(/<Title><!\[CDATA\[(.*?)\]\]><\/Title>/);
      if (titleMatch) result.Title = titleMatch[1];

      const descriptionMatch = xmlString.match(/<Description><!\[CDATA\[(.*?)\]\]><\/Description>/);
      if (descriptionMatch) result.Description = descriptionMatch[1];

      const urlMatch = xmlString.match(/<Url><!\[CDATA\[(.*?)\]\]><\/Url>/);
      if (urlMatch) result.Url = urlMatch[1];

      const picUrlMatch = xmlString.match(/<PicUrl><!\[CDATA\[(.*?)\]\]><\/PicUrl>/);
      if (picUrlMatch) result.PicUrl = picUrlMatch[1];
    }

    // 提取文件消息字段
    if (result.MsgType === 'file') {
      const fileNameMatch = xmlString.match(/<FileName><!\[CDATA\[(.*?)\]\]><\/FileName>/);
      if (fileNameMatch) result.FileName = fileNameMatch[1];

      const fileSizeMatch = xmlString.match(/<FileSize>(\d+)<\/FileSize>/);
      if (fileSizeMatch) result.FileSize = parseInt(fileSizeMatch[1]);
    }

    // 提取事件类型
    const eventMatch = xmlString.match(/<Event><!\[CDATA\[(.*?)\]\]><\/Event>/);
    if (eventMatch) result.Event = eventMatch[1];

    // 提取Token（企业微信客服特有）
    const tokenMatch = xmlString.match(/<Token><!\[CDATA\[(.*?)\]\]><\/Token>/);
    if (tokenMatch) result.Token = tokenMatch[1];

    // 提取OpenKfId（企业微信客服特有）
    const openKfIdMatch = xmlString.match(/<OpenKfId><!\[CDATA\[(.*?)\]\]><\/OpenKfId>/);
    if (openKfIdMatch) result.OpenKfId = openKfIdMatch[1];

    logInfo('解密后XML解析结果:', {
      MsgType: result.MsgType,
      FromUserName: result.FromUserName ? result.FromUserName.substring(0, 10) + '...' : 'undefined',
      hasContent: !!result.Content,
      contentPreview: result.Content ? result.Content.substring(0, 50) + '...' : 'undefined',
      Event: result.Event,
      hasToken: !!result.Token,
      hasOpenKfId: !!result.OpenKfId
    });

    // 输出完整的XML内容用于调试
    logInfo('完整的解密XML内容:', xmlString);

    return result;
  } catch (error) {
    logError('解密后XML解析失败:', error);
    return null;
  }
};

module.exports = {
  verifyWebhookUrl,
  verifyMessageSignature,
  decryptMessage,
  parseDecryptedXML,
  validateConfig,
  getConfigValidation
};
