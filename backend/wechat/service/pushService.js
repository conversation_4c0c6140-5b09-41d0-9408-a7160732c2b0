// 推送服务模块
// 使用极光推送统一集成各个厂家的推送通道

const axios = require('axios');
const deviceManager = require('./deviceManager');

// JPush配置 - 统一管理所有推送通道
const JPUSH_CONFIG = {
  app_key: process.env.JPUSH_APP_KEY,
  master_secret: process.env.JPUSH_MASTER_SECRET,
  api_url: 'https://api.jpush.cn/v3/push'
};

/**
 * 生成JPush认证头
 * @returns {string} Base64编码的认证字符串
 */
const generateJPushAuth = () => {
  const credentials = `${JPUSH_CONFIG.app_key}:${JPUSH_CONFIG.master_secret}`;
  return Buffer.from(credentials).toString('base64');
};

/**
 * 通过JPush统一推送（支持iOS、Android、华为、小米等所有平台）
 * @param {Array} devices 设备列表
 * @param {Object} message 推送消息
 * @returns {Promise<Object>} 推送结果
 */
const sendJPushNotification = async (devices, message) => {
  try {
    const { title, content, data } = message;
    
    // 极光推送会自动根据设备类型选择对应的推送通道
    const pushData = {
      platform: ['ios', 'android'], // 支持所有平台
      audience: {
        registration_id: devices.map(d => d.push_token).filter(Boolean)
      },
      notification: {
        alert: content,
        android: {
          title: title,
          alert: content,
          extras: data,
          // 华为、小米等厂商通道会自动处理
          channel_id: 'default',
          priority: 2
        },
        ios: {
          alert: content,
          badge: 1,
          sound: 'default',
          extras: data,
          // APNS会自动处理
          category: 'message'
        }
      },
      message: {
        msg_content: content,
        title: title,
        extras: data
      },
      options: {
        time_to_live: 3600,
        apns_production: process.env.NODE_ENV === 'production',
        // 启用厂商通道
        third_party_channel: {
          huawei: true,
          xiaomi: true,
          oppo: true,
          vivo: true
        }
      }
    };
    
    const response = await axios.post(JPUSH_CONFIG.api_url, pushData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${generateJPushAuth()}`
      },
      timeout: 10000
    });
    
    if (response.status === 200) {
      return {
        success: true,
        message_id: response.data.msg_id,
        sent_count: devices.length,
        provider: 'jpush_unified'
      };
    } else {
      throw new Error(`JPush推送失败: ${response.data.error?.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('❌ JPush统一推送失败:', error.message);
    return {
      success: false,
      message: error.message,
      provider: 'jpush_unified'
    };
  }
};

/**
 * 统一推送消息到多个设备（简化版本）
 * @param {Array} devices 设备列表
 * @param {Object} message 推送消息
 * @returns {Promise<Object>} 推送结果
 */
const sendPushNotification = async (devices, message) => {
  try {
    if (!devices || devices.length === 0) {
      return {
        success: true,
        message: '没有设备需要推送',
        results: []
      };
    }
    
    // 直接使用JPush统一推送，无需按提供商分组
    const result = await sendJPushNotification(devices, message);
    
    return {
      success: result.success,
      message: result.success ? `推送完成: 成功${result.sent_count}个设备` : result.message,
      total_devices: devices.length,
      sent_count: result.sent_count || 0,
      failed_count: devices.length - (result.sent_count || 0),
      results: [result]
    };
  } catch (error) {
    console.error('❌ 统一推送失败:', error.message);
    return {
      success: false,
      message: error.message,
      results: []
    };
  }
};

/**
 * 发送消息通知推送（智能预览版本）
 * @param {Array} devices 设备列表
 * @param {Object} messageInfo 消息信息
 * @returns {Promise<Object>} 推送结果
 */
const sendMessageNotification = async (devices, messageInfo) => {
  const { message_type, metadata, content, file_name, file_size, mime_type, file_extension } = messageInfo;

  // 生成智能消息预览
  const messagePreview = generateIntelligentMessagePreview({
    message_type,
    content,
    file_name,
    file_size,
    mime_type,
    file_extension,
    metadata
  });

  return await sendPushNotification(devices, {
    title: messagePreview.title,
    content: messagePreview.content,
    data: {
      type: 'new_message',
      message_type,
      message_id: messageInfo.message_id,
      timestamp: Date.now(),
      preview: messagePreview,
      // 添加文件信息用于移动端处理
      file_info: {
        name: file_name,
        size: file_size,
        type: mime_type,
        extension: file_extension
      }
    }
  });
};

/**
 * 生成智能消息预览
 * @param {Object} messageData 消息数据
 * @returns {Object} 预览信息
 */
const generateIntelligentMessagePreview = (messageData) => {
  const { message_type, content, file_name, file_size, mime_type, file_extension, metadata } = messageData;

  // 获取发送者信息
  const senderName = metadata?.senderName || metadata?.from_user || '联系人';
  const shortSenderName = senderName.length > 8 ? senderName.substring(0, 8) + '...' : senderName;

  switch (message_type) {
    case 'text':
      // 文本消息显示前30个字符
      const textPreview = content && content.length > 30
        ? content.substring(0, 30) + '...'
        : content || '发送了一条消息';
      return {
        title: `${shortSenderName}`,
        content: textPreview,
        icon: '💬',
        category: 'message'
      };

    case 'image':
      return {
        title: `${shortSenderName} 发送了图片`,
        content: file_name ? `图片: ${file_name}` : '查看图片',
        icon: '🖼️',
        category: 'media'
      };

    case 'voice':
      const duration = metadata?.duration || 0;
      const durationText = duration > 0 ? ` (${duration}秒)` : '';
      return {
        title: `${shortSenderName} 发送了语音`,
        content: `语音消息${durationText}`,
        icon: '🎵',
        category: 'media'
      };

    case 'video':
      const videoSize = file_size ? formatFileSize(file_size) : '';
      const videoSizeText = videoSize ? ` (${videoSize})` : '';
      return {
        title: `${shortSenderName} 发送了视频`,
        content: `${file_name || '视频文件'}${videoSizeText}`,
        icon: '🎬',
        category: 'media'
      };

    case 'file':
      // 根据文件类型生成智能预览
      const fileTypeInfo = getFileTypeInfo(mime_type, file_extension, file_name);
      const fileSizeText = file_size ? ` (${formatFileSize(file_size)})` : '';

      return {
        title: `${shortSenderName} 发送了${fileTypeInfo.description}`,
        content: `${file_name || '文件'}${fileSizeText}`,
        icon: fileTypeInfo.icon,
        category: 'file'
      };

    case 'location':
      const locationLabel = metadata?.location_label || '位置信息';
      return {
        title: `${shortSenderName} 发送了位置`,
        content: locationLabel,
        icon: '📍',
        category: 'location'
      };

    case 'link':
      const linkTitle = metadata?.link_title || '链接';
      const linkUrl = metadata?.link_url || '';
      const domain = linkUrl ? extractDomain(linkUrl) : '';
      return {
        title: `${shortSenderName} 分享了链接`,
        content: `${linkTitle}${domain ? ` (${domain})` : ''}`,
        icon: '🔗',
        category: 'link'
      };

    case 'event':
      const eventType = metadata?.event_type || '系统事件';
      return {
        title: '系统通知',
        content: `${eventType}`,
        icon: '⚡',
        category: 'system'
      };

    default:
      return {
        title: `${shortSenderName}`,
        content: '发送了一条消息',
        icon: '📨',
        category: 'unknown'
      };
  }
};

// 引入统一文件类型配置
const { detectFileTypeFromName } = require('../config/fileTypes');

/**
 * 获取文件类型信息
 * @param {string} mimeType MIME类型
 * @param {string} fileExtension 文件扩展名
 * @param {string} fileName 文件名
 * @returns {Object} 文件类型信息
 */
const getFileTypeInfo = (mimeType, fileExtension, fileName) => {
  // 优先使用文件名进行检测
  if (fileName) {
    const typeInfo = detectFileTypeFromName(fileName);
    return { 
      description: typeInfo.description, 
      icon: typeInfo.icon 
    };
  }

  // 如果没有文件名，使用扩展名
  if (fileExtension) {
    const typeInfo = detectFileTypeFromName(`file.${fileExtension}`);
    return { 
      description: typeInfo.description, 
      icon: typeInfo.icon 
    };
  }

  // 最后使用MIME类型
  if (mimeType) {
    // 根据MIME类型推断文件类型
    if (mimeType.startsWith('image/')) {
      return { description: '图片', icon: '🖼️' };
    }
    if (mimeType.startsWith('video/')) {
      return { description: '视频', icon: '🎬' };
    }
    if (mimeType.startsWith('audio/')) {
      return { description: '音频', icon: '🎵' };
    }
    if (mimeType.includes('pdf')) {
      return { description: 'PDF文档', icon: '📄' };
    }
    if (mimeType.includes('ofd')) {
      return { description: 'OFD文档', icon: '📋' };
    }
    if (mimeType.includes('word')) {
      return { description: 'Word文档', icon: '📝' };
    }
    if (mimeType.includes('excel')) {
      return { description: 'Excel表格', icon: '📊' };
    }
    if (mimeType.includes('powerpoint')) {
      return { description: 'PPT演示', icon: '📽️' };
    }
  }

  // 默认文件
  return { description: '文件', icon: '📁' };
};

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小
 */
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0B';

  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  const size = (bytes / Math.pow(1024, i)).toFixed(i === 0 ? 0 : 1);

  return `${size}${sizes[i]}`;
};

/**
 * 从URL中提取域名
 * @param {string} url URL地址
 * @returns {string} 域名
 */
const extractDomain = (url) => {
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
    return urlObj.hostname;
  } catch (error) {
    return '';
  }
};

/**
 * 发送下载指令推送 (旧版本 - 兼容性保留)
 * @param {Array} devices 设备列表
 * @param {Object} downloadInfo 下载信息
 * @returns {Promise<Object>} 推送结果
 */
const sendDownloadNotification = async (devices, downloadInfo) => {
  const { media_id, media_type, filename, access_token } = downloadInfo;
  
  return await sendPushNotification(devices, {
    title: '文件下载',
    content: `可以下载文件: ${filename || '未知文件'}`,
    data: {
      type: 'download_instruction',
      media_id,
      media_type,
      filename,
      access_token,
      timestamp: Date.now()
    }
  });
};

/**
 * 发送新的文件下载通知推送 (支持下载链接)
 * @param {Array} devices 设备列表
 * @param {Object} fileInfo 文件信息
 * @returns {Promise<Object>} 推送结果
 */
const sendFileDownloadNotification = async (devices, fileInfo) => {
  const { 
    messageId, 
    fileName, 
    fileSize, 
    contentType, 
    downloadUrl, 
    downloadExpiresAt,
    messageType = 'file'
  } = fileInfo;
  
  // 根据文件类型生成不同的通知内容
  let title, content;
  switch (messageType) {
    case 'image':
      title = '📸 收到图片';
      content = `图片文件: ${fileName || '未知图片'}`;
      break;
    case 'voice':
      title = '🎵 收到语音';
      content = `语音文件: ${fileName || '语音消息'}`;
      break;
    case 'video':
      title = '🎬 收到视频';
      content = `视频文件: ${fileName || '未知视频'}`;
      break;
    case 'file':
      title = '📁 收到文件';
      content = `文件: ${fileName || '未知文件'}`;
      break;
    default:
      title = '📎 收到附件';
      content = `附件: ${fileName || '未知文件'}`;
  }
  
  // 添加文件大小信息
  if (fileSize) {
    const sizeInMB = (fileSize / 1024 / 1024).toFixed(2);
    content += ` (${sizeInMB}MB)`;
  }
  
  return await sendPushNotification(devices, {
    title,
    content,
    data: {
      type: 'file_download_ready',
      message_id: messageId,
      message_type: messageType,
      file_name: fileName,
      file_size: fileSize,
      content_type: contentType,
      download_url: downloadUrl,
      download_expires_at: downloadExpiresAt,
      timestamp: Date.now()
    }
  });
};

/**
 * 检查推送服务配置
 * @returns {Object} 配置检查结果
 */
const checkPushConfig = () => {
  return {
    jpush: {
      configured: !!(JPUSH_CONFIG.app_key && JPUSH_CONFIG.master_secret),
      app_key: JPUSH_CONFIG.app_key ? '已配置' : '未配置',
      master_secret: JPUSH_CONFIG.master_secret ? '已配置' : '未配置',
      description: '极光推送统一管理所有平台（iOS、Android、华为、小米等）'
    }
  };
};

/**
 * 获取支持的推送提供商列表
 * @returns {Array} 推送提供商列表
 */
const getSupportedPushProviders = () => {
  return [{
    provider: 'jpush',
    name: '极光推送统一通道',
    configured: checkPushConfig().jpush?.configured || false,
    description: '支持iOS、Android、华为、小米、OPPO、vivo等所有平台'
  }];
};

/**
 * 发送微信绑定成功通知
 * @param {Array} devices 设备列表
 * @param {string} userUuid 用户UUID
 * @returns {Promise<Object>} 推送结果
 */
const sendBindingSuccessNotification = async (devices, userUuid) => {
  return await sendPushNotification(devices, {
    title: '微信绑定成功',
    content: '您的微信账号已成功绑定到公职猫，现在可以转发消息了！',
    data: {
      type: 'wechat_binding_success',
      user_uuid: userUuid,
      binding_time: Date.now(),
      timestamp: Date.now()
    }
  });
};

module.exports = {
  sendPushNotification,
  sendMessageNotification,
  sendFileDownloadNotification,
  sendBindingSuccessNotification,
  checkPushConfig,
  getSupportedPushProviders
}; 