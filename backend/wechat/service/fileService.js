/**
 * 文件服务模块 - 文件下载、加密存储与临时缓存
 * 支持"阅后即焚"机制：用户下载后或3天后自动删除
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const jwt = require('jsonwebtoken');

class FileService {
  constructor() {
    this.cacheDir = process.env.FILE_STORAGE_PATH || '/var/www/cache';
    this.encryptionKey = process.env.FILE_ENCRYPTION_KEY || 'gongzhimall-file-encryption-2025';
    this.maxFileSize = parseInt(process.env.MAX_FILE_SIZE) || 104857600; // 100MB
    this.downloadTokenSecret = process.env.TOKEN_SECRET;
    this.downloadTokenExpiresHours = parseInt(process.env.DOWNLOAD_TOKEN_EXPIRES_HOURS) || 24;
    
    this.initCacheDirectory();
  }

  /**
   * 初始化缓存目录
   */
  async initCacheDirectory() {
    try {
      await fs.mkdir(this.cacheDir, { recursive: true });
      console.log(`📁 文件缓存目录初始化完成: ${this.cacheDir}`);
    } catch (error) {
      console.error('缓存目录初始化失败:', error);
      throw error;
    }
  }

  /**
   * 处理文件下载请求
   * @param {string} token 下载令牌
   * @param {Object} res Express响应对象
   */
  async handleFileDownload(token, res) {
    try {
      // 1. 验证下载令牌
      const tokenData = this.verifyDownloadToken(token);
      if (!tokenData) {
        return res.status(401).json({
          success: false,
          error: { code: 'INVALID_TOKEN', message: '下载令牌无效或已过期' }
        });
      }

      const { messageId, fileName, filePath, contentType, fileSize } = tokenData;
      const fullPath = path.join(this.cacheDir, filePath);

      // 2. 检查文件是否存在
      try {
        await fs.access(fullPath);
      } catch (error) {
        return res.status(404).json({
          success: false,
          error: { code: 'FILE_NOT_FOUND', message: '文件不存在或已被删除' }
        });
      }

      // 3. 读取并解密文件
      console.log(`📤 开始传输文件: ${fileName} (${fileSize} bytes)`);
      
      const encryptedData = await fs.readFile(fullPath);
      const decryptedData = this.decryptData(encryptedData);

      // 4. 设置响应头
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Length', decryptedData.length);
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');

      // 5. 发送文件数据
      res.send(decryptedData);

      // 6. 文件下载完成后立即删除（阅后即焚）
      this.deleteFileAndRecord(messageId, fullPath);
      
      console.log(`✅ 文件下载完成并已删除: ${fileName}`);

    } catch (error) {
      console.error('文件下载处理失败:', error);
      res.status(500).json({
        success: false,
        error: { code: 'DOWNLOAD_ERROR', message: '文件下载失败' }
      });
    }
  }

  /**
   * 重新生成过期的下载链接
   * @param {Object} fileRecord 文件记录
   * @returns {Object} 新的文件信息
   */
  async regenerateDownloadLink(fileRecord) {
    try {
      // 检查文件是否仍然存在
      const fullPath = path.join(this.cacheDir, fileRecord.file_path);
      await fs.access(fullPath);

      // 生成新的下载令牌
      const downloadToken = this.generateDownloadToken({
        messageId: fileRecord.id,
        fileName: fileRecord.file_name,
        filePath: fileRecord.file_path,
        contentType: fileRecord.content_type || 'application/octet-stream',
        fileSize: fileRecord.file_size
      });

      const newFileInfo = {
        downloadToken,
        downloadUrl: `/api/media/download/${downloadToken}`,
        downloadExpiresAt: new Date(Date.now() + this.downloadTokenExpiresHours * 60 * 60 * 1000)
      };

      console.log(`🔄 重新生成下载链接: ${fileRecord.file_name}`);
      return newFileInfo;

    } catch (error) {
      console.error('重新生成下载链接失败:', error);
      return null;
    }
  }

  /**
   * 清理过期文件
   */
  async cleanupExpiredFiles() {
    console.log('🧹 开始清理过期文件...');
    
    try {
      const files = await fs.readdir(this.cacheDir);
      let deletedCount = 0;

      for (const file of files) {
        const filePath = path.join(this.cacheDir, file);
        const stats = await fs.stat(filePath);
        
        // 删除3天前的文件
        const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
        if (stats.ctime < threeDaysAgo) {
          await fs.unlink(filePath);
          deletedCount++;
          console.log(`🗑️ 删除过期文件: ${file}`);
        }
      }

      console.log(`✅ 文件清理完成，删除了 ${deletedCount} 个过期文件`);
      return deletedCount;

    } catch (error) {
      console.error('文件清理失败:', error);
      throw error;
    }
  }

  /**
   * 加密数据
   * @param {Buffer} data 要加密的数据
   * @returns {Buffer} 加密后的数据
   */
  encryptData(data) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipheriv(algorithm, key, iv);
    cipher.setAutoPadding(true);
    
    const encrypted = Buffer.concat([cipher.update(data), cipher.final()]);
    return Buffer.concat([iv, encrypted]);
  }

  /**
   * 解密数据
   * @param {Buffer} encryptedData 加密的数据
   * @returns {Buffer} 解密后的数据
   */
  decryptData(encryptedData) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
    
    // 提取IV和加密数据
    const iv = encryptedData.slice(0, 16);
    const encrypted = encryptedData.slice(16);
    
    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    const decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]);
    
    return decrypted;
  }

  /**
   * 生成加密文件名
   * @param {string} messageId 消息ID
   * @param {string} originalName 原始文件名
   * @returns {string} 加密文件名
   */
  generateEncryptedFileName(messageId, originalName) {
    const hash = crypto.createHash('sha256')
      .update(`${messageId}_${originalName}_${Date.now()}`)
      .digest('hex');
    
    const extension = path.extname(originalName) || '.bin';
    return `${hash.substring(0, 32)}${extension}.enc`;
  }

  /**
   * 生成下载令牌
   * @param {Object} payload 令牌载荷
   * @returns {string} JWT令牌
   */
  generateDownloadToken(payload) {
    return jwt.sign(payload, this.downloadTokenSecret, {
      expiresIn: `${this.downloadTokenExpiresHours}h`,
      issuer: 'gongzhimall-wechat-service'
    });
  }

  /**
   * 验证下载令牌
   * @param {string} token JWT令牌
   * @returns {Object|null} 令牌数据或null
   */
  verifyDownloadToken(token) {
    try {
      return jwt.verify(token, this.downloadTokenSecret);
    } catch (error) {
      console.error('下载令牌验证失败:', error.message);
      return null;
    }
  }

  /**
   * 删除文件和相关记录
   * @param {string} messageId 消息ID
   * @param {string} filePath 文件路径
   */
  async deleteFileAndRecord(messageId, filePath) {
    try {
      // 删除物理文件
      await fs.unlink(filePath);
      console.log(`🗑️ 已删除文件: ${path.basename(filePath)}`);
      
      // 更新数据库记录，标记文件已下载和已删除
      try {
        const db = require('../data/database');
        await db.markFileAsDownloaded(messageId);
        console.log(`📝 已标记文件为已下载: ${messageId}`);
      } catch (dbError) {
        console.error('更新数据库记录失败:', dbError);
      }
      
    } catch (error) {
      console.error('删除文件失败:', error);
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Promise<Object>} 缓存统计
   */
  async getCacheStats() {
    try {
      const files = await fs.readdir(this.cacheDir);
      let totalSize = 0;
      let fileCount = 0;

      for (const file of files) {
        const filePath = path.join(this.cacheDir, file);
        const stats = await fs.stat(filePath);
        totalSize += stats.size;
        fileCount++;
      }

      return {
        fileCount,
        totalSize,
        totalSizeMB: Math.round(totalSize / 1024 / 1024 * 100) / 100,
        cacheDir: this.cacheDir
      };
    } catch (error) {
      console.error('获取缓存统计失败:', error);
      return { fileCount: 0, totalSize: 0, totalSizeMB: 0, cacheDir: this.cacheDir };
    }
  }

  /**
   * 下载微信媒体文件并加密存储
   * @param {string} mediaId 企业微信媒体ID
   * @param {string} messageId 消息ID（用于文件命名）
   * @param {string} originalFileName 原始文件名
   * @param {string} userUuid 用户唯一标识（可选，便于日志追踪）
   * @returns {Promise<Object>} 文件信息（含下载令牌、下载链接等）
   */
  async downloadWeChatMediaAndCache(mediaId, messageId, originalFileName = null, userUuid = null) {
    console.log(`📥 开始下载文件: mediaId=${mediaId}, messageId=${messageId}`);
    
    try {
      // 1. 通过 wechatApi 下载文件
      const wechatApi = require('./wechatApi');
      const downloadResult = await wechatApi.downloadMedia(mediaId);
      
      if (!downloadResult.success) {
        throw new Error(`下载失败: ${downloadResult.message || '未知错误'}`);
      }

      const { data: fileData, fileName, contentType, size: contentLength } = downloadResult;

      console.log(`📄 文件信息: ${fileName}, ${contentLength} bytes, ${contentType}`);

      if (contentLength > this.maxFileSize) {
        throw new Error(`文件过大: ${contentLength} bytes > ${this.maxFileSize} bytes`);
      }

      // 2. 生成加密文件路径
      const finalFileName = originalFileName || fileName;
      const encryptedFileName = this.generateEncryptedFileName(messageId, finalFileName);
      const filePath = path.join(this.cacheDir, encryptedFileName);

      // 3. 加密并存储文件
      const encryptedData = this.encryptData(fileData);
      await fs.writeFile(filePath, encryptedData);

      // 4. 生成下载令牌
      const downloadToken = this.generateDownloadToken({
        messageId,
        fileName: finalFileName,
        filePath: encryptedFileName,
        contentType,
        fileSize: contentLength
      });

      const fileInfo = {
        messageId,
        fileName: finalFileName,
        filePath: encryptedFileName,
        fileSize: contentLength,
        contentType,
        downloadToken,
        downloadUrl: `/api/media/download/${downloadToken}`,
        downloadExpiresAt: new Date(Date.now() + this.downloadTokenExpiresHours * 60 * 60 * 1000),
        fileExpiresAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3天后过期
        createdAt: new Date(),
        downloaded: false
      };

      // 5. 添加用户标识（如果提供）
      if (userUuid) {
        fileInfo.userUuid = userUuid;
      }

      console.log(`✅ 文件下载并缓存成功: ${fileName}`);
      return {
        success: true,
        ...fileInfo
      };

    } catch (error) {
      console.error(`❌ 文件下载失败: ${error.message}`);
      return {
        success: false,
        message: error.message
      };
    }
  }
}

module.exports = new FileService(); 