// 企业微信API调用模块
// 实现企业微信接口的真实调用
//
// 架构设计说明：


const axios = require('axios');
const crypto = require('crypto');
const { logInfo, logError } = require('../api/errorHandler');

// 引入统一文件类型配置
const { getExtensionFromMimeType: getExtensionFromMimeTypeUnified } = require('../config/fileTypes');

/**
 * 根据MIME类型获取文件扩展名
 */
const getExtensionFromMimeType = (mimeType) => {
  return getExtensionFromMimeTypeUnified(mimeType);
};

/**
 * 通过文件魔数检测文件类型
 */
const detectFileType = (buffer) => {
  if (!buffer || buffer.length < 16) {
    return null;
  }

  // 获取文件头部字节
  const header = buffer.slice(0, 16);
  const headerHex = Array.from(new Uint8Array(header))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');

  // 常见文件格式的魔数检测
  if (headerHex.startsWith('ffd8ff')) return 'image/jpeg';
  if (headerHex.startsWith('89504e47')) return 'image/png';
  if (headerHex.startsWith('47494638')) return 'image/gif';
  if (headerHex.startsWith('52494646') && headerHex.includes('57454250')) return 'image/webp';
  if (headerHex.startsWith('424d')) return 'image/bmp';

  if (headerHex.startsWith('25504446')) return 'application/pdf';
  if (headerHex.startsWith('504b0304')) {
    // ZIP格式，可能是Office文档
    // 需要进一步检查内部结构来区分docx/xlsx/pptx
    return 'application/zip';
  }

  // Office文档的特殊检测
  if (headerHex.startsWith('d0cf11e0a1b11ae1')) {
    // 老版本Office文档 (.doc, .xls, .ppt)
    return 'application/msword'; // 默认返回Word格式，实际使用时需要更精确的检测
  }

  // 音频格式
  if (headerHex.startsWith('2321414d52')) return 'audio/amr';
  if (headerHex.startsWith('494433') || headerHex.startsWith('fffb')) return 'audio/mp3';
  if (headerHex.startsWith('52494646') && headerHex.includes('57415645')) return 'audio/wav';

  // 视频格式
  if (headerHex.includes('667479706d703432') || headerHex.includes('667479706d703431')) return 'video/mp4';
  if (headerHex.startsWith('52494646') && headerHex.includes('41564920')) return 'video/avi';

  return null; // 无法确定类型
};

// 企业微信API配置
const WECHAT_API_BASE = process.env.WECHAT_API_BASE || 'https://qyapi.weixin.qq.com';
const CORP_ID = process.env.WECHAT_CORP_ID;
const CORP_SECRET = process.env.WECHAT_CORP_SECRET;
const AGENT_ID = process.env.WECHAT_AGENT_ID;
const OPEN_KFID = process.env.WECHAT_DEFAULT_OPEN_KFID;

// 验证API配置
const validateApiConfig = () => {
  const requiredConfigs = {
    'WECHAT_API_BASE': WECHAT_API_BASE,
    'WECHAT_CORP_ID': CORP_ID,
    'WECHAT_CORP_SECRET': CORP_SECRET,
    'WECHAT_AGENT_ID': AGENT_ID
  };

  const missingConfigs = Object.entries(requiredConfigs)
    .filter(([key, value]) => !value)
    .map(([key]) => key);

  if (missingConfigs.length > 0) {
    console.warn(`⚠️ 缺少企业微信配置: ${missingConfigs.join(', ')}`);
  }

  // 验证API基础URL格式
  try {
    new URL(WECHAT_API_BASE);
  } catch (error) {
    console.error(`❌ 无效的WECHAT_API_BASE: ${WECHAT_API_BASE}`);
  }
};

// 在模块加载时验证配置
validateApiConfig();


/**
 * 获取企业微信访问令牌和过期时间
 * @returns {Promise<{token: string, expiresAt: Date}>} 访问令牌和过期时间
 */
const getAccessTokenWithExpiry = async () => {
  try {
    // 引入数据库模块（延迟引入避免循环依赖）
    const db = require('../data/database');

    // 检查数据库缓存
    const cacheResult = await db.getAccessTokenFromCache(CORP_ID);
    if (cacheResult.success) {
      console.log('✅ 从数据库缓存获取access_token成功');
      const expiresAt = new Date(cacheResult.expires_at);
      return {
        token: cacheResult.access_token,
        expiresAt: expiresAt
      };
    }

    console.log('🔄 数据库缓存中无有效token，尝试获取刷新锁');

    // 尝试获取刷新锁，防止多个实例同时调用微信API
    const lockResult = await db.tryAcquireTokenRefreshLock(CORP_ID);
    if (!lockResult.success) {
      // 等待其他实例完成刷新，然后重新尝试从缓存获取
      console.log('⏳ 等待其他实例完成token刷新...');
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒

      const retryResult = await db.getAccessTokenFromCache(CORP_ID);
      if (retryResult.success) {
        console.log('✅ 其他实例已完成token刷新，从缓存获取成功');
        const expiresAt = new Date(retryResult.expires_at);
        return {
          token: retryResult.access_token,
          expiresAt: expiresAt
        };
      } else {
        throw new Error('等待其他实例刷新token超时');
      }
    }

    try {
      console.log('🔄 获取刷新锁成功，检查API调用限制');

      // 检查今日API调用是否超限
      const limitCheck = await db.checkTokenApiLimit(CORP_ID);
      if (limitCheck.success && limitCheck.isOverLimit) {
        throw new Error(`access_token API调用已达每日限制: ${limitCheck.todayCount}/${limitCheck.limit}`);
      }

      console.log(`📊 API调用限制检查通过，剩余次数: ${limitCheck.remaining}`);

      // 调用企业微信API获取访问令牌
      const response = await axios.get(`${WECHAT_API_BASE}/cgi-bin/gettoken`, {
        params: {
          corpid: CORP_ID,
          corpsecret: CORP_SECRET
        },
        timeout: 10000
      });

      const { data } = response;

      if (data.errcode === 0) {
        // 记录API调用
        await db.recordTokenApiCall(CORP_ID);

        // 计算过期时间
        const expiresAt = new Date(Date.now() + data.expires_in * 1000);

        // 存储到数据库缓存并释放锁
        await db.storeAccessTokenToCache(CORP_ID, data.access_token, data.expires_in);

        console.log('✅ 企业微信访问令牌获取成功并已缓存');
        return {
          token: data.access_token,
          expiresAt: expiresAt
        };
      } else {
        // 即使失败也要记录API调用（因为消耗了配额）
        await db.recordTokenApiCall(CORP_ID);
        throw new Error(`获取访问令牌失败: ${data.errmsg} (${data.errcode})`);
      }
    } catch (error) {
      // 发生错误时释放锁
      await db.releaseTokenRefreshLock(CORP_ID);
      throw error;
    }
  } catch (error) {
    console.error('❌ 获取企业微信访问令牌失败:', error.message);
    throw error;
  }
};

/**
 * 获取企业微信访问令牌（兼容性方法）
 * @returns {Promise<string>} 访问令牌
 */
const getAccessToken = async () => {
  const result = await getAccessTokenWithExpiry();
  return result.token;
};

/**
 * 下载媒体文件
 * 
 * 
 * @param {string} mediaId 媒体ID
 * @returns {Promise<Object>} 下载结果
 */
const downloadMedia = async (mediaId) => {
  try {
    console.log(`📥 开始下载媒体文件: ${mediaId}`);
    const accessToken = await getAccessToken();

    const response = await axios.get(`${WECHAT_API_BASE}/cgi-bin/media/get`, {
      params: {
        access_token: accessToken,
        media_id: mediaId
      },
      responseType: 'arraybuffer', // 改为arraybuffer以便处理二进制数据
      timeout: 30000
    });

    // 检查响应头，确认是否为文件
    const contentType = response.headers['content-type'];
    const contentDisposition = response.headers['content-disposition'];
    const contentLength = response.headers['content-length'];

    console.log(`📄 响应头信息:`, {
      contentType,
      contentDisposition,
      contentLength
    });

    // 检查是否为错误响应（JSON格式）
    if (contentType && contentType.includes('application/json')) {
      try {
        const responseText = Buffer.from(response.data).toString();
        const errorData = JSON.parse(responseText);
        throw new Error(`下载媒体文件失败: ${errorData.errmsg} (${errorData.errcode})`);
      } catch (parseError) {
        throw new Error(`下载媒体文件失败: 无法解析错误响应 - ${parseError.message}`);
      }
    }

    // 获取文件数据
    const fileData = Buffer.from(response.data);

    // 从Content-Disposition中提取文件名
    let fileName = '';
    if (contentDisposition) {
      const matches = /filename[*]?=['"]?([^'";]+)['"]?/i.exec(contentDisposition);
      if (matches && matches[1]) {
        fileName = decodeURIComponent(matches[1]);
      }
    }

    // 智能文件类型检测
    let detectedContentType = contentType;
    let fileExtension = '';

    // 1. 首先尝试通过文件魔数检测
    const magicDetectedType = detectFileType(fileData);
    if (magicDetectedType) {
      detectedContentType = magicDetectedType;
      console.log(`🔍 通过魔数检测到文件类型: ${magicDetectedType}`);
    }

    // 2. 根据最终确定的MIME类型获取扩展名
    fileExtension = getExtensionFromMimeType(detectedContentType);

    // 3. 如果没有文件名，根据检测结果生成
    if (!fileName) {
      fileName = `media_${mediaId}.${fileExtension}`;
    } else if (!fileName.includes('.')) {
      // 如果文件名没有扩展名，添加检测到的扩展名
      fileName = `${fileName}.${fileExtension}`;
    }

    console.log(`✅ 文件下载成功:`, {
      mediaId,
      fileName,
      contentType: detectedContentType,
      fileExtension,
      size: fileData.length
    });

    return {
      success: true,
      data: fileData,
      fileName,
      contentType: detectedContentType,
      fileExtension,
      size: fileData.length,
      originalContentType: contentType // 保留原始Content-Type用于调试
    };
  } catch (error) {
    console.error('❌ 下载媒体文件失败:', error.message);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 获取媒体文件信息
 * @param {string} mediaId 媒体ID
 * @returns {Promise<Object>} 媒体文件信息
 */
const getMediaInfo = async (mediaId) => {
  try {
    const accessToken = await getAccessToken();
    
    const response = await axios.get(`${WECHAT_API_BASE}/cgi-bin/media/get/jssdk`, {
      params: {
        access_token: accessToken,
        media_id: mediaId
      },
      timeout: 10000
    });
    
    const { data } = response;
    
    if (data.errcode === 0) {
      return {
        success: true,
        media_info: {
          media_id: mediaId,
          created_at: data.created_at,
          type: data.type,
          size: data.size
        }
      };
    } else {
      throw new Error(`获取媒体信息失败: ${data.errmsg} (${data.errcode})`);
    }
  } catch (error) {
    console.error('❌ 获取媒体信息失败:', error.message);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 发送客服文本消息（用于企业微信客服场景）
 * @param {string} externalUserId 外部用户ID
 * @param {string} content 消息内容
 * @returns {Promise<Object>} 发送结果
 */
const sendTextMessage = async (externalUserId, content) => {
  try {
    const accessToken = await getAccessToken();

    // 使用企业微信客服消息API
    const messageData = {
      touser: externalUserId,
      open_kfid: OPEN_KFID,
      msgtype: 'text',
      text: {
        content: content
      }
    };

    console.log('🔄 发送客服消息:', {
      touser: externalUserId.substring(0, 10) + '...',
      content: content.substring(0, 50) + '...'
    });

    const response = await axios.post(
      `${WECHAT_API_BASE}/cgi-bin/kf/send_msg?access_token=${accessToken}`,
      messageData,
      {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    const { data } = response;

    if (data.errcode === 0) {
      console.log('✅ 客服消息发送成功');
      return {
        success: true,
        message_id: data.msgid
      };
    } else {
      throw new Error(`发送客服消息失败: ${data.errmsg} (${data.errcode})`);
    }
  } catch (error) {
    console.error('❌ 发送客服消息失败:', error.message);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 发送欢迎语消息（使用welcome_code）
 * @param {string} welcomeCode 欢迎语代码
 * @param {string} content 消息内容
 * @returns {Promise<Object>} 发送结果
 */
const sendWelcomeMessage = async (welcomeCode, content) => {
  try {
    const accessToken = await getAccessToken();

    // 使用企业微信欢迎语API
    const messageData = {
      code: welcomeCode,
      msgtype: 'text',
      text: {
        content: content
      }
    };

    console.log('🎉 发送欢迎语:', {
      code: welcomeCode.substring(0, 10) + '...',
      content: content.substring(0, 50) + '...'
    });

    const response = await axios.post(
      `${WECHAT_API_BASE}/cgi-bin/kf/send_msg_on_event?access_token=${accessToken}`,
      messageData,
      {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    const { data } = response;

    if (data.errcode === 0) {
      console.log('✅ 欢迎语发送成功');
      return {
        success: true,
        message_id: data.msgid
      };
    } else {
      console.error('❌ 欢迎语发送失败:', data);
      return {
        success: false,
        error: data,
        errcode: data.errcode,
        errmsg: data.errmsg
      };
    }
  } catch (error) {
    console.error('发送欢迎语异常:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 获取用户信息
 * @param {string} userId 用户ID
 * @returns {Promise<Object>} 用户信息
 */
const getUserInfo = async (userId) => {
  try {
    const accessToken = await getAccessToken();
    
    const response = await axios.get(`${WECHAT_API_BASE}/cgi-bin/user/get`, {
      params: {
        access_token: accessToken,
        userid: userId
      },
      timeout: 10000
    });
    
    const { data } = response;
    
    if (data.errcode === 0) {
      return {
        success: true,
        user_info: {
          userid: data.userid,
          name: data.name,
          department: data.department,
          position: data.position,
          mobile: data.mobile,
          email: data.email,
          avatar: data.avatar,
          status: data.status
        }
      };
    } else {
      throw new Error(`获取用户信息失败: ${data.errmsg} (${data.errcode})`);
    }
  } catch (error) {
    console.error('❌ 获取用户信息失败:', error.message);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 验证企业微信回调URL
 * @param {string} msgSignature 消息签名
 * @param {string} timestamp 时间戳
 * @param {string} nonce 随机数
 * @param {string} echostr 验证字符串
 * @returns {string} 解密后的验证字符串
 */
const verifyCallbackUrl = (msgSignature, timestamp, nonce, echostr) => {
  try {
    const token = process.env.WECHAT_TOKEN;
    const encodingAESKey = process.env.WECHAT_ENCODING_AES_KEY;
    
    // 验证签名
    const signature = crypto
      .createHash('sha1')
      .update([token, timestamp, nonce, echostr].sort().join(''))
      .digest('hex');
    
    if (signature !== msgSignature) {
      throw new Error('签名验证失败');
    }
    
    // 解密echostr
    const key = Buffer.from(encodingAESKey + '=', 'base64');
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, key.slice(0, 16));
    
    let decrypted = decipher.update(echostr, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    
    // 去除随机字符串和corpid
    const content = decrypted.slice(16);
    const contentLength = content.slice(0, 4);
    const message = content.slice(4, 4 + parseInt(contentLength));
    
    return message;
  } catch (error) {
    console.error('❌ 验证回调URL失败:', error.message);
    throw error;
  }
};

/**
 * 解密企业微信消息
 * @param {string} msgSignature 消息签名
 * @param {string} timestamp 时间戳
 * @param {string} nonce 随机数
 * @param {string} encryptedMsg 加密消息
 * @returns {Object} 解密后的消息
 */
const decryptMessage = (msgSignature, timestamp, nonce, encryptedMsg) => {
  try {
    const token = process.env.WECHAT_TOKEN;
    const encodingAESKey = process.env.WECHAT_ENCODING_AES_KEY;
    
    // 验证签名
    const signature = crypto
      .createHash('sha1')
      .update([token, timestamp, nonce, encryptedMsg].sort().join(''))
      .digest('hex');
    
    if (signature !== msgSignature) {
      throw new Error('消息签名验证失败');
    }
    
    // 解密消息
    const key = Buffer.from(encodingAESKey + '=', 'base64');
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, key.slice(0, 16));
    
    let decrypted = decipher.update(encryptedMsg, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    
    // 解析消息内容
    const content = decrypted.slice(16);
    const contentLength = content.slice(0, 4);
    const message = content.slice(4, 4 + parseInt(contentLength));
    
    // 解析XML消息
    const xml2js = require('xml2js');
    const parser = new xml2js.Parser({ explicitArray: false });
    
    return new Promise((resolve, reject) => {
      parser.parseString(message, (err, result) => {
        if (err) {
          reject(new Error(`解析XML消息失败: ${err.message}`));
        } else {
          resolve(result.xml);
        }
      });
    });
  } catch (error) {
    console.error('❌ 解密企业微信消息失败:', error.message);
    throw error;
  }
};



/**
 * 获取客服账号链接（用于生成带scene_param的绑定链接）
 * @param {string} openKfId 客服账号ID
 * @param {string} scene 场景值，字符串类型，由开发者自定义，不多于32字节
 * @returns {Promise<Object>} 客服链接结果
 */
const getKfContactWay = async (openKfId, scene) => {
  try {
    const db = require('../data/database');

    // 1. 先尝试从缓存获取
    const cacheResult = await db.getKfLinkFromCache(openKfId, scene);
    if (cacheResult.success) {
      console.log('✅ 使用缓存的客服链接');
      return cacheResult;
    }

    // 2. 缓存未命中，调用企业微信API
    console.log('🔗 缓存未命中，调用企业微信API获取客服链接');
    const tokenInfo = await getAccessTokenWithExpiry();

    const response = await axios.post(
      `${WECHAT_API_BASE}/cgi-bin/kf/add_contact_way?access_token=${tokenInfo.token}`,
      {
        open_kfid: openKfId,
        scene: scene
      },
      {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    const { data } = response;

    if (data.errcode === 0) {
      console.log('✅ 获取客服链接成功');
      const baseUrl = data.url;

      // 3. 保存到缓存（与access_token过期时间同步）
      await db.saveKfLinkToCache(openKfId, scene, baseUrl, tokenInfo.expiresAt);

      return {
        success: true,
        url: baseUrl,
        cached: false
      };
    } else {
      throw new Error(`获取客服链接失败: ${data.errmsg} (${data.errcode})`);
    }
  } catch (error) {
    console.error('❌ 获取客服链接失败:', error.message);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 生成带绑定参数的客服链接
 * @param {string} openKfId 客服账号ID
 * @param {string} userUuid 用户UUID（用于绑定）
 * @param {string} scene 场景值（可选，默认为'binding'）
 * @returns {Promise<Object>} 生成结果
 */
const generateBindingKfLink = async (openKfId, userUuid, scene = 'binding') => {
  try {
    console.log('🔗 开始生成绑定客服链接:', { openKfId, userUuid, scene });

    // 验证输入参数
    if (!openKfId || !userUuid) {
      throw new Error('缺少必要参数：openKfId或userUuid');
    }

    // 验证userUuid格式
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userUuid)) {
      console.error('❌ 无效的UUID格式:', userUuid);
      throw new Error('用户UUID格式无效');
    }

    console.log('✅ 输入参数验证通过');

    // 根据openKfId的格式选择不同的处理方式
    let baseUrl;

    if (openKfId.startsWith('wk')) {
      // 这是完整的open_kfid，需要通过API获取链接
      console.log('🔗 检测到完整的open_kfid，通过API获取客服链接');
      const kfLinkResult = await getKfContactWay(openKfId, scene);
      if (!kfLinkResult.success) {
        throw new Error(`获取客服链接失败: ${kfLinkResult.message}`);
      }
      baseUrl = kfLinkResult.url;
    } else {
      // 这是简短的客服ID，直接构建链接
      console.log('🔗 检测到简短的客服ID，直接构建链接');
      baseUrl = `https://work.weixin.qq.com/kfid/${openKfId}`;
    }

    console.log('获取到基础客服链接:', baseUrl);

    // 2. 生成短token作为scene_param（解决企业微信64字符限制）
    const crypto = require('crypto');

    console.log('🔐 开始生成短token');

    // 生成16字符的随机token（加前缀区分新旧版本）
    const randomPart = crypto.randomBytes(6).toString('hex'); // 12字符
    const shortToken = 'v2' + randomPart; // v2前缀 + 12字符随机 = 14字符
    console.log('生成的短token:', shortToken);
    console.log('Token长度:', shortToken.length);
    console.log('Token格式:', 'v2前缀 + 12字符随机');

    // 将token和用户UUID的映射关系存储在数据库中（持久化方案）
    const db = require('../data/database');

    const tokenData = {
      user_uuid: userUuid,
      created_at: new Date(),
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时过期
    };

    try {
      // 使用数据库层函数存储绑定token
      await db.storeBindingToken(userUuid, shortToken, tokenData.expires_at);
      console.log('Token映射已存储到数据库:', { token: shortToken, user_uuid: userUuid });

      // 清理过期的token
      await db.cleanupExpiredBindingTokens();

    } catch (dbError) {
      console.error('❌ 数据库存储token失败:', dbError);
      throw new Error('Token存储失败: ' + dbError.message);
    }

    // 直接使用短token，不需要加密
    const combined = shortToken;

    console.log('🔍 Token结果:');
    console.log('- Token长度:', combined.length);
    console.log('- Token内容:', combined);
    console.log('- 是否符合企业微信限制:', combined.length <= 64 ? '✅ 符合' : '❌ 超出');

    // 验证token映射
    console.log('🔍 验证token映射...');
    try {
      const verifyResult = await db.getBindingByToken(shortToken);

      if (verifyResult && verifyResult.user_uuid === userUuid) {
        console.log('✅ Token映射验证成功');
      } else {
        console.error('❌ Token映射验证失败');
        throw new Error('Token映射验证失败');
      }
    } catch (verifyError) {
      console.error('❌ Token验证查询失败:', verifyError);
      throw new Error('Token验证失败: ' + verifyError.message);
    }

    // 3. URL编码scene_param
    const sceneParam = encodeURIComponent(combined);
    console.log('🌐 URL编码后的scene_param长度:', sceneParam.length);
    console.log('URL编码前后是否有变化:', combined !== sceneParam);

    // 4. 拼接scene_param到客服链接（添加防缓存参数）
    const separator = baseUrl.includes('?') ? '&' : '?';
    const timestamp = Date.now();
    const finalUrl = `${baseUrl}${separator}scene_param=${sceneParam}&t=${timestamp}`;

    console.log('✅ 生成绑定链接成功');
    console.log('最终链接长度:', finalUrl.length);
    console.log('防缓存时间戳:', timestamp);
    
    return {
      success: true,
      binding_url: finalUrl,
      scene_param: sceneParam,
      base_url: baseUrl,
      expires_at: tokenData.expires_at.toISOString(),
      debug_info: {
        original_uuid: userUuid,
        token_length: combined.length,
        url_encoded_length: sceneParam.length,
        verification_passed: true,
        storage_method: 'database'
      }
    };

  } catch (error) {
    console.error('❌ 生成绑定客服链接失败:', error.message);
    console.error('错误堆栈:', error.stack);
    return {
      success: false,
      message: error.message,
      debug_info: {
        error_type: error.constructor.name,
        error_message: error.message
      }
    };
  }
};

/**
 * 同步客服消息
 * @param {Object} params - 同步参数
 * @param {string} params.token - 回调事件返回的token字段
 * @param {string} params.open_kfid - 客服账号ID
 * @param {string} [params.cursor] - 上次调用返回的next_cursor
 * @param {number} [params.limit=10] - 期望请求的数据量
 * @returns {Promise<Object>} 同步结果
 */
const syncKfMessages = async (params) => {
  try {
    const { token, open_kfid, cursor, limit = 10 } = params;

    if (!token || !open_kfid) {
      return {
        success: false,
        message: '缺少必要参数：token和open_kfid'
      };
    }

    // 获取access_token
    const accessToken = await getAccessToken();
    if (!accessToken) {
      return {
        success: false,
        message: '获取access_token失败'
      };
    }

    const url = `https://qyapi.weixin.qq.com/cgi-bin/kf/sync_msg?access_token=${accessToken}`;

    const requestBody = {
      token,
      open_kfid,
      limit
    };

    if (cursor) {
      requestBody.cursor = cursor;
    }

    logInfo('调用sync_msg接口:', {
      url,
      body: {
        ...requestBody,
        token: token.substring(0, 20) + '...'
      }
    });

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    const result = await response.json();

    logInfo('sync_msg接口响应:', {
      errcode: result.errcode,
      errmsg: result.errmsg,
      has_more: result.has_more,
      msg_count: result.msg_list?.length || 0
    });

    if (result.errcode !== 0) {
      return {
        success: false,
        message: `sync_msg接口调用失败: ${result.errmsg}`,
        errcode: result.errcode
      };
    }

    return {
      success: true,
      next_cursor: result.next_cursor,
      has_more: result.has_more,
      msg_list: result.msg_list || []
    };

  } catch (error) {
    logError('sync_msg接口调用异常:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 检查API配置
 * @returns {Promise<Object>} 配置检查结果
 */
const checkApiConfig = async () => {
  try {
    const requiredEnvVars = [
      'WECHAT_CORP_ID',
      'WECHAT_CORP_SECRET',
      'WECHAT_AGENT_ID',
      'WECHAT_TOKEN',
      'WECHAT_ENCODING_AES_KEY'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      return {
        success: false,
        message: `缺少必要的环境变量: ${missingVars.join(', ')}`
      };
    }

    // 测试API连接
    const accessToken = await getAccessToken();

    return {
      success: true,
      message: '企业微信API配置正常',
      config: {
        corp_id: CORP_ID,
        agent_id: AGENT_ID,
        has_access_token: !!accessToken
      }
    };
  } catch (error) {
    return {
      success: false,
      message: `API配置检查失败: ${error.message}`
    };
  }
};

module.exports = {
  getAccessToken,
  getAccessTokenWithExpiry,
  downloadMedia,
  getMediaInfo,
  sendTextMessage,
  sendWelcomeMessage,
  getUserInfo,
  verifyCallbackUrl,
  decryptMessage,
  getKfContactWay,
  generateBindingKfLink,
  syncKfMessages,
  checkApiConfig,
  // 新增的文件类型检测函数
  getExtensionFromMimeType,
  detectFileType
};