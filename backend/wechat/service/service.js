/**
 * 微信转发功能服务层 - 重构后的统一入口
 * 将原有的庞大service.js拆分为多个专门的服务模块
 * 本文件作为统一的导出入口，保持API接口的一致性
 *
 * 重构架构说明：
 * - WebhookService: 处理企业微信Webhook验证和消息解密
 * - MessageProcessService: 处理各种类型的微信消息
 * - UserBindingService: 处理用户绑定相关功能
 * - MessageSyncService: 处理消息同步和设备管理
 * - MediaDownloadService: 处理媒体文件下载和管理
 */

const WebhookService = require('./WebhookService');
const MessageProcessService = require('./MessageProcessService');
const UserBindingService = require('./UserBindingService');
const MessageSyncService = require('./MessageSyncService');
const MediaDownloadService = require('./MediaDownloadService');

/**
 * 统一导出所有微信转发功能的API
 * 重构后的服务层架构，每个服务专注于单一职责
 */
module.exports = {
  // ==================== Webhook处理服务 ====================
  // 企业微信Webhook验证和消息解密
  verifyWebhookUrl: WebhookService.verifyWebhookUrl,
  verifyMessageSignature: WebhookService.verifyMessageSignature,
  decryptMessage: WebhookService.decryptMessage,
  parseDecryptedXML: WebhookService.parseDecryptedXML,
  validateConfig: WebhookService.validateConfig,
  getConfigValidation: WebhookService.getConfigValidation,

  // ==================== 消息处理服务 ====================
  // 各种类型的微信消息处理
  processWeChatMessage: MessageProcessService.processWeChatMessage,
  processTextMessage: MessageProcessService.processTextMessage,
  processImageMessage: MessageProcessService.processImageMessage,
  processVoiceMessage: MessageProcessService.processVoiceMessage,
  processVideoMessage: MessageProcessService.processVideoMessage,
  processFileMessage: MessageProcessService.processFileMessage,
  processLocationMessage: MessageProcessService.processLocationMessage,
  processLinkMessage: MessageProcessService.processLinkMessage,
  processEventMessage: MessageProcessService.processEventMessage,
  processUnknownMessage: MessageProcessService.processUnknownMessage,

  // ==================== 用户绑定服务 ====================
  // 用户绑定相关功能
  getBindingByUserUuid: UserBindingService.getBindingByUserUuid,
  getBindingByExternalUserId: UserBindingService.getBindingByExternalUserId,
  unbindUser: UserBindingService.unbindUser,
  generateWeChatBindingLink: UserBindingService.generateWeChatBindingLink,
  pushBindingSuccessToDevices: UserBindingService.pushBindingSuccessToDevices,

  // ==================== 消息同步服务 ====================
  // 消息同步和设备管理
  saveMessageMetadata: MessageSyncService.saveMessageMetadata,
  pushMessageToDevices: MessageSyncService.pushMessageToDevices,
  pushDownloadInstructionToDevices: MessageSyncService.pushDownloadInstructionToDevices,
  getIncrementalMessages: MessageSyncService.getIncrementalMessages,
  updateDeviceSyncStatus: MessageSyncService.updateDeviceSyncStatus,
  registerDevice: MessageSyncService.registerDevice,

  getUserMessages: MessageSyncService.getUserMessages,
  markMessageAsRead: MessageSyncService.markMessageAsRead,
  cleanupExpiredMessages: MessageSyncService.cleanupExpiredMessages,

  // ==================== 媒体下载服务 ====================
  // 媒体文件下载和管理
  downloadWeChatMediaFile: MediaDownloadService.downloadWeChatMediaFile,
  batchDownloadMediaFiles: MediaDownloadService.batchDownloadMediaFiles,
  getMediaFileInfo: MediaDownloadService.getMediaFileInfo,
  validateMediaAccess: MediaDownloadService.validateMediaAccess,
  cleanupExpiredMediaCache: MediaDownloadService.cleanupExpiredMediaCache,
  getDownloadStats: MediaDownloadService.getDownloadStats,
  preloadMediaFiles: MediaDownloadService.preloadMediaFiles,
  getAccessToken: MediaDownloadService.getAccessToken,
};