/**
 * 消息处理服务
 * 专门处理各种类型的微信消息
 */

const { logInfo, logError } = require('../api/errorHandler');
const WebhookService = require('./WebhookService');
const db = require('../data/database');

/**
 * 处理企业微信消息的主入口
 */
const processWeChatMessage = async (messageData) => {
  try {
    logInfo('📨 【使用接收】处理企业微信消息');
    
    // 解密消息内容
    const decryptedMessage = WebhookService.decryptMessage(messageData);
    if (!decryptedMessage) {
      return {
        success: false,
        message: '消息解密失败'
      };
    }
    
    logInfo('解密后的消息:', decryptedMessage);
    logInfo('消息字段详情:', {
      FromUserName: decryptedMessage.FromUserName,
      ToUserName: decryptedMessage.ToUserName,
      MsgType: decryptedMessage.MsgType,
      msgtype: decryptedMessage.msgtype,
      Content: decryptedMessage.Content,
      Event: decryptedMessage.Event,
      allKeys: Object.keys(decryptedMessage)
    });
    
    // 根据消息类型处理
    const messageType = decryptedMessage.MsgType || decryptedMessage.msgtype;
    
    switch (messageType) {
      case 'text':
        return await processTextMessage(decryptedMessage);
      case 'image':
        return await processImageMessage(decryptedMessage);
      case 'voice':
        return await processVoiceMessage(decryptedMessage);
      case 'video':
        return await processVideoMessage(decryptedMessage);
      case 'file':
        return await processFileMessage(decryptedMessage);
      case 'location':
        return await processLocationMessage(decryptedMessage);
      case 'link':
        return await processLinkMessage(decryptedMessage);
      case 'event':
        return await processEventMessage(decryptedMessage);
      default:
        logInfo('未处理的消息类型:', messageType);
        return await processUnknownMessage(decryptedMessage, messageType);
    }
  } catch (error) {
    logError('【使用接收】消息处理异常:', error);
    return {
      success: false,
      message: '消息处理异常: ' + error.message
    };
  }
};

/**
 * 处理文本消息
 */
const processTextMessage = async (message) => {
  try {
    logInfo('处理文本消息:', message.Content);
    
    const fromUser = message.FromUserName;

    // 检查用户是否已绑定
    const binding = await getBindingByExternalUserId(fromUser);
    if (!binding) {
      logInfo('用户未绑定，忽略文本消息');
      return {
        success: true,
        message: '用户未绑定，文本消息已忽略'
      };
    }

    // 处理已绑定用户的文本消息
    return await processUserTextMessage(binding, message);
  } catch (error) {
    logError('文本消息处理异常:', error);
    return {
      success: false,
      message: '文本消息处理失败'
    };
  }
};

/**
 * 处理图片消息
 */
const processImageMessage = async (message) => {
  try {
    logInfo('处理图片消息:', message.MsgType);
    const fromUser = message.FromUserName;
    const toUser = message.ToUserName;
    const msgId = message.MsgId;
    const mediaId = message.MediaId;

    // 检查用户是否已绑定
    const binding = await getBindingByExternalUserId(fromUser);
    if (!binding) {
      logInfo('用户未绑定，忽略图片消息');
      return {
        success: true,
        message: '用户未绑定，图片消息已忽略'
      };
    }

    // 记录消息到数据库（仅元数据）
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msgId,
      message_type: 'image',
      media_id: mediaId,
      media_id_expires_at: new Date(Date.now() + 72 * 60 * 60 * 1000), // 72小时后过期
      file_extension: 'jpg', // 图片消息默认为jpg，实际类型在下载时检测
      mime_type: 'image/jpeg',
      metadata: {
        timestamp: message.CreateTime,
        from_user: fromUser,
        to_user: toUser,
        detected_type: 'image'
      }
    });

    if (messageRecord.success) {
      // 推送下载指令到用户所有设备
      await pushDownloadInstructionToDevices(binding.user_uuid, {
        message_id: messageRecord.message_id,
        message_type: 'image',
        media_id: mediaId,
        expires_at: messageRecord.media_id_expires_at
      });

      return {
        success: true,
        message: '图片消息已处理并推送下载指令到设备'
      };
    } else {
      return {
        success: false,
        message: '消息记录失败'
      };
    }
  } catch (error) {
    logError('图片消息处理异常:', error);
    return {
      success: false,
      message: '图片消息处理失败'
    };
  }
};

/**
 * 处理语音消息
 */
const processVoiceMessage = async (message) => {
  try {
    logInfo('处理语音消息:', message.MsgType);
    const fromUser = message.FromUserName;
    const toUser = message.ToUserName;
    const msgId = message.MsgId;
    const mediaId = message.MediaId;

    // 检查用户是否已绑定
    const binding = await getBindingByExternalUserId(fromUser);
    if (!binding) {
      logInfo('用户未绑定，忽略语音消息');
      return {
        success: true,
        message: '用户未绑定，语音消息已忽略'
      };
    }

    // 记录消息到数据库（仅元数据）
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msgId,
      message_type: 'voice',
      media_id: mediaId,
      media_id_expires_at: new Date(Date.now() + 72 * 60 * 60 * 1000), // 72小时后过期
      metadata: {
        timestamp: message.CreateTime,
        from_user: fromUser,
        to_user: toUser,
        format: message.Format,
        recognition: message.Recognition
      }
    });

    if (messageRecord.success) {
      // 推送下载指令到用户所有设备
      await pushDownloadInstructionToDevices(binding.user_uuid, {
        message_id: messageRecord.message_id,
        message_type: 'voice',
        media_id: mediaId,
        expires_at: messageRecord.media_id_expires_at,
        format: message.Format
      });

      return {
        success: true,
        message: '语音消息已处理并推送下载指令到设备'
      };
    } else {
      return {
        success: false,
        message: '消息记录失败'
      };
    }
  } catch (error) {
    logError('语音消息处理异常:', error);
    return {
      success: false,
      message: '语音消息处理失败'
    };
  }
};

/**
 * 处理视频消息
 */
const processVideoMessage = async (message) => {
  try {
    logInfo('处理视频消息:', message.MsgType);
    const fromUser = message.FromUserName;
    const toUser = message.ToUserName;
    const msgId = message.MsgId;
    const mediaId = message.MediaId;

    // 检查用户是否已绑定
    const binding = await getBindingByExternalUserId(fromUser);
    if (!binding) {
      logInfo('用户未绑定，忽略视频消息');
      return {
        success: true,
        message: '用户未绑定，视频消息已忽略'
      };
    }

    // 记录消息到数据库（仅元数据）
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msgId,
      message_type: 'video',
      media_id: mediaId,
      media_id_expires_at: new Date(Date.now() + 72 * 60 * 60 * 1000), // 72小时后过期
      metadata: {
        timestamp: message.CreateTime,
        from_user: fromUser,
        to_user: toUser,
        thumb_media_id: message.ThumbMediaId
      }
    });

    if (messageRecord.success) {
      // 推送下载指令到用户所有设备
      await pushDownloadInstructionToDevices(binding.user_uuid, {
        message_id: messageRecord.message_id,
        message_type: 'video',
        media_id: mediaId,
        expires_at: messageRecord.media_id_expires_at,
        thumb_media_id: message.ThumbMediaId
      });

      return {
        success: true,
        message: '视频消息已处理并推送下载指令到设备'
      };
    } else {
      return {
        success: false,
        message: '消息记录失败'
      };
    }
  } catch (error) {
    logError('视频消息处理异常:', error);
    return {
      success: false,
      message: '视频消息处理失败'
    };
  }
};

/**
 * 处理文件消息
 */
const processFileMessage = async (message) => {
  try {
    logInfo('处理文件消息:', message.MsgType);
    const fromUser = message.FromUserName;
    const toUser = message.ToUserName;
    const msgId = message.MsgId;
    const mediaId = message.MediaId;

    // 检查用户是否已绑定
    const binding = await getBindingByExternalUserId(fromUser);
    if (!binding) {
      logInfo('用户未绑定，忽略文件消息');
      return {
        success: true,
        message: '用户未绑定，文件消息已忽略'
      };
    }

    // 预检测文件类型
    const fileTypeInfo = await detectFileTypeFromName(message.FileName);

    // 记录消息到数据库（包含文件类型信息）
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msgId,
      message_type: fileTypeInfo.category, // 使用检测到的文件类型
      media_id: mediaId,
      media_id_expires_at: new Date(Date.now() + 72 * 60 * 60 * 1000), // 72小时后过期
      file_extension: fileTypeInfo.extension,
      mime_type: fileTypeInfo.mimeType,
      metadata: {
        timestamp: message.CreateTime,
        from_user: fromUser,
        to_user: toUser,
        file_name: message.FileName,
        file_size: message.FileSize,
        detected_type: fileTypeInfo.category,
        file_extension: fileTypeInfo.extension
      }
    });

    if (messageRecord.success) {
      // 推送下载指令到用户所有设备
      await pushDownloadInstructionToDevices(binding.user_uuid, {
        message_id: messageRecord.message_id,
        message_type: fileTypeInfo.category,
        media_id: mediaId,
        expires_at: messageRecord.media_id_expires_at,
        file_name: message.FileName,
        file_size: message.FileSize,
        file_extension: fileTypeInfo.extension,
        mime_type: fileTypeInfo.mimeType,
        detected_type: fileTypeInfo.category
      });

      return {
        success: true,
        message: '文件消息已处理并推送下载指令到设备'
      };
    } else {
      return {
        success: false,
        message: '消息记录失败'
      };
    }
  } catch (error) {
    logError('文件消息处理异常:', error);
    return {
      success: false,
      message: '文件消息处理失败'
    };
  }
};

/**
 * 处理位置消息
 */
const processLocationMessage = async (message) => {
  try {
    logInfo('处理位置消息:', message.MsgType);
    const fromUser = message.FromUserName;
    const toUser = message.ToUserName;
    const msgId = message.MsgId;

    // 检查用户是否已绑定
    const binding = await getBindingByExternalUserId(fromUser);
    if (!binding) {
      logInfo('用户未绑定，忽略位置消息');
      return {
        success: true,
        message: '用户未绑定，位置消息已忽略'
      };
    }

    // 提取位置信息
    const locationX = message.Location_X; // 纬度
    const locationY = message.Location_Y; // 经度
    const scale = message.Scale; // 地图缩放大小
    const label = message.Label; // 地理位置信息

    // 记录消息到数据库（仅元数据）
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msgId,
      message_type: 'location',
      content: label || '位置信息',
      metadata: {
        timestamp: message.CreateTime,
        from_user: fromUser,
        to_user: toUser,
        location_x: locationX,
        location_y: locationY,
        scale: scale,
        label: label
      }
    });

    if (messageRecord.success) {
      // 推送到用户所有设备
      await pushMessageToDevices(binding.user_uuid, messageRecord.message_id);

      return {
        success: true,
        message: '位置消息已处理并推送到设备'
      };
    } else {
      return {
        success: false,
        message: '位置消息记录失败'
      };
    }
  } catch (error) {
    logError('位置消息处理异常:', error);
    return {
      success: false,
      message: '位置消息处理失败'
    };
  }
};

/**
 * 处理链接消息
 */
const processLinkMessage = async (message) => {
  try {
    logInfo('处理链接消息:', message.MsgType);
    const fromUser = message.FromUserName;
    const toUser = message.ToUserName;
    const msgId = message.MsgId;

    // 检查用户是否已绑定
    const binding = await getBindingByExternalUserId(fromUser);
    if (!binding) {
      logInfo('用户未绑定，忽略链接消息');
      return {
        success: true,
        message: '用户未绑定，链接消息已忽略'
      };
    }

    // 提取链接信息
    const title = message.Title; // 链接标题
    const description = message.Description; // 链接描述
    const url = message.Url; // 链接地址
    const picUrl = message.PicUrl; // 链接缩略图

    // 记录消息到数据库（仅元数据）
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msgId,
      message_type: 'link',
      content: url,
      metadata: {
        timestamp: message.CreateTime,
        from_user: fromUser,
        to_user: toUser,
        title: title,
        description: description,
        url: url,
        pic_url: picUrl
      }
    });

    if (messageRecord.success) {
      // 推送到用户所有设备
      await pushMessageToDevices(binding.user_uuid, messageRecord.message_id);

      return {
        success: true,
        message: '链接消息已处理并推送到设备'
      };
    } else {
      return {
        success: false,
        message: '链接消息记录失败'
      };
    }
  } catch (error) {
    logError('链接消息处理异常:', error);
    return {
      success: false,
      message: '链接消息处理失败'
    };
  }
};

/**
 * 处理事件消息
 */
const processEventMessage = async (message) => {
  try {
    logInfo('处理事件消息:', message.Event);
    const fromUser = message.FromUserName;
    const toUser = message.ToUserName;
    const eventType = message.Event;

    // 特殊处理：检查是否为企业微信客服事件
    if (eventType === 'kf_msg_or_event' && message.Token && message.OpenKfId) {
      logInfo('🔗 检测到企业微信客服事件，需要拉取具体消息');
      logInfo('Token:', message.Token.substring(0, 20) + '...');
      logInfo('OpenKfId:', message.OpenKfId);
      return await processKfEvent(message);
    }

    // 检查用户是否已绑定
    const binding = await getBindingByExternalUserId(fromUser);
    if (!binding) {
      logInfo('用户未绑定，忽略事件消息');
      return {
        success: true,
        message: '用户未绑定，事件消息已忽略'
      };
    }

    // 记录事件消息到数据库
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: message.MsgId || `event_${Date.now()}`,
      message_type: 'event',
      content: eventType,
      metadata: {
        timestamp: message.CreateTime,
        from_user: fromUser,
        to_user: toUser,
        event_type: eventType,
        event_key: message.EventKey,
        event_data: message
      }
    });

    if (messageRecord.success) {
      // 推送到用户所有设备
      await pushMessageToDevices(binding.user_uuid, messageRecord.message_id);

      return {
        success: true,
        message: '事件消息已处理并推送到设备'
      };
    } else {
      return {
        success: false,
        message: '事件消息记录失败'
      };
    }
  } catch (error) {
    logError('事件消息处理异常:', error);
    return {
      success: false,
      message: '事件消息处理失败'
    };
  }
};

/**
 * 处理未知类型消息
 */
const processUnknownMessage = async (message, messageType) => {
  try {
    logInfo('处理未知类型消息:', messageType);
    const fromUser = message.FromUserName;
    const toUser = message.ToUserName;

    // 检查用户是否已绑定
    const binding = await getBindingByExternalUserId(fromUser);
    if (!binding) {
      logInfo('用户未绑定，忽略未知类型消息');
      return {
        success: true,
        message: '用户未绑定，未知类型消息已忽略'
      };
    }

    // 记录未知消息到数据库
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: message.MsgId || `unknown_${Date.now()}`,
      message_type: 'unknown',
      content: `未知消息类型: ${messageType}`,
      metadata: {
        timestamp: message.CreateTime,
        from_user: fromUser,
        to_user: toUser,
        original_type: messageType,
        raw_message: message
      }
    });

    if (messageRecord.success) {
      // 推送到用户所有设备
      await pushMessageToDevices(binding.user_uuid, messageRecord.message_id);

      return {
        success: true,
        message: '未知类型消息已记录并推送到设备'
      };
    } else {
      return {
        success: false,
        message: '未知类型消息记录失败'
      };
    }
  } catch (error) {
    logError('未知类型消息处理异常:', error);
    return {
      success: false,
      message: '未知类型消息处理失败'
    };
  }
};

/**
 * 处理已绑定用户的文本消息
 */
const processUserTextMessage = async (binding, message) => {
  try {
    // 记录消息到数据库（仅元数据）
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: message.MsgId,
      message_type: 'text',
      content: message.Content,
      metadata: {
        timestamp: message.CreateTime,
        from_user: message.FromUserName,
        to_user: message.ToUserName
      }
    });

    if (messageRecord.success) {
      // 推送到用户所有设备
      await pushMessageToDevices(binding.user_uuid, messageRecord.message_id);

      return {
        success: true,
        message: '文本消息已处理并推送到设备'
      };
    } else {
      return {
        success: false,
        message: '消息记录失败'
      };
    }
  } catch (error) {
    logError('处理用户文本消息异常:', error);
    return {
      success: false,
      message: '文本消息处理失败'
    };
  }
};

// 引入统一文件类型配置
const { detectFileTypeFromName: detectFileTypeFromNameUnified } = require('../config/fileTypes');

/**
 * 根据文件名检测文件类型
 */
const detectFileTypeFromName = async (fileName) => {
  try {
    const typeInfo = detectFileTypeFromNameUnified(fileName);
    logInfo(`文件类型检测成功: ${fileName} -> ${typeInfo.category} (${typeInfo.extension})`);
    return {
      category: typeInfo.category,
      extension: typeInfo.extension,
      mimeType: typeInfo.mimeType
    };
  } catch (error) {
    logError('文件类型检测失败:', error);
    return {
      category: 'file',
      extension: 'bin',
      mimeType: 'application/octet-stream'
    };
  }
};

// ==================== 辅助函数 ====================

/**
 * 根据external_userid查询绑定信息
 */
const getBindingByExternalUserId = async (externalUserId) => {
  try {
    const binding = await db.getBindingByExternalUserId(externalUserId);
    if (binding) {
      return {
        user_uuid: binding.user_uuid,
        external_userid: binding.external_userid,
        binding_status: binding.binding_status,
        binding_time: binding.binding_time
      };
    }
    return null;
  } catch (error) {
    logError('根据external_userid查询绑定信息失败:', error);
    return null;
  }
};

/**
 * 解析绑定令牌的辅助函数（支持短token方案）
 */
const decryptBindingToken = async (tokenParam) => {
  try {
    logInfo('🔍 开始解析绑定令牌');
    logInfo('令牌长度:', tokenParam.length);

    // 先进行URL解码
    const decodedToken = decodeURIComponent(tokenParam);
    logInfo('URL解码后长度:', decodedToken.length);
    logInfo('URL解码后内容:', decodedToken.substring(0, 50) + '...');

    // 检查是否是短token（v2前缀 + 12字符hex，或旧的16字符hex）
    const isV2Token = decodedToken.length === 14 && decodedToken.startsWith('v2') && /^v2[0-9a-f]{12}$/i.test(decodedToken);
    const isV1Token = decodedToken.length === 16 && /^[0-9a-f]{16}$/i.test(decodedToken);

    if (isV2Token || isV1Token) {
      const tokenVersion = isV2Token ? 'v2' : 'v1';
      logInfo(`检测到短token格式 (${tokenVersion})，从数据库中查找`);

      try {
        const db = require('../data/database');
        const tokenData = await db.getBindingByToken(decodedToken);

        if (!tokenData) {
          logError('❌ 令牌不存在或已过期');
          return { success: false, message: '令牌不存在或已过期' };
        }

        logInfo('✅ 短token解析成功，用户UUID:', tokenData.user_uuid.substring(0, 10) + '...');

        return {
          success: true,
          data: {
            user_uuid: tokenData.user_uuid,
            timestamp: tokenData.created_at,
            expires_at: tokenData.token_expires_at
          }
        };

      } catch (dbError) {
        logError('❌ 数据库查询token失败:', dbError);
        return { success: false, message: '数据库查询失败: ' + dbError.message };
      }
    }

    // 如果不是短token，尝试传统的加密解密方式
    logInfo('检测到传统加密格式，尝试解密');
    const crypto = require('crypto');
    const secretKey = process.env.WECHAT_BINDING_SECRET || 'default_secret_key';

    // 首先尝试简化解密（针对企业微信缓存的旧格式）
    try {
      logInfo('尝试简化解密方式（兼容企业微信缓存）');
      const decipher = crypto.createDecipher('aes-256-cbc', secretKey);
      let decryptedData = decipher.update(decodedToken, 'base64', 'utf8');
      decryptedData += decipher.final('utf8');

      if (decryptedData) {
        logInfo('✅ 简化解密成功，数据长度:', decryptedData.length);
        logInfo('解密后的原始数据:', decryptedData.substring(0, 100) + '...');

        const parsedData = JSON.parse(decryptedData);

        logInfo('解密后的数据:', {
          user_uuid: parsedData.user_uuid ? parsedData.user_uuid.substring(0, 10) + '...' : 'undefined',
          uuid: parsedData.uuid ? parsedData.uuid.substring(0, 10) + '...' : 'undefined',
          timestamp: parsedData.timestamp,
          expires_at: parsedData.expires_at
        });

        return {
          success: true,
          data: {
            user_uuid: parsedData.uuid || parsedData.user_uuid,
            timestamp: parsedData.timestamp || new Date().toISOString(),
            expires_at: parsedData.expires_at || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          }
        };
      }
    } catch (simpleDecryptError) {
      logInfo('简化解密失败，尝试标准解密:', simpleDecryptError.message);
    }

    // 首先尝试简化解密（针对企业微信缓存的旧格式）
    try {
      logInfo('尝试简化解密方式（兼容企业微信缓存）');
      const decipher = crypto.createDecipher('aes-256-cbc', secretKey);
      let decryptedData = decipher.update(decodedToken, 'base64', 'utf8');
      decryptedData += decipher.final('utf8');

      if (decryptedData) {
        logInfo('✅ 简化解密成功，数据长度:', decryptedData.length);
        const parsedData = JSON.parse(decryptedData);

        logInfo('解密后的数据:', {
          user_uuid: parsedData.user_uuid ? parsedData.user_uuid.substring(0, 10) + '...' : 'undefined',
          uuid: parsedData.uuid ? parsedData.uuid.substring(0, 10) + '...' : 'undefined',
          timestamp: parsedData.timestamp,
          expires_at: parsedData.expires_at
        });

        return {
          success: true,
          data: {
            user_uuid: parsedData.uuid || parsedData.user_uuid,
            timestamp: parsedData.timestamp || new Date().toISOString(),
            expires_at: parsedData.expires_at || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          }
        };
      }
    } catch (simpleDecryptError) {
      logInfo('简化解密失败，尝试标准解密:', simpleDecryptError.message);
    }

    // 检查是否包含冒号分隔符
    if (decodedToken.includes(':')) {
      // 标准格式（IV:加密数据）
      const parts = decodedToken.split(':');
      if (parts.length !== 2) {
        logError('❌ 绑定令牌格式错误，parts长度:', parts.length);
        logError('分割结果:', parts);
        throw new Error('绑定令牌格式无效');
      }

      // 处理标准格式的解密...
      logInfo('使用标准格式解密（IV:加密数据）');
      // 这里继续原来的解密逻辑
    } else {
      // 简化格式（直接加密数据，无IV）
      logInfo('检测到简化格式，尝试直接解密');
      try {
        const decipher = crypto.createDecipher('aes-256-cbc', secretKey);
        let decryptedData = decipher.update(decodedToken, 'base64', 'utf8');
        decryptedData += decipher.final('utf8');

        if (!decryptedData) {
          logError('❌ 简化解密返回为空');
          return { success: false, message: '解密失败' };
        }

        logInfo('✅ 简化解密成功，数据长度:', decryptedData.length);
        const parsedData = JSON.parse(decryptedData);

        return {
          success: true,
          data: {
            user_uuid: parsedData.uuid || parsedData.user_uuid,
            timestamp: parsedData.timestamp || new Date().toISOString(),
            expires_at: parsedData.expires_at || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          }
        };
      } catch (error) {
        logError('❌ 简化解密失败:', error);
        return { success: false, message: `简化解密失败: ${error.message}` };
      }
    }

    // 标准格式（带iv）
    const ivBase64 = parts[0];
    const encryptedDataBase64 = parts[1];

    logInfo('IV (base64)长度:', ivBase64.length);
    logInfo('加密数据 (base64)长度:', encryptedDataBase64.length);

    // 使用与加密端相同的密钥生成方式
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(secretKey, 'salt', 32);
    const iv = Buffer.from(ivBase64, 'base64');

    logInfo('🔍 解密参数:');
    logInfo('- 算法:', algorithm);
    logInfo('- Key长度:', key.length);
    logInfo('- IV长度:', iv.length);

    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    let decryptedData = decipher.update(encryptedDataBase64, 'base64', 'utf8');
    decryptedData += decipher.final('utf8');

    if (!decryptedData) {
      logError('❌ 解密返回为空');
      return { success: false, message: '解密失败' };
    }

    logInfo('✅ 解密成功，数据长度:', decryptedData.length);
    return { success: true, data: JSON.parse(decryptedData) };

  } catch (error) {
    logError('❌ 绑定令牌解密异常:', error);
    if (error.message.includes('Malformed UTF-8 data') || error.message.includes('bad decrypt')) {
      return { success: false, message: '令牌密钥不匹配' };
    }
    return { success: false, message: `解密失败: ${error.message}` };
  }
};

/**
 * 处理企业微信客服事件（需要调用sync_msg接口获取具体消息）
 */
const processKfEvent = async (message) => {
  try {
    logInfo('🔗 开始处理企业微信客服事件');

    const token = message.Token;
    const openKfId = message.OpenKfId;

    if (!token || !openKfId) {
      logError('❌ 客服事件中缺少Token或OpenKfId');
      return {
        success: false,
        message: '客服事件缺少必要参数'
      };
    }

    logInfo('调用sync_msg接口获取具体消息...');

    // 调用企业微信sync_msg接口获取具体消息
    const wechatApi = require('./wechatApi');
    const syncResult = await wechatApi.syncKfMessages({
      token: token,
      open_kfid: openKfId,
      limit: 10 // 获取最近10条消息
    });

    if (!syncResult.success) {
      logError('❌ 调用sync_msg接口失败:', syncResult.message);
      return {
        success: false,
        message: '获取客服消息失败'
      };
    }

    logInfo('sync_msg接口返回消息数量:', syncResult.msg_list?.length || 0);

    // 处理返回的消息列表
    if (syncResult.msg_list && syncResult.msg_list.length > 0) {
      for (const msg of syncResult.msg_list) {
        logInfo('处理消息原始数据:', {
          msgtype: msg.msgtype,
          external_userid: msg.external_userid,
          origin: msg.origin,
          msgid: msg.msgid,
          send_time: msg.send_time,
          完整消息对象: msg
        });

        // 查找用户进入会话事件
        if (msg.msgtype === 'event' && msg.event?.event_type === 'enter_session') {
          logInfo('🎯 找到用户进入会话事件');
          logInfo('事件详情:', {
            msgtype: msg.msgtype,
            external_userid: msg.external_userid,
            event_type: msg.event?.event_type,
            scene_param: msg.event?.scene_param,
            scene: msg.event?.scene,
            open_kfid: msg.event?.open_kfid,
            welcome_code: msg.event?.welcome_code,
            allEventKeys: Object.keys(msg.event || {})
          });

          const sceneParam = msg.event.scene_param;
          const externalUserId = msg.external_userid;

          if (sceneParam) {
            logInfo('🔗 处理客服绑定令牌:', sceneParam.substring(0, 20) + '...');
            // 直接处理绑定，external_userid可能为空但我们仍然尝试绑定
            return await processKfBindingToken(sceneParam, externalUserId);
          } else {
            logInfo('绑定事件缺少scene_param:', {
              hasSceneParam: !!sceneParam,
              hasExternalUserId: !!externalUserId,
              sceneParam: sceneParam ? sceneParam.substring(0, 20) + '...' : 'undefined',
              externalUserId: externalUserId ? externalUserId.substring(0, 20) + '...' : 'undefined'
            });
          }
        }
      }
    }

    logInfo('未找到绑定相关的消息');
    return {
      success: true,
      message: '客服事件处理完成，未发现绑定消息'
    };

  } catch (error) {
    logError('客服事件处理异常:', error);
    return {
      success: false,
      message: '客服事件处理失败: ' + error.message
    };
  }
};

/**
 * 处理客服绑定令牌（专门用于客服场景，external_userid可能为空）
 */
const processKfBindingToken = async (sceneParam, externalUserId = null) => {
  try {
    logInfo('🔗 开始处理客服绑定令牌');
    logInfo('Scene参数:', sceneParam.substring(0, 20) + '...');
    logInfo('External用户ID:', externalUserId ? externalUserId.substring(0, 10) + '...' : 'null');

    // 解密绑定令牌获取用户信息
    const decryptResult = await decryptBindingToken(sceneParam);
    if (!decryptResult.success) {
      logError('❌ 绑定令牌解密失败:', decryptResult.message);
      return {
        success: false,
        message: '绑定令牌无效'
      };
    }

    const { user_uuid, timestamp } = decryptResult.data;
    logInfo('解密成功，用户UUID:', user_uuid.substring(0, 10) + '...');

    // 检查令牌是否过期（10分钟有效期）
    const now = Date.now();
    const tokenAge = now - timestamp;
    const maxAge = 10 * 60 * 1000; // 10分钟

    if (tokenAge > maxAge) {
      logError('❌ 绑定令牌已过期');
      return {
        success: false,
        message: '绑定链接已过期，请重新获取'
      };
    }

    // 检查用户是否已经绑定了其他微信账号
    const existingBinding = await getBindingByUserUuid(user_uuid);
    if (existingBinding) {
      logInfo('⚠️ 用户已有绑定记录，更新绑定信息');
      // 对于客服绑定，如果external_userid为空，我们先保持原有绑定
      if (externalUserId) {
        const updateResult = await db.createOrUpdateBinding(user_uuid, externalUserId);
        if (updateResult.success) {
          logInfo('✅ 用户绑定更新成功');

          // 推送绑定成功通知到用户所有设备
          await pushBindingSuccessToDevices(user_uuid);

          return {
            success: true,
            message: '绑定更新成功'
          };
        } else {
          logError('❌ 用户绑定更新失败:', updateResult.message);
          return {
            success: false,
            message: '绑定更新失败: ' + updateResult.message
          };
        }
      } else {
        // external_userid为空时，保持现有绑定
        logInfo('✅ 客服绑定确认，保持现有绑定');
        return {
          success: true,
          message: '绑定确认成功'
        };
      }
    }

    // 验证用户UUID格式
    if (!user_uuid || typeof user_uuid !== 'string' || user_uuid.trim().length === 0) {
      logError('❌ 无效的用户UUID:', user_uuid);
      return {
        success: false,
        message: '绑定链接已过期或无效，请重新生成绑定链接'
      };
    }

    // 创建新的绑定关系
    // 注意：对于客服绑定，external_userid可能为null，我们使用一个临时值
    const tempExternalUserId = externalUserId || `temp_kf_${user_uuid}_${Date.now()}`;
    const bindingResult = await db.createOrUpdateBinding(user_uuid, tempExternalUserId);
    if (!bindingResult.success) {
      logError('❌ 创建绑定记录失败:', bindingResult.message);

      // 如果是外键约束错误，提供更友好的错误信息
      if (bindingResult.message && bindingResult.message.includes('foreign key constraint')) {
        logError('外键约束失败，用户UUID可能不存在于app_users表中:', user_uuid);
        return {
          success: false,
          message: '绑定链接已过期或无效，请重新生成绑定链接'
        };
      }

      return {
        success: false,
        message: '绑定失败，请稍后重试'
      };
    }

    logInfo('✅ 客服绑定成功');

    // 推送绑定成功通知到用户所有设备
    await pushBindingSuccessToDevices(user_uuid);

    return {
      success: true,
      message: '绑定成功',
      binding_id: bindingResult.binding_id
    };

  } catch (error) {
    logError('客服绑定令牌处理异常:', error);
    return {
      success: false,
      message: '绑定处理失败: ' + error.message
    };
  }
};

/**
 * 处理绑定事件（包含scene_param）
 */
const processBindingEvent = async (externalUserId, message) => {
  try {
    logInfo('🔗 开始处理绑定事件');
    logInfo('外部用户ID:', externalUserId ? externalUserId.substring(0, 10) + '...' : 'undefined');

    // 提取scene_param（绑定令牌）
    const sceneParam = message.scene_param || message.SceneParam;
    if (!sceneParam) {
      logError('❌ 绑定事件中缺少scene_param');
      return {
        success: false,
        message: '绑定事件缺少必要参数'
      };
    }

    logInfo('Scene参数:', sceneParam.substring(0, 20) + '...');

    // 调用绑定令牌处理逻辑
    return await processBindingToken(externalUserId, sceneParam);
  } catch (error) {
    logError('绑定事件处理异常:', error);
    return {
      success: false,
      message: '绑定事件处理失败: ' + error.message
    };
  }
};

/**
 * 处理绑定令牌
 */
const processBindingToken = async (externalUserId, encryptedToken) => {
  try {
    logInfo('🔗 开始处理绑定令牌');
    logInfo('外部用户ID:', externalUserId ? externalUserId.substring(0, 10) + '...' : 'undefined');
    logInfo('加密令牌:', encryptedToken ? encryptedToken.substring(0, 20) + '...' : 'undefined');

    // 解密绑定令牌
    const decryptResult = await decryptBindingToken(encryptedToken);
    if (!decryptResult.success) {
      logError('❌ 绑定令牌解密失败:', decryptResult.message);
      return {
        success: false,
        message: '绑定令牌无效'
      };
    }

    const tokenData = decryptResult.data;
    const userUuid = tokenData.user_uuid;
    const timestamp = tokenData.timestamp;

    logInfo('解密后的令牌数据:', {
      user_uuid: userUuid ? userUuid.substring(0, 10) + '...' : 'undefined',
      timestamp: new Date(timestamp).toISOString()
    });

    // 检查令牌是否过期（24小时有效期）
    const now = Date.now();
    const tokenAge = now - timestamp;
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    if (tokenAge > maxAge) {
      logError('❌ 绑定令牌已过期');
      return {
        success: false,
        message: '绑定令牌已过期，请重新生成'
      };
    }

    // 检查用户是否已经绑定了其他微信账号
    const existingBinding = await getBindingByUserUuid(userUuid);
    if (existingBinding && existingBinding.binding_status === 'active') {
      logInfo('⚠️ 用户已绑定其他微信账号，更新绑定');
      // 更新绑定到新的微信账号
      const updateResult = await db.updateUserBinding(userUuid, externalUserId);
      if (updateResult.success) {
        logInfo('✅ 用户绑定更新成功');
        return {
          success: true,
          message: '绑定更新成功'
        };
      } else {
        return {
          success: false,
          message: '绑定更新失败'
        };
      }
    }

    // 创建新的绑定关系
    const bindingResult = await db.createUserBinding({
      user_uuid: userUuid,
      external_userid: externalUserId,
      binding_status: 'active',
      binding_time: new Date(),
      binding_method: 'token'
    });

    if (bindingResult.success) {
      logInfo('✅ 用户绑定创建成功');

      // 推送绑定成功通知到用户所有设备
      await pushBindingSuccessToDevices(userUuid);

      return {
        success: true,
        message: '绑定成功'
      };
    } else {
      return {
        success: false,
        message: '绑定创建失败'
      };
    }
  } catch (error) {
    logError('处理绑定令牌异常:', error);
    return {
      success: false,
      message: '绑定处理失败: ' + error.message
    };
  }
};

/**
 * 根据用户UUID查询绑定信息
 */
const getBindingByUserUuid = async (userUuid) => {
  try {
    const binding = await db.getBindingByUserUuid(userUuid);
    if (binding) {
      return {
        user_uuid: binding.user_uuid,
        external_userid: binding.external_userid,
        binding_status: binding.binding_status,
        binding_time: binding.binding_time
      };
    }
    return null;
  } catch (error) {
    logError('根据用户UUID查询绑定信息失败:', error);
    return null;
  }
};

/**
 * 推送绑定成功通知到用户所有设备
 */
const pushBindingSuccessToDevices = async (userUuid) => {
  try {
    // 获取用户所有活跃设备
    const devices = await db.query(`
      SELECT device_id, platform, push_token, push_provider
      FROM user_device_bindings
      WHERE user_uuid = ? AND device_status = 'active'
    `, [userUuid]);

    if (devices.length === 0) {
      logInfo('用户没有活跃设备，跳过推送通知');
      return { success: true };
    }

    // 推送绑定成功通知
    const pushService = require('./pushService');
    for (const device of devices) {
      if (device.push_token) {
        await pushService.sendPushNotification(device.push_token, {
          title: '微信绑定成功',
          body: '您的微信账号已成功绑定到公职猫',
          data: {
            type: 'binding_success',
            user_uuid: userUuid
          }
        }, device.platform);
      }
    }

    return { success: true };
  } catch (error) {
    logError('推送绑定成功通知失败:', error);
    return { success: false, message: error.message };
  }
};

/**
 * 保存消息元数据（不存储内容）
 */
const saveMessageMetadata = async (messageData) => {
  try {
    const result = await db.saveMessageMetadata(messageData);
    if (result.success) {
      return {
        success: true,
        message_id: result.message_id,
        media_id_expires_at: messageData.media_id_expires_at
      };
    } else {
      return {
        success: false,
        message: '消息元数据保存失败'
      };
    }
  } catch (error) {
    logError('保存消息元数据异常:', error);
    return {
      success: false,
      message: '消息元数据保存异常: ' + error.message
    };
  }
};

/**
 * 推送下载指令到用户所有设备
 */
const pushDownloadInstructionToDevices = async (userUuid, downloadInstruction) => {
  try {
    const result = await db.pushDownloadInstructionToDevices(userUuid, downloadInstruction);
    if (result.success) {
      return {
        success: true,
        message: '下载指令推送成功'
      };
    } else {
      return {
        success: false,
        message: '下载指令推送失败'
      };
    }
  } catch (error) {
    logError('推送下载指令异常:', error);
    return {
      success: false,
      message: '下载指令推送异常: ' + error.message
    };
  }
};

/**
 * 推送消息到用户所有设备
 */
const pushMessageToDevices = async (userUuid, messageId) => {
  try {
    const result = await db.pushMessageToDevices(userUuid, messageId);
    if (result.success) {
      return {
        success: true,
        message: '消息推送成功'
      };
    } else {
      return {
        success: false,
        message: '消息推送失败'
      };
    }
  } catch (error) {
    logError('推送消息到设备异常:', error);
    return {
      success: false,
      message: '消息推送异常: ' + error.message
    };
  }
};

module.exports = {
  processWeChatMessage,
  processTextMessage,
  processImageMessage,
  processVoiceMessage,
  processVideoMessage,
  processFileMessage,
  processLocationMessage,
  processLinkMessage,
  processEventMessage,
  processUnknownMessage,
  processUserTextMessage,
  detectFileTypeFromName,
  getBindingByExternalUserId,
  decryptBindingToken,
  processKfEvent,
  processKfBindingToken,
  getBindingByUserUuid,
  pushBindingSuccessToDevices,
  saveMessageMetadata,
  pushDownloadInstructionToDevices,
  pushMessageToDevices
};
