/**
 * 消息处理服务
 * 专门处理企业微信客服消息
 */

const { logInfo, logError } = require('../api/errorHandler');
const WebhookService = require('./WebhookService');
const db = require('../data/database');

/**
 * 通用用户绑定检查函数
 * 检查external_userid是否已绑定，如果未绑定则返回错误响应
 */
const validateUserBinding = async (externalUserId, messageType) => {
  if (!externalUserId) {
    logInfo(`${messageType}消息缺少external_userid，忽略处理`);
    return {
      success: true,
      message: `${messageType}消息缺少用户ID，已忽略`,
      binding: null
    };
  }

  // 检查用户是否已绑定
  const binding = await getBindingByExternalUserId(externalUserId);
  if (!binding) {
    logInfo('用户未绑定，忽略客服消息');
    return {
      success: true,
      message: '用户未绑定，客服消息已忽略',
      binding: null
    };
  }

  return {
    success: true,
    binding: binding
  };
};

/**
 * 处理企业微信消息的主入口
 * 简化后只处理客服事件，其他消息类型直接忽略
 */
const processWeChatMessage = async (messageData) => {
  try {
    logInfo('📨 【使用接收】处理企业微信消息');
    
    // 解密消息内容
    const decryptedMessage = WebhookService.decryptMessage(messageData);
    if (!decryptedMessage) {
      return {
        success: false,
        message: '消息解密失败'
      };
    }
    
    logInfo('解密后的消息:', decryptedMessage);
    logInfo('消息字段详情:', {
      FromUserName: decryptedMessage.FromUserName,
      ToUserName: decryptedMessage.ToUserName,
      MsgType: decryptedMessage.MsgType,
      msgtype: decryptedMessage.msgtype,
      Content: decryptedMessage.Content,
      Event: decryptedMessage.Event,
      allKeys: Object.keys(decryptedMessage)
    });
    
    // 只处理客服事件，其他消息类型直接忽略
    const messageType = decryptedMessage.MsgType || decryptedMessage.msgtype;
    const eventType = decryptedMessage.Event;
    
    // 检查是否为客服事件
    if (eventType === 'kf_msg_or_event' && decryptedMessage.Token && decryptedMessage.OpenKfId) {
      logInfo('🔗 检测到企业微信客服事件，开始处理');
      return await processKfEvent(decryptedMessage);
    }
    
    // 非客服事件直接忽略
    logInfo(`忽略非客服消息: ${messageType || eventType}`);
    return {
      success: true,
      message: '非客服消息已忽略'
    };
    
  } catch (error) {
    logError('【使用接收】消息处理异常:', error);
    return {
      success: false,
      message: '消息处理异常: ' + error.message
    };
  }
};

// ==================== 客服消息处理 ====================

/**
 * 处理企业微信客服事件（需要调用sync_msg接口获取具体消息）
 */
const processKfEvent = async (message) => {
  try {
    logInfo('🔗 开始处理企业微信客服事件');

    const token = message.Token;
    const openKfId = message.OpenKfId;

    if (!token || !openKfId) {
      logError('❌ 客服事件中缺少Token或OpenKfId');
      return {
        success: false,
        message: '客服事件缺少必要参数'
      };
    }

    logInfo('调用sync_msg接口获取具体消息...');

    // 调用企业微信sync_msg接口获取具体消息
    const wechatApi = require('./wechatApi');
    const syncResult = await wechatApi.syncKfMessages({
      token: token,
      open_kfid: openKfId,
      limit: 10 // 获取最近10条消息
    });

    if (!syncResult.success) {
      logError('❌ 调用sync_msg接口失败:', syncResult.message);
      return {
        success: false,
        message: '获取客服消息失败'
      };
    }

    logInfo('sync_msg接口返回消息数量:', syncResult.msg_list?.length || 0);

    // 处理返回的消息列表
    if (syncResult.msg_list && syncResult.msg_list.length > 0) {
      // 按消息类型和时间戳排序，确保绑定事件优先处理
      const sortedMessages = syncResult.msg_list.sort((a, b) => {
        // 绑定事件优先处理
        const aIsBindingEvent = a.msgtype === 'event' && a.event?.event_type === 'enter_session';
        const bIsBindingEvent = b.msgtype === 'event' && b.event?.event_type === 'enter_session';
        
        if (aIsBindingEvent && !bIsBindingEvent) return -1;
        if (!aIsBindingEvent && bIsBindingEvent) return 1;
        
        // 如果都是绑定事件或都不是，按时间戳排序
        const aTime = a.send_time || 0;
        const bTime = b.send_time || 0;
        return aTime - bTime;
      });

      logInfo('消息排序结果:', sortedMessages.map(msg => ({
        msgtype: msg.msgtype,
        event_type: msg.event?.event_type,
        send_time: msg.send_time,
        is_binding: msg.msgtype === 'event' && msg.event?.event_type === 'enter_session'
      })));

      for (const msg of sortedMessages) {
        logInfo('处理消息原始数据:', {
          msgtype: msg.msgtype,
          external_userid: msg.external_userid,
          origin: msg.origin,
          msgid: msg.msgid,
          send_time: msg.send_time,
          完整消息对象: msg
        });

        // 处理不同类型的消息
        switch (msg.msgtype) {
          case 'event':
            // 处理事件消息（如enter_session）
            if (msg.event?.event_type === 'enter_session') {
              logInfo('🎯 找到用户进入会话事件');
              logInfo('事件详情:', {
                msgtype: msg.msgtype,
                external_userid: msg.external_userid,
                event_type: msg.event?.event_type,
                scene_param: msg.event?.scene_param,
                scene: msg.event?.scene,
                open_kfid: msg.event?.open_kfid,
                welcome_code: msg.event?.welcome_code,
                allEventKeys: Object.keys(msg.event || {})
              });

              const sceneParam = msg.event.scene_param;
              const externalUserId = msg.external_userid;

              if (sceneParam) {
                logInfo('🔗 处理客服绑定令牌:', sceneParam.substring(0, 20) + '...');
                // 直接处理绑定，external_userid可能为空但我们仍然尝试绑定
                return await processKfBindingToken(sceneParam, externalUserId);
              } else {
                logInfo('绑定事件缺少scene_param:', {
                  hasSceneParam: !!sceneParam,
                  hasExternalUserId: !!externalUserId,
                  sceneParam: sceneParam ? sceneParam.substring(0, 20) + '...' : 'undefined',
                  externalUserId: externalUserId ? externalUserId.substring(0, 20) + '...' : 'undefined'
                });
              }
            }
            break;

          case 'text':
            // 处理文本消息
            logInfo('📝 处理客服文本消息');
            return await processKfTextMessage(msg);

          case 'image':
            // 处理图片消息
            logInfo('🖼️ 处理客服图片消息');
            return await processKfImageMessage(msg);

          case 'voice':
            // 处理语音消息
            logInfo('🎵 处理客服语音消息');
            return await processKfVoiceMessage(msg);

          case 'video':
            // 处理视频消息
            logInfo('🎬 处理客服视频消息');
            return await processKfVideoMessage(msg);

          case 'file':
            // 处理文件消息
            logInfo('📁 处理客服文件消息');
            return await processKfFileMessage(msg);

          case 'location':
            // 处理位置消息
            logInfo('📍 处理客服位置消息');
            return await processKfLocationMessage(msg);

          case 'link':
            // 处理链接消息
            logInfo('🔗 处理客服链接消息');
            return await processKfLinkMessage(msg);

          default:
            logInfo(`❓ 未处理的客服消息类型: ${msg.msgtype}`);
            break;
        }
      }
    }

    logInfo('未找到需要处理的消息');
    return {
      success: true,
      message: '客服事件处理完成，未发现需要处理的消息'
    };

  } catch (error) {
    logError('客服事件处理异常:', error);
    return {
      success: false,
      message: '客服事件处理失败: ' + error.message
    };
  }
};

// ==================== 客服消息类型处理函数 ====================

/**
 * 处理客服文本消息
 */
const processKfTextMessage = async (msg) => {
  try {
    logInfo('📝 处理客服文本消息:', msg.text?.content);
    
    const externalUserId = msg.external_userid;
    
    // 使用通用绑定检查函数
    const bindingCheck = await validateUserBinding(externalUserId, '客服文本');
    if (!bindingCheck.binding) {
      return {
        success: bindingCheck.success,
        message: bindingCheck.message
      };
    }

    const binding = bindingCheck.binding;

    // 记录消息到数据库
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msg.msgid,
      message_type: 'text',
      content: msg.text?.content || '',
      metadata: {
        timestamp: msg.send_time,
        from_user: externalUserId,
        to_user: 'kf_system',
        origin: msg.origin
      }
    });

    if (messageRecord.success) {
      // 推送到用户所有设备
      await pushMessageToDevices(binding.user_uuid, messageRecord.message_id);
      return {
        success: true,
        message: '客服文本消息已处理并推送到设备'
      };
    } else {
      return {
        success: false,
        message: '客服文本消息记录失败'
      };
    }
  } catch (error) {
    logError('处理客服文本消息异常:', error);
    return {
      success: false,
      message: '客服文本消息处理失败'
    };
  }
};

/**
 * 处理客服图片消息
 */
const processKfImageMessage = async (msg) => {
  try {
    logInfo('🖼️ 处理客服图片消息');
    
    const externalUserId = msg.external_userid;
    
    // 使用通用绑定检查函数
    const bindingCheck = await validateUserBinding(externalUserId, '客服图片');
    if (!bindingCheck.binding) {
      return {
        success: bindingCheck.success,
        message: bindingCheck.message
      };
    }

    const binding = bindingCheck.binding;

    // 记录消息到数据库
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msg.msgid,
      message_type: 'image',
      media_id: msg.image?.media_id,
      media_id_expires_at: new Date(Date.now() + 72 * 60 * 60 * 1000), // 72小时后过期
      file_extension: 'jpg',
      mime_type: 'image/jpeg',
      metadata: {
        timestamp: msg.send_time,
        from_user: externalUserId,
        to_user: 'kf_system',
        origin: msg.origin
      }
    });

    if (messageRecord.success) {
      // 推送下载指令到用户所有设备
      await pushDownloadInstructionToDevices(binding.user_uuid, {
        message_id: messageRecord.message_id,
        message_type: 'image',
        media_id: msg.image?.media_id,
        expires_at: messageRecord.media_id_expires_at
      });

      return {
        success: true,
        message: '客服图片消息已处理并推送下载指令到设备'
      };
    } else {
      return {
        success: false,
        message: '客服图片消息记录失败'
      };
    }
  } catch (error) {
    logError('处理客服图片消息异常:', error);
    return {
      success: false,
      message: '客服图片消息处理失败'
    };
  }
};

/**
 * 处理客服语音消息
 */
const processKfVoiceMessage = async (msg) => {
  try {
    logInfo('🎵 处理客服语音消息');
    
    const externalUserId = msg.external_userid;
    
    // 使用通用绑定检查函数
    const bindingCheck = await validateUserBinding(externalUserId, '客服语音');
    if (!bindingCheck.binding) {
      return {
        success: bindingCheck.success,
        message: bindingCheck.message
      };
    }

    const binding = bindingCheck.binding;

    // 记录消息到数据库
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msg.msgid,
      message_type: 'voice',
      media_id: msg.voice?.media_id,
      media_id_expires_at: new Date(Date.now() + 72 * 60 * 60 * 1000), // 72小时后过期
      metadata: {
        timestamp: msg.send_time,
        from_user: externalUserId,
        to_user: 'kf_system',
        origin: msg.origin,
        format: msg.voice?.format,
        recognition: msg.voice?.recognition
      }
    });

    if (messageRecord.success) {
      // 推送下载指令到用户所有设备
      await pushDownloadInstructionToDevices(binding.user_uuid, {
        message_id: messageRecord.message_id,
        message_type: 'voice',
        media_id: msg.voice?.media_id,
        expires_at: messageRecord.media_id_expires_at,
        format: msg.voice?.format
      });

      return {
        success: true,
        message: '客服语音消息已处理并推送下载指令到设备'
      };
    } else {
      return {
        success: false,
        message: '客服语音消息记录失败'
      };
    }
  } catch (error) {
    logError('处理客服语音消息异常:', error);
    return {
      success: false,
      message: '客服语音消息处理失败'
    };
  }
};

/**
 * 处理客服视频消息
 */
const processKfVideoMessage = async (msg) => {
  try {
    logInfo('🎬 处理客服视频消息');
    
    const externalUserId = msg.external_userid;
    
    // 使用通用绑定检查函数
    const bindingCheck = await validateUserBinding(externalUserId, '客服视频');
    if (!bindingCheck.binding) {
      return {
        success: bindingCheck.success,
        message: bindingCheck.message
      };
    }

    const binding = bindingCheck.binding;

    // 记录消息到数据库
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msg.msgid,
      message_type: 'video',
      media_id: msg.video?.media_id,
      media_id_expires_at: new Date(Date.now() + 72 * 60 * 60 * 1000), // 72小时后过期
      metadata: {
        timestamp: msg.send_time,
        from_user: externalUserId,
        to_user: 'kf_system',
        origin: msg.origin,
        thumb_media_id: msg.video?.thumb_media_id
      }
    });

    if (messageRecord.success) {
      // 推送下载指令到用户所有设备
      await pushDownloadInstructionToDevices(binding.user_uuid, {
        message_id: messageRecord.message_id,
        message_type: 'video',
        media_id: msg.video?.media_id,
        expires_at: messageRecord.media_id_expires_at,
        thumb_media_id: msg.video?.thumb_media_id
      });

      return {
        success: true,
        message: '客服视频消息已处理并推送下载指令到设备'
      };
    } else {
      return {
        success: false,
        message: '客服视频消息记录失败'
      };
    }
  } catch (error) {
    logError('处理客服视频消息异常:', error);
    return {
      success: false,
      message: '客服视频消息处理失败'
    };
  }
};

/**
 * 处理客服文件消息
 */
const processKfFileMessage = async (msg) => {
  try {
    logInfo('📁 处理客服文件消息');
    
    const externalUserId = msg.external_userid;
    
    // 使用通用绑定检查函数
    const bindingCheck = await validateUserBinding(externalUserId, '客服文件');
    if (!bindingCheck.binding) {
      return {
        success: bindingCheck.success,
        message: bindingCheck.message
      };
    }

    const binding = bindingCheck.binding;

    // 预检测文件类型
    const fileTypeInfo = await detectFileTypeFromName(msg.file?.name || '');

    // 记录消息到数据库
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msg.msgid,
      message_type: fileTypeInfo.category,
      media_id: msg.file?.media_id,
      media_id_expires_at: new Date(Date.now() + 72 * 60 * 60 * 1000), // 72小时后过期
      file_extension: fileTypeInfo.extension,
      mime_type: fileTypeInfo.mimeType,
      metadata: {
        timestamp: msg.send_time,
        from_user: externalUserId,
        to_user: 'kf_system',
        origin: msg.origin,
        file_name: msg.file?.name,
        file_size: msg.file?.size,
        detected_type: fileTypeInfo.category,
        file_extension: fileTypeInfo.extension
      }
    });

    if (messageRecord.success) {
      // 推送下载指令到用户所有设备
      await pushDownloadInstructionToDevices(binding.user_uuid, {
        message_id: messageRecord.message_id,
        message_type: fileTypeInfo.category,
        media_id: msg.file?.media_id,
        expires_at: messageRecord.media_id_expires_at,
        file_name: msg.file?.name,
        file_size: msg.file?.size,
        file_extension: fileTypeInfo.extension,
        mime_type: fileTypeInfo.mimeType,
        detected_type: fileTypeInfo.category
      });

      return {
        success: true,
        message: '客服文件消息已处理并推送下载指令到设备'
      };
    } else {
      return {
        success: false,
        message: '客服文件消息记录失败'
      };
    }
  } catch (error) {
    logError('处理客服文件消息异常:', error);
    return {
      success: false,
      message: '客服文件消息处理失败'
    };
  }
};

/**
 * 处理客服位置消息
 */
const processKfLocationMessage = async (msg) => {
  try {
    logInfo('📍 处理客服位置消息');
    
    const externalUserId = msg.external_userid;
    
    // 使用通用绑定检查函数
    const bindingCheck = await validateUserBinding(externalUserId, '客服位置');
    if (!bindingCheck.binding) {
      return {
        success: bindingCheck.success,
        message: bindingCheck.message
      };
    }

    const binding = bindingCheck.binding;

    // 提取位置信息
    const locationX = msg.location?.latitude;
    const locationY = msg.location?.longitude;
    const scale = msg.location?.scale;
    const label = msg.location?.name;

    // 记录消息到数据库
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msg.msgid,
      message_type: 'location',
      content: label || '位置信息',
      metadata: {
        timestamp: msg.send_time,
        from_user: externalUserId,
        to_user: 'kf_system',
        origin: msg.origin,
        location_x: locationX,
        location_y: locationY,
        scale: scale,
        label: label
      }
    });

    if (messageRecord.success) {
      // 推送到用户所有设备
      await pushMessageToDevices(binding.user_uuid, messageRecord.message_id);

      return {
        success: true,
        message: '客服位置消息已处理并推送到设备'
      };
    } else {
      return {
        success: false,
        message: '客服位置消息记录失败'
      };
    }
  } catch (error) {
    logError('处理客服位置消息异常:', error);
    return {
      success: false,
      message: '客服位置消息处理失败'
    };
  }
};

/**
 * 处理客服链接消息
 */
const processKfLinkMessage = async (msg) => {
  try {
    logInfo('🔗 处理客服链接消息');
    
    const externalUserId = msg.external_userid;
    
    // 使用通用绑定检查函数
    const bindingCheck = await validateUserBinding(externalUserId, '客服链接');
    if (!bindingCheck.binding) {
      return {
        success: bindingCheck.success,
        message: bindingCheck.message
      };
    }

    const binding = bindingCheck.binding;

    // 提取链接信息
    const title = msg.link?.title;
    const description = msg.link?.description;
    const url = msg.link?.url;
    const picUrl = msg.link?.pic_url;

    // 记录消息到数据库
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: msg.msgid,
      message_type: 'link',
      content: url,
      metadata: {
        timestamp: msg.send_time,
        from_user: externalUserId,
        to_user: 'kf_system',
        origin: msg.origin,
        title: title,
        description: description,
        url: url,
        pic_url: picUrl
      }
    });

    if (messageRecord.success) {
      // 推送到用户所有设备
      await pushMessageToDevices(binding.user_uuid, messageRecord.message_id);

      return {
        success: true,
        message: '客服链接消息已处理并推送到设备'
      };
    } else {
      return {
        success: false,
        message: '客服链接消息记录失败'
      };
    }
  } catch (error) {
    logError('处理客服链接消息异常:', error);
    return {
      success: false,
      message: '客服链接消息处理失败'
    };
  }
};

/**
 * 处理客服绑定令牌（专门用于客服场景，external_userid可能为空）
 */
const processKfBindingToken = async (sceneParam, externalUserId = null) => {
  try {
    logInfo('🔗 开始处理客服绑定令牌');
    logInfo('Scene参数:', sceneParam.substring(0, 20) + '...');
    logInfo('External用户ID:', externalUserId ? externalUserId.substring(0, 10) + '...' : 'null');

    // 使用wechatApi中的解密函数
    const wechatApi = require('./wechatApi');
    const decryptResult = await wechatApi.decryptBindingToken(sceneParam);
    if (!decryptResult.success) {
      logError('❌ 绑定令牌解密失败:', decryptResult.message);
      return {
        success: false,
        message: '绑定令牌无效'
      };
    }

    const { user_uuid, timestamp } = decryptResult.data;
    logInfo('解密成功，用户UUID:', user_uuid.substring(0, 10) + '...');

    // 检查令牌是否过期（10分钟有效期）
    const now = Date.now();
    const tokenAge = now - timestamp;
    const maxAge = 10 * 60 * 1000; // 10分钟

    if (tokenAge > maxAge) {
      logError('❌ 绑定令牌已过期');
      return {
        success: false,
        message: '绑定链接已过期，请重新获取'
      };
    }

    // 检查用户是否已经绑定了其他微信账号
    const existingBinding = await getBindingByUserUuid(user_uuid);
    if (existingBinding && existingBinding.binding_status === 'active') {
      logInfo('⚠️ 用户已绑定其他微信账号，更新绑定');
      // 对于客服绑定，如果external_userid为空，我们先保持原有绑定
      if (externalUserId) {
        const updateResult = await db.updateUserBinding(user_uuid, externalUserId);
        if (updateResult.success) {
          logInfo('✅ 用户绑定更新成功');
          return {
            success: true,
            message: '绑定更新成功'
          };
        } else {
          return {
            success: false,
            message: '绑定更新失败'
          };
        }
      } else {
        // external_userid为空时，保持现有绑定
        logInfo('✅ 客服绑定确认，保持现有绑定');
        return {
          success: true,
          message: '绑定确认成功'
        };
      }
    }

    // 验证用户UUID格式
    if (!user_uuid || typeof user_uuid !== 'string' || user_uuid.trim().length === 0) {
      logError('❌ 无效的用户UUID:', user_uuid);
      return {
        success: false,
        message: '绑定链接已过期或无效，请重新生成绑定链接'
      };
    }

    // 创建新的绑定关系
    // 注意：对于客服绑定，external_userid可能为null，我们使用一个临时值
    const tempExternalUserId = externalUserId || `temp_kf_${user_uuid}_${Date.now()}`;
    const bindingResult = await db.createOrUpdateBinding(user_uuid, tempExternalUserId);
    if (!bindingResult.success) {
      logError('❌ 创建绑定记录失败:', bindingResult.message);

      // 如果是外键约束错误，提供更友好的错误信息
      if (bindingResult.message && bindingResult.message.includes('foreign key constraint')) {
        logError('外键约束失败，用户UUID可能不存在于app_users表中:', user_uuid);
        return {
          success: false,
          message: '绑定链接已过期或无效，请重新生成绑定链接'
        };
      }

      return {
        success: false,
        message: '绑定失败，请稍后重试'
      };
    }

    logInfo('✅ 客服绑定成功');

    // 推送绑定成功通知到用户所有设备
    await pushBindingSuccessToDevices(user_uuid);

    return {
      success: true,
      message: '绑定成功',
      binding_id: bindingResult.binding_id
    };

  } catch (error) {
    logError('客服绑定令牌处理异常:', error);
    return {
      success: false,
      message: '绑定处理失败: ' + error.message
    };
  }
};

// ==================== 辅助函数 ====================

/**
 * 根据external_userid查询绑定信息
 */
const getBindingByExternalUserId = async (externalUserId) => {
  try {
    const binding = await db.getBindingByExternalUserId(externalUserId);
    if (binding) {
      return {
        user_uuid: binding.user_uuid,
        external_userid: binding.external_userid,
        binding_status: binding.binding_status,
        binding_time: binding.binding_time
      };
    }
    return null;
  } catch (error) {
    logError('根据external_userid查询绑定信息失败:', error);
    return null;
  }
};

/**
 * 根据用户UUID查询绑定信息
 */
const getBindingByUserUuid = async (userUuid) => {
  try {
    const binding = await db.getBindingByUserUuid(userUuid);
    if (binding) {
      return {
        user_uuid: binding.user_uuid,
        external_userid: binding.external_userid,
        binding_status: binding.binding_status,
        binding_time: binding.binding_time
      };
    }
    return null;
  } catch (error) {
    logError('根据用户UUID查询绑定信息失败:', error);
    return null;
  }
};

/**
 * 推送绑定成功通知到用户所有设备
 */
const pushBindingSuccessToDevices = async (userUuid) => {
  try {
    // 获取用户所有活跃设备
    const devices = await db.query(`
      SELECT device_id, platform, push_token, push_provider
      FROM user_device_bindings
      WHERE user_uuid = ? AND device_status = 'active'
    `, [userUuid]);

    if (devices.length === 0) {
      logInfo('用户没有活跃设备，跳过推送通知');
      return { success: true };
    }

    // 推送绑定成功通知
    const pushService = require('./pushService');
    for (const device of devices) {
      if (device.push_token) {
        await pushService.sendPushNotification(device.push_token, {
          title: '微信绑定成功',
          body: '您的微信账号已成功绑定到公职猫',
          data: {
            type: 'binding_success',
            user_uuid: userUuid
          }
        }, device.platform);
      }
    }

    return { success: true };
  } catch (error) {
    logError('推送绑定成功通知失败:', error);
    return { success: false, message: error.message };
  }
};

/**
 * 保存消息元数据（不存储内容）
 */
const saveMessageMetadata = async (messageData) => {
  try {
    const result = await db.saveMessageMetadata(messageData);
    if (result.success) {
      return {
        success: true,
        message_id: result.message_id,
        media_id_expires_at: messageData.media_id_expires_at
      };
    } else {
      return {
        success: false,
        message: '消息元数据保存失败'
      };
    }
  } catch (error) {
    logError('保存消息元数据异常:', error);
    return {
      success: false,
      message: '消息元数据保存异常: ' + error.message
    };
  }
};

/**
 * 推送下载指令到用户所有设备
 */
const pushDownloadInstructionToDevices = async (userUuid, downloadInstruction) => {
  try {
    const result = await db.pushDownloadInstructionToDevices(userUuid, downloadInstruction);
    if (result.success) {
      return {
        success: true,
        message: '下载指令推送成功'
      };
    } else {
      return {
        success: false,
        message: '下载指令推送失败'
      };
    }
  } catch (error) {
    logError('推送下载指令异常:', error);
    return {
      success: false,
      message: '下载指令推送异常: ' + error.message
    };
  }
};

/**
 * 推送消息到用户所有设备
 */
const pushMessageToDevices = async (userUuid, messageId) => {
  try {
    const result = await db.pushMessageToDevices(userUuid, messageId);
    if (result.success) {
      return {
        success: true,
        message: '消息推送成功'
      };
    } else {
      return {
        success: false,
        message: '消息推送失败'
      };
    }
  } catch (error) {
    logError('推送消息到设备异常:', error);
    return {
      success: false,
      message: '消息推送异常: ' + error.message
    };
  }
};

// 引入统一文件类型配置
const { detectFileTypeFromName: detectFileTypeFromNameUnified } = require('../config/fileTypes');

/**
 * 根据文件名检测文件类型
 */
const detectFileTypeFromName = async (fileName) => {
  try {
    const typeInfo = detectFileTypeFromNameUnified(fileName);
    logInfo(`文件类型检测成功: ${fileName} -> ${typeInfo.category} (${typeInfo.extension})`);
    return {
      category: typeInfo.category,
      extension: typeInfo.extension,
      mimeType: typeInfo.mimeType
    };
  } catch (error) {
    logError('文件类型检测失败:', error);
    return {
      category: 'file',
      extension: 'bin',
      mimeType: 'application/octet-stream'
    };
  }
};

module.exports = {
  processWeChatMessage,
  processKfEvent,
  processKfBindingToken,
  validateUserBinding,
  getBindingByExternalUserId,
  getBindingByUserUuid,
  pushBindingSuccessToDevices,
  saveMessageMetadata,
  pushDownloadInstructionToDevices,
  pushMessageToDevices,
  detectFileTypeFromName
};
