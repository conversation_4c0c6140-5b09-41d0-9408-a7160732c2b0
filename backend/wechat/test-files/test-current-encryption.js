/**
 * 测试当前的加密逻辑
 * 验证scene_param的生成是否正确
 */

require('dotenv').config();
const crypto = require('crypto');

console.log('🔍 测试当前加密逻辑...\n');

// 使用实际的参数
const testUserUuid = '729933f6-275a-4d58-8e4a-65e42edae756';
const bindingSecret = process.env.WECHAT_BINDING_SECRET || 'gongzhimall_binding_secret_2025';

console.log('=== 测试参数 ===');
console.log('用户UUID:', testUserUuid);
console.log('绑定密钥:', bindingSecret);
console.log('');

// 模拟当前的加密过程（与wechatApi.js完全相同）
function testCurrentEncryption() {
  console.log('=== 当前加密过程 ===');
  
  // 创建绑定数据
  const bindingData = {
    user_uuid: testUserUuid,
    timestamp: new Date().toISOString(),
    expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
  };
  
  console.log('1. 绑定数据:', bindingData);
  
  const bindingDataJson = JSON.stringify(bindingData);
  console.log('2. JSON序列化长度:', bindingDataJson.length);
  
  // 使用AES加密
  const algorithm = 'aes-256-cbc';
  const key = crypto.scryptSync(bindingSecret, 'salt', 32);
  const iv = crypto.randomBytes(16);
  
  console.log('3. 加密参数:');
  console.log('   - 算法:', algorithm);
  console.log('   - Key长度:', key.length);
  console.log('   - IV长度:', iv.length);
  console.log('   - IV (base64):', iv.toString('base64'));
  
  const cipher = crypto.createCipheriv(algorithm, key, iv);
  let encryptedData = cipher.update(bindingDataJson, 'utf8', 'base64');
  encryptedData += cipher.final('base64');
  
  console.log('4. 加密结果:');
  console.log('   - 加密数据长度:', encryptedData.length);
  console.log('   - 加密数据前缀:', encryptedData.substring(0, 30) + '...');
  
  // 组合IV和加密数据
  const combined = iv.toString('base64') + ':' + encryptedData;
  console.log('5. 组合结果:');
  console.log('   - Combined长度:', combined.length);
  console.log('   - Combined包含冒号:', combined.includes(':'));
  console.log('   - Combined前缀:', combined.substring(0, 50) + '...');
  
  // URL编码
  const sceneParam = encodeURIComponent(combined);
  console.log('6. URL编码结果:');
  console.log('   - URL编码长度:', sceneParam.length);
  console.log('   - URL编码前后是否相同:', combined === sceneParam);
  console.log('   - URL编码前缀:', sceneParam.substring(0, 50) + '...');
  
  return {
    combined,
    sceneParam,
    bindingData
  };
}

// 测试解密过程
function testCurrentDecryption(sceneParam) {
  console.log('\n=== 当前解密过程 ===');
  
  try {
    // URL解码
    const decodedToken = decodeURIComponent(sceneParam);
    console.log('1. URL解码:');
    console.log('   - 解码后长度:', decodedToken.length);
    console.log('   - 解码后包含冒号:', decodedToken.includes(':'));
    console.log('   - 解码后前缀:', decodedToken.substring(0, 50) + '...');
    
    // 分割
    const parts = decodedToken.split(':');
    console.log('2. 分割结果:');
    console.log('   - Parts数量:', parts.length);
    if (parts.length === 2) {
      console.log('   - IV部分长度:', parts[0].length);
      console.log('   - 加密数据部分长度:', parts[1].length);
    } else {
      console.log('   - Parts内容:', parts);
      return false;
    }
    
    // 解密
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(bindingSecret, 'salt', 32);
    const iv = Buffer.from(parts[0], 'base64');
    
    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    let decryptedData = decipher.update(parts[1], 'base64', 'utf8');
    decryptedData += decipher.final('utf8');
    
    console.log('3. 解密成功:');
    console.log('   - 解密数据长度:', decryptedData.length);
    
    const parsedData = JSON.parse(decryptedData);
    console.log('4. 解析结果:', parsedData);
    
    return true;
  } catch (error) {
    console.log('❌ 解密失败:', error.message);
    return false;
  }
}

// 对比实际接收到的scene_param
function compareWithActual() {
  console.log('\n=== 对比实际接收的scene_param ===');
  
  const actualSceneParam = 'WdHPjPWDgv7PIxrteCYny44+opvZtSlfYUsXBKZYIvP37PhdqrRh3Ed0eZxGw7HI';
  console.log('实际接收的scene_param:', actualSceneParam);
  console.log('长度:', actualSceneParam.length);
  console.log('包含冒号:', actualSceneParam.includes(':'));
  
  // 尝试base64解码
  try {
    const base64Decoded = Buffer.from(actualSceneParam, 'base64').toString('utf8');
    console.log('Base64解码结果:', base64Decoded.substring(0, 100) + '...');
    console.log('Base64解码后包含冒号:', base64Decoded.includes(':'));
    
    if (base64Decoded.includes(':')) {
      console.log('✅ 实际scene_param是base64编码的完整数据');
      return testCurrentDecryption(base64Decoded);
    }
  } catch (error) {
    console.log('Base64解码失败:', error.message);
  }
  
  return false;
}

// 运行测试
console.log('开始测试当前加密逻辑...\n');

const encryptResult = testCurrentEncryption();
const decryptSuccess = testCurrentDecryption(encryptResult.sceneParam);

console.log('\n=== 测试结果 ===');
console.log('加密过程:', '✅ 成功');
console.log('解密过程:', decryptSuccess ? '✅ 成功' : '❌ 失败');

const actualTestResult = compareWithActual();
console.log('实际数据测试:', actualTestResult ? '✅ 成功' : '❌ 失败');

if (!actualTestResult) {
  console.log('\n=== 问题分析 ===');
  console.log('1. 实际接收的scene_param没有冒号分隔符');
  console.log('2. 可能的原因:');
  console.log('   - 企业微信在传递scene_param时进行了base64编码');
  console.log('   - 或者加密时使用了不同的格式');
  console.log('   - 或者URL传输过程中被截断');
}
