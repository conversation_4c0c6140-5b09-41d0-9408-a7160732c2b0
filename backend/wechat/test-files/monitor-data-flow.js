/**
 * 监测微信绑定数据流
 * 1. 监测云服务器返回的绑定链接和scene_param
 * 2. 监测Webhook接收到的scene_param
 * 3. 监测external_userid的值
 */

require('dotenv').config();
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

console.log('🔍 开始监测微信绑定数据流...\n');

// 1. 监测绑定链接生成
async function monitorBindingLinkGeneration() {
  console.log('=== 1. 监测绑定链接生成 ===');
  
  try {
    // 查看最近的绑定链接生成日志
    const command = `ssh <EMAIL> "mariadb -u wechat_user -p'wechat@gongzhimall123' -S /var/lib/mysql/mysql.sock gongzhimall_wechat -e \\"SELECT * FROM system_logs WHERE function_name LIKE '%bind%' AND message LIKE '%生成%' OR message LIKE '%链接%' OR message LIKE '%scene_param%' ORDER BY created_at DESC LIMIT 10;\\"" | tail -20`;
    
    const { stdout } = await execAsync(command);
    console.log('绑定链接生成日志:');
    console.log(stdout);
    
    return stdout;
  } catch (error) {
    console.log('❌ 监测绑定链接生成失败:', error.message);
    return null;
  }
}

// 2. 监测Webhook接收数据
async function monitorWebhookData() {
  console.log('\n=== 2. 监测Webhook接收数据 ===');
  
  try {
    // 查看最近的Webhook数据
    const command = `ssh <EMAIL> "mariadb -u wechat_user -p'wechat@gongzhimall123' -S /var/lib/mysql/mysql.sock gongzhimall_wechat -e \\"SELECT * FROM system_logs WHERE (message LIKE '%scene_param%' OR message LIKE '%external_userid%' OR message LIKE '%enter_session%') AND created_at >= '2025-07-27 13:04:00' ORDER BY created_at DESC LIMIT 15;\\"" | tail -30`;
    
    const { stdout } = await execAsync(command);
    console.log('Webhook接收数据:');
    console.log(stdout);
    
    return stdout;
  } catch (error) {
    console.log('❌ 监测Webhook数据失败:', error.message);
    return null;
  }
}

// 3. 提取和对比scene_param
function extractSceneParams(bindingLog, webhookLog) {
  console.log('\n=== 3. 提取和对比scene_param ===');
  
  // 从绑定日志中提取scene_param
  let sentSceneParam = null;
  if (bindingLog) {
    const sceneParamMatch = bindingLog.match(/scene_param[=:]([^&\s]+)/);
    if (sceneParamMatch) {
      sentSceneParam = sceneParamMatch[1];
    }
  }
  
  // 从Webhook日志中提取scene_param
  let receivedSceneParam = null;
  if (webhookLog) {
    const receivedMatch = webhookLog.match(/WdHPjPWDgv7PIxrteCYny44\+opvZtSlfYUsXBKZYIvP37PhdqrRh3Ed0eZxGw7HI/);
    if (receivedMatch) {
      receivedSceneParam = receivedMatch[0];
    }
  }
  
  console.log('发送的scene_param:', sentSceneParam || '未找到');
  console.log('接收的scene_param:', receivedSceneParam || '未找到');
  console.log('scene_param是否一致:', sentSceneParam === receivedSceneParam ? '✅ 一致' : '❌ 不一致');
  
  return { sentSceneParam, receivedSceneParam };
}

// 4. 分析external_userid问题
async function analyzeExternalUserIdIssue() {
  console.log('\n=== 4. 分析external_userid问题 ===');
  
  try {
    // 查看企业微信API相关日志
    const command = `ssh <EMAIL> "mariadb -u wechat_user -p'wechat@gongzhimall123' -S /var/lib/mysql/mysql.sock gongzhimall_wechat -e \\"SELECT * FROM system_logs WHERE message LIKE '%external_userid%' OR message LIKE '%企业微信%' OR message LIKE '%客服%' ORDER BY created_at DESC LIMIT 10;\\"" | tail -20`;
    
    const { stdout } = await execAsync(command);
    console.log('external_userid相关日志:');
    console.log(stdout);
    
    // 检查是否有external_userid不为null的情况
    const hasValidExternalUserId = stdout.includes('external_userid') && !stdout.includes('null');
    console.log('是否有有效的external_userid:', hasValidExternalUserId ? '✅ 有' : '❌ 全部为null');
    
    return stdout;
  } catch (error) {
    console.log('❌ 分析external_userid失败:', error.message);
    return null;
  }
}

// 5. 检查企业微信配置
async function checkWeChatConfig() {
  console.log('\n=== 5. 检查企业微信配置 ===');
  
  const corpId = process.env.WECHAT_CORP_ID;
  const openKfId = process.env.WECHAT_DEFAULT_OPEN_KFID;
  
  console.log('企业微信配置:');
  console.log('CORP_ID:', corpId);
  console.log('OPEN_KFID:', openKfId);
  
  // 检查客服配置是否正确
  if (!openKfId || openKfId.length < 10) {
    console.log('❌ 客服ID配置可能有问题');
    return false;
  }
  
  console.log('✅ 基础配置看起来正常');
  return true;
}

// 6. 实时监测新的绑定尝试
async function monitorNewBindingAttempt() {
  console.log('\n=== 6. 实时监测新的绑定尝试 ===');
  console.log('请在APP中重新生成绑定链接并跳转到微信...');
  
  let lastLogId = 0;
  
  // 获取当前最新的日志ID
  try {
    const { stdout } = await execAsync(`ssh <EMAIL> "mariadb -u wechat_user -p'wechat@gongzhimall123' -S /var/lib/mysql/mysql.sock gongzhimall_wechat -e 'SELECT MAX(id) as max_id FROM system_logs;'"`);
    const maxIdMatch = stdout.match(/(\d+)/);
    if (maxIdMatch) {
      lastLogId = parseInt(maxIdMatch[1]);
      console.log('当前最新日志ID:', lastLogId);
    }
  } catch (error) {
    console.log('获取最新日志ID失败:', error.message);
  }
  
  return new Promise((resolve) => {
    const checkInterval = setInterval(async () => {
      try {
        // 检查是否有新的日志
        const { stdout } = await execAsync(`ssh <EMAIL> "mariadb -u wechat_user -p'wechat@gongzhimall123' -S /var/lib/mysql/mysql.sock gongzhimall_wechat -e 'SELECT * FROM system_logs WHERE id > ${lastLogId} AND (message LIKE \"%绑定%\" OR message LIKE \"%bind%\" OR message LIKE \"%webhook%\" OR message LIKE \"%scene_param%\") ORDER BY id ASC;'"`);
        
        if (stdout.trim() && !stdout.includes('Empty set')) {
          console.log('\n🔔 检测到新的绑定相关日志:');
          console.log(stdout);
          
          // 更新lastLogId
          const lines = stdout.split('\n').filter(line => line.trim());
          if (lines.length > 0) {
            const lastLine = lines[lines.length - 1];
            const idMatch = lastLine.match(/^(\d+)/);
            if (idMatch) {
              lastLogId = parseInt(idMatch[1]);
            }
          }
          
          // 如果检测到Webhook调用，解析详细数据
          if (stdout.includes('webhook') || stdout.includes('scene_param')) {
            console.log('\n🎯 检测到Webhook调用，开始详细分析...');
            clearInterval(checkInterval);
            resolve(stdout);
          }
        }
      } catch (error) {
        console.log('监测过程中出错:', error.message);
      }
    }, 3000); // 每3秒检查一次
    
    // 30秒后超时
    setTimeout(() => {
      clearInterval(checkInterval);
      console.log('\n⏰ 监测超时，请手动触发绑定操作');
      resolve(null);
    }, 30000);
  });
}

// 主函数
async function main() {
  try {
    console.log('开始完整的数据流监测...\n');
    
    // 1. 检查企业微信配置
    await checkWeChatConfig();
    
    // 2. 监测历史数据
    const bindingLog = await monitorBindingLinkGeneration();
    const webhookLog = await monitorWebhookData();
    
    // 3. 对比scene_param
    const { sentSceneParam, receivedSceneParam } = extractSceneParams(bindingLog, webhookLog);
    
    // 4. 分析external_userid问题
    await analyzeExternalUserIdIssue();
    
    // 5. 实时监测新的绑定尝试
    console.log('\n' + '='.repeat(60));
    console.log('准备实时监测新的绑定尝试...');
    const newAttemptLog = await monitorNewBindingAttempt();
    
    if (newAttemptLog) {
      console.log('\n=== 新绑定尝试分析结果 ===');
      console.log('检测到新的绑定数据，请查看上方日志进行分析');
    }
    
    console.log('\n=== 监测总结 ===');
    console.log('1. 企业微信配置: ✅ 正常');
    console.log('2. scene_param传递:', sentSceneParam && receivedSceneParam ? '✅ 有数据' : '❌ 缺少数据');
    console.log('3. external_userid问题: 需要进一步调查');
    console.log('4. 建议检查企业微信应用权限和客服配置');
    
  } catch (error) {
    console.error('监测过程中发生错误:', error);
  }
}

main();
