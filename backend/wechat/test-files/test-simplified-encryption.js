/**
 * 测试简化的加密格式
 * 验证新的加密/解密逻辑
 */

require('dotenv').config();
const crypto = require('crypto');

console.log('🔍 测试简化加密格式...\n');

const testUserUuid = '729933f6-275a-4d58-8e4a-65e42edae756';
const bindingSecret = process.env.WECHAT_BINDING_SECRET || 'gongzhimall_binding_secret_2025';

// 新的简化加密
function simplifiedEncryption(userUuid, secret) {
  const bindingData = {
    user_uuid: userUuid,
    timestamp: new Date().toISOString(),
    expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
  };
  
  const bindingDataJson = JSON.stringify(bindingData);
  
  // 使用简化的加密方式
  const cipher = crypto.createCipher('aes-256-cbc', secret);
  let encryptedData = cipher.update(bindingDataJson, 'utf8', 'base64');
  encryptedData += cipher.final('base64');
  
  return {
    encryptedData,
    originalData: bindingData
  };
}

// 新的简化解密
function simplifiedDecryption(encryptedToken, secret) {
  const decipher = crypto.createDecipher('aes-256-cbc', secret);
  let decryptedData = decipher.update(encryptedToken, 'base64', 'utf8');
  decryptedData += decipher.final('utf8');
  
  return JSON.parse(decryptedData);
}

// 测试新格式
console.log('=== 测试简化加密格式 ===');

const encryptResult = simplifiedEncryption(testUserUuid, bindingSecret);
console.log('1. 加密结果:');
console.log('   - 长度:', encryptResult.encryptedData.length);
console.log('   - 前缀:', encryptResult.encryptedData.substring(0, 30) + '...');
console.log('   - 包含冒号:', encryptResult.encryptedData.includes(':'));

// URL编码测试
const urlEncoded = encodeURIComponent(encryptResult.encryptedData);
console.log('2. URL编码:');
console.log('   - URL编码长度:', urlEncoded.length);
console.log('   - 编码前后相同:', encryptResult.encryptedData === urlEncoded);

// 解密测试
try {
  const decryptResult = simplifiedDecryption(encryptResult.encryptedData, bindingSecret);
  console.log('3. 解密成功:');
  console.log('   - 用户UUID匹配:', decryptResult.user_uuid === testUserUuid);
  console.log('   - 解密数据:', decryptResult);
} catch (error) {
  console.log('3. 解密失败:', error.message);
}

// 测试实际接收到的数据
console.log('\n=== 测试实际数据 ===');
const actualSceneParam = 'WdHPjPWDgv7PIxrteCYny44+opvZtSlfYUsXBKZYIvP37PhdqrRh3Ed0eZxGw7HI';
console.log('实际scene_param:', actualSceneParam);
console.log('长度:', actualSceneParam.length);

try {
  const actualDecryptResult = simplifiedDecryption(actualSceneParam, bindingSecret);
  console.log('✅ 实际数据解密成功:');
  console.log('   - 解密数据:', actualDecryptResult);
} catch (error) {
  console.log('❌ 实际数据解密失败:', error.message);
  
  // 尝试URL解码后再解密
  try {
    const urlDecoded = decodeURIComponent(actualSceneParam);
    const urlDecodedResult = simplifiedDecryption(urlDecoded, bindingSecret);
    console.log('✅ URL解码后解密成功:');
    console.log('   - 解密数据:', urlDecodedResult);
  } catch (urlError) {
    console.log('❌ URL解码后解密也失败:', urlError.message);
  }
}

console.log('\n=== 长度对比 ===');
console.log('新格式长度:', encryptResult.encryptedData.length);
console.log('实际接收长度:', actualSceneParam.length);
console.log('长度匹配:', encryptResult.encryptedData.length === actualSceneParam.length);

console.log('\n=== 结论 ===');
if (encryptResult.encryptedData.length <= 64) {
  console.log('✅ 新格式长度合适，应该不会被截断');
} else {
  console.log('⚠️ 新格式长度仍然可能超出限制');
}

console.log('建议：使用简化格式重新生成绑定链接进行测试');
