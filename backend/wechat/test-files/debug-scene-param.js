/**
 * 调试scene_param问题
 * 分析实际接收到的scene_param格式
 */

require('dotenv').config();

console.log('🔍 调试scene_param问题...\n');

// 从日志中提取的实际scene_param
const actualSceneParam = 'WdHPjPWDgv7PIxrteCYny44+opvZtSlfYUsXBKZYIvP37PhdqrRh3Ed0eZxGw7HI';

console.log('=== 实际接收到的scene_param ===');
console.log('原始内容:', actualSceneParam);
console.log('长度:', actualSceneParam.length);
console.log('包含冒号:', actualSceneParam.includes(':'));
console.log('');

// 尝试URL解码
console.log('=== URL解码测试 ===');
const urlDecoded = decodeURIComponent(actualSceneParam);
console.log('URL解码后:', urlDecoded);
console.log('解码后长度:', urlDecoded.length);
console.log('解码后包含冒号:', urlDecoded.includes(':'));
console.log('解码前后是否相同:', actualSceneParam === urlDecoded);
console.log('');

// 分析格式
console.log('=== 格式分析 ===');
const parts = urlDecoded.split(':');
console.log('分割后parts数量:', parts.length);
console.log('Parts内容:', parts);
console.log('');

// 检查是否是base64格式
console.log('=== Base64格式检查 ===');
try {
  const base64Decoded = Buffer.from(actualSceneParam, 'base64').toString('utf8');
  console.log('Base64解码成功:', base64Decoded.substring(0, 100) + '...');
  console.log('Base64解码后包含冒号:', base64Decoded.includes(':'));
  
  if (base64Decoded.includes(':')) {
    const base64Parts = base64Decoded.split(':');
    console.log('Base64解码后分割parts数量:', base64Parts.length);
    console.log('Base64 Parts:', base64Parts.map(p => p.substring(0, 20) + '...'));
  }
} catch (error) {
  console.log('Base64解码失败:', error.message);
}
console.log('');

// 模拟正确的加密过程
console.log('=== 模拟正确的加密过程 ===');
const crypto = require('crypto');
const testUserUuid = '729933f6-275a-4d58-8e4a-65e42edae756';
const bindingSecret = process.env.WECHAT_BINDING_SECRET || 'gongzhimall_binding_secret_2025';

const bindingData = {
  user_uuid: testUserUuid,
  timestamp: new Date().toISOString(),
  expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
};

const algorithm = 'aes-256-cbc';
const key = crypto.scryptSync(bindingSecret, 'salt', 32);
const iv = crypto.randomBytes(16);

const cipher = crypto.createCipheriv(algorithm, key, iv);
let encryptedData = cipher.update(JSON.stringify(bindingData), 'utf8', 'base64');
encryptedData += cipher.final('base64');

const combined = iv.toString('base64') + ':' + encryptedData;
const urlEncoded = encodeURIComponent(combined);

console.log('正确的加密结果:');
console.log('- Combined长度:', combined.length);
console.log('- Combined包含冒号:', combined.includes(':'));
console.log('- URL编码后长度:', urlEncoded.length);
console.log('- Combined前缀:', combined.substring(0, 50) + '...');
console.log('- URL编码前缀:', urlEncoded.substring(0, 50) + '...');
console.log('');

// 对比分析
console.log('=== 对比分析 ===');
console.log('实际scene_param长度:', actualSceneParam.length);
console.log('正确URL编码长度:', urlEncoded.length);
console.log('长度是否匹配:', actualSceneParam.length === urlEncoded.length);
console.log('');

// 尝试不同的解码方式
console.log('=== 尝试不同解码方式 ===');

// 1. 直接作为base64解码
try {
  const directBase64 = Buffer.from(actualSceneParam, 'base64');
  console.log('1. 直接base64解码长度:', directBase64.length);
} catch (error) {
  console.log('1. 直接base64解码失败:', error.message);
}

// 2. 先URL解码再base64解码
try {
  const urlThenBase64 = Buffer.from(urlDecoded, 'base64');
  console.log('2. URL+base64解码长度:', urlThenBase64.length);
} catch (error) {
  console.log('2. URL+base64解码失败:', error.message);
}

// 3. 检查是否需要补充padding
const paddedSceneParam = actualSceneParam + '='.repeat((4 - actualSceneParam.length % 4) % 4);
try {
  const paddedBase64 = Buffer.from(paddedSceneParam, 'base64').toString('utf8');
  console.log('3. 补充padding后base64解码:', paddedBase64.substring(0, 100) + '...');
  console.log('3. 补充padding后包含冒号:', paddedBase64.includes(':'));
} catch (error) {
  console.log('3. 补充padding后base64解码失败:', error.message);
}

console.log('\n=== 结论 ===');
console.log('问题可能是:');
console.log('1. 加密时没有正确生成IV:encryptedData格式');
console.log('2. URL编码/解码过程中丢失了冒号分隔符');
console.log('3. 企业微信传递scene_param时进行了额外处理');
console.log('4. 加密和解密使用了不同的格式');
