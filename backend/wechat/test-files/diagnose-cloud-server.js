/**
 * 云服务器诊断脚本
 * 检查云服务器数据库连接和微信转发功能状态
 */

require('dotenv').config();
const axios = require('axios');

console.log('🔍 开始云服务器诊断...\n');

// 1. 检查云服务器API状态
async function checkCloudServerAPI() {
  console.log('=== 1. 云服务器API状态检查 ===');
  
  const serverDomain = process.env.SERVER_DOMAIN;
  if (!serverDomain) {
    console.log('❌ 服务器域名未配置');
    return false;
  }
  
  try {
    // 检查健康状态
    const healthUrl = `https://${serverDomain}/health`;
    console.log(`检查健康端点: ${healthUrl}`);
    
    const healthResponse = await axios.get(healthUrl, { timeout: 10000 });
    console.log('✅ 服务器健康检查通过');
    console.log('服务器响应:', JSON.stringify(healthResponse.data, null, 2));
    
    // 检查数据库状态
    if (healthResponse.data.checks && healthResponse.data.checks.database === 'ok') {
      console.log('✅ 云服务器数据库连接正常');
      return true;
    } else {
      console.log('❌ 云服务器数据库连接异常');
      return false;
    }
    
  } catch (error) {
    console.log('❌ 云服务器API检查失败:', error.message);
    return false;
  }
}

// 2. 测试绑定状态API
async function testBindingStatusAPI() {
  console.log('\n=== 2. 绑定状态API测试 ===');
  
  const serverDomain = process.env.SERVER_DOMAIN;
  const testUserUuid = 'test-user-uuid-' + Date.now();
  
  try {
    // 生成API签名（简化版本，实际需要完整的签名逻辑）
    const timestamp = Math.floor(Date.now() / 1000);
    const apiUrl = `https://${serverDomain}/api/bind/status`;
    
    console.log(`测试绑定状态API: ${apiUrl}`);
    console.log(`测试用户UUID: ${testUserUuid}`);
    
    // 注意：这里需要正确的签名，暂时测试连通性
    const response = await axios.get(apiUrl, {
      params: {
        user_uuid: testUserUuid,
        timestamp: timestamp
      },
      timeout: 10000,
      validateStatus: function (status) {
        return status < 500; // 接受4xx错误，只要不是服务器错误
      }
    });
    
    console.log('API响应状态:', response.status);
    console.log('API响应数据:', JSON.stringify(response.data, null, 2));
    
    if (response.status === 401 || response.status === 403) {
      console.log('✅ API端点可访问（需要正确签名）');
      return true;
    } else if (response.status === 200) {
      console.log('✅ API调用成功');
      return true;
    } else {
      console.log('⚠️ API响应异常');
      return false;
    }
    
  } catch (error) {
    console.log('❌ 绑定状态API测试失败:', error.message);
    return false;
  }
}

// 3. 测试企业微信配置
async function testWeChatConfig() {
  console.log('\n=== 3. 企业微信配置测试 ===');
  
  const corpId = process.env.WECHAT_CORP_ID;
  const corpSecret = process.env.WECHAT_CORP_SECRET;
  const openKfId = process.env.WECHAT_DEFAULT_OPEN_KFID;
  
  console.log('企业微信配置:');
  console.log(`CORP_ID: ${corpId}`);
  console.log(`OPEN_KFID: ${openKfId}`);
  
  try {
    // 测试获取access_token
    const tokenUrl = `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${corpId}&corpsecret=${corpSecret}`;
    const response = await axios.get(tokenUrl, { timeout: 10000 });
    
    if (response.data.errcode === 0) {
      console.log('✅ 企业微信API配置正确');
      console.log(`Access Token过期时间: ${response.data.expires_in}秒`);
      
      // 测试客服配置
      const accessToken = response.data.access_token;
      try {
        const kfUrl = `https://qyapi.weixin.qq.com/cgi-bin/kf/add_contact_way?access_token=${accessToken}`;
        const kfResponse = await axios.post(kfUrl, {
          open_kfid: openKfId,
          scene: 'test_scene'
        }, { 
          timeout: 10000,
          validateStatus: function (status) {
            return status < 500;
          }
        });
        
        console.log('客服API测试响应:', kfResponse.data);
        if (kfResponse.data.errcode === 0 || kfResponse.data.errcode === 40013) {
          console.log('✅ 客服配置可访问');
        } else {
          console.log('⚠️ 客服配置可能有问题:', kfResponse.data.errmsg);
        }
      } catch (kfError) {
        console.log('⚠️ 客服API测试失败:', kfError.message);
      }
      
      return true;
    } else {
      console.log('❌ 企业微信API配置错误:', response.data.errmsg);
      return false;
    }
    
  } catch (error) {
    console.log('❌ 企业微信配置测试失败:', error.message);
    return false;
  }
}

// 4. 检查推送服务配置
async function checkPushService() {
  console.log('\n=== 4. 推送服务配置检查 ===');
  
  const jpushAppKey = process.env.JPUSH_APP_KEY;
  const jpushMasterSecret = process.env.JPUSH_MASTER_SECRET;
  
  console.log(`JPush App Key: ${jpushAppKey}`);
  console.log(`JPush Master Secret: ${jpushMasterSecret ? '***已配置***' : '未配置'}`);
  
  if (jpushAppKey && jpushMasterSecret) {
    console.log('✅ 极光推送配置完整');
    return true;
  } else {
    console.log('❌ 极光推送配置不完整');
    return false;
  }
}

// 5. 生成问题报告
function generateProblemReport(results) {
  console.log('\n=== 5. 问题诊断报告 ===');
  
  const { cloudServer, bindingAPI, wechatConfig, pushService } = results;
  
  console.log('诊断结果汇总:');
  console.log(`云服务器状态: ${cloudServer ? '✅ 正常' : '❌ 异常'}`);
  console.log(`绑定API状态: ${bindingAPI ? '✅ 正常' : '❌ 异常'}`);
  console.log(`企业微信配置: ${wechatConfig ? '✅ 正常' : '❌ 异常'}`);
  console.log(`推送服务配置: ${pushService ? '✅ 正常' : '❌ 异常'}`);
  
  console.log('\n可能的问题原因:');
  
  if (!cloudServer) {
    console.log('1. 云服务器数据库连接问题');
    console.log('   - 检查云服务器MySQL服务状态');
    console.log('   - 检查数据库用户权限');
    console.log('   - 检查网络连接');
  }
  
  if (!bindingAPI) {
    console.log('2. 绑定API问题');
    console.log('   - 检查API签名机制');
    console.log('   - 检查路由配置');
    console.log('   - 检查中间件配置');
  }
  
  if (!wechatConfig) {
    console.log('3. 企业微信配置问题');
    console.log('   - 检查CORP_ID和CORP_SECRET');
    console.log('   - 检查客服账号配置');
    console.log('   - 检查企业微信应用权限');
  }
  
  if (!pushService) {
    console.log('4. 推送服务配置问题');
    console.log('   - 检查极光推送配置');
    console.log('   - 检查推送证书');
  }
  
  console.log('\n建议的修复步骤:');
  console.log('1. 优先修复云服务器数据库连接');
  console.log('2. 验证企业微信配置的正确性');
  console.log('3. 测试完整的绑定流程');
  console.log('4. 检查消息同步机制');
}

// 主函数
async function main() {
  try {
    console.log('开始诊断云服务器微信转发功能...\n');
    
    const results = {
      cloudServer: await checkCloudServerAPI(),
      bindingAPI: await testBindingStatusAPI(),
      wechatConfig: await testWeChatConfig(),
      pushService: await checkPushService()
    };
    
    generateProblemReport(results);
    
    console.log('\n🔍 云服务器诊断完成');
    
  } catch (error) {
    console.error('诊断过程中发生错误:', error);
  }
}

main();
