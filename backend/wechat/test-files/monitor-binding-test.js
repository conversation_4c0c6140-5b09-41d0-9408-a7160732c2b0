/**
 * 监控绑定测试结果
 * 实时查看数据库绑定记录和日志
 */

require('dotenv').config();
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

console.log('📊 开始监控微信绑定测试结果...\n');

// 检查数据库绑定记录
async function checkBindingRecords() {
  try {
    const command = `ssh <EMAIL> "mariadb -u wechat_user -p'wechat@gongzhimall123' -S /var/lib/mysql/mysql.sock gongzhimall_wechat -e 'SELECT COUNT(*) as total_bindings FROM wechat_bindings; SELECT * FROM wechat_bindings ORDER BY created_at DESC LIMIT 3;'"`;
    
    const { stdout } = await execAsync(command);
    console.log('=== 数据库绑定记录 ===');
    console.log(stdout);
    
    return stdout.includes('total_bindings') && !stdout.includes('0\t0');
  } catch (error) {
    console.log('❌ 检查绑定记录失败:', error.message);
    return false;
  }
}

// 检查最近的日志
async function checkRecentLogs() {
  try {
    const command = `ssh <EMAIL> "mariadb -u wechat_user -p'wechat@gongzhimall123' -S /var/lib/mysql/mysql.sock gongzhimall_wechat -e \\"SELECT * FROM system_logs WHERE message LIKE '%绑定%' OR message LIKE '%bind%' OR message LIKE '%解密%' OR message LIKE '%客服%' ORDER BY created_at DESC LIMIT 10;\\"" | tail -20`;
    
    const { stdout } = await execAsync(command);
    console.log('=== 最近的绑定相关日志 ===');
    console.log(stdout);
    
    return stdout.includes('解密成功') || stdout.includes('绑定成功');
  } catch (error) {
    console.log('❌ 检查日志失败:', error.message);
    return false;
  }
}

// 检查消息记录
async function checkMessageRecords() {
  try {
    const command = `ssh <EMAIL> "mariadb -u wechat_user -p'wechat@gongzhimall123' -S /var/lib/mysql/mysql.sock gongzhimall_wechat -e 'SELECT COUNT(*) as total_messages FROM wechat_message_logs; SELECT * FROM wechat_message_logs ORDER BY created_at DESC LIMIT 3;'"`;
    
    const { stdout } = await execAsync(command);
    console.log('=== 消息记录 ===');
    console.log(stdout);
    
    return stdout.includes('total_messages');
  } catch (error) {
    console.log('❌ 检查消息记录失败:', error.message);
    return false;
  }
}

// 主监控函数
async function monitorBindingTest() {
  console.log(`[${new Date().toLocaleTimeString()}] 🔍 检查绑定状态...\n`);
  
  const hasBindings = await checkBindingRecords();
  const hasSuccessLogs = await checkRecentLogs();
  const hasMessages = await checkMessageRecords();
  
  console.log('\n=== 监控结果汇总 ===');
  console.log(`绑定记录: ${hasBindings ? '✅ 有新绑定' : '⏳ 暂无绑定'}`);
  console.log(`成功日志: ${hasSuccessLogs ? '✅ 有成功日志' : '⏳ 暂无成功日志'}`);
  console.log(`消息记录: ${hasMessages ? '✅ 有消息记录' : '⏳ 暂无消息'}`);
  
  if (hasBindings && hasSuccessLogs) {
    console.log('\n🎉 绑定测试成功！微信转发功能已修复！');
    return true;
  } else if (hasSuccessLogs) {
    console.log('\n✅ 解密修复生效，等待绑定完成...');
    return false;
  } else {
    console.log('\n⏳ 等待用户进行绑定测试...');
    return false;
  }
}

// 持续监控
async function startMonitoring() {
  console.log('开始持续监控绑定测试结果...');
  console.log('请在APP中重新生成绑定链接并跳转到微信进行绑定测试\n');
  
  let monitorCount = 0;
  const maxMonitors = 20; // 最多监控20次（约10分钟）
  
  const monitorInterval = setInterval(async () => {
    monitorCount++;
    
    try {
      const isSuccess = await monitorBindingTest();
      
      if (isSuccess) {
        console.log('\n🎉 监控完成：绑定测试成功！');
        clearInterval(monitorInterval);
        process.exit(0);
      } else if (monitorCount >= maxMonitors) {
        console.log('\n⏰ 监控超时，请手动检查绑定状态');
        clearInterval(monitorInterval);
        process.exit(0);
      } else {
        console.log(`\n⏳ 继续监控... (${monitorCount}/${maxMonitors})`);
        console.log('=' .repeat(60));
      }
    } catch (error) {
      console.error('监控过程中发生错误:', error);
    }
  }, 30000); // 每30秒检查一次
  
  // 立即执行一次
  await monitorBindingTest();
}

// 启动监控
startMonitoring().catch(error => {
  console.error('启动监控失败:', error);
  process.exit(1);
});
