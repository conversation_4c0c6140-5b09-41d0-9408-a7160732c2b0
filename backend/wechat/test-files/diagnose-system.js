/**
 * 系统诊断脚本
 * 检查环境变量、数据库连接、服务器状态等关键配置
 */

require('dotenv').config();
const db = require('../data/database/core');
const axios = require('axios');

console.log('🔍 开始系统诊断...\n');

// 1. 检查环境变量
console.log('=== 1. 环境变量检查 ===');
const requiredEnvVars = [
  'MYSQL_HOST',
  'MYSQL_PORT', 
  'MYSQL_USER',
  'MYSQL_PASSWORD',
  'MYSQL_DATABASE',
  'WECHAT_CORP_ID',
  'WECHAT_CORP_SECRET',
  'WECHAT_AGENT_ID',
  'WECHAT_TOKEN',
  'WECHAT_ENCODING_AES_KEY',
  'WECHAT_DEFAULT_OPEN_KFID',
  'WECHAT_BINDING_SECRET',
  'JPUSH_APP_KEY',
  'JPUSH_MASTER_SECRET',
  'SERVER_DOMAIN'
];

let envCheckPassed = true;
requiredEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${varName.includes('SECRET') || varName.includes('PASSWORD') ? '***已配置***' : value}`);
  } else {
    console.log(`❌ ${varName}: 未配置`);
    envCheckPassed = false;
  }
});

console.log(`\n环境变量检查结果: ${envCheckPassed ? '✅ 通过' : '❌ 失败'}\n`);

// 2. 检查数据库连接
console.log('=== 2. 数据库连接检查 ===');
async function checkDatabase() {
  try {
    const isConnected = await db.checkConnection();
    if (isConnected) {
      console.log('✅ 数据库连接正常');
      
      // 检查关键表是否存在
      const tables = ['app_users', 'user_bindings', 'wechat_message_logs', 'device_sync_status'];
      for (const table of tables) {
        try {
          const result = await db.query(`SELECT COUNT(*) as count FROM ${table} LIMIT 1`);
          console.log(`✅ 表 ${table} 存在，记录数: ${result[0].count}`);
        } catch (error) {
          console.log(`❌ 表 ${table} 不存在或无法访问: ${error.message}`);
        }
      }
    } else {
      console.log('❌ 数据库连接失败');
    }
  } catch (error) {
    console.log('❌ 数据库检查异常:', error.message);
  }
}

// 3. 检查企业微信API连接
console.log('\n=== 3. 企业微信API检查 ===');
async function checkWeChatAPI() {
  try {
    const corpId = process.env.WECHAT_CORP_ID;
    const corpSecret = process.env.WECHAT_CORP_SECRET;
    
    if (!corpId || !corpSecret) {
      console.log('❌ 企业微信配置不完整');
      return;
    }
    
    // 测试获取access_token
    const tokenUrl = `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${corpId}&corpsecret=${corpSecret}`;
    const response = await axios.get(tokenUrl, { timeout: 10000 });
    
    if (response.data.errcode === 0) {
      console.log('✅ 企业微信API连接正常');
      console.log(`✅ Access Token获取成功，过期时间: ${response.data.expires_in}秒`);
    } else {
      console.log(`❌ 企业微信API错误: ${response.data.errmsg} (${response.data.errcode})`);
    }
  } catch (error) {
    console.log('❌ 企业微信API检查失败:', error.message);
  }
}

// 4. 检查服务器状态
console.log('\n=== 4. 服务器状态检查 ===');
async function checkServerStatus() {
  try {
    const serverDomain = process.env.SERVER_DOMAIN;
    const port = process.env.PORT || 3000;
    
    if (!serverDomain) {
      console.log('❌ 服务器域名未配置');
      return;
    }
    
    // 检查健康检查端点
    const healthUrl = `https://${serverDomain}/health`;
    console.log(`检查健康端点: ${healthUrl}`);
    
    const response = await axios.get(healthUrl, { timeout: 10000 });
    console.log('✅ 服务器健康检查通过');
    console.log('服务器响应:', response.data);
  } catch (error) {
    console.log('❌ 服务器状态检查失败:', error.message);
    
    // 尝试检查本地端口
    try {
      const localUrl = `http://localhost:${process.env.PORT || 3000}/health`;
      console.log(`尝试本地检查: ${localUrl}`);
      const localResponse = await axios.get(localUrl, { timeout: 5000 });
      console.log('✅ 本地服务器运行正常');
    } catch (localError) {
      console.log('❌ 本地服务器也无法访问:', localError.message);
    }
  }
}

// 5. 检查日志配置
console.log('\n=== 5. 日志配置检查 ===');
function checkLogConfig() {
  const logLevel = process.env.LOG_LEVEL || 'INFO';
  const logPath = process.env.LOG_FILE_PATH;
  const enableDbLogging = process.env.ENABLE_DB_LOGGING;
  
  console.log(`✅ 日志级别: ${logLevel}`);
  console.log(`${logPath ? '✅' : '⚠️'} 日志文件路径: ${logPath || '未配置'}`);
  console.log(`✅ 数据库日志: ${enableDbLogging === 'true' ? '启用' : '禁用'}`);
}

// 执行所有检查
async function runDiagnosis() {
  checkLogConfig();
  await checkDatabase();
  await checkWeChatAPI();
  await checkServerStatus();
  
  console.log('\n🔍 系统诊断完成');
  console.log('\n建议检查项目:');
  console.log('1. 如果数据库连接失败，请检查MySQL服务是否运行');
  console.log('2. 如果企业微信API失败，请检查CORP_ID和CORP_SECRET');
  console.log('3. 如果服务器无法访问，请检查服务是否启动');
  console.log('4. 检查防火墙和网络配置');
  
  process.exit(0);
}

runDiagnosis().catch(error => {
  console.error('诊断过程中发生错误:', error);
  process.exit(1);
});
