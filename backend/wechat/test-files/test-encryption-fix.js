/**
 * 测试加密修复是否有效
 * 模拟完整的加密/解密流程
 */

require('dotenv').config();
const crypto = require('crypto');

console.log('🔧 测试加密/解密修复...\n');

// 模拟加密过程（与wechatApi.js相同）
function encryptBindingData(userUuid, bindingSecret) {
  const bindingData = {
    user_uuid: userUuid,
    timestamp: new Date().toISOString(),
    expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
  };

  const algorithm = 'aes-256-cbc';
  const key = crypto.scryptSync(bindingSecret, 'salt', 32);
  const iv = crypto.randomBytes(16);

  const cipher = crypto.createCipheriv(algorithm, key, iv);
  let encryptedData = cipher.update(JSON.stringify(bindingData), 'utf8', 'base64');
  encryptedData += cipher.final('base64');

  const combined = iv.toString('base64') + ':' + encryptedData;
  
  return {
    combined,
    originalData: bindingData,
    urlEncoded: encodeURIComponent(combined)
  };
}

// 模拟解密过程（与修复后的MessageProcessService.js相同）
function decryptBindingData(encryptedToken, bindingSecret) {
  const algorithm = 'aes-256-cbc';
  const key = crypto.scryptSync(bindingSecret, 'salt', 32);

  // 先进行URL解码
  const decodedToken = decodeURIComponent(encryptedToken);

  const parts = decodedToken.split(':');
  if (parts.length !== 2) {
    throw new Error('绑定令牌格式无效');
  }

  const ivBase64 = parts[0];
  const encryptedDataBase64 = parts[1];

  const iv = Buffer.from(ivBase64, 'base64');
  
  const decipher = crypto.createDecipheriv(algorithm, key, iv);
  let decryptedData = decipher.update(encryptedDataBase64, 'base64', 'utf8');
  decryptedData += decipher.final('utf8');

  return JSON.parse(decryptedData);
}

// 测试函数
function testEncryptionDecryption() {
  const testUserUuid = '729933f6-275a-4d58-8e4a-65e42edae756'; // 使用实际的用户UUID
  const bindingSecret = process.env.WECHAT_BINDING_SECRET || 'gongzhimall_binding_secret_2025';
  
  console.log('=== 测试参数 ===');
  console.log('用户UUID:', testUserUuid);
  console.log('绑定密钥:', bindingSecret);
  console.log('');
  
  try {
    // 1. 加密测试
    console.log('=== 1. 加密测试 ===');
    const encryptResult = encryptBindingData(testUserUuid, bindingSecret);
    console.log('✅ 加密成功');
    console.log('原始数据:', encryptResult.originalData);
    console.log('加密后长度:', encryptResult.combined.length);
    console.log('URL编码后长度:', encryptResult.urlEncoded.length);
    console.log('加密数据前缀:', encryptResult.combined.substring(0, 50) + '...');
    console.log('');
    
    // 2. 解密测试
    console.log('=== 2. 解密测试 ===');
    const decryptResult = decryptBindingData(encryptResult.urlEncoded, bindingSecret);
    console.log('✅ 解密成功');
    console.log('解密数据:', decryptResult);
    console.log('');
    
    // 3. 数据一致性验证
    console.log('=== 3. 数据一致性验证 ===');
    const isUserUuidMatch = decryptResult.user_uuid === encryptResult.originalData.user_uuid;
    const isTimestampMatch = decryptResult.timestamp === encryptResult.originalData.timestamp;
    const isExpiresAtMatch = decryptResult.expires_at === encryptResult.originalData.expires_at;
    
    console.log('用户UUID匹配:', isUserUuidMatch ? '✅' : '❌');
    console.log('时间戳匹配:', isTimestampMatch ? '✅' : '❌');
    console.log('过期时间匹配:', isExpiresAtMatch ? '✅' : '❌');
    
    if (isUserUuidMatch && isTimestampMatch && isExpiresAtMatch) {
      console.log('');
      console.log('🎉 加密/解密修复验证成功！');
      console.log('现在可以重新测试微信绑定功能了。');
      return true;
    } else {
      console.log('');
      console.log('❌ 数据一致性验证失败');
      return false;
    }
    
  } catch (error) {
    console.log('❌ 测试失败:', error.message);
    console.log('错误详情:', error);
    return false;
  }
}

// 运行测试
console.log('开始测试加密/解密修复...\n');
const testResult = testEncryptionDecryption();

if (testResult) {
  console.log('\n=== 下一步建议 ===');
  console.log('1. 重新在APP中生成绑定链接');
  console.log('2. 跳转到微信客服进行绑定');
  console.log('3. 检查数据库中是否有新的绑定记录');
  console.log('4. 验证客服消息功能是否正常');
} else {
  console.log('\n=== 问题排查建议 ===');
  console.log('1. 检查环境变量WECHAT_BINDING_SECRET是否正确');
  console.log('2. 确认服务器和本地使用相同的密钥');
  console.log('3. 检查Node.js版本兼容性');
}
