/**
 * 文件类型检测一致性测试
 * 验证服务端和移动端的文件类型判断是否一致
 */

const { detectFileTypeFromName } = require('../config/fileTypes');

// 测试用例
const testCases = [
  // 图片文件
  { fileName: 'photo.jpg', expected: { category: 'image', mimeType: 'image/jpeg', description: 'JPEG图片' } },
  { fileName: 'image.png', expected: { category: 'image', mimeType: 'image/png', description: 'PNG图片' } },
  { fileName: 'animation.gif', expected: { category: 'image', mimeType: 'image/gif', description: 'GIF图片' } },
  { fileName: 'webp_image.webp', expected: { category: 'image', mimeType: 'image/webp', description: 'WebP图片' } },
  { fileName: 'bitmap.bmp', expected: { category: 'image', mimeType: 'image/bmp', description: 'BMP图片' } },
  { fileName: 'vector.svg', expected: { category: 'image', mimeType: 'image/svg+xml', description: 'SVG图片' } },

  // 视频文件
  { fileName: 'video.mp4', expected: { category: 'video', mimeType: 'video/mp4', description: 'MP4视频' } },
  { fileName: 'movie.avi', expected: { category: 'video', mimeType: 'video/avi', description: 'AVI视频' } },
  { fileName: 'quicktime.mov', expected: { category: 'video', mimeType: 'video/mov', description: 'MOV视频' } },
  { fileName: 'windows_media.wmv', expected: { category: 'video', mimeType: 'video/wmv', description: 'WMV视频' } },
  { fileName: 'flash.flv', expected: { category: 'video', mimeType: 'video/flv', description: 'FLV视频' } },
  { fileName: 'web_video.webm', expected: { category: 'video', mimeType: 'video/webm', description: 'WebM视频' } },

  // 音频文件
  { fileName: 'music.mp3', expected: { category: 'voice', mimeType: 'audio/mpeg', description: 'MP3音频' } },
  { fileName: 'voice.amr', expected: { category: 'voice', mimeType: 'audio/amr', description: 'AMR音频' } },
  { fileName: 'audio.wav', expected: { category: 'voice', mimeType: 'audio/wav', description: 'WAV音频' } },
  { fileName: 'aac_audio.aac', expected: { category: 'voice', mimeType: 'audio/aac', description: 'AAC音频' } },
  { fileName: 'm4a_audio.m4a', expected: { category: 'voice', mimeType: 'audio/m4a', description: 'M4A音频' } },
  { fileName: 'ogg_audio.ogg', expected: { category: 'voice', mimeType: 'audio/ogg', description: 'OGG音频' } },

  // 文档文件
  { fileName: 'document.pdf', expected: { category: 'file', mimeType: 'application/pdf', description: 'PDF文档' } },
  { fileName: 'ofd_document.ofd', expected: { category: 'file', mimeType: 'application/ofd', description: 'OFD文档' } },
  
  // Microsoft Office
  { fileName: 'word.doc', expected: { category: 'file', mimeType: 'application/msword', description: 'Word文档' } },
  { fileName: 'word.docx', expected: { category: 'file', mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', description: 'Word文档' } },
  { fileName: 'word_macro.docm', expected: { category: 'file', mimeType: 'application/vnd.ms-word.document.macroEnabled.12', description: 'Word文档(宏)' } },
  { fileName: 'excel.xls', expected: { category: 'file', mimeType: 'application/vnd.ms-excel', description: 'Excel表格' } },
  { fileName: 'excel.xlsx', expected: { category: 'file', mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', description: 'Excel表格' } },
  { fileName: 'excel_macro.xlsm', expected: { category: 'file', mimeType: 'application/vnd.ms-excel.sheet.macroEnabled.12', description: 'Excel表格(宏)' } },
  { fileName: 'powerpoint.ppt', expected: { category: 'file', mimeType: 'application/vnd.ms-powerpoint', description: 'PowerPoint演示' } },
  { fileName: 'powerpoint.pptx', expected: { category: 'file', mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation', description: 'PowerPoint演示' } },
  { fileName: 'powerpoint_macro.pptm', expected: { category: 'file', mimeType: 'application/vnd.ms-powerpoint.presentation.macroEnabled.12', description: 'PowerPoint演示(宏)' } },

  // WPS Office
  { fileName: 'wps_document.wps', expected: { category: 'file', mimeType: 'application/wps-office.wps', description: 'WPS文字' } },
  { fileName: 'wps_spreadsheet.et', expected: { category: 'file', mimeType: 'application/wps-office.et', description: 'WPS表格' } },
  { fileName: 'wps_presentation.dps', expected: { category: 'file', mimeType: 'application/wps-office.dps', description: 'WPS演示' } },

  // 文本文件
  { fileName: 'text.txt', expected: { category: 'file', mimeType: 'text/plain', description: '纯文本' } },
  { fileName: 'rich_text.rtf', expected: { category: 'file', mimeType: 'application/rtf', description: '富文本' } },
  { fileName: 'data.csv', expected: { category: 'file', mimeType: 'text/csv', description: 'CSV数据' } },
  { fileName: 'markdown.md', expected: { category: 'file', mimeType: 'text/markdown', description: 'Markdown文档' } },
  { fileName: 'json_data.json', expected: { category: 'file', mimeType: 'application/json', description: 'JSON数据' } },
  { fileName: 'xml_document.xml', expected: { category: 'file', mimeType: 'application/xml', description: 'XML文档' } },

  // 压缩文件
  { fileName: 'archive.zip', expected: { category: 'file', mimeType: 'application/zip', description: 'ZIP压缩包' } },
  { fileName: 'archive.rar', expected: { category: 'file', mimeType: 'application/x-rar-compressed', description: 'RAR压缩包' } },
  { fileName: 'archive.7z', expected: { category: 'file', mimeType: 'application/x-7z-compressed', description: '7Z压缩包' } },

  // 边界情况
  { fileName: '', expected: { category: 'file', mimeType: 'application/octet-stream', description: '未知文件' } },
  { fileName: 'no_extension', expected: { category: 'file', mimeType: 'application/octet-stream', description: 'NO_EXTENSION文件' } },
  { fileName: 'unknown.xyz', expected: { category: 'file', mimeType: 'application/octet-stream', description: 'XYZ文件' } },
];

/**
 * 运行一致性测试
 */
function runConsistencyTest() {
  console.log('🔍 开始文件类型检测一致性测试...\n');

  let passedTests = 0;
  let failedTests = 0;
  const failures = [];

  for (const testCase of testCases) {
    const { fileName, expected } = testCase;
    const result = detectFileTypeFromName(fileName);
    
    // 检查关键属性
    const categoryMatch = result.category === expected.category;
    const mimeTypeMatch = result.mimeType === expected.mimeType;
    const descriptionMatch = result.description === expected.description;
    
    if (categoryMatch && mimeTypeMatch && descriptionMatch) {
      passedTests++;
      console.log(`✅ ${fileName} -> ${result.description} (${result.category})`);
    } else {
      failedTests++;
      const failure = {
        fileName,
        expected,
        actual: result,
        mismatches: []
      };
      
      if (!categoryMatch) failure.mismatches.push(`category: expected ${expected.category}, got ${result.category}`);
      if (!mimeTypeMatch) failure.mismatches.push(`mimeType: expected ${expected.mimeType}, got ${result.mimeType}`);
      if (!descriptionMatch) failure.mismatches.push(`description: expected ${expected.description}, got ${result.description}`);
      
      failures.push(failure);
      console.log(`❌ ${fileName} -> 期望: ${expected.description}, 实际: ${result.description}`);
    }
  }

  console.log('\n📊 测试结果汇总:');
  console.log(`✅ 通过: ${passedTests} 个测试`);
  console.log(`❌ 失败: ${failedTests} 个测试`);
  console.log(`📈 成功率: ${((passedTests / testCases.length) * 100).toFixed(1)}%`);

  if (failures.length > 0) {
    console.log('\n🔍 失败详情:');
    failures.forEach(failure => {
      console.log(`\n文件: ${failure.fileName}`);
      failure.mismatches.forEach(mismatch => {
        console.log(`  - ${mismatch}`);
      });
    });
  }

  return {
    total: testCases.length,
    passed: passedTests,
    failed: failedTests,
    successRate: (passedTests / testCases.length) * 100,
    failures
  };
}

/**
 * 测试移动端和服务端一致性
 */
function testMobileServerConsistency() {
  console.log('\n🔄 测试移动端和服务端一致性...\n');
  
  // 这里可以添加移动端和服务端的对比测试
  // 由于移动端是TypeScript，需要单独运行测试
  
  console.log('📱 移动端测试需要在移动端环境中运行');
  console.log('💻 服务端测试已完成');
}

// 运行测试
if (require.main === module) {
  const results = runConsistencyTest();
  testMobileServerConsistency();
  
  console.log('\n🎯 测试完成!');
  console.log(`📊 总体结果: ${results.passed}/${results.total} 通过 (${results.successRate.toFixed(1)}%)`);
  
  if (results.failed > 0) {
    console.log('\n⚠️  发现不一致问题，需要检查文件类型配置');
    process.exit(1);
  } else {
    console.log('\n✅ 所有测试通过，文件类型检测一致!');
  }
}

module.exports = {
  runConsistencyTest,
  testMobileServerConsistency,
  testCases
}; 