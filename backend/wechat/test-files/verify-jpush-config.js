#!/usr/bin/env node
/**
 * 推送服务配置验证脚本
 * 验证推送服务配置是否正确
 */
const path = require('path');
// 加载环境变量
require('dotenv').config({ path: path.resolve(__dirname, '..', '.env') });

const JPush = require('jpush-sdk');

// 检查必要的环境变量
const requiredEnvVars = ['JPUSH_APP_KEY', 'JPUSH_MASTER_SECRET'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error('❌ 缺少必要的环境变量:', missingEnvVars.join(', '));
  console.error('请在.env文件或系统环境变量中设置这些变量');
  process.exit(1);
}

// 极光推送配置
const JPUSH_CONFIG = {
  appKey: process.env.JPUSH_APP_KEY,
  masterSecret: process.env.JPUSH_MASTER_SECRET,
  production: process.env.NODE_ENV === 'production'
};

console.log('🔍 开始验证极光推送配置...');
console.log('AppKey:', JPUSH_CONFIG.appKey);
console.log('Master Secret:', JPUSH_CONFIG.masterSecret.substring(0, 8) + '...');
console.log('Production Mode:', JPUSH_CONFIG.production);

// 初始化极光推送客户端
const jpushClient = JPush.buildClient(JPUSH_CONFIG.appKey, JPUSH_CONFIG.masterSecret);

async function verifyJPushConfig() {
  try {
    console.log('\n📡 测试极光推送连接...');
    
    // 发送测试推送
    const result = await jpushClient.push()
      .setPlatform('all')
      .setAudience(JPush.tag('test'))
      .setNotification('测试通知', {
        android: {
          alert: '公职猫极光推送配置测试',
          title: '配置验证',
          extras: { test: true }
        },
        ios: {
          alert: '公职猫极光推送配置测试',
          badge: 1,
          extras: { test: true }
        }
      })
      .setOptions({
        time_to_live: 60,
        apns_production: JPUSH_CONFIG.production
      })
      .send();
    
    console.log('✅ 极光推送配置验证成功！');
    console.log('推送结果:', result);
    console.log('消息ID:', result.msg_id);
    console.log('发送时间:', new Date().toLocaleString());
    
    return true;
    
  } catch (error) {
    console.error('❌ 极光推送配置验证失败:');
    console.error('错误信息:', error.message);
    
    if (error.message.includes('Unauthorized')) {
      console.error('🔑 认证失败，请检查 AppKey 和 Master Secret 是否正确');
    } else if (error.message.includes('Invalid')) {
      console.error('📝 请求格式错误，请检查推送内容格式');
    } else if (error.message.includes('Network')) {
      console.error('🌐 网络连接失败，请检查网络连接');
    }
    
    return false;
  }
}

async function main() {
  console.log('🚀 公职猫微信转发 - 极光推送配置验证');
  console.log('=' * 50);
  
  const isValid = await verifyJPushConfig();
  
  console.log('\n' + '=' * 50);
  if (isValid) {
    console.log('🎉 配置验证完成，极光推送服务可以正常使用！');
    console.log('\n📋 下一步操作：');
    console.log('1. 部署服务端代码到腾讯云SCF');
    console.log('2. 在移动端APP中测试推送接收');
    console.log('3. 测试完整的微信消息转发流程');
  } else {
    console.log('💥 配置验证失败，请检查配置后重试');
    console.log('\n🔧 故障排除：');
    console.log('1. 确认极光推送账号状态正常');
    console.log('2. 检查 AppKey 和 Master Secret 是否正确');
    console.log('3. 确认网络连接正常');
    console.log('4. 查看极光推送控制台是否有错误信息');
  }
  
  process.exit(isValid ? 0 : 1);
}

// 运行验证
main().catch(error => {
  console.error('验证脚本执行失败:', error);
  process.exit(1);
});
