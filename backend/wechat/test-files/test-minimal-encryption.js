/**
 * 测试极简加密格式
 * 验证最新的加密/解密逻辑
 */

require('dotenv').config();
const crypto = require('crypto');

console.log('🔍 测试极简加密格式...\n');

const testUserUuid = '729933f6-275a-4d58-8e4a-65e42edae756';
const bindingSecret = process.env.WECHAT_BINDING_SECRET || 'gongzhimall_binding_secret_2025';

// 极简加密（与最新的wechatApi.js一致）
function minimalEncryption(userUuid, secret) {
  // 极简的绑定数据
  const bindingData = {
    uuid: userUuid,
    exp: Math.floor(Date.now() / 1000) + 86400 // 24小时后过期
  };
  
  const bindingDataJson = JSON.stringify(bindingData);
  console.log('1. 绑定数据:', bindingData);
  console.log('2. JSON长度:', bindingDataJson.length);
  console.log('3. JSON内容:', bindingDataJson);
  
  // 使用简化的加密方式
  const cipher = crypto.createCipher('aes-256-cbc', secret);
  let encryptedData = cipher.update(bindingDataJson, 'utf8', 'base64');
  encryptedData += cipher.final('base64');
  
  return {
    encryptedData,
    originalData: bindingData
  };
}

// 极简解密（与最新的MessageProcessService.js一致）
function minimalDecryption(encryptedToken, secret) {
  // URL解码
  const decodedToken = decodeURIComponent(encryptedToken);
  
  // 简化解密
  const decipher = crypto.createDecipher('aes-256-cbc', secret);
  let decryptedData = decipher.update(decodedToken, 'base64', 'utf8');
  decryptedData += decipher.final('utf8');
  
  return JSON.parse(decryptedData);
}

// 测试极简格式
console.log('=== 测试极简加密格式 ===');

const encryptResult = minimalEncryption(testUserUuid, bindingSecret);
console.log('4. 加密结果:');
console.log('   - 长度:', encryptResult.encryptedData.length);
console.log('   - 前缀:', encryptResult.encryptedData.substring(0, 30) + '...');
console.log('   - 包含冒号:', encryptResult.encryptedData.includes(':'));

// URL编码测试
const urlEncoded = encodeURIComponent(encryptResult.encryptedData);
console.log('5. URL编码:');
console.log('   - URL编码长度:', urlEncoded.length);
console.log('   - 编码前后相同:', encryptResult.encryptedData === urlEncoded);

// 解密测试
try {
  const decryptResult = minimalDecryption(encryptResult.encryptedData, bindingSecret);
  console.log('6. 解密成功:');
  console.log('   - UUID匹配:', decryptResult.uuid === testUserUuid);
  console.log('   - 过期时间:', new Date(decryptResult.exp * 1000).toISOString());
  console.log('   - 解密数据:', decryptResult);
} catch (error) {
  console.log('6. 解密失败:', error.message);
}

// 长度对比
console.log('\n=== 长度分析 ===');
console.log('极简格式长度:', encryptResult.encryptedData.length);
console.log('企业微信限制:', '通常64字符以内');
console.log('是否符合限制:', encryptResult.encryptedData.length <= 64 ? '✅ 符合' : '❌ 超出');

if (encryptResult.encryptedData.length <= 64) {
  console.log('\n🎉 极简格式长度合适！');
  console.log('现在可以重新生成绑定链接进行测试了。');
} else {
  console.log('\n⚠️ 长度仍然超出，需要进一步优化');
  
  // 尝试更极简的数据
  console.log('\n=== 尝试更极简的数据 ===');
  const ultraMinimal = {
    u: testUserUuid.replace(/-/g, ''), // 移除连字符
    e: Math.floor(Date.now() / 1000) + 86400
  };
  
  const ultraJson = JSON.stringify(ultraMinimal);
  console.log('超极简JSON:', ultraJson);
  console.log('超极简JSON长度:', ultraJson.length);
  
  const cipher2 = crypto.createCipher('aes-256-cbc', bindingSecret);
  let encrypted2 = cipher2.update(ultraJson, 'utf8', 'base64');
  encrypted2 += cipher2.final('base64');
  
  console.log('超极简加密长度:', encrypted2.length);
  console.log('是否符合限制:', encrypted2.length <= 64 ? '✅ 符合' : '❌ 超出');
}
