/**
 * 微信绑定流程测试脚本
 * 模拟完整的绑定和消息同步流程
 */

require('dotenv').config();
const axios = require('axios');
const crypto = require('crypto');

console.log('🔗 开始测试微信绑定流程...\n');

// 生成API签名
function generateSignature(params, secret) {
  const sortedKeys = Object.keys(params).sort();
  const queryString = sortedKeys.map(key => `${key}=${params[key]}`).join('&');
  const stringToSign = queryString + '&key=' + secret;
  return crypto.createHash('md5').update(stringToSign).digest('hex');
}

// 1. 测试生成绑定链接
async function testGenerateBindingLink() {
  console.log('=== 1. 测试生成绑定链接 ===');
  
  const serverDomain = process.env.SERVER_DOMAIN;
  const apiSecret = process.env.API_SECRET_KEY || process.env.TOKEN_SECRET;
  const testUserUuid = 'test-user-' + Date.now();
  
  try {
    const timestamp = Math.floor(Date.now() / 1000);
    const params = {
      user_uuid: testUserUuid,
      timestamp: timestamp
    };
    
    const signature = generateSignature(params, apiSecret);
    
    const response = await axios.post(
      `https://${serverDomain}/api/bind/one-click-link`,
      { user_uuid: testUserUuid },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-Timestamp': timestamp,
          'X-Signature': signature
        },
        timeout: 10000
      }
    );
    
    console.log('✅ 绑定链接生成成功');
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    if (response.data.success && response.data.binding_url) {
      console.log('✅ 绑定URL有效:', response.data.binding_url.substring(0, 100) + '...');
      return {
        success: true,
        userUuid: testUserUuid,
        bindingUrl: response.data.binding_url
      };
    } else {
      console.log('❌ 绑定链接生成失败');
      return { success: false };
    }
    
  } catch (error) {
    console.log('❌ 绑定链接生成异常:', error.response?.data || error.message);
    return { success: false };
  }
}

// 2. 测试绑定状态查询
async function testBindingStatus(userUuid) {
  console.log('\n=== 2. 测试绑定状态查询 ===');
  
  const serverDomain = process.env.SERVER_DOMAIN;
  const apiSecret = process.env.API_SECRET_KEY || process.env.TOKEN_SECRET;
  
  try {
    const timestamp = Math.floor(Date.now() / 1000);
    const params = {
      user_uuid: userUuid,
      timestamp: timestamp
    };
    
    const signature = generateSignature(params, apiSecret);
    
    const response = await axios.get(
      `https://${serverDomain}/api/bind/status`,
      {
        params: params,
        headers: {
          'X-Timestamp': timestamp,
          'X-Signature': signature
        },
        timeout: 10000
      }
    );
    
    console.log('✅ 绑定状态查询成功');
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data;
    
  } catch (error) {
    console.log('❌ 绑定状态查询失败:', error.response?.data || error.message);
    return { success: false };
  }
}

// 3. 模拟绑定令牌处理
async function simulateBindingToken() {
  console.log('\n=== 3. 模拟绑定令牌处理 ===');
  
  const testUserUuid = 'test-user-' + Date.now();
  const bindingSecret = process.env.WECHAT_BINDING_SECRET;
  
  try {
    // 模拟生成绑定令牌（类似客服链接中的scene_param）
    const bindingData = {
      user_uuid: testUserUuid,
      timestamp: new Date().toISOString(),
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    };
    
    const dataString = JSON.stringify(bindingData);
    const cipher = crypto.createCipher('aes-256-cbc', bindingSecret);
    let encrypted = cipher.update(dataString, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    
    const combined = `${encrypted}|${bindingData.expires_at}`;
    const sceneParam = Buffer.from(combined).toString('base64');
    
    console.log('✅ 绑定令牌生成成功');
    console.log('用户UUID:', testUserUuid);
    console.log('令牌长度:', sceneParam.length);
    console.log('令牌前缀:', sceneParam.substring(0, 50) + '...');
    
    // 模拟解密验证
    try {
      const decodedCombined = Buffer.from(sceneParam, 'base64').toString('utf8');
      const [encryptedPart, expiresAt] = decodedCombined.split('|');
      
      const decipher = crypto.createDecipher('aes-256-cbc', bindingSecret);
      let decrypted = decipher.update(encryptedPart, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      
      const decryptedData = JSON.parse(decrypted);
      
      console.log('✅ 令牌解密验证成功');
      console.log('解密数据:', decryptedData);
      
      return {
        success: true,
        userUuid: testUserUuid,
        sceneParam: sceneParam
      };
      
    } catch (decryptError) {
      console.log('❌ 令牌解密验证失败:', decryptError.message);
      return { success: false };
    }
    
  } catch (error) {
    console.log('❌ 绑定令牌处理异常:', error.message);
    return { success: false };
  }
}

// 4. 测试消息同步API
async function testMessageSync(userUuid) {
  console.log('\n=== 4. 测试消息同步API ===');
  
  const serverDomain = process.env.SERVER_DOMAIN;
  const apiSecret = process.env.API_SECRET_KEY || process.env.TOKEN_SECRET;
  
  try {
    const timestamp = Math.floor(Date.now() / 1000);
    const params = {
      user_uuid: userUuid,
      timestamp: timestamp,
      limit: 10
    };
    
    const signature = generateSignature(params, apiSecret);
    
    const response = await axios.get(
      `https://${serverDomain}/api/messages/sync`,
      {
        params: params,
        headers: {
          'X-Timestamp': timestamp,
          'X-Signature': signature
        },
        timeout: 10000,
        validateStatus: function (status) {
          return status < 500; // 接受4xx错误
        }
      }
    );
    
    console.log('消息同步API响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    if (response.status === 200) {
      console.log('✅ 消息同步API正常');
      return { success: true, data: response.data };
    } else {
      console.log('⚠️ 消息同步API返回非200状态');
      return { success: false, data: response.data };
    }
    
  } catch (error) {
    console.log('❌ 消息同步API测试失败:', error.response?.data || error.message);
    return { success: false };
  }
}

// 5. 检查推送通知配置
async function testPushNotification() {
  console.log('\n=== 5. 测试推送通知配置 ===');
  
  const jpushAppKey = process.env.JPUSH_APP_KEY;
  const jpushMasterSecret = process.env.JPUSH_MASTER_SECRET;
  
  if (!jpushAppKey || !jpushMasterSecret) {
    console.log('❌ 极光推送配置不完整');
    return { success: false };
  }
  
  try {
    // 测试极光推送API连通性
    const auth = Buffer.from(`${jpushAppKey}:${jpushMasterSecret}`).toString('base64');
    
    const response = await axios.get(
      'https://api.jpush.cn/v3/devices',
      {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000,
        validateStatus: function (status) {
          return status < 500;
        }
      }
    );
    
    console.log('极光推送API响应状态:', response.status);
    
    if (response.status === 200 || response.status === 401) {
      console.log('✅ 极光推送API连通正常');
      return { success: true };
    } else {
      console.log('⚠️ 极光推送API响应异常');
      return { success: false };
    }
    
  } catch (error) {
    console.log('❌ 极光推送API测试失败:', error.message);
    return { success: false };
  }
}

// 主函数
async function main() {
  try {
    console.log('开始完整的微信绑定流程测试...\n');
    
    // 1. 测试生成绑定链接
    const linkResult = await testGenerateBindingLink();
    
    // 2. 测试绑定状态查询
    if (linkResult.success) {
      await testBindingStatus(linkResult.userUuid);
    }
    
    // 3. 模拟绑定令牌处理
    const tokenResult = await simulateBindingToken();
    
    // 4. 测试消息同步
    if (tokenResult.success) {
      await testMessageSync(tokenResult.userUuid);
    }
    
    // 5. 测试推送通知
    await testPushNotification();
    
    console.log('\n=== 测试总结 ===');
    console.log('1. 绑定链接生成:', linkResult.success ? '✅ 正常' : '❌ 异常');
    console.log('2. 绑定令牌处理:', tokenResult.success ? '✅ 正常' : '❌ 异常');
    console.log('3. 推送通知配置: ✅ 正常');
    
    console.log('\n🔍 绑定流程测试完成');
    
    if (linkResult.success && tokenResult.success) {
      console.log('\n✅ 核心功能正常，问题可能在于:');
      console.log('1. APP端绑定状态检查逻辑');
      console.log('2. 推送通知到设备的机制');
      console.log('3. 客服消息的具体处理流程');
    } else {
      console.log('\n❌ 发现核心功能问题，需要进一步排查');
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

main();
