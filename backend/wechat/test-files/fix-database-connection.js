/**
 * 数据库连接修复脚本
 * 检查并修复数据库连接问题
 */

require('dotenv').config();
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

console.log('🔧 开始修复数据库连接问题...\n');

// 1. 检查MySQL服务状态
async function checkMySQLService() {
  console.log('=== 1. 检查MySQL服务状态 ===');
  
  try {
    // 检查MySQL进程
    const { stdout } = await execAsync('ps aux | grep mysql | grep -v grep');
    if (stdout.trim()) {
      console.log('✅ MySQL进程正在运行:');
      console.log(stdout.trim());
      return true;
    } else {
      console.log('❌ MySQL进程未运行');
      return false;
    }
  } catch (error) {
    console.log('❌ 无法检查MySQL进程状态');
    return false;
  }
}

// 2. 尝试启动MySQL服务
async function startMySQLService() {
  console.log('\n=== 2. 尝试启动MySQL服务 ===');
  
  const startCommands = [
    'brew services start mysql',
    'sudo systemctl start mysql',
    'sudo service mysql start',
    '/usr/local/mysql/support-files/mysql.server start'
  ];
  
  for (const command of startCommands) {
    try {
      console.log(`尝试执行: ${command}`);
      const { stdout, stderr } = await execAsync(command);
      if (stdout) console.log('输出:', stdout.trim());
      if (stderr) console.log('错误:', stderr.trim());
      
      // 等待2秒后检查服务状态
      await new Promise(resolve => setTimeout(resolve, 2000));
      const isRunning = await checkMySQLService();
      if (isRunning) {
        console.log('✅ MySQL服务启动成功');
        return true;
      }
    } catch (error) {
      console.log(`❌ 命令执行失败: ${error.message}`);
    }
  }
  
  console.log('❌ 所有启动命令都失败了');
  return false;
}

// 3. 检查MySQL安装
async function checkMySQLInstallation() {
  console.log('\n=== 3. 检查MySQL安装 ===');
  
  const checkCommands = [
    'mysql --version',
    'brew list mysql',
    'which mysql',
    'ls -la /usr/local/mysql',
    'ls -la /opt/homebrew/bin/mysql'
  ];
  
  for (const command of checkCommands) {
    try {
      const { stdout } = await execAsync(command);
      if (stdout.trim()) {
        console.log(`✅ ${command}:`);
        console.log(stdout.trim());
      }
    } catch (error) {
      console.log(`❌ ${command}: 未找到`);
    }
  }
}

// 4. 提供安装建议
function provideSolutions() {
  console.log('\n=== 4. 解决方案建议 ===');
  
  console.log('如果MySQL未安装，请选择以下方案之一：');
  console.log('');
  console.log('方案1: 使用Homebrew安装MySQL');
  console.log('  brew install mysql');
  console.log('  brew services start mysql');
  console.log('');
  console.log('方案2: 使用Docker运行MySQL');
  console.log('  docker run --name mysql-gongzhimall \\');
  console.log('    -e MYSQL_ROOT_PASSWORD=root123 \\');
  console.log('    -e MYSQL_DATABASE=gongzhimall_wechat \\');
  console.log('    -e MYSQL_USER=wechat_user \\');
  console.log('    -e MYSQL_PASSWORD=wechat@gongzhimall123 \\');
  console.log('    -p 3306:3306 -d mysql:8.0');
  console.log('');
  console.log('方案3: 修改配置使用远程数据库');
  console.log('  编辑 .env 文件，修改 MYSQL_HOST 为远程数据库地址');
  console.log('');
  console.log('方案4: 使用SQLite作为本地开发数据库');
  console.log('  修改数据库配置使用SQLite（需要代码调整）');
}

// 5. 测试数据库连接
async function testDatabaseConnection() {
  console.log('\n=== 5. 测试数据库连接 ===');
  
  try {
    const db = require('../data/database/core');
    const isConnected = await db.checkConnection();
    
    if (isConnected) {
      console.log('✅ 数据库连接测试成功');
      
      // 测试基本查询
      try {
        const result = await db.query('SELECT NOW() as current_time');
        console.log('✅ 数据库查询测试成功:', result[0]);
      } catch (queryError) {
        console.log('❌ 数据库查询测试失败:', queryError.message);
      }
      
      return true;
    } else {
      console.log('❌ 数据库连接测试失败');
      return false;
    }
  } catch (error) {
    console.log('❌ 数据库连接测试异常:', error.message);
    return false;
  }
}

// 主函数
async function main() {
  try {
    // 检查MySQL服务状态
    const isRunning = await checkMySQLService();
    
    if (!isRunning) {
      // 尝试启动MySQL服务
      const startSuccess = await startMySQLService();
      
      if (!startSuccess) {
        // 检查MySQL安装
        await checkMySQLInstallation();
        // 提供解决方案
        provideSolutions();
        return;
      }
    }
    
    // 测试数据库连接
    const connectionSuccess = await testDatabaseConnection();
    
    if (connectionSuccess) {
      console.log('\n🎉 数据库连接修复成功！');
      console.log('现在可以重新测试微信转发功能了。');
    } else {
      console.log('\n❌ 数据库连接仍然失败');
      console.log('请检查数据库配置和权限设置。');
    }
    
  } catch (error) {
    console.error('修复过程中发生错误:', error);
  }
}

main();
