#!/usr/bin/env node

/**
 * 文件清理脚本
 * 用于手动或定时清理过期的缓存文件
 */

require('dotenv').config();
const fileService = require('../service/fileService');

async function main() {
  try {
    console.log('🧹 开始执行文件清理任务...');
    console.log(`📁 缓存目录: ${process.env.FILE_STORAGE_PATH || '/var/www/cache'}`);
    
    // 获取清理前的统计信息
    const beforeStats = await fileService.getCacheStats();
    console.log(`📊 清理前统计: ${beforeStats.fileCount} 个文件, ${beforeStats.totalSizeMB} MB`);
    
    // 执行清理
    const deletedCount = await fileService.cleanupExpiredFiles();
    
    // 获取清理后的统计信息
    const afterStats = await fileService.getCacheStats();
    console.log(`📊 清理后统计: ${afterStats.fileCount} 个文件, ${afterStats.totalSizeMB} MB`);
    
    console.log(`✅ 文件清理完成！删除了 ${deletedCount} 个过期文件`);
    console.log(`💾 释放空间: ${(beforeStats.totalSizeMB - afterStats.totalSizeMB).toFixed(2)} MB`);
    
  } catch (error) {
    console.error('❌ 文件清理失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = main;