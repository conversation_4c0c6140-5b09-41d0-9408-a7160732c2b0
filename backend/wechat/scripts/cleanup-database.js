#!/usr/bin/env node

/**
 * 数据库清理脚本
 * 用于清理过期的消息记录和文件引用
 */

require('dotenv').config();
const { query } = require('../data/database');

async function cleanupExpiredRecords() {
  try {
    console.log('🗄️ 开始清理过期的消息记录...');
    
    // 实现真正的“阅后即焚”：立即清理所有已下载的记录，移除1天缓存期
    const downloadedQuery = `
      DELETE FROM wechat_message_logs 
      WHERE downloaded = true
    `;
    
    const downloadedResult = await query(downloadedQuery);
    console.log(`🗑️ 清理已下载记录: ${downloadedResult.affectedRows} 条`);
    
    // 清理已过期的文件记录 (所有消息类型都遵循此逻辑)
    const expiredQuery = `
      DELETE FROM wechat_message_logs 
      WHERE file_expires_at IS NOT NULL 
      AND file_expires_at < NOW()
    `;
    
    const expiredResult = await query(expiredQuery);
    console.log(`🗑️ 清理过期文件记录: ${expiredResult.affectedRows} 条`);
    
    /*
    // 逻辑修正：根据用户要求，所有消息记录（包括普通消息）与文件处理逻辑统一，不再按30天清理。
    const oldMessagesQuery = \`
      DELETE FROM wechat_message_logs 
      WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
    \`;
    
    const oldMessagesResult = await query(oldMessagesQuery);
    console.log(\`🗑️ 清理30天前消息: \${oldMessagesResult.affectedRows} 条\`);
    */
    
    /* 
    // 暂时禁用：根据用户要求，保留设备绑定记录用于投资人演示
    const inactiveDevicesQuery = \`
      DELETE FROM user_device_bindings 
      WHERE last_active_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
    \`;
    
    const inactiveDevicesResult = await query(inactiveDevicesQuery);
    console.log(\`🗑️ 清理无效设备绑定: \${inactiveDevicesResult.affectedRows} 条\`);
    */
   
    // 为保持结构，定义默认返回对象
    const oldMessagesResult = { affectedRows: 0 };
    const inactiveDevicesResult = { affectedRows: 0 };
    
    const totalCleaned = downloadedResult.affectedRows + 
                        expiredResult.affectedRows + 
                        oldMessagesResult.affectedRows + 
                        inactiveDevicesResult.affectedRows;
    
    console.log(`✅ 数据库清理完成！总计清理 ${totalCleaned} 条记录`);
    
    return totalCleaned;
    
  } catch (error) {
    console.error('❌ 数据库清理失败:', error);
    throw error;
  }
}

async function optimizeDatabase() {
  try {
    console.log('🔧 开始优化数据库表...');
    
    const tables = ['wechat_message_logs', 'user_device_bindings', 'wechat_bindings'];
    
    for (const table of tables) {
      await query(`OPTIMIZE TABLE ${table}`);
      console.log(`✅ 优化表: ${table}`);
    }
    
    console.log('🚀 数据库优化完成！');
    
  } catch (error) {
    console.error('❌ 数据库优化失败:', error);
    throw error;
  }
}

async function main() {
  try {
    console.log('🗄️ 开始执行数据库清理任务...');
    
    // 清理过期记录
    const cleanedCount = await cleanupExpiredRecords();
    
    // 如果清理了记录，则优化数据库
    if (cleanedCount > 0) {
      await optimizeDatabase();
    }
    
    console.log('✅ 数据库维护任务完成！');
    
  } catch (error) {
    console.error('❌ 数据库清理任务失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { cleanupExpiredRecords, optimizeDatabase, main }; 