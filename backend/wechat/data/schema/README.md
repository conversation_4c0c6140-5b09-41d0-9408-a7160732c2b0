# 数据库架构统一管理方案

## 🎯 目标

- **单一权威源**：所有数据库结构变更都通过一个地方管理
- **避免重复维护**：消除多个SQL文件的不一致问题
- **清晰版本控制**：明确的架构演进历史
- **简化部署**：减少人工维护错误

## 📁 文件结构

```
backend/wechat/data/schema/
├── README.md                    # 本文档
├── core/                        # 核心表结构
│   ├── 01_users.sql            # 用户相关表
│   ├── 02_bindings.sql         # 绑定相关表
│   ├── 03_messages.sql         # 消息相关表
│   └── 04_system.sql           # 系统相关表
├── features/                    # 功能特性表
│   ├── 01_cache.sql            # 缓存相关表
│   └── 02_views.sql            # 视图定义
├── environments/                # 环境特定配置
│   ├── production.sql          # 生产环境特定配置
│   └── development.sql         # 开发环境特定配置
├── scripts/                     # 构建脚本
│   └── build-schema.js         # 架构构建脚本
└── build/                      # 构建输出
    └── schema.sql              # 最终生成的完整架构文件
```

## 🚀 使用方法

### 1. 构建架构

```bash
# 构建完整架构（包含所有功能）
cd backend/wechat/data/schema
node scripts/build-schema.js

# 构建特定环境架构
node scripts/build-schema.js --env=production
```

### 2. 应用架构

```bash
# 应用生成的架构文件
mysql -u username -p < build/schema.sql
```

### 3. 验证部署

```bash
# 检查表是否创建成功
mysql -u username -p -e "USE gongzhimall_wechat; SHOW TABLES;"
```

## 🔧 维护流程

### 添加新表

1. 在 `core/` 目录下创建新的SQL文件
2. 按功能命名，如 `05_notifications.sql`
3. 运行构建脚本生成新的架构文件

### 修改现有表

1. 直接修改 `core/` 目录下对应的SQL文件
2. 运行构建脚本重新生成架构文件
3. 应用新的架构文件到数据库

### 环境特定配置

- 开发环境：`environments/development.sql`
- 生产环境：`environments/production.sql`

## 📊 解决的问题

### 原有问题

- **重复维护**：`init_wechat_tables.sql` 和 `database.sql` 有大量重复内容
- **不一致风险**：两个文件容易产生差异，导致部署问题
- **缺乏版本控制**：没有清晰的架构演进历史
- **环境适配困难**：不同环境需要手动调整配置

### 解决方案

- **单一权威源**：所有表结构在一个地方维护
- **模块化管理**：按功能模块拆分，便于维护
- **自动化构建**：通过脚本自动生成最终SQL文件
- **环境适配**：支持不同环境的特定配置

## 🔍 外部调用影响分析

### 无影响的调用

- **数据库连接**：连接字符串和数据库名称不变
- **表名**：所有表名保持完全一致
- **字段名**：核心字段名称不变
- **索引名**：主要索引名称保持一致

### 需要检查的调用

- **新增字段**：检查是否有代码依赖新增的字段
- **字段类型变更**：确认没有代码依赖字段类型
- **新增表**：确认没有代码直接引用新增的表

### 兼容性保证

- 保持所有现有表名和字段名不变
- 新增的字段都有默认值或允许NULL
- 新增的表不影响现有功能

## 📋 检查清单

### 部署前检查

- [ ] 运行构建脚本生成架构文件
- [ ] 检查生成的SQL文件语法正确
- [ ] 确认所有必需的表和字段都存在
- [ ] 验证环境特定配置正确

### 部署后验证

- [ ] 所有表创建成功
- [ ] 索引创建正确
- [ ] 视图创建成功
- [ ] 配置数据插入正确
