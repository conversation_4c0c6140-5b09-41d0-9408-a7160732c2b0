-- ==================== 绑定相关表 ====================
-- 版本：v1.1.0
-- 描述：微信绑定关系表，支持未绑定用户

-- 微信绑定表（支持未绑定用户）
CREATE TABLE IF NOT EXISTS wechat_bindings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    external_userid VARCHAR(128) NULL COMMENT '企业微信外部用户ID（未绑定时为NULL）',
    binding_status ENUM('active', 'inactive', 'pending') DEFAULT 'pending' COMMENT '绑定状态',
    binding_token VARCHAR(255) NULL COMMENT '绑定Token',
    token_expires_at TIMESTAMP NULL COMMENT 'Token过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_user_uuid (user_uuid),
    UNIQUE KEY uk_external_userid_not_null (external_userid),
    INDEX idx_binding_status (binding_status),
    INDEX idx_binding_token (binding_token),
    INDEX idx_user_binding_status (user_uuid, binding_status),
    INDEX idx_created_at (created_at),

    -- 外键约束
    FOREIGN KEY fk_wechat_user (user_uuid) REFERENCES app_users(user_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信绑定关系表（支持未绑定用户）';

-- 同步配置表
CREATE TABLE IF NOT EXISTS wechat_sync_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    sync_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用同步',
    sync_types JSON COMMENT '同步的消息类型配置',
    sync_frequency INT DEFAULT 300 COMMENT '同步频率（秒）',
    last_sync_time TIMESTAMP NULL COMMENT '最后同步时间',
    sync_status ENUM('active', 'paused', 'error') DEFAULT 'active' COMMENT '同步状态',
    error_count INT DEFAULT 0 COMMENT '错误计数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_user_uuid (user_uuid),
    INDEX idx_sync_enabled (sync_enabled),
    INDEX idx_sync_status (sync_status),
    INDEX idx_last_sync_time (last_sync_time),
    
    -- 外键约束
    FOREIGN KEY fk_sync_user (user_uuid) REFERENCES wechat_bindings(user_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信同步配置表'; 