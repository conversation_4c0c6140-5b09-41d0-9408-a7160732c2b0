-- ==================== 系统相关表 ====================
-- 版本：v1.1.0
-- 描述：系统日志和配置相关表结构

-- 系统日志表（适配轻量服务器环境，使用简单的年份分区）
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT AUTO_INCREMENT COMMENT '主键ID',
    log_level ENUM('ERROR', 'WARN', 'INFO', 'DEBUG') NOT NULL COMMENT '日志级别',
    message TEXT NOT NULL COMMENT '日志消息',
    module VARCHAR(100) COMMENT '模块名称',
    function_name VARCHAR(100) COMMENT '服务名称',
    request_id VARCHAR(100) COMMENT '请求ID（服务器RequestId）',
    user_uuid VARCHAR(36) COMMENT '用户UUID',
    external_userid VARCHAR(128) COMMENT '企业微信用户ID',
    error_code VARCHAR(50) COMMENT '错误代码',
    error_stack TEXT COMMENT '错误堆栈',
    metadata JSON COMMENT '额外的元数据',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT 'User-Agent',
    execution_time INT COMMENT '执行时间（毫秒）',
    memory_usage INT COMMENT '内存使用量（MB）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    log_year INT NOT NULL DEFAULT (YEAR(CURRENT_TIMESTAMP)) COMMENT '日志年份（用于分区）',
    
    -- 复合主键，包含分区字段
    PRIMARY KEY (id, log_year),
    
    -- 索引
    INDEX idx_log_level (log_level),
    INDEX idx_module (module),
    INDEX idx_function_name (function_name),
    INDEX idx_request_id (request_id),
    INDEX idx_user_uuid (user_uuid),
    INDEX idx_external_userid (external_userid),
    INDEX idx_error_code (error_code),
    INDEX idx_created_at (created_at),
    INDEX idx_log_year (log_year)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表（适配轻量服务器环境）';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入初始系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('wechat_webhook_enabled', 'true', 'boolean', '微信Webhook是否启用'),
('max_sync_attempts', '3', 'number', '最大同步重试次数'),
('token_expire_minutes', '10', 'number', '绑定Token过期时间（分钟）'),
('sync_batch_size', '50', 'number', '批量同步消息数量'),
('webhook_verify_signature', 'true', 'boolean', '是否验证Webhook签名'),
('media_id_expire_hours', '72', 'number', '媒体文件ID有效期（小时）')
ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    updated_at = CURRENT_TIMESTAMP; 