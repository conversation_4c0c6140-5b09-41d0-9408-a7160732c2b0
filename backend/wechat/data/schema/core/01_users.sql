-- ==================== 用户相关表 ====================
-- 版本：v1.1.0
-- 描述：用户注册和设备绑定相关表结构

-- 用户注册表（设备注册时创建，独立于微信绑定）
CREATE TABLE IF NOT EXISTS app_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    first_device_id VARCHAR(128) COMMENT '首次注册的设备ID',
    device_count INT DEFAULT 0 COMMENT '设备数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_user_uuid (user_uuid),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用用户表';

-- 用户设备绑定表（支持跨端增量同步）
CREATE TABLE IF NOT EXISTS user_device_bindings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    device_id VARCHAR(128) NOT NULL COMMENT '设备唯一标识',
    device_name VARCHAR(255) COMMENT '设备名称',
    platform VARCHAR(50) NOT NULL COMMENT '设备平台（ios, android, desktop, web, harmonyos, etc.）',
    platform_version VARCHAR(50) COMMENT '平台版本号',
    app_version VARCHAR(50) COMMENT '应用版本',
    device_model VARCHAR(100) COMMENT '设备型号',
    last_synced_id BIGINT DEFAULT 0 COMMENT '该设备已同步的最新消息ID',
    last_active_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
    device_status ENUM('active', 'inactive', 'blocked') DEFAULT 'active' COMMENT '设备状态',
    push_token VARCHAR(255) COMMENT 'JPush推送Token',
    push_provider VARCHAR(50) DEFAULT 'jpush' COMMENT '推送服务提供商（jpush, apns, fcm, hms等）',
    device_fingerprint VARCHAR(255) COMMENT '设备指纹（用于安全验证）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_user_device (user_uuid, device_id),
    INDEX idx_user_uuid (user_uuid),
    INDEX idx_device_id (device_id),
    INDEX idx_platform (platform),
    INDEX idx_last_synced_id (last_synced_id),
    INDEX idx_last_active_at (last_active_at),
    INDEX idx_device_status (device_status),
    INDEX idx_push_provider (push_provider),

    -- 外键约束
    FOREIGN KEY fk_device_user (user_uuid) REFERENCES app_users(user_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户设备绑定表（支持跨端增量同步）'; 