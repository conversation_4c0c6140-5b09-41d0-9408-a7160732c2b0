-- ==================== 消息相关表 ====================
-- 版本：v1.1.0
-- 描述：微信消息索引表，支持所有消息类型和增强文件处理

-- 微信消息索引表（完整版本，包含所有字段）
CREATE TABLE IF NOT EXISTS wechat_message_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    external_userid VARCHAR(128) NOT NULL COMMENT '企业微信外部用户ID',
    wechat_message_id VARCHAR(128) NOT NULL COMMENT '微信消息ID（用于去重）',
    message_type ENUM('text', 'image', 'voice', 'video', 'file', 'location', 'link', 'event', 'unknown') NOT NULL COMMENT '消息类型',
    
    -- 文本消息内容
    content TEXT NULL COMMENT '文本消息内容',
    
    -- 文件缓存机制
    file_name VARCHAR(500) NULL COMMENT '文件名称',
    file_path VARCHAR(1000) NULL COMMENT '加密文件在服务器上的存储路径',
    file_size BIGINT NULL COMMENT '文件大小（字节）',
    content_type VARCHAR(200) NULL COMMENT '文件MIME类型',
    
    -- 文件格式识别字段
    file_extension VARCHAR(20) NULL COMMENT '文件扩展名（如jpg、pdf、docx）',
    mime_type VARCHAR(200) NULL COMMENT '检测到的MIME类型',
    original_mime_type VARCHAR(200) NULL COMMENT '原始MIME类型（用于对比）',
    file_magic_detected BOOLEAN DEFAULT FALSE COMMENT '是否通过文件魔数检测到类型',
    media_id VARCHAR(255) NULL COMMENT '企业微信媒体ID',
    media_id_expires_at TIMESTAMP NULL COMMENT '媒体ID过期时间（72小时）',
    
    -- 位置消息字段
    location_x DECIMAL(10, 6) NULL COMMENT '位置纬度',
    location_y DECIMAL(10, 6) NULL COMMENT '位置经度',
    location_scale INT NULL COMMENT '地图缩放级别',
    location_label VARCHAR(500) NULL COMMENT '位置标签描述',
    
    -- 链接消息字段
    link_title VARCHAR(500) NULL COMMENT '链接标题',
    link_description TEXT NULL COMMENT '链接描述',
    link_url TEXT NULL COMMENT '链接地址',
    link_pic_url TEXT NULL COMMENT '链接缩略图地址',
    
    -- 事件消息字段
    event_type VARCHAR(100) NULL COMMENT '事件类型',
    event_key VARCHAR(255) NULL COMMENT '事件键值',
    event_data JSON NULL COMMENT '事件数据',
    
    -- 下载管理
    download_token TEXT NULL COMMENT '下载令牌（JWT）',
    download_expires_at TIMESTAMP NULL COMMENT '下载链接过期时间（24小时）',
    file_expires_at TIMESTAMP NULL COMMENT '文件过期时间（3天后删除）',
    downloaded BOOLEAN DEFAULT FALSE COMMENT '是否已被用户下载（阅后即焚标记）',
    downloaded_at TIMESTAMP NULL COMMENT '下载时间',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    last_download_at TIMESTAMP NULL COMMENT '最后下载时间',
    download_user_agent TEXT NULL COMMENT '下载时的User-Agent',
    
    -- 文件处理状态
    processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '文件处理状态',
    processing_error TEXT NULL COMMENT '处理错误信息',
    file_hash VARCHAR(64) NULL COMMENT '文件SHA256哈希值（用于去重）',
    
    -- 同步状态
    metadata JSON COMMENT '消息元数据（文件名、大小等非隐私信息）',
    sync_status ENUM('pending', 'synced', 'failed') DEFAULT 'pending' COMMENT '同步状态',
    sync_attempts INT DEFAULT 0 COMMENT '同步尝试次数',
    last_sync_at TIMESTAMP NULL COMMENT '最后同步时间',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_user_uuid (user_uuid),
    INDEX idx_external_userid (external_userid),
    INDEX idx_wechat_message_id (wechat_message_id),
    INDEX idx_message_type (message_type),
    INDEX idx_sync_status (sync_status),
    INDEX idx_download_expires (download_expires_at),
    INDEX idx_file_expires (file_expires_at),
    INDEX idx_downloaded (downloaded),
    INDEX idx_created_at (created_at),
    
    -- 文件类型相关索引
    INDEX idx_file_extension (file_extension),
    INDEX idx_mime_type (mime_type),
    INDEX idx_media_id (media_id),
    INDEX idx_media_id_expires (media_id_expires_at),
    INDEX idx_location_coords (location_x, location_y),
    INDEX idx_event_type (event_type),
    INDEX idx_processing_status (processing_status),
    INDEX idx_file_hash (file_hash),
    INDEX idx_download_count (download_count),
    
    -- 唯一约束防止消息重复
    UNIQUE KEY uk_wechat_message_id (wechat_message_id),
    
    -- 外键约束
    FOREIGN KEY fk_binding (user_uuid) REFERENCES wechat_bindings(user_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信消息索引表（完整版本，支持所有消息类型）'; 