-- ==================== 缓存相关表 ====================
-- 版本：v1.1.0
-- 描述：缓存功能相关表结构

-- access_token缓存表（云函数环境优化）
CREATE TABLE IF NOT EXISTS access_token_cache (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    corp_id VARCHAR(255) NOT NULL UNIQUE COMMENT '企业ID',
    access_token TEXT NOT NULL COMMENT '访问令牌',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    is_refreshing TINYINT(1) DEFAULT 0 COMMENT '是否正在刷新（并发控制）',
    refresh_started_at DATETIME NULL COMMENT '刷新开始时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_corp_id (corp_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_corp_refreshing (corp_id, is_refreshing)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='access_token缓存表（云函数环境优化）';

-- 客服链接缓存表
CREATE TABLE IF NOT EXISTS kf_link_cache (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    open_kfid VARCHAR(255) NOT NULL COMMENT '客服账号ID',
    scene VARCHAR(64) NOT NULL COMMENT '场景值',
    base_url TEXT NOT NULL COMMENT '客服链接',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引和约束
    UNIQUE KEY unique_kf_scene (open_kfid, scene),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服链接缓存表';

-- API调用统计表
CREATE TABLE IF NOT EXISTS token_api_calls (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    corp_id VARCHAR(255) NOT NULL COMMENT '企业ID',
    call_date DATE NOT NULL COMMENT '调用日期',
    call_count INT DEFAULT 1 COMMENT '调用次数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引和约束
    UNIQUE KEY unique_corp_date (corp_id, call_date),
    INDEX idx_call_date (call_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API调用统计表';

-- 客服同步游标表
CREATE TABLE IF NOT EXISTS kf_sync_cursors (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    open_kfid VARCHAR(255) NOT NULL UNIQUE COMMENT '客服账号ID',
    last_cursor VARCHAR(255) COMMENT '最后同步游标',
    last_sync_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后同步时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_open_kfid (open_kfid),
    INDEX idx_last_sync_at (last_sync_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服同步游标表'; 