-- ==================== 视图定义 ====================
-- 版本：v1.1.0
-- 描述：统计和监控相关的视图

-- 创建视图：活跃绑定统计
CREATE OR REPLACE VIEW v_active_bindings AS
SELECT 
    DATE(created_at) as binding_date,
    COUNT(*) as daily_bindings,
    COUNT(CASE WHEN binding_status = 'active' THEN 1 END) as active_bindings,
    COUNT(CASE WHEN binding_status = 'pending' THEN 1 END) as pending_bindings
FROM wechat_bindings 
WHERE created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY binding_date DESC;

-- 创建视图：消息同步统计
CREATE OR REPLACE VIEW v_sync_statistics AS
SELECT 
    DATE(created_at) as sync_date,
    message_type,
    COUNT(*) as total_messages,
    COUNT(CASE WHEN sync_status = 'synced' THEN 1 END) as synced_messages,
    COUNT(CASE WHEN sync_status = 'failed' THEN 1 END) as failed_messages,
    AVG(sync_attempts) as avg_attempts
FROM wechat_message_logs 
WHERE created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)
GROUP BY DATE(created_at), message_type
ORDER BY sync_date DESC, message_type;

-- 创建视图：文件处理统计
CREATE OR REPLACE VIEW v_file_processing_stats AS
SELECT 
    DATE(created_at) as processing_date,
    message_type,
    processing_status,
    COUNT(*) as total_files,
    AVG(file_size) as avg_file_size,
    COUNT(CASE WHEN file_magic_detected = TRUE THEN 1 END) as magic_detected_count
FROM wechat_message_logs 
WHERE message_type IN ('image', 'voice', 'video', 'file')
    AND created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)
GROUP BY DATE(created_at), message_type, processing_status
ORDER BY processing_date DESC, message_type;

-- 创建视图：下载统计
CREATE OR REPLACE VIEW v_download_stats AS
SELECT 
    DATE(created_at) as download_date,
    message_type,
    COUNT(*) as total_downloadable,
    COUNT(CASE WHEN downloaded = TRUE THEN 1 END) as downloaded_count,
    AVG(download_count) as avg_downloads,
    MAX(download_count) as max_downloads
FROM wechat_message_logs 
WHERE message_type IN ('image', 'voice', 'video', 'file')
    AND created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)
GROUP BY DATE(created_at), message_type
ORDER BY download_date DESC, message_type; 