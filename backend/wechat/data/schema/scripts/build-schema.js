#!/usr/bin/env node

/**
 * 数据库架构构建脚本
 * 版本：v1.1.0
 * 描述：自动构建统一的数据库架构文件
 */

const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
  coreDir: path.join(__dirname, '../core'),
  featuresDir: path.join(__dirname, '../features'),
  environmentsDir: path.join(__dirname, '../environments'),
  buildDir: path.join(__dirname, '../build'),
  outputFile: 'schema.sql'
};

/**
 * 读取SQL文件内容
 */
function readSqlFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`❌ 读取文件失败: ${filePath}`, error.message);
    return '';
  }
}

/**
 * 获取目录下的所有SQL文件（按文件名排序）
 */
function getSqlFiles(dirPath) {
  try {
    if (!fs.existsSync(dirPath)) {
      console.warn(`⚠️ 目录不存在: ${dirPath}`);
      return [];
    }

    const files = fs.readdirSync(dirPath)
      .filter(file => file.endsWith('.sql'))
      .sort(); // 按文件名排序

    return files.map(file => path.join(dirPath, file));
  } catch (error) {
    console.error(`❌ 读取目录失败: ${dirPath}`, error.message);
    return [];
  }
}

/**
 * 构建完整的SQL架构
 */
function buildSchema(environment = 'development') {
  console.log('🚀 开始构建数据库架构...');
  console.log(`📁 环境: ${environment}`);

  let schema = `-- ==================== 微信转发服务数据库架构 ====================\n`;
  schema += `-- 版本：v1.1.0\n`;
  schema += `-- 构建时间：${new Date().toISOString()}\n`;
  schema += `-- 环境：${environment}\n`;
  schema += `-- 描述：统一管理的数据库架构，支持所有功能特性\n\n`;

  // 创建数据库
  schema += `-- 创建数据库（如果不存在）\n`;
  schema += `CREATE DATABASE IF NOT EXISTS gongzhimall_wechat \n`;
  schema += `CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n\n`;
  schema += `USE gongzhimall_wechat;\n\n`;

  // 读取核心表结构
  console.log('📋 加载核心表结构...');
  const coreFiles = getSqlFiles(CONFIG.coreDir);
  for (const file of coreFiles) {
    console.log(`  - ${path.basename(file)}`);
    schema += readSqlFile(file) + '\n\n';
  }

  // 读取功能特性表
  console.log('🔧 加载功能特性表...');
  const featureFiles = getSqlFiles(CONFIG.featuresDir);
  for (const file of featureFiles) {
    console.log(`  - ${path.basename(file)}`);
    schema += readSqlFile(file) + '\n\n';
  }

  // 读取环境特定配置
  const envFile = path.join(CONFIG.environmentsDir, `${environment}.sql`);
  if (fs.existsSync(envFile)) {
    console.log(`⚙️ 应用环境配置: ${environment}.sql`);
    schema += readSqlFile(envFile) + '\n\n';
  } else {
    console.log(`⚠️ 环境配置文件不存在: ${envFile}`);
  }

  // 添加提交语句
  schema += `COMMIT;\n`;

  return schema;
}

/**
 * 保存构建结果
 */
function saveSchema(schema) {
  try {
    // 确保构建目录存在
    if (!fs.existsSync(CONFIG.buildDir)) {
      fs.mkdirSync(CONFIG.buildDir, { recursive: true });
    }

    const outputPath = path.join(CONFIG.buildDir, CONFIG.outputFile);
    fs.writeFileSync(outputPath, schema, 'utf8');

    console.log(`✅ 架构文件已保存: ${outputPath}`);
    console.log(`📊 文件大小: ${(schema.length / 1024).toFixed(2)} KB`);

    return outputPath;
  } catch (error) {
    console.error('❌ 保存架构文件失败:', error.message);
    throw error;
  }
}

/**
 * 验证构建结果
 */
function validateSchema(schema) {
  const checks = [
    { name: '数据库创建', pattern: /CREATE DATABASE/ },
    { name: '用户表', pattern: /CREATE TABLE.*app_users/ },
    { name: '绑定表', pattern: /CREATE TABLE.*wechat_bindings/ },
    { name: '消息表', pattern: /CREATE TABLE.*wechat_message_logs/ },
    { name: '系统表', pattern: /CREATE TABLE.*system_logs/ },
    { name: '缓存表', pattern: /CREATE TABLE.*access_token_cache/ },
    { name: '视图', pattern: /CREATE OR REPLACE VIEW/ }
  ];

  console.log('🔍 验证架构完整性...');
  let passed = 0;
  let total = checks.length;

  for (const check of checks) {
    if (check.pattern.test(schema)) {
      console.log(`  ✅ ${check.name}`);
      passed++;
    } else {
      console.log(`  ❌ ${check.name} - 未找到`);
    }
  }

  console.log(`\n📊 验证结果: ${passed}/${total} 通过`);
  return passed === total;
}

/**
 * 主函数
 */
function main() {
  try {
    // 解析命令行参数
    const args = process.argv.slice(2);
    const env = args.find(arg => arg.startsWith('--env='))?.split('=')[1] || 'development';

    console.log('🔧 数据库架构构建工具');
    console.log('=====================================\n');

    // 构建架构
    const schema = buildSchema(env);

    // 验证架构
    const isValid = validateSchema(schema);

    if (isValid) {
      // 保存架构
      const outputPath = saveSchema(schema);
      console.log(`\n🎉 架构构建成功！`);
      console.log(`📁 输出文件: ${outputPath}`);
    } else {
      console.error('\n❌ 架构验证失败，请检查构建过程');
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  buildSchema,
  validateSchema,
  saveSchema
}; 