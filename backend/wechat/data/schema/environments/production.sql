-- ==================== 生产环境配置 ====================
-- 版本：v1.1.0
-- 描述：生产环境特定的数据库配置

-- 生产环境：优化性能配置
INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('debug_mode', 'false', 'boolean', '调试模式开关'),
('log_level', 'WARN', 'string', '日志级别'),
('enable_sql_logging', 'false', 'boolean', '启用SQL日志记录'),
('test_mode', 'false', 'boolean', '测试模式开关'),
('max_connections', '100', 'number', '最大数据库连接数'),
('connection_timeout', '30', 'number', '连接超时时间（秒）'),
('query_timeout', '60', 'number', '查询超时时间（秒）')
ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    updated_at = CURRENT_TIMESTAMP;

-- 生产环境：创建性能优化索引
-- 注意：这些索引可能影响写入性能，请根据实际使用情况调整
-- 注意：MariaDB不支持带WHERE条件的CREATE INDEX，使用普通索引
CREATE INDEX IF NOT EXISTS idx_message_user_type ON wechat_message_logs(user_uuid, message_type);
CREATE INDEX IF NOT EXISTS idx_message_created_at ON wechat_message_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_binding_status_active ON wechat_bindings(binding_status);
CREATE INDEX IF NOT EXISTS idx_sync_status_pending ON wechat_message_logs(sync_status); 