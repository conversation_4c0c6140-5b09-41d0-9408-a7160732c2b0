-- ==================== 开发环境配置 ====================
-- 版本：v1.1.0
-- 描述：开发环境特定的数据库配置

-- 开发环境：启用调试日志
INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('debug_mode', 'true', 'boolean', '调试模式开关'),
('log_level', 'DEBUG', 'string', '日志级别'),
('enable_sql_logging', 'true', 'boolean', '启用SQL日志记录'),
('test_mode', 'true', 'boolean', '测试模式开关')
ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    updated_at = CURRENT_TIMESTAMP;

-- 开发环境：插入测试数据（可选）
-- 注意：这些数据仅用于开发测试，生产环境不应包含
INSERT IGNORE INTO app_users (user_uuid, first_device_id, device_count) VALUES
('test-user-001', 'test-device-001', 1),
('test-user-002', 'test-device-002', 1);

INSERT IGNORE INTO wechat_bindings (user_uuid, external_userid, binding_status) VALUES
('test-user-001', 'test-external-001', 'active'),
('test-user-002', 'test-external-002', 'pending'); 