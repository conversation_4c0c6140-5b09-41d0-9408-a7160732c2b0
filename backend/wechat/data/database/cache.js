/**
 * 缓存管理模块
 * 包含access_token、kf_link等缓存管理功能
 */

const { query } = require('./core');

/**
 * 获取或创建access_token缓存表
 */
const ensureAccessTokenCacheTable = async () => {
  const createTableQuery = `
    CREATE TABLE IF NOT EXISTS access_token_cache (
      id INT AUTO_INCREMENT PRIMARY KEY,
      corp_id VARCHAR(255) NOT NULL UNIQUE,
      access_token TEXT NOT NULL,
      expires_at DATETIME NOT NULL,
      is_refreshing TINYINT(1) DEFAULT 0,
      refresh_started_at DATETIME NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_corp_id (corp_id),
      INDEX idx_expires_at (expires_at),
      INDEX idx_corp_refreshing (corp_id, is_refreshing)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  await query(createTableQuery);
};

/**
 * 获取或创建客服链接缓存表
 */
const ensureKfLinkCacheTable = async () => {
  const createTableQuery = `
    CREATE TABLE IF NOT EXISTS kf_link_cache (
      id INT AUTO_INCREMENT PRIMARY KEY,
      open_kfid VARCHAR(255) NOT NULL,
      scene VARCHAR(64) NOT NULL,
      base_url TEXT NOT NULL,
      expires_at DATETIME NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      UNIQUE KEY unique_kf_scene (open_kfid, scene),
      INDEX idx_expires_at (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  await query(createTableQuery);
};

/**
 * 从数据库获取客服链接缓存
 */
const getKfLinkFromCache = async (openKfId, scene) => {
  try {
    await ensureKfLinkCacheTable();

    const cacheQuery = `
      SELECT base_url, expires_at
      FROM kf_link_cache
      WHERE open_kfid = ? AND scene = ? AND expires_at > NOW()
    `;

    const result = await query(cacheQuery, [openKfId, scene]);

    if (result.length > 0) {
      console.log('✅ 从缓存获取客服链接成功');
      return {
        success: true,
        url: result[0].base_url,
        cached: true
      };
    } else {
      console.log('ℹ️ 客服链接缓存未命中或已过期');
      return {
        success: false,
        message: '缓存未命中'
      };
    }
  } catch (error) {
    console.error('❌ 获取客服链接缓存失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 保存客服链接到缓存
 * 缓存有效期与access_token同步，确保链接有效性
 */
const saveKfLinkToCache = async (openKfId, scene, baseUrl, accessTokenExpiresAt) => {
  try {
    await ensureKfLinkCacheTable();

    // 客服链接的有效期应该与access_token同步，但提前5分钟过期以确保安全
    const expiresAt = new Date(accessTokenExpiresAt);
    expiresAt.setMinutes(expiresAt.getMinutes() - 5); // 提前5分钟过期

    // 确保不会设置过去的时间
    const now = new Date();
    if (expiresAt <= now) {
      console.warn('⚠️ access_token即将过期，不缓存客服链接');
      return { success: false, message: 'access_token即将过期' };
    }

    const insertQuery = `
      INSERT INTO kf_link_cache (open_kfid, scene, base_url, expires_at)
      VALUES (?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        base_url = VALUES(base_url),
        expires_at = VALUES(expires_at),
        updated_at = CURRENT_TIMESTAMP
    `;

    await query(insertQuery, [openKfId, scene, baseUrl, expiresAt]);
    console.log('✅ 客服链接已保存到缓存，过期时间:', expiresAt.toISOString());

    return { success: true };
  } catch (error) {
    console.error('❌ 保存客服链接缓存失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 尝试获取access_token刷新锁
 * 防止多个云函数实例同时刷新token
 */
const tryAcquireTokenRefreshLock = async (corpId) => {
  try {
    await ensureAccessTokenCacheTable();

    // 检查是否有其他实例正在刷新（超过2分钟的锁视为过期）
    const checkQuery = `
      SELECT is_refreshing, refresh_started_at
      FROM access_token_cache
      WHERE corp_id = ? AND is_refreshing = 1 AND refresh_started_at > DATE_SUB(NOW(), INTERVAL 2 MINUTE)
    `;

    const existingLock = await query(checkQuery, [corpId]);
    if (existingLock.length > 0) {
      console.log('⏳ 其他实例正在刷新access_token，等待中...');
      return { success: false, message: '其他实例正在刷新' };
    }

    // 尝试获取锁
    const lockQuery = `
      INSERT INTO access_token_cache (corp_id, access_token, expires_at, is_refreshing, refresh_started_at)
      VALUES (?, '', '1970-01-01 00:00:00', 1, NOW())
      ON DUPLICATE KEY UPDATE
        is_refreshing = 1,
        refresh_started_at = NOW()
    `;

    await query(lockQuery, [corpId]);
    console.log('🔒 获取access_token刷新锁成功');
    return { success: true };
  } catch (error) {
    console.error('❌ 获取access_token刷新锁失败:', error);
    return { success: false, message: error.message };
  }
};

/**
 * 释放access_token刷新锁
 */
const releaseTokenRefreshLock = async (corpId) => {
  try {
    const unlockQuery = `
      UPDATE access_token_cache
      SET is_refreshing = 0, refresh_started_at = NULL
      WHERE corp_id = ?
    `;

    await query(unlockQuery, [corpId]);
    console.log('🔓 释放access_token刷新锁成功');
    return { success: true };
  } catch (error) {
    console.error('❌ 释放access_token刷新锁失败:', error);
    return { success: false, message: error.message };
  }
};

/**
 * 从数据库获取access_token缓存
 */
const getAccessTokenFromCache = async (corpId) => {
  try {
    await ensureAccessTokenCacheTable();

    const cacheQuery = `
      SELECT access_token, expires_at
      FROM access_token_cache
      WHERE corp_id = ? AND expires_at > NOW()
    `;

    const result = await query(cacheQuery, [corpId]);

    if (result.length > 0) {
      const cache = result[0];
      console.log('✅ 从数据库缓存获取access_token成功');
      return {
        success: true,
        access_token: cache.access_token,
        expires_at: cache.expires_at
      };
    }

    return {
      success: false,
      message: '缓存中没有有效的access_token'
    };
  } catch (error) {
    console.error('从缓存获取access_token失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 将access_token存储到数据库缓存
 */
const storeAccessTokenToCache = async (corpId, accessToken, expiresIn) => {
  try {
    await ensureAccessTokenCacheTable();

    // 计算过期时间（提前5分钟过期）
    const expiresAt = new Date(Date.now() + (expiresIn - 300) * 1000);

    const upsertQuery = `
      INSERT INTO access_token_cache (corp_id, access_token, expires_at, is_refreshing, refresh_started_at)
      VALUES (?, ?, ?, 0, NULL)
      ON DUPLICATE KEY UPDATE
        access_token = VALUES(access_token),
        expires_at = VALUES(expires_at),
        is_refreshing = 0,
        refresh_started_at = NULL,
        updated_at = NOW()
    `;

    await query(upsertQuery, [corpId, accessToken, expiresAt]);

    console.log('✅ access_token已存储到数据库缓存');
    return {
      success: true,
      expires_at: expiresAt
    };
  } catch (error) {
    console.error('存储access_token到缓存失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 记录access_token API调用
 */
const recordTokenApiCall = async (corpId) => {
  try {
    // 确保调用记录表存在
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS token_api_calls (
        id INT AUTO_INCREMENT PRIMARY KEY,
        corp_id VARCHAR(255) NOT NULL,
        call_date DATE NOT NULL,
        call_count INT DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_corp_date (corp_id, call_date),
        INDEX idx_call_date (call_date)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await query(createTableQuery);

    // 记录今日调用
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
    const recordQuery = `
      INSERT INTO token_api_calls (corp_id, call_date, call_count)
      VALUES (?, ?, 1)
      ON DUPLICATE KEY UPDATE
        call_count = call_count + 1,
        updated_at = NOW()
    `;

    await query(recordQuery, [corpId, today]);

    // 获取今日调用次数
    const countQuery = `
      SELECT call_count
      FROM token_api_calls
      WHERE corp_id = ? AND call_date = ?
    `;

    const result = await query(countQuery, [corpId, today]);
    const todayCount = result.length > 0 ? result[0].call_count : 0;

    console.log(`📊 今日access_token API调用次数: ${todayCount}/400`);

    return {
      success: true,
      todayCount: todayCount,
      limit: 400,
      remaining: 400 - todayCount
    };
  } catch (error) {
    console.error('❌ 记录access_token API调用失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 检查今日access_token API调用是否超限
 */
const checkTokenApiLimit = async (corpId) => {
  try {
    const today = new Date().toISOString().split('T')[0];
    const countQuery = `
      SELECT call_count
      FROM token_api_calls
      WHERE corp_id = ? AND call_date = ?
    `;

    const result = await query(countQuery, [corpId, today]);
    const todayCount = result.length > 0 ? result[0].call_count : 0;

    const limit = 400;
    const isOverLimit = todayCount >= limit;

    if (isOverLimit) {
      console.warn(`⚠️ access_token API调用已达每日限制: ${todayCount}/${limit}`);
    }

    return {
      success: true,
      todayCount: todayCount,
      limit: limit,
      remaining: limit - todayCount,
      isOverLimit: isOverLimit
    };
  } catch (error) {
    console.error('❌ 检查access_token API调用限制失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 清理过期的access_token缓存
 */
const cleanupExpiredAccessTokens = async () => {
  try {
    await ensureAccessTokenCacheTable();

    const cleanupQuery = `
      DELETE FROM access_token_cache
      WHERE expires_at < NOW()
    `;

    const result = await query(cleanupQuery);

    if (result.affectedRows > 0) {
      console.log(`🗑️ 清理了 ${result.affectedRows} 个过期的access_token`);
    }

    return {
      success: true,
      cleaned_count: result.affectedRows
    };
  } catch (error) {
    console.error('清理过期access_token失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 确保客服同步游标表存在
 */
const ensureKfSyncCursorsTable = async () => {
  const createTableQuery = `
    CREATE TABLE IF NOT EXISTS kf_sync_cursors (
      id INT AUTO_INCREMENT PRIMARY KEY,
      open_kfid VARCHAR(255) NOT NULL UNIQUE,
      last_cursor VARCHAR(255),
      last_sync_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_open_kfid (open_kfid),
      INDEX idx_last_sync_at (last_sync_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  await query(createTableQuery);
};

/**
 * 获取客服账号的同步游标
 */
const getKfSyncCursor = async (openKfId) => {
  try {
    await ensureKfSyncCursorsTable();

    const cursorQuery = `
      SELECT last_cursor, last_sync_at
      FROM kf_sync_cursors
      WHERE open_kfid = ?
    `;

    const result = await query(cursorQuery, [openKfId]);

    if (result.length > 0) {
      return {
        success: true,
        cursor: result[0].last_cursor,
        last_sync_at: result[0].last_sync_at
      };
    }

    return {
      success: true,
      cursor: null, // 首次同步
      last_sync_at: null
    };
  } catch (error) {
    console.error('获取同步游标失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 更新客服账号的同步游标
 */
const updateKfSyncCursor = async (openKfId, nextCursor) => {
  try {
    await ensureKfSyncCursorsTable();

    const upsertQuery = `
      INSERT INTO kf_sync_cursors (open_kfid, last_cursor, last_sync_at)
      VALUES (?, ?, NOW())
      ON DUPLICATE KEY UPDATE
        last_cursor = VALUES(last_cursor),
        last_sync_at = VALUES(last_sync_at),
        updated_at = NOW()
    `;

    await query(upsertQuery, [openKfId, nextCursor]);

    console.log(`✅ 更新客服账号 ${openKfId} 的同步游标: ${nextCursor}`);
    return {
      success: true
    };
  } catch (error) {
    console.error('更新同步游标失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

module.exports = {
  // access_token缓存相关
  getAccessTokenFromCache,
  storeAccessTokenToCache,
  cleanupExpiredAccessTokens,
  tryAcquireTokenRefreshLock,
  releaseTokenRefreshLock,
  recordTokenApiCall,
  checkTokenApiLimit,

  // 客服链接缓存相关
  getKfLinkFromCache,
  saveKfLinkToCache,

  // 客服同步游标相关
  getKfSyncCursor,
  updateKfSyncCursor,
}; 