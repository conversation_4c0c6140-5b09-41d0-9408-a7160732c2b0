/**
 * 清理机制模块
 * 包含过期数据清理功能
 */

const core = require('./core');

/**
 * 清理过期数据（替代存储过程）
 */
async function cleanupExpiredData() {
  console.log('🧹 [DB] 开始清理过期数据...');
  let connection;
  try {
    connection = await core.pool.getConnection();
    await connection.beginTransaction();
    
    // 清理1年前的消息索引（符合三级等保要求）
    const [result1] = await connection.query(`
      DELETE FROM wechat_message_logs 
      WHERE created_at < DATE_SUB(NOW(), INTERVAL 365 DAY)
      AND sync_status = 'synced'
    `);
    console.log(`🗑️ [DB] 清理了 ${result1.affectedRows} 条过期消息记录`);
    
    // 清理过期的绑定Token
    const [result2] = await connection.query(`
      UPDATE wechat_bindings 
      SET binding_token = NULL, token_expires_at = NULL
      WHERE token_expires_at < NOW()
    `);
    console.log(`🔄 [DB] 清理了 ${result2.affectedRows} 个过期Token`);
    
    // 清理过期的MediaId记录
    const [result3] = await connection.query(`
      UPDATE wechat_message_logs 
      SET media_id = NULL, media_id_expires_at = NULL
      WHERE media_id_expires_at < NOW()
    `);
    console.log(`📎 [DB] 清理了 ${result3.affectedRows} 个过期MediaId`);
    
    await connection.commit();
    console.log('✅ [DB] 过期数据清理完成');
  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('❌ [DB] 清理过期数据失败:', error);
    // 清理失败不应该影响服务启动
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

module.exports = {
  cleanupExpiredData
}; 