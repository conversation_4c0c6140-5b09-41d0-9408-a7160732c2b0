/**
 * 分区管理模块
 * 包含日志分区管理功能
 */

const core = require('./core');

/**
 * 动态管理system_logs表的分区
 * 自动创建未来3个月的分区，删除6个月前的分区
 */
async function manageLogPartitions() {
  console.log('🚀 [DB] 开始管理日志分区...');
  let connection;
  try {
    connection = await core.pool.getConnection();
    
    // 首先检查表是否已经分区
    const [partitions] = await connection.query(`
      SELECT PARTITION_NAME, PARTITION_DESCRIPTION 
      FROM INFORMATION_SCHEMA.PARTITIONS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'system_logs' AND PARTITION_NAME IS NOT NULL
    `, [process.env.DB_NAME || 'gongzhimall_wechat']);
    
    // 如果表还没有分区，先添加分区
    if (partitions.length === 0) {
      console.log('🔧 [DB] 表未分区，开始添加分区...');
      
      // 获取当前年月
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1; // 0-based转1-based
      
      // 创建基于年份的分区（简单稳定，符合等保要求）
      const partitionStatements = [];
      for (let i = 0; i < 3; i++) {
        const year = currentYear + i;
        const partitionName = `p${year}`;
        const partitionValue = year + 1;
        
        partitionStatements.push(`PARTITION ${partitionName} VALUES LESS THAN (${partitionValue})`);
      }
      
      // 添加未来分区
      partitionStatements.push('PARTITION p_future VALUES LESS THAN MAXVALUE');
      
      const alterSql = `
        ALTER TABLE system_logs 
        PARTITION BY RANGE (log_year) (
          ${partitionStatements.join(',\n          ')}
        )
      `;
      
      await connection.query(alterSql);
      console.log('✅ [DB] 分区添加完成');
    } else {
      console.log('📊 [DB] 表已分区，检查是否需要新增分区...');
      
      // 检查是否需要新增未来年份分区
      const now = new Date();
      const nextYear = now.getFullYear() + 1;
      const nextPartitionName = `p${nextYear}`;
      
      const existingPartition = partitions.find(p => p.PARTITION_NAME === nextPartitionName);
      if (!existingPartition) {
        console.log(`🔧 [DB] 需要创建新分区: ${nextPartitionName}`);
        
        // 重新组织分区，添加新的年份分区
        const partitionValue = nextYear + 1;
        
        await connection.query(`
          ALTER TABLE system_logs 
          REORGANIZE PARTITION p_future INTO (
            PARTITION ${nextPartitionName} VALUES LESS THAN (${partitionValue}),
            PARTITION p_future VALUES LESS THAN MAXVALUE
          )
        `);
        
        console.log(`✅ [DB] 新分区 ${nextPartitionName} 创建完成`);
      }
      
      // 清理2年前的分区（符合三级等保要求，保留1年+1年缓冲）
      const twoYearsAgo = now.getFullYear() - 2;
      const oldPartitionName = `p${twoYearsAgo}`;
      
      const oldPartition = partitions.find(p => p.PARTITION_NAME === oldPartitionName);
      if (oldPartition) {
        console.log(`🗑️ [DB] 清理过期分区: ${oldPartitionName}（等保合规：保留1年+缓冲）`);
        await connection.query(`ALTER TABLE system_logs DROP PARTITION ${oldPartitionName}`);
        console.log(`✅ [DB] 过期分区 ${oldPartitionName} 清理完成`);
      }
    }
    
    console.log('✅ [DB] 分区管理完成');
  } catch (error) {
    console.error('❌ [DB] 分区管理失败:', error);
    // 分区管理失败不应该影响服务启动
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

module.exports = {
  manageLogPartitions
}; 