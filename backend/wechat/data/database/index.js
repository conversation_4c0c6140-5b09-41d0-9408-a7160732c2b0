/**
 * 数据库模块 - 统一架构版本
 * 
 * 安全等级：三级等保
 * - 一级等保：日志保存不少于3个月
 * - 二级等保：日志保存不少于6个月
 * - 三级等保：日志保存不少于1年
 * 
 * 当前配置（符合三级等保要求）：
 * - 消息记录保存：365天（1年）
 * - 系统日志分区保存：13个月（1年+1个月缓冲）
 * - Token清理：立即清理过期Token（不涉及审计）
 */

const fs = require('fs');
const path = require('path');

// 导入各模块
const core = require('./core');
const business = require('./business');
const cache = require('./cache');
const partitions = require('./partitions');
const cleanup = require('./cleanup');

/**
 * 确保数据库表和服务就绪
 * 从统一架构文件确保所有表和基础数据都已创建
 */
async function ensureTablesFromSql() {
  try {
    console.log('🚀 [DB] 正在执行统一架构文件...');
    
    const schemaPath = path.resolve(__dirname, '../schema/build/schema.sql');
    
    if (!fs.existsSync(schemaPath)) {
      console.error('❌ [DB] 统一架构文件不存在:', schemaPath);
      console.error('请先运行: cd data/schema && node scripts/build-schema.js');
      throw new Error('统一架构文件不存在，请先构建架构文件');
    }

    const sqlContent = fs.readFileSync(schemaPath, 'utf8');

    // 分割SQL语句并过滤掉USE语句（因为连接时已指定数据库）
    const sqlStatements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.match(/^\s*--/) && !stmt.match(/^\s*USE\s+/i));

    console.log(`[DB] 开始执行架构文件: ${schemaPath} (${sqlStatements.length} 条语句)`);

    // 逐条执行SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const stmt = sqlStatements[i];
      if (stmt.trim()) {
        try {
          await core.query(stmt);
          console.log(`[DB] 执行语句 ${i + 1}/${sqlStatements.length} 成功`);
        } catch (error) {
          console.error(`[DB] 执行语句 ${i + 1} 失败:`, stmt.substring(0, 100) + '...');
          throw error;
        }
      }
    }
    
    console.log('✅ [DB] 架构文件执行成功！数据库表结构和基础数据已成功同步！');

  } catch (error) {
    console.error('❌ [DB] 架构文件执行失败:', error);
    // 抛出错误以中断启动，防止服务在不完整的数据库结构上运行
    throw error;
  }
}

/**
 * 初始化数据库模块
 * 包含表创建、分区管理、清理等操作
 */
async function initializeDatabase() {
  try {
    // 确保表结构存在
    await ensureTablesFromSql();
    
    // 管理日志分区
    await partitions.manageLogPartitions();
    
    // 清理过期数据
    await cleanup.cleanupExpiredData();
    
    console.log('✅ [DB] 数据库模块初始化完成');
  } catch (error) {
    console.error('❌ [DB] 数据库模块初始化失败:', error);
    throw error;
  }
}

// 统一导出所有功能
const dbExports = {
  // 核心数据库操作
  ...core,
  
  // 业务数据库操作
  ...business,
  
  // 缓存管理
  ...cache,
  
  // 分区管理
  ...partitions,
  
  // 清理机制
  ...cleanup,
  
  // 初始化函数
  ensureTablesFromSql,
  initializeDatabase,
};

// 延迟访问pool，避免模块加载时验证配置
Object.defineProperty(dbExports, 'pool', {
  get() {
    return core.pool;
  }
});

module.exports = dbExports; 