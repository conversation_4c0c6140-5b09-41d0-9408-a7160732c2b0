/**
 * 核心数据库操作模块
 * 包含基础CRUD操作、连接池管理和数据库配置
 */

const mysql = require('mysql2/promise');

// 数据库连接配置 - 生产环境必须从环境变量获取
const dbConfig = {
  host: process.env.MYSQL_HOST,
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
  database: process.env.MYSQL_DATABASE,
  charset: 'utf8mb4',
  timezone: '+08:00',
  connectionLimit: 10,
  multipleStatements: true // <-- 允许执行多个SQL语句
};

// 数据库配置验证 - 仅在运行时验证，避免模块加载时抛出错误
const validateConfig = () => {
  if (!dbConfig.host || !dbConfig.user || !dbConfig.password || !dbConfig.database) {
    throw new Error('缺少必要的数据库配置环境变量: MYSQL_HOST, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DATABASE');
  }
};

// 创建连接池 - 延迟初始化
let pool = null;

const getPool = () => {
  if (!pool) {
    validateConfig();
    pool = mysql.createPool(dbConfig);
  }
  return pool;
};

/**
 * 执行查询
 * @param {string} sql SQL语句
 * @param {Array} params 参数数组
 * @returns {Promise<Array>} 查询结果
 */
const query = async (sql, params = []) => {
  try {
    console.log('执行SQL:', sql);
    console.log('参数:', params);
    
    const [rows] = await getPool().execute(sql, params);
    return rows;
  } catch (error) {
    console.error('数据库查询错误:', error);
    throw error;
  }
};

/**
 * 执行事务
 * @param {Function} callback 事务回调函数
 * @returns {Promise<any>} 事务结果
 */
const transaction = async (callback) => {
  const connection = await getPool().getConnection();
  
  try {
    await connection.beginTransaction();
    
    const result = await callback(connection);
    
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    console.error('事务执行错误:', error);
    throw error;
  } finally {
    connection.release();
  }
};

/**
 * 获取单条记录
 * @param {string} sql SQL语句
 * @param {Array} params 参数数组
 * @returns {Promise<Object|null>} 单条记录或null
 */
const getOne = async (sql, params = []) => {
  const rows = await query(sql, params);
  return rows.length > 0 ? rows[0] : null;
};

/**
 * 插入记录
 * @param {string} table 表名
 * @param {Object} data 数据对象
 * @returns {Promise<Object>} 插入结果
 */
const insert = async (table, data) => {
  // 过滤掉undefined值，将其转换为null
  const cleanData = {};
  Object.keys(data).forEach(key => {
    cleanData[key] = data[key] === undefined ? null : data[key];
  });
  
  const fields = Object.keys(cleanData);
  const values = Object.values(cleanData);
  const placeholders = fields.map(() => '?').join(',');
  
  const sql = `INSERT INTO ${table} (${fields.join(',')}) VALUES (${placeholders})`;
  
  try {
    const [result] = await getPool().execute(sql, values);
    return {
      success: true,
      insertId: result.insertId,
      affectedRows: result.affectedRows
    };
  } catch (error) {
    console.error('插入记录错误:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 更新记录
 * @param {string} table 表名
 * @param {Object} data 更新数据
 * @param {Object} where 条件
 * @returns {Promise<Object>} 更新结果
 */
const update = async (table, data, where) => {
  const setClause = Object.keys(data).map(key => `${key} = ?`).join(',');
  const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
  
  const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
  const params = [...Object.values(data), ...Object.values(where)];
  
  try {
    const [result] = await getPool().execute(sql, params);
    return {
      success: true,
      affectedRows: result.affectedRows,
      changedRows: result.changedRows
    };
  } catch (error) {
    console.error('更新记录错误:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 删除记录
 * @param {string} table 表名
 * @param {Object} where 条件
 * @returns {Promise<Object>} 删除结果
 */
const deleteRecord = async (table, where) => {
  const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
  const sql = `DELETE FROM ${table} WHERE ${whereClause}`;
  const params = Object.values(where);
  
  try {
    const [result] = await getPool().execute(sql, params);
    return {
      success: true,
      affectedRows: result.affectedRows
    };
  } catch (error) {
    console.error('删除记录错误:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 检查数据库连接
 * @returns {Promise<boolean>} 连接状态
 */
const checkConnection = async () => {
  try {
    await query('SELECT 1');
    console.log('✅ 数据库连接正常');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
};

/**
 * 关闭连接池
 */
const closePool = async () => {
  try {
    if (pool) {
      await pool.end();
      pool = null;
      console.log('数据库连接池已关闭');
    }
  } catch (error) {
    console.error('关闭连接池错误:', error);
  }
};

module.exports = {
  get pool() { return getPool(); },
  query,
  transaction,
  getOne,
  insert,
  update,
  deleteRecord,
  checkConnection,
  closePool
}; 