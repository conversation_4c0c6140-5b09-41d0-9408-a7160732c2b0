# 公职猫项目环境变量配置模板
# 复制此文件为 .env 并填入实际值

# ================================
# 开发环境配置
# ================================
NODE_ENV=development
DEBUG=true
LOG_LEVEL=info

# ================================
# AI服务配置 (可选)
# ================================
# OpenRouter API密钥 (用于云端AI功能)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Perplexity API密钥 (用于研究功能)
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# ================================
# 数据库配置
# ================================
# SQLite数据库加密密钥 (生产环境必须设置)
DB_ENCRYPTION_KEY=your_32_character_encryption_key_here

# 数据库文件路径
DB_PATH=./data/gongzhimall.db

# PostgreSQL数据库连接
DATABASE_URL="postgresql://dev:dev123@localhost:5432/gongzhimall_dev"
DATABASE_MAX_CONNECTIONS=10
DATABASE_TIMEOUT=30000
DATABASE_SSL=false

# ================================
# 应用配置
# ================================
# 应用名称
APP_NAME=公职猫
APP_VERSION=1.0.0

# 端口配置
MOBILE_PORT=8081
DESKTOP_PORT=3000

# ================================
# 安全配置
# ================================
# JWT密钥 (用于本地认证)
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN="7d"
JWT_ALGORITHM="HS256"

# 会话超时时间 (分钟)
SESSION_TIMEOUT=60

# ================================
# Redis配置
# ================================
REDIS_URL="redis://localhost:6379"
REDIS_PREFIX="gongzhimall:"
REDIS_TTL=3600

# ================================
# 功能开关
# ================================
# 启用云端AI功能
ENABLE_CLOUD_AI=true

# 启用本地AI功能
ENABLE_LOCAL_AI=true

# 启用规则引擎
ENABLE_RULE_ENGINE=true

# 启用数据同步
ENABLE_DATA_SYNC=true

# 功能开关
ENABLE_USER_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_SMS_VERIFICATION=true
ENABLE_SOCIAL_LOGIN=false

# ================================
# 开发工具配置
# ================================
# TaskMaster配置
TASKMASTER_PROJECT_ROOT=/path/to/your/project
DEFAULT_SUBTASKS=5
DEFAULT_PRIORITY="medium"
PROJECT_NAME="公职猫"
PROJECT_VERSION="1.0.0"

# React Native开发配置
RN_PACKAGER_PORT=8081
REACT_NATIVE_PACKAGER_HOSTNAME=localhost

# Electron开发配置
ELECTRON_IS_DEV=true

# 热重载
HOT_RELOAD=true
WATCH_FILES=true

# API文档
ENABLE_SWAGGER=true
SWAGGER_PATH="/api-docs"

# 测试配置
TEST_TIMEOUT=30000
MOCK_EXTERNAL_APIS=true

# ================================
# 文件上传配置
# ================================
UPLOAD_PROVIDER="local"
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES="jpg,jpeg,png,gif,pdf,doc,docx"

# 阿里云OSS配置（生产环境）
ALIYUN_ACCESS_KEY=""
ALIYUN_SECRET_KEY=""
ALIYUN_BUCKET=""
ALIYUN_REGION=""

# ================================
# 邮件服务配置
# ================================
# SMTP配置
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-email-password"
SMTP_SECURE=true

# 邮件模板配置
EMAIL_FROM_NAME="公职猫"
EMAIL_FROM_ADDRESS="<EMAIL>"

# ================================
# 前端地址配置
# ================================
FRONTEND_URL="http://localhost:3000"
ADMIN_URL="http://localhost:3001"
MOBILE_DEEP_LINK="gongzhimall://"

# ================================
# 第三方支付配置
# ================================
# 微信支付
WECHAT_APP_ID=""
WECHAT_APP_SECRET=""
WECHAT_MERCHANT_ID=""
WECHAT_API_KEY=""

# 支付宝
ALIPAY_APP_ID=""
ALIPAY_PRIVATE_KEY=""
ALIPAY_PUBLIC_KEY=""

# ================================
# 短信服务配置
# ================================
SMS_PROVIDER="aliyun"
ALIYUN_SMS_ACCESS_KEY=""
ALIYUN_SMS_SECRET_KEY=""
SMS_SIGN_NAME="公职猫"
SMS_TEMPLATE_CODE=""

# ================================
# 日志配置
# ================================
# 日志文件路径
LOG_FILE_PATH=./logs/app.log

# 日志保留天数
LOG_RETENTION_DAYS=30

LOG_TO_FILE=false
LOG_ROTATION="daily"

# ================================
# 缓存配置
# ================================
# 缓存大小 (MB)
CACHE_SIZE=128

ENABLE_CACHE=true
CACHE_TTL=3600
CACHE_PREFIX="cache:"

# ================================
# 备份配置
# ================================
# 自动备份间隔 (小时)
AUTO_BACKUP_INTERVAL=24

# 备份文件保留数量
BACKUP_RETENTION_COUNT=7

# 备份存储路径
BACKUP_PATH=./backups

# ================================
# 性能配置
# ================================
# 内存限制 (MB)
MEMORY_LIMIT=512

# ================================
# 网络配置
# ================================
# 请求超时时间 (秒)
REQUEST_TIMEOUT=30

# 重试次数
MAX_RETRIES=3

# CORS配置
CORS_ORIGINS="http://localhost:3000,http://localhost:3001"
CORS_CREDENTIALS=true

# 请求限制
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_STRICT=false

# 安全中间件
HELMET_ENABLED=true
COMPRESSION_ENABLED=true

# ================================
# 用户体验配置
# ================================
# 默认语言
DEFAULT_LANGUAGE=zh-CN

# 主题模式
DEFAULT_THEME=auto

# 启用动画
ENABLE_ANIMATIONS=true

# ================================
# 监控配置
# ================================
ENABLE_MONITORING=false
SENTRY_DSN=""
SENTRY_ENVIRONMENT="development"

# 性能监控
ENABLE_METRICS=false
METRICS_PORT=9090

# ================================
# 维护模式
# ================================
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="系统维护中，请稍后再试"

# 调试功能
ENABLE_DEBUG_ROUTES=true
ENABLE_PROFILING=false