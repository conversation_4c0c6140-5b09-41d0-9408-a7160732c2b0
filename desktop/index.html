<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>公职猫 - 桌面版</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline';">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .container {
            text-align: center;
            max-width: 600px;
        }
        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .subtitle {
            font-size: 1.2em;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        .status {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .version {
            position: absolute;
            bottom: 20px;
            right: 20px;
            opacity: 0.7;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐱 公职猫</h1>
        <div class="subtitle">U盘级私密，秘书般懂你</div>
        <div class="status">
            <h3>✅ 桌面端环境搭建成功</h3>
            <p>Electron 应用已成功启动</p>
            <p>开发环境配置完成</p>
        </div>
    </div>
    <div class="version">
        Desktop v1.0.0 - MVP
    </div>

    <script>
        // 简单的环境验证脚本
        console.log('公职猫桌面版启动成功');
        console.log('Node.js版本:', process.versions.node);
        console.log('Electron版本:', process.versions.electron);
        console.log('Chrome版本:', process.versions.chrome);
    </script>
</body>
</html> 