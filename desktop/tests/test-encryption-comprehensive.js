/**
 * 综合加密功能测试套件
 * 深入测试数据库加密的性能、安全性和可靠性
 */

const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

// 模拟 Electron 环境
const mockApp = {
  getPath: (name) => {
    if (name === 'userData') {
      return path.join(__dirname, 'test-data');
    }
    return __dirname;
  }
};

const mockSafeStorage = {
  isEncryptionAvailable: () => true,
  encryptString: (data) => Buffer.from(data, 'utf8'),
  decryptString: (buffer) => buffer.toString('utf8')
};

// 替换 electron 模块
const Module = require('module');
const originalRequire = Module.prototype.require;
Module.prototype.require = function(id) {
  if (id === 'electron') {
    return { app: mockApp, safeStorage: mockSafeStorage };
  }
  return originalRequire.apply(this, arguments);
};

const databaseManager = require('./src/db/database');
const keyManager = require('./src/db/keyManager');

class EncryptionTestSuite {
  constructor() {
    this.testResults = [];
    this.testDataDir = path.join(__dirname, 'test-data');
  }

  /**
   * 记录测试结果
   */
  recordTest(testName, passed, details = '', duration = 0) {
    this.testResults.push({
      testName,
      passed,
      details,
      duration,
      timestamp: new Date().toISOString()
    });
    
    const status = passed ? '✅' : '❌';
    const durationStr = duration > 0 ? ` (${duration}ms)` : '';
    console.log(`${status} ${testName}${durationStr}`);
    if (details) {
      console.log(`   ${details}`);
    }
  }

  /**
   * 生成测试数据
   */
  generateTestData(size = 1000) {
    const data = [];
    for (let i = 0; i < size; i++) {
      data.push({
        id: i + 1,
        name: `测试用户${i + 1}`,
        email: `user${i + 1}@gongzhimall.com`,
        data: crypto.randomBytes(100).toString('hex'), // 随机数据
        created_at: new Date().toISOString()
      });
    }
    return data;
  }

  /**
   * 测试1：加密强度验证
   */
  async testEncryptionStrength() {
    console.log('\n🔒 测试1：加密强度验证');
    
    try {
      // 测试不同密钥长度
      const keyLengths = [16, 24, 32]; // 128, 192, 256 位
      
      for (const keyLength of keyLengths) {
        const startTime = Date.now();
        
        // 生成测试密钥
        const testKey = keyManager.generateDatabaseKey(keyLength);
        const dbPath = path.join(this.testDataDir, `test-strength-${keyLength * 8}bit.db`);
        
        // 初始化数据库
        databaseManager.initialize(dbPath, testKey);
        
        // 创建测试表
        databaseManager.exec(`
          CREATE TABLE test_strength (
            id INTEGER PRIMARY KEY,
            sensitive_data TEXT,
            hash_check TEXT
          )
        `);
        
        // 插入敏感数据
        const sensitiveData = '这是高度敏感的数据：身份证号123456789012345678';
        const hashCheck = crypto.createHash('sha256').update(sensitiveData).digest('hex');
        
        databaseManager.run(
          'INSERT INTO test_strength (sensitive_data, hash_check) VALUES (?, ?)',
          [sensitiveData, hashCheck]
        );
        
        // 验证数据完整性
        const retrieved = databaseManager.get('SELECT * FROM test_strength WHERE id = 1');
        const retrievedHash = crypto.createHash('sha256').update(retrieved.sensitive_data).digest('hex');
        
        const isValid = retrievedHash === hashCheck;
        const duration = Date.now() - startTime;
        
        databaseManager.close();
        
        this.recordTest(
          `${keyLength * 8}位密钥加密`,
          isValid,
          `数据完整性: ${isValid ? '通过' : '失败'}`,
          duration
        );
      }
    } catch (error) {
      this.recordTest('加密强度验证', false, `错误: ${error.message}`);
    }
  }

  /**
   * 测试2：性能基准测试
   */
  async testPerformance() {
    console.log('\n⚡ 测试2：性能基准测试');
    
    try {
      const testSizes = [100, 1000];
      
      for (const size of testSizes) {
        // 加密数据库测试
        const encryptedStartTime = Date.now();
        const encryptedDbPath = path.join(this.testDataDir, `perf-encrypted-${size}.db`);
        
        databaseManager.initialize(encryptedDbPath);
        databaseManager.exec(`
          CREATE TABLE perf_test (
            id INTEGER PRIMARY KEY,
            name TEXT,
            email TEXT,
            data TEXT,
            created_at TEXT
          )
        `);
        
        const testData = this.generateTestData(size);
        const insertStmt = databaseManager.prepare(
          'INSERT INTO perf_test (name, email, data, created_at) VALUES (?, ?, ?, ?)'
        );
        
        // 批量插入（加密）
        for (const record of testData) {
          insertStmt.run([record.name, record.email, record.data, record.created_at]);
        }
        
        // 查询测试（加密）
        const queryStart = Date.now();
        const results = databaseManager.all('SELECT * FROM perf_test LIMIT 100');
        const queryTime = Date.now() - queryStart;
        
        databaseManager.close();
        const encryptedTotalTime = Date.now() - encryptedStartTime;
        
        this.recordTest(
          `${size}条记录性能测试`,
          results.length === Math.min(size, 100),
          `插入+查询总时间: ${encryptedTotalTime}ms, 查询时间: ${queryTime}ms`,
          encryptedTotalTime
        );
      }
    } catch (error) {
      this.recordTest('性能基准测试', false, `错误: ${error.message}`);
    }
  }

  /**
   * 测试3：错误密钥访问测试
   */
  async testWrongKeyAccess() {
    console.log('\n🚫 测试3：错误密钥访问测试');
    
    try {
      const dbPath = path.join(this.testDataDir, 'wrong-key-test.db');
      const correctKey = keyManager.generateDatabaseKey(32);
      const wrongKey = keyManager.generateDatabaseKey(32);
      
      // 用正确密钥创建数据库
      databaseManager.initialize(dbPath, correctKey);
      databaseManager.exec(`
        CREATE TABLE secret_data (
          id INTEGER PRIMARY KEY,
          secret TEXT
        )
      `);
      
      databaseManager.run(
        'INSERT INTO secret_data (secret) VALUES (?)',
        ['这是机密信息']
      );
      
      databaseManager.close();
      
      // 尝试用错误密钥打开
      let wrongKeyFailed = false;
      try {
        databaseManager.initialize(dbPath, wrongKey);
        databaseManager.get('SELECT * FROM secret_data');
        databaseManager.close();
      } catch (error) {
        wrongKeyFailed = true;
        if (databaseManager.isReady()) {
          databaseManager.close();
        }
      }
      
      // 验证正确密钥仍然可以访问
      let correctKeyWorks = false;
      try {
        databaseManager.initialize(dbPath, correctKey);
        const data = databaseManager.get('SELECT * FROM secret_data');
        correctKeyWorks = data && data.secret === '这是机密信息';
        databaseManager.close();
      } catch (error) {
        console.error('正确密钥访问失败:', error);
      }
      
      this.recordTest(
        '错误密钥访问阻止',
        wrongKeyFailed,
        `错误密钥被阻止: ${wrongKeyFailed}`
      );
      
      this.recordTest(
        '正确密钥访问正常',
        correctKeyWorks,
        `正确密钥可访问: ${correctKeyWorks}`
      );
      
    } catch (error) {
      this.recordTest('错误密钥访问测试', false, `错误: ${error.message}`);
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('\n📊 测试报告');
    console.log('='.repeat(50));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${successRate}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`  - ${r.testName}: ${r.details}`);
        });
    }
    
    const totalDuration = this.testResults.reduce((sum, r) => sum + r.duration, 0);
    console.log(`\n⏱️  总耗时: ${totalDuration}ms`);
    
    return {
      totalTests,
      passedTests,
      failedTests,
      successRate: parseFloat(successRate),
      totalDuration
    };
  }

  /**
   * 清理测试数据
   */
  cleanup() {
    try {
      if (fs.existsSync(this.testDataDir)) {
        fs.rmSync(this.testDataDir, { recursive: true, force: true });
        console.log('🧹 测试数据已清理');
      }
    } catch (error) {
      console.error('清理失败:', error);
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🔐 开始综合加密功能测试...');
    
    try {
      // 初始化密钥管理器
      keyManager.initialize();
      
      // 运行所有测试
      await this.testEncryptionStrength();
      await this.testPerformance();
      await this.testWrongKeyAccess();
      
      // 生成报告
      const report = this.generateReport();
      
      if (report.successRate >= 90) {
        console.log('\n🎉 加密功能测试全面通过！');
        return true;
      } else {
        console.log('\n⚠️  部分测试失败，需要进一步检查');
        return false;
      }
      
    } catch (error) {
      console.error('❌ 测试套件执行失败:', error);
      return false;
    } finally {
      this.cleanup();
    }
  }
}

// 运行测试
const testSuite = new EncryptionTestSuite();
testSuite.runAllTests()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('测试执行异常:', error);
    process.exit(1);
  }); 