/**
 * 密钥管理器测试脚本
 * 验证密钥生成、存储、获取和安全性功能
 */

const path = require('path');
const fs = require('fs');

// 模拟 Electron app 和 safeStorage 对象
const mockApp = {
  getPath: (name) => {
    if (name === 'userData') {
      return path.join(__dirname, 'test-data');
    }
    return __dirname;
  }
};

const mockSafeStorage = {
  isEncryptionAvailable: () => true,
  encryptString: (data) => Buffer.from(data, 'utf8'),
  decryptString: (buffer) => buffer.toString('utf8')
};

// 临时替换 electron 模块
const Module = require('module');
const originalRequire = Module.prototype.require;
Module.prototype.require = function(id) {
  if (id === 'electron') {
    return { app: mockApp, safeStorage: mockSafeStorage };
  }
  return originalRequire.apply(this, arguments);
};

const keyManager = require('./src/db/keyManager');

async function runKeyManagerTest() {
  console.log('🔐 开始测试密钥管理器功能...\n');

  try {
    // 1. 测试密钥管理器初始化
    console.log('1️⃣ 测试密钥管理器初始化...');
    keyManager.initialize();
    const state = keyManager.validateState();
    console.log('✅ 密钥管理器初始化成功');
    console.log('状态:', state);
    console.log();

    // 2. 测试密钥生成
    console.log('2️⃣ 测试密钥生成...');
    const testKey1 = keyManager.generateDatabaseKey(16); // 128位
    const testKey2 = keyManager.generateDatabaseKey(32); // 256位
    console.log('✅ 128位密钥:', testKey1, '(长度:', testKey1.length, ')');
    console.log('✅ 256位密钥:', testKey2, '(长度:', testKey2.length, ')');
    console.log();

    // 3. 测试密钥存储和获取
    console.log('3️⃣ 测试密钥存储和获取...');
    const testKeyId = 'test_key_001';
    const testKeyValue = keyManager.generateDatabaseKey(32);
    
    // 存储密钥
    keyManager.storeKey(testKeyId, testKeyValue);
    console.log('✅ 密钥存储成功');
    
    // 获取密钥
    const retrievedKey = keyManager.getKey(testKeyId);
    if (retrievedKey === testKeyValue) {
      console.log('✅ 密钥获取成功，值匹配');
    } else {
      throw new Error('密钥值不匹配');
    }
    console.log();

    // 4. 测试数据库主密钥
    console.log('4️⃣ 测试数据库主密钥...');
    const masterKey1 = keyManager.getDatabaseMasterKey();
    const masterKey2 = keyManager.getDatabaseMasterKey();
    
    if (masterKey1 === masterKey2) {
      console.log('✅ 数据库主密钥一致性验证成功');
      console.log('主密钥长度:', masterKey1.length);
    } else {
      throw new Error('数据库主密钥不一致');
    }
    console.log();

    // 5. 测试密钥列表功能
    console.log('5️⃣ 测试密钥列表功能...');
    const keyList = keyManager.listKeys();
    console.log('✅ 当前存储的密钥:', keyList);
    
    if (keyList.includes('database_master_key') && keyList.includes(testKeyId)) {
      console.log('✅ 密钥列表包含预期的密钥');
    } else {
      throw new Error('密钥列表不完整');
    }
    console.log();

    // 6. 测试密钥轮换
    console.log('6️⃣ 测试密钥轮换...');
    const oldKeyId = 'old_test_key';
    const newKeyId = 'new_test_key';
    
    // 先创建一个旧密钥
    keyManager.storeKey(oldKeyId, keyManager.generateDatabaseKey(32));
    
    // 执行密钥轮换
    const newKey = keyManager.rotateKey(oldKeyId, newKeyId);
    
    // 验证新密钥存在
    const rotatedKey = keyManager.getKey(newKeyId);
    if (rotatedKey === newKey) {
      console.log('✅ 密钥轮换成功');
    } else {
      throw new Error('密钥轮换失败');
    }
    console.log();

    // 7. 测试密钥删除
    console.log('7️⃣ 测试密钥删除...');
    keyManager.deleteKey(testKeyId);
    const deletedKey = keyManager.getKey(testKeyId);
    
    if (deletedKey === null) {
      console.log('✅ 密钥删除成功');
    } else {
      throw new Error('密钥删除失败');
    }
    console.log();

    // 8. 测试缓存功能
    console.log('8️⃣ 测试缓存功能...');
    const cacheTestKey = 'cache_test_key';
    const cacheTestValue = keyManager.generateDatabaseKey(32);
    
    // 存储并获取（应该被缓存）
    keyManager.storeKey(cacheTestKey, cacheTestValue);
    const cachedValue1 = keyManager.getKey(cacheTestKey);
    
    // 清理缓存
    keyManager.clearCache();
    
    // 再次获取（应该从文件读取）
    const cachedValue2 = keyManager.getKey(cacheTestKey);
    
    if (cachedValue1 === cachedValue2 && cachedValue1 === cacheTestValue) {
      console.log('✅ 缓存功能正常');
    } else {
      throw new Error('缓存功能异常');
    }
    console.log();

    // 9. 测试与数据库管理器的集成
    console.log('9️⃣ 测试与数据库管理器的集成...');
    const databaseManager = require('./src/db/database');
    
    // 使用密钥管理器初始化数据库
    const testDbPath = path.join(__dirname, 'test-data', 'test-integration.db');
    databaseManager.initialize(testDbPath); // 不指定密钥，应该自动使用密钥管理器
    
    // 测试数据库操作
    databaseManager.exec(`
      CREATE TABLE IF NOT EXISTS integration_test (
        id INTEGER PRIMARY KEY,
        data TEXT
      )
    `);
    
    const insertResult = databaseManager.run(
      'INSERT INTO integration_test (data) VALUES (?)',
      ['密钥管理器集成测试']
    );
    
    const testData = databaseManager.get(
      'SELECT * FROM integration_test WHERE id = ?',
      [insertResult.lastInsertRowid]
    );
    
    if (testData && testData.data === '密钥管理器集成测试') {
      console.log('✅ 数据库管理器集成成功');
    } else {
      throw new Error('数据库管理器集成失败');
    }
    
    databaseManager.close();
    console.log();

    console.log('🎉 所有密钥管理器测试通过！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  } finally {
    // 清理测试数据
    const testDataDir = path.join(__dirname, 'test-data');
    if (fs.existsSync(testDataDir)) {
      fs.rmSync(testDataDir, { recursive: true, force: true });
      console.log('🧹 测试数据已清理');
    }
  }
}

// 运行测试
runKeyManagerTest().catch(console.error); 