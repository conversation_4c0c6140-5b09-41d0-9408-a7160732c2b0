{"name": "gongzhimall-desktop", "version": "1.0.0", "description": "公职猫桌面版 - U盘级私密的个人AI助理", "main": "main.js", "scripts": {"start": "electron .", "dev": "NODE_ENV=development electron .", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron", "desktop", "ai", "assistant"], "author": "公职猫团队", "license": "ISC", "devDependencies": {"@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "electron": "^36.4.0", "typescript": "^5.8.3"}, "dependencies": {"better-sqlite3-multiple-ciphers": "^11.10.0", "react": "^19.1.0", "react-dom": "^19.1.0", "uuid": "^11.1.0"}}