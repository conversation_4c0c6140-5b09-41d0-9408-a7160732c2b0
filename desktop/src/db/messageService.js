const databaseManager = require('./databaseManager');
const { v4: uuidv4 } = require('uuid');

class MessageService {
  /**
   * 创建一个新的会话
   * @param {object} sessionData - { name?: string, ext?: string }
   * @returns {string} 新会话的ID
   */
  createSession(sessionData = {}) {
    const db = databaseManager.getConnection();
    const id = uuidv4(); // 使用uuid库生成ID
    const now = Date.now();

    const stmt = db.prepare(
      'INSERT INTO sessions (id, name, ext, created_at, last_modified) VALUES (?, ?, ?, ?, ?)'
    );
    
    stmt.run(id, sessionData.name || null, sessionData.ext || null, now, now);
    return id;
  }

  /**
   * 创建一条新消息
   * @param {object} messageData - { sessionId, sender, type, content, mediaUri, status, ext }
   * @returns {string} 新消息的ID
   */
  createMessage(messageData) {
    const db = databaseManager.getConnection();
    const id = uuidv4(); // 使用uuid库生成ID
    const now = Date.now();

    const stmt = db.prepare(`
      INSERT INTO messages (id, session_id, sender, type, content, media_uri, status, ext, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id,
      messageData.sessionId,
      messageData.sender,
      messageData.type,
      messageData.content,
      messageData.mediaUri || null,
      messageData.status || 'sent',
      messageData.ext || null,
      messageData.createdAt || now
    );
    
    return id;
  }

  /**
   * 获取指定会话的所有消息
   * @param {string} sessionId
   * @param {object} options - { limit?: number, offset?: number }
   * @returns {Array<object>} 消息列表
   */
  getMessagesForSession(sessionId, options = { limit: 50, offset: 0 }) {
    const db = databaseManager.getConnection();
    const stmt = db.prepare(`
      SELECT * FROM messages
      WHERE session_id = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `);
    return stmt.all(sessionId, options.limit, options.offset);
  }

  /**
   * 获取所有会话列表
   * @returns {Array<object>} 会话列表
   */
  getAllSessions() {
    const db = databaseManager.getConnection();
    const stmt = db.prepare('SELECT * FROM sessions ORDER BY last_modified DESC');
    return stmt.all();
  }
}

module.exports = new MessageService(); 