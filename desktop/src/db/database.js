const Database = require('better-sqlite3-multiple-ciphers');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');
const keyManager = require('./keyManager');

class DatabaseManager {
  constructor() {
    this.db = null;
    this.isInitialized = false;
  }

  /**
   * 初始化加密数据库连接
   * @param {string} dbPath - 数据库文件路径（可选，默认使用用户数据目录）
   * @param {string} encryptionKey - 加密密钥（可选，默认使用密钥管理器）
   */
  initialize(dbPath = null, encryptionKey = null) {
    try {
      // 初始化密钥管理器
      if (!keyManager.isInitialized) {
        keyManager.initialize();
      }

      // 如果没有指定路径，使用用户数据目录
      if (!dbPath) {
        const userDataPath = app.getPath('userData');
        dbPath = path.join(userDataPath, 'gongzhimall.db');
      }

      // 如果没有指定密钥，使用密钥管理器获取主密钥
      if (!encryptionKey) {
        encryptionKey = keyManager.getDatabaseMasterKey();
      }

      // 确保数据库目录存在
      const dbDir = path.dirname(dbPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      // 创建数据库连接
      this.db = new Database(dbPath);

      // 设置加密密钥（使用默认的sqleet加密算法）
      this.db.pragma(`key='${encryptionKey}'`);

      // 启用WAL模式以提高性能
      this.db.pragma('journal_mode = WAL');

      // 启用外键约束
      this.db.pragma('foreign_keys = ON');

      // 创建或更新表结构
      this._createSchema();

      this.isInitialized = true;
      console.log('数据库初始化成功:', dbPath);

      return true;
    } catch (error) {
      console.error('数据库初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取数据库连接实例
   */
  getConnection() {
    if (!this.isInitialized || !this.db) {
      throw new Error('数据库未初始化，请先调用 initialize() 方法');
    }
    return this.db;
  }

  /**
   * 执行SQL语句（用于建表等DDL操作）
   * @param {string} sql - SQL语句
   */
  exec(sql) {
    const db = this.getConnection();
    return db.exec(sql);
  }

  /**
   * 准备SQL语句
   * @param {string} sql - SQL语句
   */
  prepare(sql) {
    const db = this.getConnection();
    return db.prepare(sql);
  }

  /**
   * 执行查询并返回所有结果
   * @param {string} sql - SQL语句
   * @param {Array|Object} params - 参数
   */
  all(sql, params = []) {
    const db = this.getConnection();
    const stmt = db.prepare(sql);
    return stmt.all(params);
  }

  /**
   * 执行查询并返回第一行结果
   * @param {string} sql - SQL语句
   * @param {Array|Object} params - 参数
   */
  get(sql, params = []) {
    const db = this.getConnection();
    const stmt = db.prepare(sql);
    return stmt.get(params);
  }

  /**
   * 执行插入、更新、删除操作
   * @param {string} sql - SQL语句
   * @param {Array|Object} params - 参数
   */
  run(sql, params = []) {
    const db = this.getConnection();
    const stmt = db.prepare(sql);
    return stmt.run(params);
  }

  /**
   * 开始事务
   */
  beginTransaction() {
    const db = this.getConnection();
    return db.transaction(() => {});
  }

  /**
   * 测试数据库连接和加密功能
   */
  testConnection() {
    try {
      // 创建测试表
      this.exec(`
        CREATE TABLE IF NOT EXISTS test_encryption (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          test_data TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 插入测试数据
      const testData = '这是一条测试数据，用于验证加密功能 - ' + new Date().toISOString();
      const result = this.run('INSERT INTO test_encryption (test_data) VALUES (?)', [testData]);

      // 查询测试数据
      const retrieved = this.get('SELECT * FROM test_encryption WHERE id = ?', [result.lastInsertRowid]);

      // 验证数据完整性
      if (retrieved && retrieved.test_data === testData) {
        console.log('数据库加密功能测试成功');
        
        // 清理测试数据
        this.run('DELETE FROM test_encryption WHERE id = ?', [result.lastInsertRowid]);
        
        return true;
      } else {
        throw new Error('数据完整性验证失败');
      }
    } catch (error) {
      console.error('数据库连接测试失败:', error);
      throw error;
    }
  }

  /**
   * 创建或更新数据库表结构
   * @private
   */
  _createSchema() {
    const db = this.getConnection();
    const schemaScript = `
      CREATE TABLE IF NOT EXISTS sessions (
        id TEXT PRIMARY KEY,
        name TEXT,
        ext TEXT,
        created_at INTEGER NOT NULL,
        last_modified INTEGER
      );

      CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        session_id TEXT NOT NULL,
        sender TEXT NOT NULL,
        type TEXT NOT NULL,
        content TEXT NOT NULL,
        media_uri TEXT,
        status TEXT NOT NULL,
        ext TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (session_id) REFERENCES sessions (id) ON DELETE CASCADE
      );
    `;
    db.exec(schemaScript);
    console.log('数据表结构检查/创建完成。');
  }

  /**
   * 关闭数据库连接
   */
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.isInitialized = false;
      console.log('数据库连接已关闭');
    }
  }

  /**
   * 检查数据库是否已初始化
   */
  isReady() {
    return this.isInitialized && this.db !== null;
  }
}

// 导出单例实例
const databaseManager = new DatabaseManager();

module.exports = databaseManager; 