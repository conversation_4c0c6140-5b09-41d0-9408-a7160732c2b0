const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const { app, safeStorage } = require('electron');

class KeyManager {
  constructor() {
    this.keyCache = new Map();
    this.isInitialized = false;
  }

  /**
   * 初始化密钥管理器
   */
  initialize() {
    try {
      // 检查系统是否支持安全存储
      if (!safeStorage.isEncryptionAvailable()) {
        console.warn('系统不支持安全存储，将使用备用方案');
      }
      
      this.isInitialized = true;
      console.log('密钥管理器初始化成功');
      return true;
    } catch (error) {
      console.error('密钥管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 生成新的数据库加密密钥
   * @param {number} length - 密钥长度（字节）
   * @returns {string} 生成的密钥（hex格式）
   */
  generateDatabaseKey(length = 32) {
    try {
      // 使用加密安全的随机数生成器
      const keyBuffer = crypto.randomBytes(length);
      return keyBuffer.toString('hex');
    } catch (error) {
      console.error('密钥生成失败:', error);
      throw error;
    }
  }

  /**
   * 安全存储密钥
   * @param {string} keyId - 密钥标识符
   * @param {string} key - 要存储的密钥
   */
  storeKey(keyId, key) {
    try {
      if (!this.isInitialized) {
        throw new Error('密钥管理器未初始化');
      }

      const keyData = {
        keyId,
        key,
        createdAt: new Date().toISOString(),
        version: '1.0'
      };

      let encryptedData;
      
      if (safeStorage.isEncryptionAvailable()) {
        // 使用 Electron 的安全存储
        const keyDataString = JSON.stringify(keyData);
        encryptedData = safeStorage.encryptString(keyDataString);
      } else {
        // 备用方案：使用简单的混淆（注意：这不是真正的加密）
        console.warn('使用备用密钥存储方案（安全性较低）');
        const keyDataString = JSON.stringify(keyData);
        encryptedData = Buffer.from(keyDataString).toString('base64');
      }

      // 存储到用户数据目录
      const keyStorePath = this.getKeyStorePath();
      const keyFilePath = path.join(keyStorePath, `${keyId}.key`);
      
      // 确保目录存在
      if (!fs.existsSync(keyStorePath)) {
        fs.mkdirSync(keyStorePath, { recursive: true, mode: 0o700 });
      }

      // 写入加密的密钥文件
      fs.writeFileSync(keyFilePath, encryptedData, { mode: 0o600 });
      
      // 缓存密钥
      this.keyCache.set(keyId, key);
      
      console.log('密钥存储成功:', keyId);
      return true;
    } catch (error) {
      console.error('密钥存储失败:', error);
      throw error;
    }
  }

  /**
   * 获取存储的密钥
   * @param {string} keyId - 密钥标识符
   * @returns {string|null} 密钥或null（如果不存在）
   */
  getKey(keyId) {
    try {
      if (!this.isInitialized) {
        throw new Error('密钥管理器未初始化');
      }

      // 先检查缓存
      if (this.keyCache.has(keyId)) {
        return this.keyCache.get(keyId);
      }

      // 从文件读取
      const keyStorePath = this.getKeyStorePath();
      const keyFilePath = path.join(keyStorePath, `${keyId}.key`);

      if (!fs.existsSync(keyFilePath)) {
        console.log('密钥文件不存在:', keyId);
        return null;
      }

      const encryptedData = fs.readFileSync(keyFilePath);
      let keyDataString;

      if (safeStorage.isEncryptionAvailable()) {
        // 使用 Electron 的安全存储解密
        keyDataString = safeStorage.decryptString(encryptedData);
      } else {
        // 备用方案：解码base64
        keyDataString = Buffer.from(encryptedData.toString(), 'base64').toString();
      }

      const keyData = JSON.parse(keyDataString);
      
      // 验证密钥数据
      if (!keyData.key || keyData.keyId !== keyId) {
        throw new Error('密钥数据损坏');
      }

      // 缓存密钥
      this.keyCache.set(keyId, keyData.key);
      
      console.log('密钥获取成功:', keyId);
      return keyData.key;
    } catch (error) {
      console.error('密钥获取失败:', error);
      return null;
    }
  }

  /**
   * 获取或创建数据库主密钥
   * @returns {string} 数据库主密钥
   */
  getDatabaseMasterKey() {
    const masterKeyId = 'database_master_key';
    
    // 尝试获取现有密钥
    let masterKey = this.getKey(masterKeyId);
    
    if (!masterKey) {
      // 生成新的主密钥
      console.log('生成新的数据库主密钥...');
      masterKey = this.generateDatabaseKey(32); // 256位密钥
      this.storeKey(masterKeyId, masterKey);
    }
    
    return masterKey;
  }

  /**
   * 删除密钥
   * @param {string} keyId - 密钥标识符
   */
  deleteKey(keyId) {
    try {
      // 从缓存删除
      this.keyCache.delete(keyId);
      
      // 从文件系统删除
      const keyStorePath = this.getKeyStorePath();
      const keyFilePath = path.join(keyStorePath, `${keyId}.key`);
      
      if (fs.existsSync(keyFilePath)) {
        fs.unlinkSync(keyFilePath);
        console.log('密钥删除成功:', keyId);
      }
      
      return true;
    } catch (error) {
      console.error('密钥删除失败:', error);
      throw error;
    }
  }

  /**
   * 列出所有存储的密钥ID
   * @returns {string[]} 密钥ID列表
   */
  listKeys() {
    try {
      const keyStorePath = this.getKeyStorePath();
      
      if (!fs.existsSync(keyStorePath)) {
        return [];
      }
      
      const files = fs.readdirSync(keyStorePath);
      return files
        .filter(file => file.endsWith('.key'))
        .map(file => path.basename(file, '.key'));
    } catch (error) {
      console.error('列出密钥失败:', error);
      return [];
    }
  }

  /**
   * 轮换数据库密钥（高级功能）
   * @param {string} oldKeyId - 旧密钥ID
   * @param {string} newKeyId - 新密钥ID
   * @returns {string} 新密钥
   */
  rotateKey(oldKeyId, newKeyId) {
    try {
      // 生成新密钥
      const newKey = this.generateDatabaseKey(32);
      
      // 存储新密钥
      this.storeKey(newKeyId, newKey);
      
      // 注意：实际的数据库重新加密需要在数据库层面处理
      console.log('密钥轮换完成:', oldKeyId, '->', newKeyId);
      
      return newKey;
    } catch (error) {
      console.error('密钥轮换失败:', error);
      throw error;
    }
  }

  /**
   * 获取密钥存储路径
   * @returns {string} 密钥存储目录路径
   */
  getKeyStorePath() {
    const userDataPath = app.getPath('userData');
    return path.join(userDataPath, '.gongzhimall', 'keys');
  }

  /**
   * 清理密钥缓存
   */
  clearCache() {
    this.keyCache.clear();
    console.log('密钥缓存已清理');
  }

  /**
   * 验证密钥管理器状态
   */
  validateState() {
    return {
      isInitialized: this.isInitialized,
      cacheSize: this.keyCache.size,
      encryptionAvailable: safeStorage.isEncryptionAvailable(),
      keyStorePath: this.getKeyStorePath()
    };
  }
}

// 导出单例实例
const keyManager = new KeyManager();

module.exports = keyManager; 