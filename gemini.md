# Gemini Project Configuration: gongzhimall

This document outlines the structure and conventions of the `gongzhimall` project to ensure consistent and effective assistance.

## Project Overview

This is a monorepo project containing several applications:

- `admin-web`: A React-based web front-end for administration.
- `backend`: A Node.js and Express-based backend server.
- `mobile`: A React Native application for mobile devices.
- `desktop`: An Electron-based desktop application.

## Application Details

### 1. Admin Web (`admin-web`)

- **Technology**: React, TypeScript
- **Source Directory**: `admin-web/src`
- **Key Scripts**:
  - `npm start`: Starts the development server.
  - `npm run build`: Builds the application for production.
  - `npm test`: Runs the tests.

### 2. Backend (`backend`)

- **Technology**: Node.js, Express
- **Main File**: `backend/server.js`
- **Routes**: `backend/routes/`
- **Key Scripts**:
  - `npm start`: Starts the backend server.

### 3. Mobile App (`mobile`)

- **Technology**: React Native
- **Main File**: `mobile/App.tsx`
- **Source Directory**: `mobile/src`
- **Key Scripts**:
  - `yarn android`: Runs the app on Android.
  - `yarn ios`: Runs the app on iOS.
  - `yarn start`: Starts the Metro bundler.
  - `yarn test`: Runs the tests.
  - `yarn lint`: Lints the code.

### 4. Desktop App (`desktop`)

- **Technology**: Electron
- **Main File**: `desktop/main.js`
- **Source Directory**: `desktop/src`
- **Key Scripts**:
  - `npm start`: Starts the desktop application.

## CI/CD

The CI/CD pipeline is defined in `.gitee/workflows/ci.yml` and appears to be configured to build and test the `backend` application.

## How to Use This Information

With this configuration, I can provide more targeted assistance. For example, you can ask me to:

- "在 `admin-web` 中添加一个新的页面" (Add a new page in `admin-web`)
- "启动 `backend` 服务器" (Start the `backend` server)
- "为 `mobile` 应用的某个组件编写测试" (Write a test for a component in the `mobile` app)
