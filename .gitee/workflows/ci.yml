name: 公职猫 CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'yarn'
      
      - name: Install dependencies
        run: yarn install --frozen-lockfile
      
      - name: Lint check
        run: |
          cd mobile && yarn lint
          cd ../backend && yarn lint
          cd ../desktop && yarn lint
      
      - name: TypeScript check
        run: |
          cd mobile && yarn tsc --noEmit
          cd ../desktop && yarn tsc --noEmit

  # 单元测试
  test:
    runs-on: ubuntu-latest
    needs: code-quality
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'yarn'
      
      - name: Install dependencies
        run: yarn install --frozen-lockfile
      
      - name: Run tests
        run: |
          cd mobile && yarn test --coverage
          cd ../backend && yarn test --coverage
          cd ../desktop && yarn test --coverage

  # Android构建（华为）
  build-android:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'yarn'
      
      - name: Setup JDK 11
        uses: actions/setup-java@v3
        with:
          java-version: '11'
          distribution: 'temurin'
      
      - name: Setup Android SDK
        uses: android-actions/setup-android@v2
      
      - name: Install dependencies
        run: |
          cd mobile && yarn install --frozen-lockfile
      
      - name: Build Android APK
        run: |
          cd mobile/android
          ./gradlew assembleRelease
        env:
          HUAWEI_KEYSTORE: ${{ secrets.HUAWEI_KEYSTORE }}
          HUAWEI_KEYSTORE_PASSWORD: ${{ secrets.HUAWEI_KEYSTORE_PASSWORD }}
      
      - name: Upload APK
        uses: actions/upload-artifact@v3
        with:
          name: gongzhimall-android-release
          path: mobile/android/app/build/outputs/apk/release/

  # iOS构建
  build-ios:
    runs-on: macos-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'yarn'
      
      - name: Install dependencies
        run: |
          cd mobile && yarn install --frozen-lockfile
      
      - name: Install CocoaPods
        run: |
          cd mobile/ios
          pod install
      
      - name: Build iOS
        run: |
          cd mobile/ios
          xcodebuild -workspace GongZhiMallMobile.xcworkspace \
                     -scheme GongZhiMallMobile \
                     -destination 'generic/platform=iOS' \
                     -archivePath GongZhiMallMobile.xcarchive \
                     archive
        env:
          IOS_CERTIFICATE_P12: ${{ secrets.IOS_CERTIFICATE_P12 }}
          IOS_CERTIFICATE_PASSWORD: ${{ secrets.IOS_CERTIFICATE_PASSWORD }}
      
      - name: Upload iOS Archive
        uses: actions/upload-artifact@v3
        with:
          name: gongzhimall-ios-archive
          path: mobile/ios/GongZhiMallMobile.xcarchive

  # 桌面端构建
  build-desktop:
    runs-on: ${{ matrix.os }}
    needs: test
    if: github.ref == 'refs/heads/main'
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'yarn'
      
      - name: Install dependencies
        run: |
          cd desktop && yarn install --frozen-lockfile
      
      - name: Build Desktop App
        run: |
          cd desktop && yarn build
      
      - name: Package Desktop App
        run: |
          cd desktop && yarn electron:package
      
      - name: Upload Desktop Package
        uses: actions/upload-artifact@v3
        with:
          name: gongzhimall-desktop-${{ matrix.os }}
          path: desktop/dist/

  # 后端构建
  build-backend:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'yarn'
      
      - name: Install dependencies
        run: |
          cd backend && yarn install --frozen-lockfile
      
      - name: Build Backend
        run: |
          cd backend && yarn build
      
      - name: Upload Backend Build
        uses: actions/upload-artifact@v3
        with:
          name: gongzhimall-backend
          path: backend/dist/

  # 发布到华为应用市场
  deploy-huawei:
    runs-on: ubuntu-latest
    needs: build-android
    if: github.ref == 'refs/heads/main' && contains(github.event.head_commit.message, '[release]')
    steps:
      - name: Download Android APK
        uses: actions/download-artifact@v3
        with:
          name: gongzhimall-android-release
      
      - name: Deploy to Huawei AppGallery
        run: |
          # 华为应用市场发布脚本
          echo "Deploying to Huawei AppGallery..."
        env:
          HUAWEI_APP_ID: ${{ secrets.HUAWEI_APP_ID }}
          HUAWEI_CLIENT_SECRET: ${{ secrets.HUAWEI_CLIENT_SECRET }}
          HUAWEI_CLIENT_ID: ${{ secrets.HUAWEI_CLIENT_ID }}

  # 发布到App Store
  deploy-ios:
    runs-on: macos-latest
    needs: build-ios
    if: github.ref == 'refs/heads/main' && contains(github.event.head_commit.message, '[release]')
    steps:
      - name: Download iOS Archive
        uses: actions/download-artifact@v3
        with:
          name: gongzhimall-ios-archive
      
      - name: Deploy to App Store
        run: |
          # App Store发布脚本
          echo "Deploying to App Store..."
        env:
          APPSTORE_CONNECT_API_KEY: ${{ secrets.APPSTORE_CONNECT_API_KEY }}
          APPSTORE_CONNECT_ISSUER_ID: ${{ secrets.APPSTORE_CONNECT_ISSUER_ID }}
          APPSTORE_CONNECT_KEY_ID: ${{ secrets.APPSTORE_CONNECT_KEY_ID }}

  # 发布到官网
  deploy-website:
    runs-on: ubuntu-latest
    needs: [build-desktop, build-backend]
    if: github.ref == 'refs/heads/main' && contains(github.event.head_commit.message, '[release]')
    steps:
      - name: Download Desktop Packages
        uses: actions/download-artifact@v3
        with:
          name: gongzhimall-desktop-windows-latest
          path: ./desktop-windows
      
      - name: Download Desktop Packages (macOS)
        uses: actions/download-artifact@v3
        with:
          name: gongzhimall-desktop-macos-latest
          path: ./desktop-macos
      
      - name: Download Backend Build
        uses: actions/download-artifact@v3
        with:
          name: gongzhimall-backend
          path: ./backend
      
      - name: Deploy to www.gongzhimall.com
        run: |
          # 官网发布脚本
          echo "Deploying to www.gongzhimall.com..."
        env:
          WEBSITE_DEPLOY_KEY: ${{ secrets.WEBSITE_DEPLOY_KEY }} 