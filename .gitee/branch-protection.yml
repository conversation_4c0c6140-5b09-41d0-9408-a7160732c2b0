# Gitee分支保护配置
# 需要在Gitee仓库设置中手动配置

master:
  protection_level: "high"
  required_reviews: 2
  dismiss_stale_reviews: true
  require_code_owner_reviews: true
  required_status_checks:
    - "ci/build"
    - "ci/test"
    - "security/scan"
  restrictions:
    push_allowlist: []  # 禁止直接推送
    merge_allowlist: ["project-manager", "lead-developer"]

develop:
  protection_level: "medium"
  required_reviews: 1
  dismiss_stale_reviews: true
  required_status_checks:
    - "ci/build"
    - "ci/test"
  restrictions:
    push_allowlist: []  # 禁止直接推送
    merge_allowlist: ["core-developers"]

feature/*:
  protection_level: "low"
  required_reviews: 0
  required_status_checks:
    - "ci/build"
  restrictions:
    push_allowlist: ["core-developers", "platform-developers"]
    merge_allowlist: ["core-developers"]

outsourcing/*:
  protection_level: "medium"
  required_reviews: 1
  required_status_checks:
    - "ci/build"
    - "ci/test"
    - "security/scan"
  restrictions:
    push_allowlist: ["outsourcing-teams"]
    merge_allowlist: ["core-developers"]
