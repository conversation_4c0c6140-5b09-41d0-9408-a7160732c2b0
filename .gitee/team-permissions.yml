# 团队权限配置
# 需要在Gitee组织设置中配置

teams:
  core-developers:
    members: ["project-manager", "lead-developer", "senior-developer"]
    permissions:
      - "admin"
      - "push"
      - "pull"
      - "maintain"
    access_paths: ["*"]
    
  platform-developers:
    android-team:
      permissions: ["push", "pull"]
      access_paths: 
        - "mobile/android/**"
        - "mobile/src/**"
        - "docs/03_design/移动端/**"
    
    ios-team:
      permissions: ["push", "pull"]
      access_paths:
        - "mobile/ios/**"
        - "mobile/src/**"
        - "docs/03_design/移动端/**"
    
    desktop-team:
      permissions: ["push", "pull"]
      access_paths:
        - "desktop/**"
        - "shared/**"
        - "docs/03_design/桌面端/**"
  
  outsourcing-teams:
    permissions: ["push", "pull"]
    restrictions:
      - "no-backend-access"
      - "no-crypto-access"
      - "no-env-files"
    access_paths:
      - "mobile/android/**"  # 仅示例，实际按分配调整
    forbidden_paths:
      - "backend/**"
      - "shared/crypto/**"
      - ".env*"
      - "secrets/**"

  designers:
    permissions: ["pull"]
    access_paths:
      - "docs/03_design/**"
      - "mobile/src/styles/**"
      - "desktop/src/styles/**"
