<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公职猫隐私政策 - 杭州芝麻巧匠科技有限公司</title>
    <meta name="description" content="公职猫App隐私政策，保护您的隐私和数据安全">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .header {
            text-align: center;
            border-bottom: 2px solid #FF6B35;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background-color: #FF6B35;
            border-radius: 16px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        h1 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 14px;
        }

        .update-info {
            background-color: #e8f4f8;
            border-left: 4px solid #FF6B35;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }

        h2 {
            color: #2c3e50;
            font-size: 20px;
            margin: 30px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }

        h3 {
            color: #34495e;
            font-size: 16px;
            margin: 20px 0 10px 0;
        }

        p {
            margin-bottom: 15px;
            text-align: justify;
        }

        ul, ol {
            margin: 15px 0;
            padding-left: 25px;
        }

        li {
            margin-bottom: 8px;
        }

        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 15px 0;
        }

        .sdk-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }

        .sdk-table th,
        .sdk-table td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: left;
        }

        .sdk-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        .sdk-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .contact-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }

        .privacy-core {
            background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .privacy-core h3 {
            color: white;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            h2 {
                font-size: 18px;
            }
            
            .sdk-table {
                font-size: 12px;
            }
            
            .sdk-table th,
            .sdk-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>公职猫隐私政策</h1>
            <p class="subtitle">杭州芝麻巧匠科技有限公司</p>
        </div>

        <div class="update-info">
            <strong>发布日期：</strong>2025年7月22日<br>
            <strong>生效日期：</strong>2025年8月1日<br>
            <strong>版本：</strong>1.0
        </div>

        <div class="privacy-core">
            <h3>🔒 我们的隐私承诺</h3>
            <p><strong>"U盘级私密，秘书般懂你"</strong> - 公职猫承诺您的个人工作内容、文档、照片等敏感信息优先本地存储和处理。我们严格保护您的隐私，不会将您的个人数据用于商业目的。您的隐私安全是我们的第一要务。</p>
        </div>

        <p>杭州芝麻巧匠科技有限公司（以下简称"我们"或"公司"）深知用户个人信息的重要性，并致力于保护您的隐私和数据安全。我们开发的应用"公职猫"（以下简称"本应用"或"App"）严格遵循《中华人民共和国网络安全法》、《中华人民共和国数据安全法》、《中华人民共和国个人信息保护法》等相关法律法规，在您使用过程中将严格按照本隐私政策处理相关信息。</p>

        <div class="highlight">
            <strong>重要提示：</strong>请您在使用本应用前仔细阅读本隐私政策。如您不同意本政策的任何内容，请立即停止使用本应用。继续使用本应用即表示您同意我们按照本政策处理您的信息。
        </div>

        <h2>一、适用范围</h2>
        <p>本隐私政策适用于公职猫移动应用程序（iOS版、Android版）及相关服务。本政策不适用于第三方服务，第三方服务的隐私保护措施请查看相应的隐私政策。</p>

        <h2>二、我们如何收集和使用您的信息</h2>
        
        <h3>2.1 我们收集的信息类型</h3>
        <p><strong>本应用采用"数据最小化"原则，不要求用户注册登录</strong>，仅在以下情况下收集有限的必要信息：</p>

        <h3>2.2 设备信息</h3>
        <p>为保障本应用功能正常运行、进行崩溃分析和性能优化，我们可能会收集以下设备信息：</p>
        <ul>
            <li>设备型号、品牌、操作系统版本</li>
            <li>设备唯一标识符（如IMEI、Android ID、IDFA等，我们会对这些标识符进行加密处理）</li>
            <li>屏幕分辨率、设备语言设置</li>
            <li>应用版本号、崩溃日志</li>
            <li>网络类型（WiFi/移动网络）</li>
        </ul>

        <h3>2.3 位置信息</h3>
        <p>仅在您主动要求时，我们可能获取您的位置信息，用于：</p>
        <ul>
            <li>拍照时记录地点信息（如您选择开启）</li>
            <li>日程安排中的地点标记</li>
            <li>为您提供基于位置的个性化服务</li>
            <li>运营统计与用户分布分析（匿名化处理）</li>
        </ul>
        <p><strong>重要说明：</strong>位置信息的收集完全基于您的主动选择，我们不会在您不知情的情况下获取位置数据。这些信息仅用于为您提供更好的服务，不会用于其他商业目的。</p>

        <h3>2.4 应用使用信息</h3>
        <p>为了改进产品功能和用户体验，我们可能收集以下使用统计信息：</p>
        <ul>
            <li>功能使用频率和时长</li>
            <li>操作流程和用户行为模式（匿名化处理）</li>
            <li>错误报告和性能数据</li>
        </ul>

        <h3>2.5 权限使用说明</h3>
        <p>本应用需要获取以下权限以提供相应功能：</p>
        <ul>
            <li><strong>相机权限：</strong>用于拍照识别文档内容（核心OCR功能）</li>
            <li><strong>存储权限：</strong>用于保存和读取本地文档、图片</li>
            <li><strong>网络权限：</strong>用于消息推送、在线功能（可选）</li>
            <li><strong>位置权限：</strong>用于获取大致位置信息（可选，用户可拒绝）</li>
        </ul>
        <p>您可以在设备的系统设置中随时撤销这些权限，但可能影响相关功能的正常使用。</p>

        <h2>三、第三方SDK信息收集</h2>
        <p>为了实现消息推送、微信分享等功能，我们集成了以下第三方SDK。这些SDK可能会收集相关信息，具体如下：</p>

        <table class="sdk-table">
            <thead>
                <tr>
                    <th>SDK名称</th>
                    <th>提供方</th>
                    <th>使用目的</th>
                    <th>涉及权限/数据类型</th>
                    <th>隐私政策</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>极光推送SDK</td>
                    <td>深圳市和讯华谷信息技术有限公司</td>
                    <td>消息推送服务</td>
                    <td>设备标识符、设备型号、网络状态、操作系统版本</td>
                    <td><a href="https://www.jiguang.cn/license/privacy" target="_blank">查看隐私政策</a></td>
                </tr>
                <tr>
                    <td>微信SDK</td>
                    <td>深圳市腾讯计算机系统有限公司</td>
                    <td>微信分享、跳转功能</td>
                    <td>操作系统、设备信息、应用列表</td>
                    <td><a href="https://privacy.qq.com/policy/tencent-privacypolicy" target="_blank">查看隐私政策</a></td>
                </tr>
            </tbody>
        </table>

        <p><strong>重要说明：</strong>第三方SDK的数据处理行为受其自身隐私政策约束。我们已要求第三方严格遵循相关法律法规，但无法完全控制第三方的数据处理行为。建议您详细阅读第三方的隐私政策。</p>

        <h2>四、我们如何存储和保护您的信息</h2>

        <h3>4.1 数据存储原则</h3>
        <div class="privacy-core">
            <h3>🔐 核心承诺：隐私优先存储</h3>
            <ul style="color: white; margin: 0;">
                <li><strong>个人工作内容：</strong>您的文档、照片、笔记、待办事项等个人数据存储在您的设备本地</li>
                <li><strong>AI处理：</strong>文字识别、智能分析等功能优先在本地执行，部分高级功能可能需要云端AI支持</li>
                <li><strong>数据安全：</strong>所有数据传输均采用加密保护，云端处理完成后立即删除临时数据</li>
                <li><strong>跨设备同步：</strong>如需跨设备使用，数据通过加密方式在您的设备间传输</li>
            </ul>
        </div>

        <h3>4.2 云端数据</h3>
        <p>我们仅在云端存储以下非敏感的统计信息：</p>
        <ul>
            <li>匿名化的设备信息和使用统计</li>
            <li>应用崩溃和错误报告</li>
            <li>功能使用频率（不包含具体内容）</li>
        </ul>

        <h3>4.3 临时缓存数据</h3>
        <p>为了提升您的使用体验，我们可能会临时缓存以下数据：</p>
        <ul>
            <li><strong>下载缓存：</strong>当您下载文档、模板等资源时，我们会临时缓存这些数据以加快下载速度</li>
            <li><strong>处理原则：</strong>缓存数据仅用于传输优化，我们不会读取、分析或使用这些数据的具体内容</li>
            <li><strong>自动删除：</strong>下载完成后，缓存数据会立即自动删除，不会在我们的服务器上保留</li>
            <li><strong>安全保障：</strong>缓存过程中数据经过加密处理，确保传输安全</li>
        </ul>
        <p><strong>重要说明：</strong>此类缓存数据纯粹用于技术实现，我们承诺不会对缓存内容进行任何形式的分析、学习或商业利用。</p>

        <h3>4.4 数据安全措施</h3>
        <p>我们采取以下安全措施保护您的信息：</p>
        <ul>
            <li><strong>加密存储：</strong>本地数据采用AES-256加密算法存储</li>
            <li><strong>传输加密：</strong>网络传输采用HTTPS/TLS加密</li>
            <li><strong>访问控制：</strong>严格限制员工对用户数据的访问权限</li>
            <li><strong>安全审计：</strong>定期进行安全评估和漏洞扫描</li>
            <li><strong>数据备份：</strong>重要数据进行多重备份，防止数据丢失</li>
        </ul>

        <h3>4.5 数据保留期限</h3>
        <ul>
            <li><strong>本地数据：</strong>由您自主控制，可随时删除</li>
            <li><strong>统计数据：</strong>保留期不超过24个月</li>
            <li><strong>日志数据：</strong>保留期不超过12个月（符合等保三级要求）</li>
            <li><strong>缓存数据：</strong>下载完成后立即删除，最长不超过24小时</li>
        </ul>

        <h2>五、我们如何使用您的信息</h2>
        <p>我们仅在以下目的范围内使用收集的信息：</p>
        <ol>
            <li><strong>提供核心服务：</strong>文档识别、智能分析、任务管理等功能</li>
            <li><strong>改进产品质量：</strong>修复bug、优化性能、提升用户体验</li>
            <li><strong>运营分析：</strong>了解用户需求、优化产品功能</li>
            <li><strong>安全保障：</strong>检测和防范安全威胁、欺诈行为</li>
            <li><strong>法律合规：</strong>遵守相关法律法规要求</li>
        </ol>

        <p><strong>我们承诺不会：</strong></p>
        <ul>
            <li>将您的个人信息出售给第三方</li>
            <li>用于商业广告推送（除非您主动同意）</li>
            <li>与其他应用共享您的个人数据</li>
            <li>进行用户画像分析或精准营销</li>
        </ul>

        <h2>六、信息共享、转让、公开披露</h2>

        <h3>6.1 共享</h3>
        <p>我们不会主动共享您的个人信息，除非：</p>
        <ul>
            <li>获得您的明确同意</li>
            <li>法律法规要求或司法机关要求</li>
            <li>为保护用户或公众的生命、财产安全</li>
            <li>与我们的关联公司共享（仅限于提供服务所必需）</li>
        </ul>

        <h3>6.2 转让</h3>
        <p>我们不会将您的个人信息转让给任何公司、组织和个人，但以下情况除外：</p>
        <ul>
            <li>获得您的明确同意</li>
            <li>发生合并、收购或破产清算时，如涉及个人信息转让，我们会要求新的持有方继续受此隐私政策约束</li>
        </ul>

        <h3>6.3 公开披露</h3>
        <p>我们仅会在以下情况下公开披露您的个人信息：</p>
        <ul>
            <li>获得您明确同意后</li>
            <li>基于法律法规、法律程序、诉讼或政府主管部门强制性要求</li>
        </ul>

        <h2>七、您的权利</h2>
        <p>根据相关法律法规，您享有以下权利：</p>

        <h3>7.1 访问权</h3>
        <p>您有权了解我们处理您个人信息的情况，包括处理目的、数据类型等。</p>

        <h3>7.2 更正权</h3>
        <p>如发现我们处理的关于您的个人信息有错误的，您有权要求我们更正。</p>

        <h3>7.3 删除权</h3>
        <p>在以下情形中，您可以要求我们删除个人信息：</p>
        <ul>
            <li>处理目的已实现、无法实现或为实现处理目的不再必要</li>
            <li>我们停止提供产品或服务，或保存期限已届满</li>
            <li>您撤回同意</li>
            <li>我们违法处理您的个人信息</li>
        </ul>

        <h3>7.4 撤回同意权</h3>
        <p>对于基于您同意处理的个人信息，您有权随时撤回同意。撤回同意不影响撤回前基于您同意已进行的个人信息处理活动的效力。</p>

        <h3>7.5 注销权</h3>
        <p>您可以随时停止使用我们的产品或服务。由于本应用不需要注册账户，您可以直接卸载应用来停止我们对您个人信息的收集。</p>

        <h3>7.6 权利行使方式</h3>
        <p>如需行使上述权利，请通过本政策末尾的联系方式与我们联系。为保障安全，我们可能需要您提供身份证明。我们将在15个工作日内回复您的请求。</p>

        <h2>八、未成年人信息保护</h2>
        <p>我们非常重视未成年人个人信息的保护：</p>
        <ul>
            <li>本应用主要面向成年用户，不主动收集未满14周岁儿童的个人信息</li>
            <li>如我们发现自己在未事先获得可证实的父母同意的情况下收集了未满14周岁儿童的个人信息，则会设法尽快删除相关数据</li>
            <li>如您是未满18周岁的未成年人，建议您请您的父母或监护人仔细阅读本隐私政策，并在征得您的父母或监护人同意后使用我们的服务或向我们提供信息</li>
        </ul>

        <h2>九、隐私政策的更新</h2>
        <p>我们可能会根据以下情况更新本隐私政策：</p>
        <ul>
            <li>法律法规、监管政策发生变化</li>
            <li>产品功能或服务内容发生重大变更</li>
            <li>数据处理方式发生变化</li>
        </ul>

        <p><strong>更新通知方式：</strong></p>
        <ul>
            <li>重大变更：应用内弹窗通知，需要您重新确认同意</li>
            <li>一般变更：在应用内或官网发布更新公告</li>
            <li>我们会在政策生效前至少7天进行通知</li>
        </ul>

        <h2>十、争议解决</h2>
        <p>如您认为我们的个人信息处理行为损害了您的合法权益，您可以：</p>
        <ol>
            <li>通过本政策提供的联系方式与我们联系</li>
            <li>向有关监管部门投诉举报</li>
            <li>通过法律途径解决争议</li>
        </ol>

        <p>本隐私政策的解释、效力及纠纷的解决将适用中华人民共和国法律。因本政策引起的或与本政策有关的争议，双方应友好协商解决；协商不成的，任何一方均可向杭州芝麻巧匠科技有限公司所在地有管辖权的人民法院提起诉讼。</p>

        <div class="contact-info">
            <h2>十一、联系我们</h2>
            <p>如您对本隐私政策有任何疑问、意见或建议，或需要行使您的权利，您可以通过以下方式与我们联系：</p>
            
            <p><strong>公司信息：</strong></p>
            <ul>
                <li>公司名称：杭州芝麻巧匠科技有限公司</li>
                <li>注册地址：[杭州市上城区四季青街道钱塘银座5楼]</li>
                <li>联系邮箱：<a href="mailto:<EMAIL>"><EMAIL></a></li>
            </ul>

            <p><strong>个人信息保护负责人：</strong></p>
            <ul>
                <li>联系邮箱：<a href="mailto:<EMAIL>"><EMAIL></a></li>
                <li>我们将在收到您的反馈后15个工作日内回复</li>
            </ul>
        </div>

        <div class="footer">
            <p><strong>杭州芝麻巧匠科技有限公司</strong></p>
            <p>本隐私政策最后更新时间：2025年7月22日</p>
            <p>© 2025 公职猫 All Rights Reserved</p>
        </div>
    </div>

    <script>
        // 添加平滑滚动效果
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // 添加返回顶部功能
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 100) {
                if (!document.querySelector('.back-to-top')) {
                    const backToTop = document.createElement('div');
                    backToTop.className = 'back-to-top';
                    backToTop.innerHTML = '↑';
                    backToTop.style.cssText = `
                        position: fixed;
                        bottom: 20px;
                        right: 20px;
                        width: 50px;
                        height: 50px;
                        background-color: #FF6B35;
                        color: white;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        font-size: 20px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                        transition: all 0.3s ease;
                        z-index: 1000;
                    `;
                    backToTop.addEventListener('click', () => {
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    });
                    document.body.appendChild(backToTop);
                }
            } else {
                const backToTop = document.querySelector('.back-to-top');
                if (backToTop) {
                    backToTop.remove();
                }
            }
        });
    </script>
</body>
</html> 