#!/bin/bash

set -e

# --- 配置 ---
SOURCE_ICON="appicon.png" # 源图标文件 (建议 512x512 或更大)
OUTPUT_DIR="dist/website_icons" # 输出目录

# --- 检查依赖 ---
if ! command -v convert &> /dev/null
then
    echo "错误: ImageMagick 未安装。请先安装 (例如, 在 macOS 上执行 'brew install imagemagick')."
    exit 1
fi

# --- 开始生成 ---
echo "正在从 $SOURCE_ICON 生成官网图标..."
mkdir -p $OUTPUT_DIR

# --- 生成不同尺寸的 PNG 图标 ---
echo "正在生成 PNG 图标..."
convert $SOURCE_ICON -resize 32x32   "$OUTPUT_DIR/favicon-32x32.png"
convert $SOURCE_ICON -resize 16x16  "$OUTPUT_DIR/favicon-16x16.png"
convert $SOURCE_ICON -resize 180x180 "$OUTPUT_DIR/apple-touch-icon.png"
convert $SOURCE_ICON -resize 192x192 "$OUTPUT_DIR/android-chrome-192x192.png"
convert $SOURCE_ICON -resize 512x512 "$OUTPUT_DIR/android-chrome-512x512.png"

# --- 生成多尺寸 favicon.ico ---
# 创建一个包含16x16和32x32尺寸的ICO文件，以获得最佳兼容性
echo "正在生成多尺寸 favicon.ico..."
convert "$OUTPUT_DIR/favicon-16x16.png" "$OUTPUT_DIR/favicon-32x32.png" -colors 256 "$OUTPUT_DIR/favicon.ico"

# --- 生成 Web App Manifest ---
echo "正在生成 site.webmanifest..."
cat > "$OUTPUT_DIR/site.webmanifest" << EOL
{
  "name": "公职猫",
  "short_name": "公职猫",
  "icons": [
    {
      "src": "./android-chrome-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "./android-chrome-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ],
  "theme_color": "#ffffff",
  "background_color": "#ffffff",
  "display": "standalone"
}
EOL

# --- HTML 使用指南 ---
echo "正在生成 HTML 使用指南..."
cat > "$OUTPUT_DIR/usage_guide.html" << EOL
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>公职猫 Icon 使用指南</title>
  <!-- 在你的 HTML <head> 中添加以下链接 -->
  <link rel="apple-touch-icon" sizes="180x180" href="./apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="./favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="./favicon-16x16.png">
  <link rel="manifest" href="./site.webmanifest">
  <link rel="shortcut icon" href="./favicon.ico">
  <meta name="msapplication-TileColor" content="#da532c">
  <meta name="theme-color" content="#ffffff">
</head>
<body>
  <h1>公职猫 Icon 使用指南</h1>
  <p>请将以下代码复制到你网站的 <code>&lt;head&gt;</code> 标签中，并确保所有图标文件与你的 HTML 文件在同一目录下或路径正确。</p>
  <pre>
    <code>
      &lt;link rel="apple-touch-icon" sizes="180x180" href="./apple-touch-icon.png"&gt;
      &lt;link rel="icon" type="image/png" sizes="32x32" href="./favicon-32x32.png"&gt;
      &lt;link rel="icon" type="image/png" sizes="16x16" href="./favicon-16x16.png"&gt;
      &lt;link rel="manifest" href="./site.webmanifest"&gt;
      &lt;link rel="shortcut icon" href="./favicon.ico"&gt;
      &lt;meta name="msapplication-TileColor" content="#da532c"&gt;
      &lt;meta name="theme-color" content="#ffffff"&gt;
    </code>
  </pre>
</body>
</html>
EOL

echo "✅ 官网图标已成功生成到 $OUTPUT_DIR 目录！"
echo "使用方法请参考: $OUTPUT_DIR/usage_guide.html"

# 使用说明:
# 1. 确保根目录有 appicon.png 文件 (推荐 512x512 或更大尺寸的PNG)
# 2. 赋予执行权限: chmod +x generate_website_icons.sh
# 3. 执行脚本: ./generate_website_icons.sh 