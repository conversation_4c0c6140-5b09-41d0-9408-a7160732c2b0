# 公职猫 - TaskMaster任务生成摘要 (PRD Summary)

> **完整需求文档**: 请参考 [`docs/02_requirements/prd.md`](../../docs/02_requirements/prd.md)
> 本文档仅包含TaskMaster任务生成所需的核心信息摘要

## 🎯 产品核心理念

**"U盘般私密，秘书般懂你"**

- **U盘般私密**: 数据主权归用户所有，100%本地存储，跨端传输自由
- **秘书般懂你**: 智能化程度高，替你记、替你想、替你做事

## 🏷 产品定位

公职猫不是写作工具，不是传统任务管理软件，而是全国首款"岗位数字分身"——智能体生态的底座。

## 🎯 MVP核心目标

- **时间目标**: 1个月内交付MVP
- **验证目标**: 证明"AI真的懂体制内工作"且"绝对安全"
- **用户目标**: 获取首批种子用户高度认可
- **核心承诺**: 真正做到"U盘般私密，秘书般懂你"

## 🏗 技术架构要求

- **移动端**: React Native (iOS & Android)
- **桌面端**: Electron (Windows & 统信UOS)
- **数据存储**: 加密SQLite本地数据库（100%本地存储）
- **AI架构**: 云端AI → 本地AI → 规则引擎（三级降级）
- **同步方案**: 移动端和桌面端数据本地同步

## 📋 MVP核心功能清单

### P0 - 必须实现

1. **智能信息采集**

   - 文字、语音、微信截图、拍照四种输入方式
   - 微信聊天截图OCR识别优化
   - 纸质文件手写批注识别
2. **文档导入与检索（U盘级存取）**

   - 多格式文档导入（Word、Excel、PDF、图片）
   - 领导日程文件智能识别
   - 全文检索功能（本地搜索引擎）
   - 文档预览管理
3. **个人重要资料管理**

   - 证件档案管理（身份证、证件照、证书）
   - 证件照处理功能（离线人像抠图、背景替换）
   - 个人档案维护
   - 快速调用功能
4. **角色智能识别与事务管理**（核心差异化功能）

   - 语境理解（体制内职级关系、流程规范）
   - 角色智能判断（主办、协办、参与）
   - 差异化管理（不同角色不同管理方式）
   - 系统能力沉淀
5. **本地安全存储（U盘般私密）**

   - 个人数据本地存储（100%本地）
   - AI个性化支持（本地学习用户习惯）
   - 跨端数据传输（移动端↔桌面端）
   - 基础加密保护
6. **日程事务管理**（增强版复杂日程支持）

   - 分类管理（简单日程vs复杂事务）
   - 进度跟踪（多状态管理）
   - 时间管理（智能提醒）
   - 工作习惯适配

   **复杂日程显示系统**：

   - **重要任务区**: 便利贴式80px×80px卡片，颜色编码优先级，支持拖拽排序
   - **跨天事件处理**:
     - 开始日显示：`23:00-次日01:00 ✈️ 飞机到杭州`
     - 结束日显示：`前日23:00-01:00 ✈️ 飞机到杭州 (续)`
     - 保持原始开始时间一致性
   - **重复事件管理**:
     - 标准格式：`09:00-10:00 部门例会 🔄`
     - 循环图标放在标题后面，保持语义完整性
     - 支持每日、每周、每月、自定义重复模式
   - **灵动符号表达系统**:
     - 全面支持：所有日程都支持emoji和语义符号
     - 智能推荐：基于关键词和场景的符号建议
     - 分类符号：交通出行、会议类型、学习培训、通讯联络等
     - 让整个日程系统灵动起来，增强可读性和情感连接
   - **4段式时间分段**:
     - 🌅 凌晨 (0-5时)
     - 🌄 上午 (6-11时)
     - 🌇 下午 (12-17时)
     - 🌃 晚上 (18-23时)

### P1 - 重要增强

- 智能提醒和风险预警
- 多设备数据同步
- 高级搜索和过滤

## 🎨 移动端主界面设计要求（最高优先级）

- **便利贴式重要跟踪任务展示**: 80px × 80px卡片，颜色编码优先级
- **日期导航区**: 支持左右切换日程
- **品牌色彩**: #FFA500背景，#FFFFFF内容区
- **中间区域布局**: 便签卡片和当日日程
- **交互功能**: 拖拽排序、快速添加、智能提醒

## 📊 复杂日程功能要求

### 重要任务区功能需求

- 便利贴式卡片设计 (80px × 80px)
- 三色优先级编码 (红色-紧急，橙色-重要，蓝色-一般)
- 拖拽排序功能
- 支持长期活动、重要事项、模糊时间段任务
- 最多显示6-8个重要任务

### 复杂事件处理需求

- **跨天事件**: 需要在多个日期正确显示，保持时间一致性
- **重复事件**: 支持每日、每周、每月、自定义重复模式
- **灵动符号**: 全面支持emoji和语义符号表达
- **角色识别**: 区分主办、协办、参与等不同角色
- **优先级管理**: 支持高、中、低优先级分类

## ✅ MVP验收标准

- **功能性**: 核心功能全部可用，关键信息识别准确率 > 60%
- **性能**: 应用启动 < 3秒，功能响应 < 2秒
- **安全性**: 数据100%本地存储，符合"U盘般私密"理念
- **智能性**: AI理解体制内工作特点，体现"秘书般懂你"
- **复杂日程处理**: 优雅处理跨天、重复、长期等复杂场景
- **用户认可**: 5-10名种子用户评分 > 4.0/5.0
- **留存**: 7日留存率 > 50%

## 🎨 核心用户价值

- **"U盘般私密"**: 数据完全本地化，可跨端传输，用户完全掌控
- **"秘书般懂你"**: AI理解体制内工作特点和用户习惯，越用越懂你
- **差异化管理**: 根据角色提供不同深度的任务管理
- **灵动表达**: 全面支持emoji符号，让日程系统生动直观
- **岗位数字分身**: 替你记、替你想、替你做事的智能体

## 🚀 开发优先级

1. **移动端主界面重构**（最高优先级）
2. **复杂日程显示系统实现**（新增高优先级）
3. **数据库设计完成**
4. **OCR识别集成**
5. **任务智能识别**
6. **跨端同步功能**

---

*本摘要专为TaskMaster任务生成优化，完整需求请参考主PRD文档*
*文档版本：v2.0  |  更新日期：2025-01-14*
