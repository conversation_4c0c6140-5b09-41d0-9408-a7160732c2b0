# 公职猫项目重新规划PRD

## 项目概述
公职猫是全国首款面向体制内个人的AI总助，核心理念"U盘般私密，秘书般懂你"。目标用户为中青年干部（17-23万潜在用户），特别是没有秘书和下属、集管理与执行于一身的人群。

## 技术架构
- **移动端**: React Native (iOS/Android)
- **桌面端**: Electron (Windows/macOS)  
- **数据存储**: SQLite + SQLCipher加密 (100%本地存储)
- **AI架构**: 云端AI → 本地AI → 规则引擎 (三级降级)

## 核心场景
1. 领导语音指示处理
2. 微信群通知识别
3. 会议截图识别
4. 纸质文档拍照
5. 领导日程导入

## MVP核心功能 (P0优先级)

### 1. 移动端主界面重构 (最高优先级)
**当前状态**: 基础任务列表，不符合设计要求
**目标界面**:
- **便利贴式重要任务展示**: 80px × 80px卡片，颜色编码(红-紧急，橙-重要，蓝-一般)，支持拖拽排序
- **日期导航**: 左右切换日期，当前日期/星期显示，快速日期跳转
- **视觉设计**: 深色主题 + 橙色主色调 (#1a1a1a背景，#FF8C00主色)
- **布局**: 上半部分便利贴卡片，下半部分日程列表，支持滑动导航

### 2. 智能信息收集
- 微信截图识别 (OCR准确率>60%)
- 语音指令理解
- 纸质文档拍照
- 文本输入

### 3. 文档导入与搜索
- 多格式导入支持
- 领导日程识别
- 全文搜索功能
- 文档预览

### 4. 个人重要资料管理
- 证件管理
- 照片处理 (离线人像提取，背景替换)
- 个人档案维护

### 5. 角色智能识别与任务管理
- 上下文理解
- 角色判断 (主办/协办/参与)
- 差异化管理

### 6. 本地安全存储
- 100%本地数据
- AI个性化
- 跨设备传输
- 基础加密

### 7. 日程与任务管理
- 分类管理
- 进度跟踪
- 时间管理
- 工作习惯适配

## 技术要求
- **响应性能**: 应用启动<3s，功能响应<2s
- **离线优先**: 核心功能完全离线
- **AI混合架构**: 云端AI → 本地AI → 规则引擎
- **安全性**: SQLite加密，HTTPS通信

## 成功指标
- 移动端界面完全符合设计要求
- 文档导入/搜索稳定可用
- 微信截图OCR准确率>60%
- 角色智能识别准确率>60%
- 5-10个种子用户认为"好用且安全" (满意度>4.0/5.0)
- 7日留存率>50%

## 周迭代标准
- **第1周**: 移动端主界面重构 (最高优先级)
- **第2周**: 数据库设计完成 + OCR集成
- **第3周**: 任务智能识别 + 规则引擎集成  
- **第4周**: 跨设备同步 + 集成测试

## 开发要求
- 优先使用AI编程提高效率
- 每个任务必须包含详细实现步骤
- 注重代码质量和用户体验
- 及时集成测试确保稳定性
- 每周交付可演示的用户价值增量

## 当前实际进度评估
- **移动端**: 20%完成度 (基础框架，界面不符合设计)
- **桌面端**: 未开始
- **数据库**: 部分完成 (基础表结构)
- **AI集成**: 测试阶段 (规则引擎基础)

## 重点关注
1. **移动端主界面**是第一优先级，必须完全符合便利贴式设计要求
2. **OCR功能**是核心差异化功能，必须真实可用
3. **角色智能识别**是商业价值核心，必须准确可靠
4. **本地存储安全**是用户信任基础，必须加密可靠
5. **周迭代交付**确保持续用户价值，每周都有可演示成果 