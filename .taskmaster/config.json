{"models": {"main": {"provider": "openrouter", "modelId": "mistralai/mistral-small-3.1-24b-instruct:free", "maxTokens": 64000, "temperature": 0.2, "model": "deepseek/deepseek-r1-0528-qwen3-8b:free"}, "research": {"provider": "openrouter", "modelId": "deepseek/deepseek-r1-0528-qwen3-8b:free", "maxTokens": 8700, "temperature": 0.1, "model": "deepseek/deepseek-r1-0528-qwen3-8b:free"}, "fallback": {"provider": "openrouter", "modelId": "thudm/glm-4-32b:free", "maxTokens": 64000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "公职猫", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "maxTokens": 4000, "temperature": 0.2, "projectVersion": "0.1.0", "userId": "**********", "defaultTag": "master"}}