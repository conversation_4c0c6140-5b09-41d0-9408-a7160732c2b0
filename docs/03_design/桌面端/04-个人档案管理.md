# 桌面端 - 04 个人档案管理

## 界面描述
桌面端个人档案管理界面，提供完整的个人重要资料管理和快速调用功能

## 线框示意图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🐱 公职猫                                                    🔔 消息  ⚙️ 设置    │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ │ 🔍 搜索档案...    ➕ 添加  📤 导出                        │
│ │ 📅 主界面        │ │                                                           │
│ │ 📋 日程任务      │ │ ┌─────────────────────┐  ┌─────────────────────────────┐ │
│ │ 📄 个人档案      │ │ │ 📁 档案分类          │  │ 📄 档案详情                  │ │
│ │ 🔍 文档处理      │ │ │                     │  │                             │ │
│ │ ────────────────│ │ │ 🆔 身份证件          │  │ 🆔 身份证信息               │ │
│ │ ⚙️ 系统设置      │ │ │ ├─ 身份证正面        │  │ ┌─────────────────────────┐ │ │
│ │ 📊 数据统计      │ │ │ ├─ 身份证反面        │  │ │ 📷 身份证正面照片        │ │ │
│ │ 💡 帮助支持      │ │ │ └─ 户口本           │  │ │ [身份证正面图片预览]     │ │ │
│ └─────────────────┘ │ │                     │  │ │                         │ │ │
│                     │ │ 📷 证件照片          │  │ └─────────────────────────┘ │ │
│                     │ │ ├─ 一寸照片 (5张)    │  │                             │ │
│                     │ │ ├─ 二寸照片 (3张)    │  │ 📝 基本信息：               │ │
│                     │ │ ├─ 免冠照片 (2张)    │  │ • 姓名：张三               │ │
│                     │ │ └─ 工作照 (1张)      │  │ • 身份证号：110101199001011234 │ │
│                     │ │                     │  │ • 性别：男                 │ │
│                     │ │ 📜 证书文件          │  │ • 民族：汉族               │ │
│                     │ │ ├─ 学历证书          │  │ • 出生日期：1990年1月1日   │ │
│                     │ │ ├─ 学位证书          │  │ • 住址：北京市朝阳区...    │ │
│                     │ │ ├─ 职业资格证        │  │                             │ │
│                     │ │ └─ 培训证书          │  │ 🔒 敏感信息已加密存储       │ │
│                     │ │                     │  │                             │ │
│                     │ │ 📝 个人简历          │  │ 🚀 快速操作：               │ │
│                     │ │ ├─ 标准简历          │  │ 📋 复制身份证号  📷 导出证件照 │ │
│                     │ │ ├─ 工作简历          │  │ 📄 生成简历                 │ │
│                     │ │ └─ 项目简历          │  │                             │ │
│                     │ └─────────────────────┘  └─────────────────────────────┘ │
│                     │                                                           │
│                     │ ┌─────────────────────────────────────────────────────┐ │
│                     │ │ 📷 证件照片管理                                      │ │
│                     │ │                                                     │ │
│                     │ │ 📷 一寸照片 (5张)                                    │ │
│                     │ │ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐            │ │
│                     │ │ │照片1│ │照片2│ │照片3│ │照片4│ │照片5│            │ │
│                     │ │ │蓝底 │ │红底 │ │白底 │ │蓝底 │ │白底 │            │ │
│                     │ │ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘            │ │
│                     │ │                                                     │ │
│                     │ │ 📷 二寸照片 (3张)                                    │ │
│                     │ │ ┌─────┐ ┌─────┐ ┌─────┐                            │ │
│                     │ │ │照片1│ │照片2│ │照片3│                            │ │
│                     │ │ │蓝底 │ │白底 │ │红底 │                            │ │
│                     │ │ └─────┘ └─────┘ └─────┘                            │ │
│                     │ │                                                     │ │
│                     │ │ 🎨 智能换底色  📏 尺寸调整  📤 批量导出  ➕ 添加新照片 │ │
│                     │ └─────────────────────────────────────────────────────┘ │
│                     │                                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 设计要点
- 左侧分类树形结构清晰
- 右上档案详情支持预览编辑
- 下方证件照片批量管理
- 支持OCR自动识别信息
- 敏感信息加密保护

## 交互说明
- 点击分类查看对应档案
- 拖拽上传新的证件照片
- 支持批量选择和操作
- 快速操作按钮提高效率
- 导出功能支持多种格式 