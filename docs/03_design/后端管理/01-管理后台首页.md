# 后端管理 - 01 管理后台首页

## 界面描述
运营公司使用的后端管理系统首页，提供系统概览和核心数据监控

## 线框示意图
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🐱 公职猫管理后台                                    👤 管理员  🔔 通知  🚪 退出 │ 顶部栏
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│ ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────┐     │
│ │ 📊 设备统计          │  │ 📈 使用情况          │  │ 🔧 系统状态          │     │
│ │                     │  │                     │  │                     │     │
│ │ 👥 总设备数          │  │ 📱 移动端活跃        │  │ 🟢 API服务正常       │     │
│ │    1,234 台         │  │    856 台/日        │  │ 🟢 数据库正常        │     │
│ │                     │  │                     │  │ 🟢 AI服务正常        │     │
│ │ 📅 今日新增          │  │ 💻 桌面端活跃        │  │ 🟡 存储使用率 78%    │     │
│ │    23 台            │  │    378 台/日        │  │                     │     │
│ │                     │  │                     │  │ ⚡ 响应时间          │     │
│ │ 🔄 活跃设备          │  │ 📄 文档处理量        │  │    平均 1.2s        │     │
│ │    892 台           │  │    2,456 个/日      │  │                     │     │
│ └─────────────────────┘  └─────────────────────┘  └─────────────────────┘     │
│                                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐   │
│ │ 📈 使用趋势图表                                                          │   │
│ │                                                                         │   │
│ │ 设备活跃度 (最近30天)                                                    │   │
│ │                                                                         │   │
│ │ 1000 ┤                                                                  │   │
│ │  800 ┤     ●                                                            │   │
│ │  600 ┤   ●   ●                                                          │   │
│ │  400 ┤ ●       ●                                                        │   │
│ │  200 ┤           ●                                                      │   │
│ │    0 └─────────────────────────────────────────────────────────────    │   │
│ │      1/1   1/8   1/15  1/22  1/29                                      │   │
│ │                                                                         │   │
│ │ 📊 功能使用排行                                                          │   │
│ │ 1. 📷 拍照识别    2,456次/日                                            │   │
│ │ 2. 📁 文档导入    1,892次/日                                            │   │
│ │ 3. 📅 日程管理    1,234次/日                                            │   │
│ │ 4. 🔍 全文搜索      892次/日                                            │   │
│ │ 5. 👤 个人档案      456次/日                                            │   │
│ └─────────────────────────────────────────────────────────────────────────┘   │
│                                                                                 │
│ ┌─────────────────────┐  ┌─────────────────────────────────────────────────┐   │
│ │ 🚨 系统告警          │  │ 🌍 用户群体画像                                  │   │
│ │                     │  │                                                 │   │
│ │ ⚠️  AI API调用异常   │  │ 📍 地区分布（区县级）                           │   │
│ │    影响23台设备     │  │ • 北京市朝阳区    89台 (7.2%)                   │   │
│ │    2小时前          │  │ • 上海市浦东区    76台 (6.2%)                   │   │
│ │                     │  │ • 深圳市南山区    65台 (5.3%)                   │   │
│ │ 💾 存储空间不足      │  │ • 广州市天河区    54台 (4.4%)                   │   │
│ │    剩余15%          │  │ • 杭州市西湖区    43台 (3.5%)                   │   │
│ │    1天前            │  │ • 其他区县        907台 (73.4%)                 │   │
│ │                     │  │                                                 │   │
│ │ 查看全部告警 →      │  │ 📱 平台分布                                     │   │
│ └─────────────────────┘  │ • Android         567台 (46%)                  │   │
│                          │ • iOS             289台 (23%)                  │   │
│                          │ • Windows         378台 (31%)                  │   │
│                          └─────────────────────────────────────────────────┘   │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 设计要点
- 核心数据一目了然
- 系统状态实时监控
- 使用趋势可视化展示
- 告警信息及时提醒

## 交互说明
- 点击数据卡片查看详细信息
- 图表支持时间范围切换
- 告警点击查看详情和处理
