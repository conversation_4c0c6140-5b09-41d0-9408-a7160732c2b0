# 公职猫设计系统规范

## 🎨 设计理念

### 核心价值观

- **U盘级私密**：极致的隐私保护，让用户安心
- **秘书般懂你**：智能理解，贴心服务
- **专业可信**：体现体制内工作的专业性和严谨性
- **温暖亲和**：在专业中融入人性化关怀

### 设计原则

1. **简洁高效**：界面简洁，操作高效，符合公务员快节奏工作需求
2. **安全可信**：视觉传达安全感和可信度
3. **智能贴心**：通过设计体现AI助手的智能和贴心
4. **品质感**：高品质的视觉呈现，提升用户信任度

## 🎨 色彩系统

### 主色调 (Light Theme)

```css
/* 主品牌色 - 温暖橙色 */
--primary-color: #FF8C00;
--primary-light: #FFB347;
--primary-dark: #E67300;

/* 背景色系 - 专业浅色 */
--background-primary: #F2F2F7;    /* 主背景 - 浅灰 */
--background-secondary: #FFFFFF;   /* 卡片背景 - 纯白 */
--background-tertiary: #E5E5EA;    /* 分割线、边框 */

/* 文字色系 */
--text-primary: #000000;           /* 主要文字 - 黑色 */
--text-secondary: #6B7280;         /* 次要文字 - 中灰 */
--text-tertiary: #9CA3AF;          /* 辅助文字 - 浅灰 */
--text-disabled: #D1D5DB;          /* 禁用文字 - 极浅灰 */

/* 功能色系 */
--success: #4CAF50;                /* 成功/完成状态 */
--warning: #FF9500;                /* 警告/协办状态 */
--error: #FF4444;                  /* 错误/紧急状态 */
--info: #2196F3;                   /* 信息提示 */

/* 状态栏和系统色 */
--status-bar-style: 'dark-content'; /* iOS状态栏文字为深色 */
--safe-area-color: #FFFFFF;         /* 安全区域背景色 */
```

### 色彩语义

- **橙色系**：品牌识别、重要操作、温暖感
- **浅色系**：专业背景、清晰感、现代感
- **绿色**：成功状态、完成任务、参与角色
- **黄色**：警告状态、协办角色
- **红色**：紧急任务、错误提示、主办角色
- **蓝色**：信息提示、链接

### 角色识别色彩系统

```css
/* 角色优先级色彩 - 用于半日色块等组件 */
--role-primary: rgba(255, 68, 68, 0.15);   /* 主办 - 淡红色背景 */
--role-secondary: rgba(255, 149, 0, 0.15); /* 协办 - 淡黄色背景 */
--role-participant: rgba(76, 175, 80, 0.15); /* 参与 - 淡绿色背景 */
--role-none: #F2F2F7;                      /* 无日程 - 默认背景 */
```

## 📝 字体系统

### 字体层级

```css
/* 标题字体 */
--font-title-large: 28px;    /* 页面主标题、重要日期 */
--font-title-medium: 24px;   /* 区块标题、页面标题 */
--font-title-small: 20px;    /* 卡片标题、周几显示 */

/* 正文字体 */
--font-body-large: 17px;     /* 重要正文、任务标题 */
--font-body-medium: 16px;    /* 常规正文、按钮文字 */
--font-body-small: 14px;     /* 辅助信息、操作栏文字 */

/* 特殊字体 */
--font-caption: 12px;        /* 说明文字、标签 */
--font-mini: 10px;           /* 星期标题、小标签 */
```

### 字重规范

- **Bold (700)**：重要标题、强调信息
- **Medium (500)**：常规标题、按钮文字、今日日期
- **Regular (400)**：正文内容、一般信息

## 📐 间距系统

### 基础间距单位

```css
--spacing-xs: 4px;    /* 最小间距 */
--spacing-sm: 8px;    /* 小间距 */
--spacing-md: 12px;   /* 中等间距 */
--spacing-lg: 16px;   /* 大间距 */
--spacing-xl: 20px;   /* 超大间距 */
--spacing-xxl: 24px;  /* 极大间距 */
--spacing-xxxl: 32px; /* 最大间距 */
```

### 应用规则

- **组件内间距**：xs(4px), sm(8px), md(12px)
- **组件间间距**：lg(16px), xl(20px)
- **页面边距**：xl(20px), xxl(24px)
- **区块间距**：xxl(24px), xxxl(32px)

## 🔲 组件规范

### 1. 便利贴任务卡片

```typescript
interface StickyNoteProps {
  title: string;
  priority: 'high' | 'medium' | 'low';
  deadline?: string;
  status: 'pending' | 'in-progress' | 'completed';
}

// 设计规范 (Light Theme)
const stickyNoteStyles = {
  backgroundColor: '#FFFFFF',           // 纯白背景
  borderRadius: 12,
  padding: 16,
  borderLeftWidth: 4,
  borderLeftColor: 'priority-color',    // 根据优先级变化
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
};
```

### 2. 智能输入组件

```typescript
interface InputComponentProps {
  type: 'text' | 'voice' | 'image';
  placeholder?: string;
  onSubmit: (data: InputData) => void;
}

// 设计规范 (Light Theme)
const inputStyles = {
  backgroundColor: '#FFFFFF',           // 纯白背景
  borderRadius: 8,
  borderWidth: 1,
  borderColor: '#E5E5EA',              // 浅灰边框
  focusBorderColor: '#FF8C00',         // 橙色聚焦边框
  padding: 12,
  minHeight: 44,                       // 符合触摸标准
  color: '#000000',                    // 黑色文字
};
```

### 3. 按钮组件

```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'ghost';
  size: 'small' | 'medium' | 'large';
  disabled?: boolean;
}

// 主按钮 (Light Theme)
const primaryButton = {
  backgroundColor: '#FF8C00',          // 橙色背景
  color: '#FFFFFF',                    // 白色文字
  borderRadius: 8,
  padding: '12px 24px',
  fontSize: 16,                        // 增大字号
  fontWeight: '500',                   // Medium字重
};

// 次要按钮 (Light Theme)
const secondaryButton = {
  backgroundColor: 'transparent',      // 透明背景
  color: '#FF8C00',                   // 橙色文字
  borderWidth: 1,
  borderColor: '#FF8C00',             // 橙色边框
  borderRadius: 8,
  padding: '12px 24px',
  fontSize: 16,
  fontWeight: '500',
};
```

### 4. 复合组件/模式

#### 4.1 日程管理 - 半日色块 (Half-day Color Block)

用于在"日程周视图"的每日卡片中，直观、简洁地展示上午和下午的日程安排情况。

- **视觉逻辑**：每日卡片在垂直方向上分为两个区域，分别代表上午和下午。
- **状态表现**：通过背景色表示该半日是否有日程及其最重要事务的角色，不直接显示文字。
- **颜色规范**：
  - **无日程**：`#F2F2F7` - 默认浅灰背景
  - **主办 (最高优先级)**：`rgba(255, 68, 68, 0.15)` - 淡红色背景
  - **协办 (中优先级)**：`rgba(255, 149, 0, 0.15)` - 淡黄色背景
  - **参会 (最低优先级)**：`rgba(76, 175, 80, 0.15)` - 淡绿色背景
- **优先级规则**：若一个半天内有多个事务，色块颜色由优先级最高的事务所决定。

#### 4.2 响应式网格系统

用于日程管理页面的8格网格布局（1个月视图 + 7个日视图）。

- **布局规则**：
  - 2列4行的网格结构
  - 动态计算网格项高度以适应不同屏幕尺寸
  - 确保在小屏设备上无需滚动即可显示完整网格

```typescript
// 响应式高度计算
const calculateGridItemHeight = (screenHeight: number, insets: any, hasLeaderSelector: boolean) => {
  const headerHeight = 80;                    // Header + 状态栏
  const leaderSelectorHeight = hasLeaderSelector ? 50 : 0;
  const bottomTabHeight = 90;                 // Bottom tab + 安全区域
  const gridPadding = 8;                      // 网格容器内边距
  const gridMargins = 32;                     // 4行 × 8px边距
  
  const availableHeight = screenHeight - headerHeight - leaderSelectorHeight - bottomTabHeight - gridPadding - gridMargins;
  const calculatedHeight = availableHeight / 4;
  
  // 设置约束条件
  const minHeight = 160;                      // 确保迷你日历完整显示
  const maxHeight = (width - 24) / 2 * 0.9;  // 保持合理宽高比
  
  return Math.max(minHeight, Math.min(maxHeight, calculatedHeight));
};
```

#### 4.3 迷你日历组件

用于日程网格中的月视图，显示完整月份的日期选择。

- **空间需求**：
  - 星期标题行：20px (高度16px + 底部边距4px)
  - 日期网格：最多6行 × 20px = 120px
  - 内边距：14px
  - **总计最小高度**：154px + 6px缓冲 = 160px

- **交互设计**：
  - 点击日期切换到对应周视图
  - 当前周高亮显示
  - 支持触觉反馈

#### 4.4 二级标签栏 (Secondary Tab Bar)

用于在特定页面（如"领导日程"）进行第二层级的内容筛选。

- **应用场景**：在"日程管理"页面，点击底部"领导"标签后，在主标题下方出现，用于切换不同领导的日程。
- **设计规范**：
  - **样式**：横向滚动的文字标签列表
  - **高度**：50px固定高度
  - **背景**：`#FFFFFF`纯白背景
  - **文字**：14px字号，选中状态使用橙色`#FF8C00`
  - **分割线**：底部1px的`#E5E5EA`分割线

## 🤝 交互设计规范

### 1. 触觉反馈系统

基于`react-native-haptic-feedback`实现的三级触觉反馈体系：

```typescript
// 触觉反馈层级
enum HapticFeedbackType {
  LIGHT = 'impactLight',      // 轻微反馈：日期点击、标签切换、视图切换
  MEDIUM = 'impactMedium',    // 中等反馈：手势滑动、重要按钮
  HEAVY = 'impactHeavy'       // 强烈反馈：长按操作、重要确认
}

// 使用示例
const handleDatePress = (date: Date) => {
  ReactNativeHapticFeedback.trigger('impactLight');
  // 处理日期选择逻辑
};
```

### 2. 手势交互

- **垂直滑动**：周视图切换（上滑下一周，下滑上一周）
- **点击交互**：日期选择、视图切换
- **长按操作**：快速操作菜单

### 3. 动画过渡

- **页面切换**：使用原生导航动画
- **状态变更**：300ms缓动动画
- **手势跟随**：实时跟随手指移动

### 4. 状态反馈

- **触摸反馈**：`activeOpacity: 0.7`或`0.8`
- **加载状态**：统一的加载指示器
- **错误状态**：红色提示 + 触觉反馈

## 🔧 实现规范

### 1. 颜色使用

```typescript
// 主题色彩定义
export const lightTheme = {
  colors: {
    primary: '#FF8C00',
    background: '#F2F2F7',
    surface: '#FFFFFF',
    text: '#000000',
    textSecondary: '#6B7280',
    textTertiary: '#9CA3AF',
    border: '#E5E5EA',
    success: '#4CAF50',
    warning: '#FF9500',
    error: '#FF4444',
  },
  spacing: {
    xs: 4, sm: 8, md: 12, lg: 16, xl: 20, xxl: 24, xxxl: 32
  }
};
```

### 2. 响应式设计

```typescript
// 屏幕尺寸断点
const breakpoints = {
  small: 375,    // iPhone SE
  medium: 414,   // iPhone Plus
  large: 428,    // iPhone Pro Max
};

// 动态尺寸计算
const getResponsiveSize = (base: number, screen: 'small' | 'medium' | 'large') => {
  const multipliers = { small: 0.9, medium: 1.0, large: 1.1 };
  return base * multipliers[screen];
};
```

### 3. 安全区域处理

```typescript
// 状态栏和安全区域
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const ScreenComponent = () => {
  const insets = useSafeAreaInsets();
  
  return (
    <View style={{ 
      paddingTop: insets.top,      // 状态栏高度
      paddingBottom: insets.bottom  // 底部安全区域
    }}>
      {/* 内容 */}
    </View>
  );
};
```

## 📱 平台适配

### iOS特定规范
- 状态栏样式：`dark-content`（深色文字）
- 导航栏：44px高度，模糊背景效果
- 触觉反馈：充分利用iOS触觉引擎
- 字体：系统字体San Francisco

### Android特定规范
- 状态栏：沉浸式设计，与header融合
- 导航栏：56px高度，Material Design风格
- 触觉反馈：使用Android Vibration API
- 字体：系统字体Roboto

## 📋 设计检查清单

### 颜色使用
- [ ] 使用了正确的亮色主题色彩
- [ ] 角色识别色彩正确应用
- [ ] 文字对比度符合可访问性要求

### 间距布局
- [ ] 使用了标准间距单位
- [ ] 响应式布局在不同屏幕尺寸下正常
- [ ] 安全区域正确处理

### 交互反馈
- [ ] 触觉反馈层级正确
- [ ] 触摸反馈视觉效果合适
- [ ] 动画过渡流畅自然

### 平台一致性
- [ ] iOS和Android平台适配正确
- [ ] 字体和图标显示正常
- [ ] 状态栏样式适配正确

---

## 更新记录

**2024-12-13**：
- 将设计系统从暗色主题完全转换为亮色主题
- 新增触觉反馈设计规范
- 新增响应式网格系统规范
- 新增迷你日历组件规范
- 完善交互设计和平台适配规范
- 更新所有色彩、字体、间距定义以匹配实际实现
