# 多输入方式页面用户体验研究

## 📊 目标用户画像

### 核心用户群体
- **身份**：公务员、事业单位、国企中青年干部（约1000万潜在用户）
- **年龄**：25-45岁
- **工作特点**：
  - 集管理与执行于一身，没有秘书和下属
  - 工作节奏快，需要快速响应和处理
  - 经常需要处理微信通知、纸质文件、会议安排等
  - 对数据安全和隐私极度敏感

### 使用场景分析

#### 1. 拍照与图像识别场景 (场景 a, b, c, d, i)
**高频场景**：
- 📷 **会议PPT拍摄**：会议中，横/竖屏连续拍摄多张PPT存档。
- 🖼️ **相册导入**：从相册选择一张或多张图片（如PPT、笔记照片）进行识别或存档。
- 📋 **剪贴板识别**：从其他App复制图片（如微信截图、网页图片）后，打开公职猫App时，自动提示是否需要识别。
- 📲 **应用间分享**：从微信、相册等App通过系统分享菜单，直接将图片或视频发送到公职猫进行处理。
- 📝 **手写笔记拍照**：拍摄会议或谈话后的手写笔记，进行OCR识别并存档，或从中提取任务。
- 📄 **纸质文件与通知**：拍摄各类红头文件、通知、领导批示等，进行识别、存档或提取关键信息。
- 📰 **期刊报告与政策**：拍摄印刷品或电子版的期刊、政策文件，进行存档和结构化信息提取。

**用户痛点**：
- 微信、钉钉的截图包含大量无关信息，需要精准提取核心内容。
- 手写体（特别是领导的）识别困难。
- 多张图片需要批量处理和管理。
- 需要快速、无缝地从其他App将信息导入公职猫。
- 需要将识别出的文字，按体制内常用公文格式（如会议纪要、请示）进行快速格式化。

**设计要求**：
- **智能识别入口**：点击拍照按钮后，应提供"拍照"、"从相册选择"选项。同时App应具备剪贴板监听和分享接收能力。
- **批量处理**：支持一次性选择或拍摄多张图片，并进行批量识别。
- **智能裁剪与降噪**：自动识别并裁剪截图中的核心内容区域，忽略无关信息。
- **强大的OCR引擎**：针对印刷体、手写体、屏幕截图进行优化，特别是体制内常用字体和术语。
- **"用完即走"**：提供一键"复制识别结果"或"复制格式化文本"功能。

#### 2. 语音输入与处理场景 (场景 f, g, h)
**高频场景**：
- 🚗 **通勤/即时记录**：开车或不便打字时，通过语音快速录入临时想法、领导的口头吩咐。
- 📞 **通话/会议录音**：直接在App内录音，或导入手机上的已有录音文件（如通话录音）。
- 🤫 **私密场合输入**：在办公室等私密环境，口述内容生成任务或笔记。
- 🎙️ **长语音转文字**：将长时间的会议录音、培训录音，转写为文字。
- ✍️ **自动格式化**：将转写的文字，一键格式化为"会议纪要"、"新闻稿"、"工作简报"等标准公文格式。

**用户痛点**：
- 体制内术语、人名、职务识别不准。
- 需要将口语化的表达，整理成书面语。
- 格式化排版工作耗时耗力。
- 无法区分不同发言人。

**设计要求**：
- **灵活的录音模式**：支持短句识别（用于快速任务）和长时间录音（用于转写）。
- **文件导入**：支持从手机文件系统导入多种格式的音频文件。
- **定制化词库**：支持用户自定义或预置体制内常用词汇、人名、职务的词库。
- **AI摘要与格式化**：内置多种公文模板，利用大模型能力进行摘要、润色和格式化。
- **说话人分离技术**：在可能的情况下，识别和区分不同的发言人。
- **后台录音**：App切换到后台时，录音应能持续进行。

#### 3. 文本与文件处理场景 (场景 b, e, i)
**高频场景**：
- 💬 **即时消息处理**：直接从微信、钉钉复制长段文字通知，粘贴到公职猫进行解析，提取任务或日程。
- 📁 **第三方文件接收**：从微信、钉钉等App接收Word、Excel、PDF、ODF等文件，通过系统分享菜单发送到公职猫进行解析、存档。
- 💻 **本地文件打开**：在公职猫内，直接打开手机或电脑（需多端协同）上的文件。
- 📄 **政策/文章格式化**：复制来的电子版政策、文章，一键套用体制内格式进行排版。

**用户痛点**：
- 跨应用操作繁琐。
- 文件版本管理混乱。
- 从不同格式文件中提取信息困难。

**设计要求**：
- **强大的文本解析引擎**：利用AI能力，精准识别非结构化文本中的时间、地点、人物、事件等要素。
- **文件处理能力**：内置或调用服务解析主流办公文件格式（.doc, .docx, .xls, .xlsx, .pdf）。
- **无缝分享集成**：深度集成系统分享菜单，成为各类文件和文本的默认接收应用之一。
- **剪贴板监听**：智能识别剪贴板中的长文本，提示用户是否需要处理。

## 🎯 核心设计目标

### 1. 秘书般智能 (Thinking Partner)
- **上下文感知**：能根据用户所处的地点（被动记录）、时间、剪贴板内容，智能预判用户意图。
- **动态重排思维**：所有信息的录入，都服务于构建任务间的逻辑关系，为后续的"思考中枢"提供高质量数据。
- **补充信息机制**：在用户导入图片、文件后，必须提供便捷的渠道（文本、语音）让其补充上下文信息。

### 2. 效率与体验 (Efficiency & Experience)
- **极速捕捉，异步处理**：拍照、录音等"捕捉"动作必须瞬间完成，不打断用户思路。识别、转写等"处理"动作应在后台异步完成，并提供清晰的状态反馈。
- **"用完即走"的工具属性**：在结果页，必须将"复制原文"、"复制格式化结果"作为最高优先级的操作，满足用户跨平台使用的需求。
- **一键直达**：核心操作路径要短，减少不必要的页面跳转和选择。

### 3. U盘级私密 (Local-First Security)
- **本地优先原则**：核心数据、特别是包含敏感信息的数据，必须优先在本地设备上进行处理和存储。
- **权限最小化**：仅申请完成核心功能所必需的系统权限（如相机、麦克风、地理位置），并向用户清晰说明用途。
- **端侧AI能力**：设计应考虑端侧小模型的可行性，以在离线状态下完成部分智能识别任务。

## 📱 移动端特殊考虑

### 单手操作优化
- **拇指热区**：重要操作按钮放在拇指可达范围内
- **手势支持**：滑动、长按等手势操作
- **横竖屏适配**：支持不同屏幕方向

### 性能优化
- **快速启动**：输入页面快速加载和响应
- **离线支持**：核心功能支持离线使用
- **电池优化**：减少CPU和电池消耗

### 隐私保护
- **被动地点记录**：在用户授权地理位置权限后，App应在后台被动记录信息创建时的地点，此数据仅用于后续的智能分析与推荐，用户无需主动操作也无需感知。
- **本地处理**：敏感信息优先本地处理
- **权限最小化**：只申请必要的系统权限
- **数据加密**：本地存储数据加密保护

## 🔄 用户流程设计

### 统一入口设计
```
主页 → 快速输入面板 → 选择输入方式 → 具体输入页面 → 结果确认 → 保存/分类
```

### 跨页面状态保持
- 输入内容自动保存草稿
- 支持页面间切换而不丢失数据
- 提供历史记录和快速恢复

### 智能引导系统
- 首次使用提供操作指导
- 根据使用习惯优化界面布局
- 提供快捷方式和个性化设置

## 📋 设计验证指标

### 效率指标
- 平均输入完成时间 < 30秒
- 错误率 < 5%
- 用户满意度 > 4.5/5.0

### 准确性指标
- OCR识别准确率 > 85%
- 语音识别准确率 > 90%
- 智能分类准确率 > 80%

### 用户体验指标
- 功能发现率 > 90%
- 重复使用率 > 70%
- 推荐意愿 > 80%

---

*本研究基于公职猫项目的目标用户群体和使用场景，为多输入方式页面设计提供用户体验指导。*

---

# 2025-06-13 重要补充与修正

## 1. MVP智能核心
- 明确MVP阶段的智能核心是"端侧能力的综合运用"，包括平台原生AI（如iOS Vision）、本地轻量模型（ONNX）、规则引擎三者协同，而非单一依赖规则引擎。

## 2. OFD格式支持
- 明确所有体制内常用文档格式（Word、Excel、PDF、OFD）都需支持，OFD为刚需，已在技术设计文档中补充。

## 3. 角色识别理念
- "主办/协办/参与"角色划分目前不强制，采用"智能推荐+用户轻量修正"原则，允许AI初步识别，用户可按需补充或忽略。
- 产品初期以默默观察和记录为主，未来根据实际场景和用户反馈持续优化。

## 4. 持续对话与微调入口
- 设计上为"持续对话式调整"预留入口。建议在AI分析结果卡片上设置"微调"按钮，MVP阶段可为简单反馈框，未来可扩展为完整对话。
- "微调"命名和交互方式需贴近日常工作语境，后续根据实际体验优化。

## 5. 连续拍照默认支持
- 拍照功能应默认支持连续拍摄，无需用户选择模式，适应会议PPT等高频场景。

## 6. "待处理区"概念与命名
- 所有未经AI处理的原始信息（拍照、录音、文件等）应有专门的收纳区，避免干扰主界面"时间资产"视图。
- "待处理区"命名和入口方式仍在讨论中。当前建议：
  - 入口设为首页顶部猫咪图标右侧的动态提示语句，既能提醒用户有未处理信息，也能让App"活起来"，形成拟人化对话氛围。
  - "待处理区"不宜放在"更多"页面，避免入口过深。

## 7. 主界面与探索区
- 主界面聚焦"时间资产"管理，所有结构化的任务、日程、重要事项均在此展示。
- "探索"按钮进入的页面，定位为个人笔记、文件、云端行业知识库检索等"智能U盘"功能的集合区。
- 所有集中收集的内容（如个人笔记、文件、知识库检索结果）建议统一在"探索"区管理。

## 8. 猫咪头像与侧边栏
- 首页猫咪头像点击后，弹出左侧侧边栏，用于存储和管理用户个人私密数据（如身份证、证件照等）。
- 侧边栏为用户的"私密空间"，与主界面和探索区功能区分明确。

## 9. 助理对话入口与原始输入回溯（2025-06-13补充）
- "待处理区"不作为显式区块单独展示，而是作为"助理的对话入口"存在，既收纳所有未处理信息，也是用户与AI助理持续对话、补充说明、再加工的起点。
- "助理对话入口"可通过首页顶部猫咪图标右侧的动态提示语进入，拟人化、主动提醒，提升交互活力。
- "全部收集内容"建议在"探索"区统一管理，便于用户集中查找和管理所有笔记、文件、知识库检索等内容。
- 每个结构化任务/日程详情页都应保留"原始输入"（如原始图片、录音、文件等），支持用户随时回溯、补充说明或再加工，提升数据资产的可用性和灵活性。

## 10. 概念澄清与定位修正（2025-06-20补充）

### 便签与便签墙的正确理解

根据项目文档和代码检查，发现对"便签"功能存在概念理解差异，现澄清如下：

- **便签的正确定位**：
  - 便签 = 灵活时间的任务（参考`docs/02_requirements/prd.md`）
  - 便签是**创建重要任务的快捷方式**，而非通用笔记功能
  - 通过底部ActionBar的"便签"按钮创建的内容，将显示在首页的"便签墙"区域
  
- **便签墙的正确定位**：
  - 首页上的重要任务区统一命名为"便签墙"，显示用户最关注的事项
  - 设计为单行滚动式布局，显示4个置顶重要事项及查看更多入口
  - 点击"查看全部"进入"全部便签"页面（原`AllImportantTasksScreen.tsx`）
  - 便签墙可包含各类内容：日程、任务、重要笔记、文件、照片等

### 设计与代码调整建议

- **命名统一性**：
  - 将代码和UI中的"重要任务"/"重要任务区"统一改为"便签墙"
  - 将"全部重要任务"页面统一改为"全部便签"
  - 相关文档和代码中保持术语一致性
  
- **功能区分**：
  - 明确区分"便签"（重要事项快捷创建）与"笔记"（内容归档与深度编辑）功能
  - 便签创建流程应简洁直接，突出"重要性"标记和"置顶"选项
  - 笔记功能应更注重内容的归档、组织和深度编辑

### 便签来源差异处理

不同来源的便签在修改和删除时需要考虑以下差异：

1. **直接创建的便签**：
   - 通过底部ActionBar的"便签"按钮直接创建
   - 修改：完整编辑权限，可修改全部内容和属性
   - 删除：直接从便签墙和系统中完全删除

2. **从其他来源引用的便签**：
   - 从日程、笔记、文件等拉到便签墙的内容
   - 修改：仅修改在便签墙中的显示属性（如优先级、标题），不影响原始内容
   - 删除：仅从便签墙中移除，不删除原始内容
   - 需设计清晰的视觉区分，表明其为"引用"而非"原始"

3. **设计考量**：
   - 引用型便签需有明确的视觉标识（如来源图标）
   - 编辑界面需清晰区分"仅从便签墙移除"与"完全删除"选项
   - 考虑原始内容变更时，便签墙中引用内容的同步机制

--- 