# 移动端 - 02 主界面

## 界面描述

聚焦核心功能的主界面，突出当日日程和重要跟踪任务的便利贴式展示，体现"秘书般懂你"的智能助手特质。整体采用清爽的浅色主题，营造温暖、亲和的氛围。

## 设计规范

### 整体视觉系统

- **主背景色**: 饱满橙色 (#FFA500) - 已修正为正确的品牌色
- **内容区背景**: 白色 (#FFFFFF)，带有圆角和轻微阴影
- **主色调**: 用于文字和图标的深灰色 (#374151)
- **辅助色**:
  - 次要文字: 中灰色 (#9CA3AF)
  - 交互元素: 饱满橙色 (#FFA500)

### 顶部区域

- **背景**: 饱满橙色 (#FFA500)
- **状态栏**: 深色内容 (dark-content)
- **问候语**: "🐱 喵～" 18px 粗体，深灰色 (#374151)
- **日期显示**: "今天 周六 6月14日" 24px 粗体，深灰色 (#374151)
- **更多按钮**: "更多" 14px，深灰色 (#374151)

### 中间内容区（白色卡片）

#### 重要任务区 (单行滚动式)
```
┌───────────────────────────────────────────┐
│ ┌──────┐ ┌──────┐ ┌──────┐ ┌───┐         │
│ │ 📚 │ │ 📋 │ │ 📊 │ │겹쳐 │         │
│ │党校培训│ │考核准备│ │总结报告│ │+5 │         │
│ └──────┘ └──────┘ └──────┘ └───┘         │
└───────────────────────────────────────────┘
```

**设计规范**：
- **布局**: 一个单行的、可水平滚动的容器。该区域在没有重要任务时，整体不显示。
- **卡片内容**: 每个任务卡片仅显示一个能够智能识别任务类型的线框图图标和任务标题。
- **交互**: 
  - 点击单个任务卡片，可以进入任务详情页。
  - 在滚动列表的末尾，会有一个堆叠卡片样式的"查看全部"按钮。
  - 点击"查看全部"按钮，会跳转到一个新的"重要任务全览"页面，以列表形式展示所有重要任务。
- **设计风格**: 任务卡片设计要简洁优雅，与整体UI风格融合，不应有强烈的颜色区分(如按优先级着色)。

#### 当日日程区（时间轴设计）

**复杂日程显示系统**：

1. **时间分段** (4段式)：
   - 🌅 凌晨 (0-5时)
   - 🌄 上午 (6-11时)  
   - 🌇 下午 (12-17时)
   - 🌃 晚上 (18-23时)

2. **跨天事件处理**：
   - **开始日显示**: `23:00-次日01:00 ✈️ 飞机到杭州`
   - **结束日显示**: `前日23:00-01:00 ✈️ 飞机到杭州 (续)`
   - **关键原则**: 保持原始开始时间，在凌晨时段显示"前日"

3. **重复事件显示**：
   - **格式**: `09:00-10:00 部门例会 🔄`
   - **原则**: 循环图标放在标题后面，保持标题完整性
   - **补充**: 可选显示"每周三重复"等说明

4. **灵动符号表达系统**：
   - **全面支持**: 所有日程都支持emoji和语义符号
   - **常用符号**: 
     - ✈️ 出行交通
     - 📚 培训学习
     - 🏛️ 政府会议
     - 📋 材料审核
     - 📊 数据汇报
     - 🤝 接待会面
     - 📞 电话会议
     - 🔄 重复事件
   - **设计理念**: 让整个日程系统灵动起来，增强可读性和情感连接

**示例显示**：
```
🌅 凌晨
├─ 前日23:00-01:00 ✈️ 飞机到杭州 (续)

🌄 上午  
├─ 09:00-10:00 部门例会 🔄
├─ 10:30-12:00 📚 开班仪式

🌇 下午
├─ 14:00-17:00 📊 专题讲座
├─ 16:00-16:30 📞 紧急电话

🌃 晚上
├─ 19:00-20:00 🤝 总结会议
└─ 23:00-次日01:00 ✈️ 飞机到杭州
```

- **样式**: `flex: 1`，`backgroundColor: '#FFFFFF'`，`borderRadius: 24px` (顶部)，轻微阴影
- **空状态**:
  - **图标**: 可爱的猫咪头像
  - **文案**: "抓住思维闪光的瞬间" 16px，中灰色 (#9CA3AF)
- **核心交互**:
  - **语音输入按钮**: 位于内容区中央，橙色麦克风图标

### 底部快速操作栏

- **样式**: 白色胶囊形状，悬浮于底部，带有阴影
- **按钮样式**:
  - **图标**: 线性图标，深灰色 (#374151)
  - **文字**: 12px，中灰色 (#9CA3AF)
- **操作项**: 拍照、笔记、便签、日程、探索

## 设计理念

- **清爽温暖**: 告别深色的沉重感，用温暖的橙黄色和白色营造轻松、愉悦的氛围。
- **聚焦核心**: 将视觉焦点引导至中间的语音输入和底部操作栏，鼓励用户快速记录。
- **情感化设计**: 使用可爱的猫咪元素和亲切的文案，建立与用户的情感连接。
- **减压设计**: 简洁的界面和操作路径，让用户无压力地记录和管理事务。
- **灵魂体现**: "U盘级私密，秘书般懂你"的产品理念在每个细节中体现。

## 交互说明

- 左右滑动查看前后日期
- 点击重要任务卡片查看详情
- 长按并拖拽重要任务卡片可进行排序
- 点击重要任务区末尾的堆叠卡片，进入全览页面
- 点击"更多"进入日程管理
- 快速操作按钮直接启动对应功能
- 侧边栏包含个人档案等次要功能

## 技术实现要点

- **响应式布局**：适配不同屏幕尺寸
- **动画效果**：便利贴拖拽、颜色变化使用流畅动画
- **性能优化**：任务列表虚拟滚动，大量数据时保持流畅
- **状态管理**：重要任务状态实时同步
- **手势支持**：支持滑动等手势操作
- **跨天事件处理**：智能识别和分段显示跨天事件
- **重复事件管理**：支持重复模式识别和多次显示
- **符号表达系统**：全面支持emoji和语义符号的输入、存储和显示

## 空状态设计规范

### 设计原则

- **避免压力**：不显示统计数据、完成率等可能给用户造成压力的信息
- **温暖引导**：体现"秘书般懂你"的产品灵魂，提供贴心而非监督式的体验
- **信息密度平衡**：重点突出核心功能，保持适度留白，让界面"会呼吸"

### 重要任务区域（空状态）

- **视觉元素**：优雅的占位图标，轻柔的脉动动画
- **文案**："准备就绪，等待您的重要事务"
- **交互**：点击区域直接进入添加任务流程
- **氛围**：体现"活着"的智能感，而非冰冷的空白

### 今日待办区域（空状态）

- **智能文案**：
  - 早晨："早安，新的一天开始了"
  - 下午："今日清净，专注当下"
  - 晚上："今日圆满，安心休息"
- **视觉**：简洁的图标，不抢夺注意力
- **情感连接**：让用户感受到被理解和关怀

### 快速操作区（空状态强化）

- **视觉权重**：在空状态下适当增强橙色渐变
- **引导性**：通过微妙的动画提示可用操作
- **温暖感**：保持专业的同时体现亲和力

### 整体空状态氛围

- **智能感**：界面元素具有生命力，体现AI助手的存在感
- **专业感**：适合体制内用户的审美和使用习惯
- **温暖感**：橙色主色调营造温暖、可信赖的氛围
- **引导感**：通过视觉层次引导用户进行核心操作

## 数据结构支持

### 重要任务数据结构
```typescript
interface ImportantTask {
  id: string;
  title: string;
  type: 'long-term' | 'important' | 'fuzzy-time';
  category?: string; // 用于智能匹配图标
  order: number; // 拖拽排序
  startDate?: Date;
  endDate?: Date;
}
```

### 日程事件数据结构
```