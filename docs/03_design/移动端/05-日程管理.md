# 移动端 - 05 日程管理 v2.0

## 界面描述

移动端日程管理的核心界面。它创新性地结合了**8格周视图**和**列表式议程视图**，并集成了强大的**多领导日程管理**及**秘书视角**功能，旨在提供一个既直观又高效的日程管理体验。

## 线框示意图 (v2.0)

```
┌───────────────────────────────────┐
│ [2025年 v] [6月 v]  日程  [本周][☰][+] │ 顶部导航
├───────────────────────────────────┤
│ [张晓虎] [戴小平] [李立光] ...       │ 二级标签栏 (领导日程)
├───────────────────────────────────┤
│                                   │
│ ┌──────────┬──────────────────────┐ │
│ │ 📅 月历   │ 周一 23              │ │
│ │          │ [上午: 主办-淡红]    │ │
│ │          │ [下午: 无]           │ │ 8格周视图
│ ├──────────┼──────────────────────┤ │ (半日色块)
│ │ 周二 24   │ 周三 25              │ │
│ │[上:协办-黄]│ [上:无]            │ │
│ │[下:参会-绿]│ [下:无]            │ │
│ └──────────┴──────────────────────┘ │
│ ... (其余4格) ...                 │
│                                   │
├───────────────────────────────────┤
│  ◀返回   👤我的   👥领导   📥收集站  │ 底部标签栏
└───────────────────────────────────┘
```

*注：线框图仅为示意，"[上午: 主办-淡红]" 等文字在实际UI中表现为淡淡的背景色块。*

## 角色与视图标识说明

### 半日色块方案

为了在保持界面简洁的同时，高效传递日程信息，我们采用"半日色块"方案，取代原有的文字和圆点标记。

- **视觉划分**：每个"每日卡片"在视觉上分为上下两个区域，代表上午和下午。
- **颜色逻辑**：
  - 如果该半天有日程，背景会渲染上对应的淡色。
  - 颜色代表该半天**最重要**的事务角色，优先级为：🔴(主办) > 🟡(协办) > 🟢(参会)。
  - 如果该半天没有日程，则保持默认背景色。
- **角色色彩**：
  - 🔴 **主办**：淡红色背景，表示您是该事务的主要负责人。
  - 🟡 **协办**：淡黄色背景，表示您是该事务的协助者。
  - 🟢 **参会**：淡绿色背景，表示您仅需参与。

## 设计要点 (v2.0)

- **双视图切换**：用户可通过顶部的 `☰` 图标在"8格周视图"和"列表式议程视图"间自由切换。
- **高效导航**：顶部提供独立的"年"、"月"选择器和"返回本周"按钮。
- **半日色块**：通过淡色背景快速判断半天的占用情况和事务重要性，避免信息过载。
- **多领导日程管理**：点击"领导"标签后，通过二级标签栏轻松切换不同领导的日程视图。
- **秘书视角**：提供创新的"合并视图"功能，一览自己与领导的全部日程，方便统筹安排。
- **信息溯源**：在日程详情中，可链接到"收集站"的原始信息。

## 交互说明 (v2.0)

- **视图切换**：点击顶部 `☰` 图标切换"周视图"和"列表视图"。
- **日期切换**：点击顶部"年"或"月"可快速跳转；在周视图中左右滑动可切换上下周。
- **打开详情**：
  - **双击月历** -> 进入**全屏月视图**。
  - **双击每日卡片** -> 进入该日的**单日详细视图**（列表形式）。
- **领导日程**：点击底部"领导"标签，显示二级标签栏；点选不同领导姓名以切换其日程。
- **秘书视角切换**：在"我的"或"领导"页面，可点击"视角切换"图标，将个人日程与领导日程合并显示。
- **添加日程**：点击 `+` 按钮快速创建新日程。

## 视图切换说明

- **默认视图**：8格周视图。
- **列表/议程视图**：按时间顺序展示所有日程，支持无限滚动，方便回顾总结。
- **单日详细视图**：展示某一天的完整日程列表。
- **全屏月视图**：专注于查看整月安排。

## 移动端优化

- **触控友好**：所有点击区域均经过优化，符合移动端操作习惯。
- **信息精简**：遵循"一眼即知"原则，核心视图只展示状态，详情在二级页面呈现。
- **快速操作**：保留了快速添加等核心快捷功能。
- **离线可用**：核心日程数据本地存储，保障无网络时基础功能可用。

## 最新更新记录

### v2.2 智能时间显示和滚动交互优化 (2025年6月18日)

**主要改进**：
- **智能时间感知**：实现基于当前时间的视觉淡化效果，过去的时间段自动变浅，只对今天生效
- **滚动感知交互**：修复iOS和Android平台的滚动隐藏功能，实现平滑的底部操作栏动画
- **布局空间优化**：继续优化时间显示区域的空间利用，改进双行时间显示的行高

### v2.1 日程列表显示优化 (2025年1月13日)

**主要改进**：
- **布局空间优化**：将时间显示区域向左移动，为日程卡片释放更多显示空间
- **智能时间显示**：实现根据日程类型的动态时间显示逻辑，支持起止时间分行显示
- **动态数据支持**：新增模拟数据文件，支持昨天、今天、明天的动态切换测试
- **用户体验优化**：改进"无日程"提示显示，隐藏滚动条，优化时段标题对齐
