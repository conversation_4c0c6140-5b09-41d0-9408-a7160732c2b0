# 移动端 - 03 拍照识别

## 界面描述
智能拍照识别界面，支持微信截图、纸质文件等多种场景

## 线框示意图

```
┌─────────────────────────────┐
│ ←  拍照识别        💡 提示   │ 顶部导航
├─────────────────────────────┤
│                             │
│ ┌─────────────────────────┐ │
│ │                         │ │
│ │                         │ │
│ │                         │ │
│ │      📷 相机预览         │ │
│ │                         │ │
│ │                         │ │
│ │                         │ │
│ │                         │ │
│ │                         │ │
│ └─────────────────────────┘ │
│                             │
│ 📱 微信截图  📄 纸质文件     │ 识别类型
│ 🖼️ 相册选择  📋 会议纪要     │
│                             │
│ ┌─────────────────────────┐ │
│ │ 💡 拍摄提示：             │ │
│ │ • 保持文字清晰可见        │ │
│ │ • 避免反光和阴影          │ │
│ │ • 微信截图可直接识别      │ │
│ └─────────────────────────┘ │
│                             │
│        📷 拍照    📁 相册    │ 操作按钮
│                             │
└─────────────────────────────┘
```

## 设计要点
- 大面积相机预览区域
- 智能识别不同文档类型
- 实时拍摄提示和指导
- 支持相册选择已有图片
- 针对体制内常见场景优化

## 交互说明
- 点击识别类型自动调整识别参数
- 拍照后自动进行OCR识别
- 识别结果可编辑和确认
- 支持批量拍摄和识别 