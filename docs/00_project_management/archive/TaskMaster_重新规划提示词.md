# TaskMaster 项目重新规划提示词

## 项目背景与核心理念

### 产品定位
公职猫是全国首款"岗位数字分身"——专为体制内中青年干部设计的个人AI助理。

**核心理念：U盘般私密，秘书般懂你**
- **U盘般私密**：数据主权归用户所有，100%本地存储，跨端传输自由
- **秘书般懂你**：智能化程度高，替你记、替你想、替你做事

### 技术栈与架构
- **移动端**：React Native（iOS/Android）
- **桌面端**：Electron + React（Windows/统信UOS）
- **数据存储**：SQLite + 基础加密（100%本地存储）
- **AI架构**：云端AI优先 → 本地AI回退 → 规则引擎兜底
- **同步方案**：移动端和桌面端数据本地同步

### 目标用户与核心场景
- **主要用户**：体制内中青年干部（综合岗科员、办公室主任、项目秘书等）
- **核心场景**：领导口头交办、微信群通知、会议纪要截图、纸质文件拍照、领导日程导入

## 项目现状分析

### 关键问题发现
1. **计划与现实脱节**：项目评审报告描述项目已相对成熟，但实际代码状态仅有基础框架
2. **移动端主界面未实现**：当前仅有基础任务列表，与设计稿要求差距巨大
3. **核心功能缺失**：OCR识别、AI智能识别、文档管理等核心功能实现度极低

### 实际进度评估
- **移动端**：基础框架完成，核心功能未实现（完成度：20%）
- **桌面端**：未开始
- **数据库设计**：进行中，8个子任务部分完成
- **AI集成**：测试阶段，未集成到主流程

## 移动端主界面设计要求（最高优先级）

### 设计稿要求（docs/03_design/移动端/02-主界面.md）
1. **便利贴式重要跟踪任务展示**：
   - 80px × 80px 卡片样式
   - 颜色编码优先级（红色-紧急，橙色-重要，蓝色-一般）
   - 支持拖拽排序功能

2. **日期导航区**：
   - 支持左右切换日程
   - 显示当前日期和星期
   - 快速跳转到指定日期

3. **视觉设计**：
   - 深色主题 + 橙色主色调
   - 背景色：#1a1a1a
   - 主色调：#FF8C00
   - 现代化UI设计，符合体制内用户审美

4. **中间区域布局**：
   - 上半部分：便签卡片展示区
   - 下半部分：当日日程列表，左右滑动切换不同日期日程，仅限前后各一天
   - 支持上下滑动查看日程列表

### 当前实现状态（mobile/src/components/HomeScreen.tsx）
- ❌ 基础任务列表，无便利贴样式
- ❌ 无日期切换功能
- ❌ 界面布局与设计稿不符
- ❌ 缺少设计稿要求的视觉样式

## MVP核心功能要求（P0功能）

### 1. 智能信息采集
- 微信截图识别（专门优化群通知、会议安排）
- 语音指令理解（识别领导口头交办）
- 纸质文件拍照（识别手写批注、圈改内容）
- 文字输入（直接输入任务信息）

### 2. 文档导入与检索（U盘级存取）
- 多格式文档导入（Word、Excel、PDF、图片）
- 领导日程文件智能识别
- 全文检索功能（本地搜索引擎）
- 文档预览管理

### 3. 个人重要资料管理
- 证件档案管理（身份证、证件照、证书）
- 证件照处理功能（离线人像抠图、背景替换）
- 个人档案维护
- 快速调用功能

### 4. 角色智能识别与事务管理（核心差异化）
- 语境理解（体制内职级关系、流程规范）
- 角色智能判断（主办、协办、参与）
- 差异化管理（不同角色不同管理方式）
- 系统能力沉淀

### 5. 本地安全存储（U盘般私密）
- 个人数据本地存储（100%本地）
- AI个性化支持（本地学习用户习惯）
- 跨端数据传输（移动端↔桌面端）
- 基础加密保护

### 6. 日程事务管理
- 分类管理（简单日程vs复杂事务）
- 进度跟踪（多状态管理）
- 时间管理（智能提醒）
- 工作习惯适配

## 技术要求与成功指标

### 技术要求
- **响应性能**：应用启动时间<3秒，功能响应时间<2秒
- **离线优先**：核心功能可完全离线使用
- **AI混合架构**：云端AI优先 → 本地AI回退 → 规则引擎兜底
- **安全要求**：SQLite数据库加密，HTTPS加密通信

### 成功指标
- [ ] 移动端主界面完全符合设计稿要求
- [ ] 文档导入和检索功能稳定可用
- [ ] 微信截图OCR识别准确率>60%
- [ ] 角色智能识别和分类准确率>60%
- [ ] 5-10名种子用户认为"有用且安全"（满意度>4.0/5.0）
- [ ] 7天留存率>50%

## AI编程项目管理要求

### 每周迭代标准
- **Week 1**：移动端主界面重构（最高优先级）
- **Week 2**：数据库设计完成 + OCR识别集成
- **Week 3**：任务智能识别 + 规则引擎集成
- **Week 4**：跨端同步 + 集成测试

### 验收标准
- 每个功能必须有可演示的用户价值
- 移动端界面必须严格符合设计稿要求
- 所有功能必须支持离线使用
- 数据100%本地存储，符合"U盘般私密"理念

### 开发模式要求
- 优先使用AI编程提高开发效率
- 每个任务必须包含详细的实现步骤
- 重视代码质量和用户体验
- 及时集成测试，确保功能稳定

## 特别注意事项

1. **移动端主界面是最高优先级**：必须严格按照设计稿要求实现
2. **核心理念贯穿始终**："U盘般私密，秘书般懂你"必须体现在每个功能设计中
3. **AI编程优化**：充分利用AI编程提高开发效率，但要确保代码质量
4. **每周迭代交付**：每周必须有可演示的功能增量，在功能实现前需要先确认详细需求和测试用例
5. **用户体验优先**：所有功能设计必须符合体制内用户的使用习惯
6. **数据安全第一**：100%本地存储，绝不妥协
7. **离线优先设计**：核心功能必须支持完全离线使用

## 预期输出

请基于以上要求，使用TaskMaster MCP工具重新规划项目任务，确保：
- 任务优先级正确（移动端主界面最高优先级）
- 任务拆解详细（适合AI编程）
- 时间安排合理（每周迭代）
- 验收标准明确（可演示的用户价值）
- 体现核心理念（U盘般私密，秘书般懂你）

---
*提示词版本：v2.0  |  创建日期：2025-01-14  |  基于TaskMaster MCP工具* 