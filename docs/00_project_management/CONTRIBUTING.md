# 🤝 贡献指南

## 📋 分支策略

### 主要分支
- **`master`**: 生产环境分支，只包含稳定的发布版本
- **`develop`**: 开发主分支，包含最新的开发功能

### 功能分支
- **`feature/*`**: 新功能开发分支
- **`bugfix/*`**: Bug修复分支
- **`hotfix/*`**: 紧急修复分支
- **`release/*`**: 发布准备分支

### 分支命名规范
```
feature/task-<任务ID>-<简短描述>
例如: feature/task-5-react-native-setup

bugfix/issue-<问题ID>-<简短描述>
例如: bugfix/issue-123-login-error

hotfix/urgent-<简短描述>
例如: hotfix/urgent-security-patch

release/v<版本号>
例如: release/v1.0.0
```

## 🔄 开发工作流

### 1. 开始新功能开发
```bash
# 从develop分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/task-<ID>-<描述>
```

### 2. 开发过程中
```bash
# 定期提交代码
git add .
git commit -m "feat(module): 实现具体功能"

# 定期同步develop分支的最新变更
git checkout develop
git pull origin develop
git checkout feature/task-<ID>-<描述>
git merge develop
```

### 3. 完成功能开发
```bash
# 推送功能分支
git push origin feature/task-<ID>-<描述>

# 在Gitee上创建Pull Request到develop分支
```

### 4. 代码审查和合并
- 创建Pull Request
- 代码审查
- 通过CI/CD检查
- 合并到develop分支
- 删除功能分支

## 📝 提交信息规范

### 提交类型
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 提交格式
```
<类型>(模块): <简短描述>

<详细描述>

<相关任务ID>
```

### 示例
```
feat(mobile): 实现用户登录功能

- 添加登录表单组件
- 集成本地认证逻辑
- 添加错误处理机制

TaskMaster Task ID: #5
```

## 🧪 代码质量要求

### 开发前检查
- [ ] 理解TaskMaster任务要求
- [ ] 确认技术方案
- [ ] 检查依赖关系

### 开发中要求
- [ ] 遵循项目代码规范
- [ ] 添加必要的注释
- [ ] 编写单元测试
- [ ] 确保类型安全(TypeScript)

### 提交前检查
- [ ] 代码编译无错误
- [ ] 测试通过
- [ ] 符合ESLint规范
- [ ] 更新相关文档

## 🔒 安全要求

### 敏感信息处理
- 绝不提交API密钥、密码等敏感信息
- 使用`.env`文件管理环境变量
- 确保`.env`文件在`.gitignore`中

### 数据安全
- 所有用户数据必须加密存储
- 遵循最小权限原则
- 定期进行安全审查

## 📋 TaskMaster集成

### 任务驱动开发
```bash
# 查看当前任务
yarn task-master next

# 更新任务状态
yarn task-master set-status --id=<task-id> --status=in-progress

# 完成任务
yarn task-master set-status --id=<task-id> --status=done
```

### 任务分支关联
- 分支名必须包含TaskMaster任务ID
- 提交信息中引用相关任务ID
- Pull Request描述中说明完成的任务

## 📄 文档错误与改进

我们鼓励所有贡献者帮助我们改进文档。如果您发现任何文档错误、过时信息或可以改进的地方，请遵循以下流程：

1.  **创建Issue**：在项目的Gitee Issues页面创建一个新的Issue。
2.  **清晰描述**：在Issue中清晰地描述问题所在，包括文件名、章节和具体内容。
3.  **标记标签**：为该Issue添加 `documentation` 标签，以便快速分类。
4.  **提交修复 (可选)**：如果您有能力修复该问题，我们非常欢迎您创建一个Pull Request并关联到该Issue。

## 🚀 发布流程

### 1. 准备发布
```bash
git checkout develop
git pull origin develop
git checkout -b release/v<版本号>
```

### 2. 发布测试
- 运行完整测试套件
- 进行用户验收测试
- 更新版本号和CHANGELOG

### 3. 合并发布
```bash
# 合并到master
git checkout master
git merge release/v<版本号>
git tag v<版本号>
git push origin master --tags

# 合并回develop
git checkout develop
git merge release/v<版本号>
git push origin develop
```

## 📞 获取帮助

- **技术问题**: 在Gitee Issues中提问
- **任务相关**: 使用TaskMaster工具查看任务详情
- **紧急问题**: 联系项目负责人

## 🔒 权限管理和团队协作

### 团队角色定义
- **核心开发者**: 拥有所有代码的完整访问权限
- **平台开发者**: 只能访问特定平台相关代码
- **外包团队**: 受限访问，仅能访问分配的功能模块
- **设计师**: 主要访问设计文档和UI相关代码

### 代码访问权限矩阵

#### 核心开发者权限
- ✅ 所有目录完整访问
- ✅ 主分支直接推送权限
- ✅ Pull Request审查权限
- ✅ 发布和部署权限

#### 平台开发者权限
```bash
# Android开发者
mobile/android/          # 完整访问
mobile/src/              # 只读访问
docs/03_design/移动端/    # 只读访问

# iOS开发者  
mobile/ios/              # 完整访问
mobile/src/              # 只读访问
docs/03_design/移动端/    # 只读访问

# 桌面端开发者
desktop/                 # 完整访问
shared/                  # 只读访问
docs/03_design/桌面端/    # 只读访问
```

#### 外包团队权限
- 🔒 禁止访问: `backend/`, `shared/crypto/`, `.env`文件
- ✅ 受限访问: 分配的功能模块目录
- ✅ 文档访问: 相关的设计和开发文档
- ❌ 分支权限: 不能直接推送到主要分支

### Git分支权限控制

#### 分支保护规则
```bash
# master分支 - 最高保护级别
- 禁止直接推送
- 需要Pull Request
- 需要至少2个核心开发者审查
- 需要通过所有CI检查
- 需要项目经理最终批准

# develop分支 - 高保护级别  
- 禁止直接推送
- 需要Pull Request
- 需要至少1个核心开发者审查
- 需要通过CI检查

# feature/*分支 - 中等保护级别
- 创建者可以直接推送
- 合并到develop需要审查
- 外包团队只能推送到指定的feature分支
```

### 外包团队Git工作流

#### 1. 仓库访问设置
```bash
# 为外包团队创建fork仓库
git clone --depth 1 https://gitee.com/gongzhimall/gongzhimall-outsourcing.git

# 设置上游仓库
git remote add upstream https://gitee.com/gongzhimall/gongzhimall.git

# 只同步允许的目录
git sparse-checkout init --cone
git sparse-checkout set mobile/android docs/03_design/移动端
```

#### 2. 外包分支命名规范
```bash
# 外包功能分支
outsourcing/<团队名>/<功能描述>
例如: outsourcing/android-team/camera-integration

# 外包修复分支  
outsourcing/<团队名>/fix-<问题描述>
例如: outsourcing/ui-team/fix-button-style
```

#### 3. 代码提交和审查流程
```bash
# 外包团队提交流程
git checkout -b outsourcing/android-team/new-feature
# 开发代码...
git add .
git commit -m "feat(android): 实现相机功能"
git push origin outsourcing/android-team/new-feature

# 创建Pull Request到develop分支
# 等待内部团队代码审查
# 审查通过后由核心开发者合并
```

## 🔐 安全审查流程

### 代码审查检查清单
- [ ] 是否包含硬编码的敏感信息
- [ ] 是否遵循项目代码规范
- [ ] 是否有潜在的安全漏洞
- [ ] 是否影响其他模块的功能
- [ ] 是否通过所有自动化测试
- [ ] 是否更新了相关文档

### 外包代码特殊审查
- [ ] 检查是否访问了禁止的API
- [ ] 验证数据处理是否符合隐私要求
- [ ] 确认没有引入恶意代码
- [ ] 检查性能影响
- [ ] 验证与现有代码的兼容性

## 📊 权限管理监控

### 访问日志记录
```bash
# 启用Git访问日志
git config --global log.showSignature true
git config --global user.signingkey <GPG-KEY-ID>

# 记录所有推送操作
git config receive.fsckObjects true
git config receive.denyNonFastForwards true
```

### 定期权限审计
- 每月审查团队成员权限
- 季度检查外包团队访问记录
- 年度全面安全审计

## 🚨 应急响应

### 权限泄露应对
1. 立即撤销相关访问权限
2. 更改所有相关密钥和密码
3. 审查可能受影响的代码
4. 通知相关团队成员
5. 记录事件并制定预防措施

### 代码安全事件处理
1. 隔离可疑代码
2. 回滚到安全版本
3. 分析安全影响范围
4. 修复安全问题
5. 加强相关安全措施

---

*遵循这些指南有助于保持代码质量和项目的有序发展。*