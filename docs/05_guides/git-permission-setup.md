# Git仓库权限管理实施指南

## 概述
本指南说明如何在Gitee上为公职猫项目配置代码访问权限，确保不同团队成员只能访问其职责范围内的代码。

## 🚀 快速开始

### 1. 运行权限配置脚本
```bash
# 在项目根目录运行
chmod +x scripts/setup-git-permissions.sh
./scripts/setup-git-permissions.sh
```

### 2. 在Gitee上配置分支保护
访问：`https://gitee.com/your-org/gongzhimall/settings/branches`

#### Master分支保护设置
- ✅ 启用分支保护
- ✅ 禁止强制推送
- ✅ 需要Pull Request审查
- ✅ 需要至少2个审查者
- ✅ 需要通过状态检查
- ✅ 限制推送权限（仅项目管理员）

#### Develop分支保护设置
- ✅ 启用分支保护
- ✅ 禁止强制推送
- ✅ 需要Pull Request审查
- ✅ 需要至少1个审查者
- ✅ 需要通过状态检查

### 3. 创建团队和分配权限
访问：`https://gitee.com/your-org/settings/teams`

#### 核心开发团队
- **成员**：项目经理、技术负责人、高级开发者
- **权限**：Admin（完整访问权限）
- **访问范围**：所有代码和配置

#### 平台开发团队
- **Android团队**：
  - 权限：Write（推送权限）
  - 访问范围：`mobile/android/`, `mobile/src/`, `docs/03_design/移动端/`
  
- **iOS团队**：
  - 权限：Write（推送权限）
  - 访问范围：`mobile/ios/`, `mobile/src/`, `docs/03_design/移动端/`

- **桌面端团队**：
  - 权限：Write（推送权限）
  - 访问范围：`desktop/`, `shared/`, `docs/03_design/桌面端/`

#### 外包团队
- **权限**：Write（受限推送权限）
- **限制**：
  - 不能访问：`backend/`, `shared/crypto/`, `.env*`文件
  - 只能推送到：`outsourcing/*`分支
  - 需要代码审查才能合并

## 🔧 技术实现

### Git Hooks自动检查
脚本会自动设置以下Git hooks：

#### pre-commit检查
- 检测API密钥泄露
- 检测密码硬编码
- 阻止.env文件提交

#### pre-push检查
- 阻止直接推送到保护分支
- 强制使用Pull Request流程

### 外包团队工作流

#### 1. 设置外包仓库访问
```bash
# 为Android外包团队设置
./scripts/setup-outsourcing-repo.sh android

# 为iOS外包团队设置
./scripts/setup-outsourcing-repo.sh ios
```

#### 2. 外包团队克隆和配置
```bash
# 外包团队克隆仓库
git clone https://gitee.com/gongzhimall/gongzhimall.git
cd gongzhimall

# 配置sparse-checkout（只下载允许的文件）
git config core.sparseCheckout true
echo "mobile/android/" > .git/info/sparse-checkout
echo "mobile/src/" >> .git/info/sparse-checkout
echo "docs/03_design/移动端/" >> .git/info/sparse-checkout
git read-tree -m -u HEAD
```

#### 3. 外包开发流程
```bash
# 创建外包功能分支
git checkout -b outsourcing/android-team/camera-feature

# 开发代码...
git add .
git commit -m "feat(android): 实现相机功能"

# 推送到外包分支
git push origin outsourcing/android-team/camera-feature

# 在Gitee上创建Pull Request到develop分支
```

## 📊 权限监控和审计

### 定期权限审计
```bash
# 运行权限审计脚本
./scripts/audit-permissions.sh

# 检查访问日志
tail -f access.log

# 检查敏感文件
find . -name "*.env*" -o -name "*.key" -o -name "*.pem"
```

### 访问日志记录
系统会自动记录：
- 代码推送记录
- 分支创建和删除
- Pull Request活动
- 敏感文件访问

## 🚨 安全事件响应

### 发现权限泄露时
1. **立即行动**：
   ```bash
   # 撤销相关访问权限
   # 在Gitee团队设置中移除用户
   
   # 更改所有API密钥
   # 更新.env.example中的示例密钥
   ```

2. **审查影响**：
   ```bash
   # 检查可能受影响的提交
   git log --author="<用户名>" --since="1 month ago"
   
   # 检查文件访问记录
   grep "<用户名>" access.log
   ```

3. **修复和预防**：
   - 回滚有问题的提交
   - 加强权限检查
   - 更新安全策略

## 📋 权限配置检查清单

### 仓库级别
- [ ] 启用分支保护（master, develop）
- [ ] 配置Pull Request必需审查
- [ ] 设置状态检查要求
- [ ] 限制强制推送

### 团队级别
- [ ] 创建核心开发团队
- [ ] 创建平台开发团队
- [ ] 创建外包团队
- [ ] 分配适当权限

### 技术级别
- [ ] 配置Git hooks
- [ ] 设置sparse-checkout
- [ ] 启用访问日志
- [ ] 配置自动化检查

### 流程级别
- [ ] 制定代码审查流程
- [ ] 建立权限申请流程
- [ ] 设置定期审计计划
- [ ] 制定应急响应预案

## 🔗 相关资源

- [Gitee分支保护文档](https://gitee.com/help/articles/4156)
- [Gitee团队管理文档](https://gitee.com/help/articles/4140)
- [Git Hooks官方文档](https://git-scm.com/book/zh/v2/自定义-Git-Git-钩子)
- [项目贡献指南](../../CONTRIBUTING.md)

## 📞 获取帮助

如果在配置过程中遇到问题：
1. 检查Gitee官方文档
2. 运行`./scripts/audit-permissions.sh`诊断
3. 联系项目技术负责人
4. 在项目Issues中提问

---

*定期审查和更新权限配置，确保项目安全。* 