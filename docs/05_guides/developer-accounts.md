# 公职猫 - 开发者账号配置

## 📱 移动端发布账号

### iOS开发者账号
- **状态**：已注册 ✅
- **用途**：App Store发布、TestFlight内测
- **需要配置**：
  - Bundle ID: `com.gongzhimall.mobile`
  - 证书和Provisioning Profile
  - App Store Connect配置

### 华为开发者账号  
- **状态**：已注册 ✅
- **用途**：华为应用市场发布、AppGallery Connect
- **需要配置**：
  - 应用包名: `com.gongzhimall.mobile`
  - 签名证书配置
  - HMS Core服务集成

## 🔧 CI/CD配置需求

### iOS自动化发布（App Store + TestFlight）
```yaml
# Gitee Actions需要的Secrets
IOS_CERTIFICATE_P12: # iOS开发者证书
IOS_CERTIFICATE_PASSWORD: # 证书密码
IOS_PROVISIONING_PROFILE: # Provisioning Profile
APPSTORE_CONNECT_API_KEY: # App Store Connect API密钥
APPSTORE_CONNECT_ISSUER_ID: # Issuer ID
APPSTORE_CONNECT_KEY_ID: # Key ID
```

### 华为应用市场自动化发布
```yaml
# Gitee Actions需要的Secrets
HUAWEI_APP_ID: # 华为应用ID
HUAWEI_CLIENT_SECRET: # 华为开发者密钥
HUAWEI_CLIENT_ID: # 华为客户端ID
HUAWEI_KEYSTORE: # Android签名证书
HUAWEI_KEYSTORE_PASSWORD: # 证书密码
```

## 📦 移动端发布策略

### Android发布
- **主要渠道**：华为应用市场（HarmonyOS优先）
- **包名**：`com.gongzhimall.mobile`
- **其他渠道**：暂不考虑，聚焦华为生态

### iOS发布
- **主要渠道**：App Store（全球）
- **测试渠道**：TestFlight（内测）
- **Bundle ID**：`com.gongzhimall.mobile`

## 🖥 桌面端发布策略

### 官网下载模式
- **Windows**：exe安装包，放置在 www.gongzhimall.com
- **macOS**：dmg安装包，放置在 www.gongzhimall.com
- **不使用应用商店**：国内用户更习惯官网下载

### 桌面端配置
- **Windows Bundle ID**：`com.gongzhimall.desktop`
- **macOS Bundle ID**：`com.gongzhimall.desktop`

## 🔐 安全配置

### 证书管理
- 所有证书和私钥使用GitHub Secrets存储
- 定期更新证书（iOS证书1年有效期）
- 备份所有关键证书和配置文件

### 访问控制
- CI/CD仅在受保护分支触发
- 发布权限仅限项目管理员
- 所有敏感配置通过环境变量管理

## 📋 待配置清单

### 立即需要 (Week 1)
- [ ] iOS开发者证书导出和配置
- [ ] 华为开发者密钥获取
- [ ] Gitee Actions Secrets配置
- [ ] 基础CI/CD流程搭建 ✅ (已完成)

### 后续需要 (Week 2-4)
- [ ] 官网自动化部署脚本
- [ ] 华为AppGallery发布脚本
- [ ] App Store发布脚本
- [ ] 自动化测试集成
- [ ] 监控和报警配置

### 暂不需要
- ~~其他Android应用市场~~（聚焦华为）
- ~~Windows/macOS应用商店~~（使用官网下载）

---

**注意**：请在CI/CD配置时提供相关证书和密钥，以便建立完整的自动化发布流程。 