# 公职猫 - 跨终端设计系统指导

## 📋 概述

本文档为公职猫项目提供跨终端设计一致性的指导原则，适用于移动端、桌面端和后端管理界面。

**重要说明**：本文档提供设计框架和原则，具体的设计稿由UI/UX团队提供时将替换当前的临时实现。

## 🎯 设计原则

### 核心品牌价值
- **专业性**：体制内用户的专业工作需求
- **私密性**：个人数据的安全感和信任感
- **效率性**：快速完成日常工作任务
- **智能性**：AI助理的智能化体验

### 跨终端一致性原则
1. **视觉统一**：相同的品牌色彩、字体层级、图标风格
2. **交互一致**：相似的操作模式和反馈机制
3. **信息架构**：统一的导航逻辑和内容组织
4. **响应式适配**：针对不同设备的合理适配

## 🎨 设计Token系统

### 颜色系统（待设计稿确定）
```
主色调系列：
- Primary: #待定 (当前临时使用 #007AFF)
- Primary Light: #待定
- Primary Dark: #待定

功能色彩：
- Success: #待定 (当前临时使用 #34C759)
- Warning: #待定 (当前临时使用 #FF9500)
- Error: #待定 (当前临时使用 #FF3B30)
- Info: #待定 (当前临时使用 #5AC8FA)

中性色系：
- Background: #待定
- Surface: #待定
- Border: #待定
- Text Primary: #待定
- Text Secondary: #待定
```

### 字体系统
```
层级定义：
- H1: 主标题 (28px/1.2)
- H2: 副标题 (22px/1.3)
- H3: 三级标题 (18px/1.3)
- Body1: 正文 (16px/1.5)
- Body2: 辅助文本 (14px/1.4)
- Caption: 说明文字 (12px/1.3)

字重：
- Regular: 400
- Medium: 500  
- Semibold: 600
- Bold: 700
```

### 间距系统
```
基础单位：4px
间距倍数：
- XS: 4px
- SM: 8px
- MD: 16px
- LG: 24px
- XL: 32px
- XXL: 48px
```

### 圆角和阴影
```
圆角：
- Small: 6px
- Medium: 8px
- Large: 12px
- Round: 25px

阴影层级：
- Small: 轻微阴影
- Medium: 中等阴影
- Large: 重阴影
```

## 📱 平台特定指导

### 移动端 (React Native)
- **主题文件**：`mobile/src/styles/theme.ts`
- **适配重点**：触摸友好、单手操作、弱网环境
- **导航模式**：Bottom Tab + Stack Navigation
- **输入模式**：文字、语音、拍照三种方式

### 桌面端 (Electron)
- **主题文件**：`desktop/src/styles/theme.ts` (待创建)
- **适配重点**：鼠标键盘交互、多窗口管理、快捷键
- **导航模式**：侧边栏 + 多标签页
- **输入模式**：键盘输入为主，支持文件拖拽

### 后端管理 (Web)
- **主题文件**：`admin-web/src/styles/theme.ts` (待创建)
- **适配重点**：数据表格、批量操作、权限管理
- **导航模式**：左侧菜单 + 面包屑导航
- **功能重点**：用户管理、数据分析、系统配置

## 🔄 设计稿接入流程

### 1. 设计稿交付标准
- **设计规范文档**：包含完整的Design Token定义
- **组件库**：各平台的标准组件设计
- **界面设计稿**：高保真原型和标注
- **交互说明**：动效和交互逻辑说明

### 2. 技术实现流程
```
设计稿到位 → 更新Theme配置 → 验证组件适配 → 全量替换测试 → 发布更新
```

### 3. 分阶段替换策略
- **第一阶段**：更新颜色和字体系统
- **第二阶段**：更新组件库样式
- **第三阶段**：完善动效和细节

## 📋 当前状态

### 已实现
- ✅ 移动端主题系统框架
- ✅ 跨平台设计原则定义
- ✅ 设计Token预留结构

### 待实现
- 🔄 桌面端主题系统
- 🔄 后端管理界面主题系统  
- 🔄 品牌规范细化
- 🔄 组件库标准化

### 临时方案
当前各平台使用iOS Human Interface Guidelines作为临时设计标准，确保：
- 基本的视觉一致性
- 良好的用户体验
- 后续设计稿的平滑替换

## 🛠 开发者指南

### 新组件开发
1. 使用平台对应的theme配置
2. 遵循设计token命名规范
3. 支持theme prop传入自定义主题
4. 添加适当的类型定义

### 样式更新
1. 避免硬编码颜色和尺寸
2. 使用theme变量替代magic number
3. 考虑深色模式适配
4. 保持跨平台的视觉一致性

---

**注意**：此文档将随着正式设计稿的确定而更新完善，当前重点是建立可扩展的技术架构。 