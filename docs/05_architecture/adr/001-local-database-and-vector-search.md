![1750649595417](image/001-local-database-and-vector-search/1750649595417.png)![1750649610125](image/001-local-database-and-vector-search/1750649610125.png)# ADR 001: 本地数据库与向量化搜索技术选型

**状态**: 已接纳

**日期**: 2024-07-30

## 上下文 (Context)

公职猫应用的核心价值主张是"U盘级私密，秘书般懂你"。这要求我们在技术选型上必须满足以下几个关键条件：
1.  **数据本地化**：所有用户数据必须默认存储在用户自己的设备上，不能依赖云端服务器。
2.  **高性能与响应性**：作为一款生产力工具，应用的UI必须流畅，数据读写和查询不能有可感知的延迟。
3.  **未来AI能力**：架构必须具备前瞻性，能够支持未来的本地AI功能，特别是基于RAG（检索增强生成）的智能搜索和问答。
4.  **跨平台兼容性**：技术栈需在React Native (iOS/Android) 环境下稳定运行。

## 决策 (Decision)

我们决定采用 **WatermelonDB + `vss-lite`** 的技术组合作为本地数据持久化和向量化搜索的解决方案。

1.  **数据库**: 使用 **WatermelonDB** 作为上层API框架，其底层依赖于原生的 **SQLite** 数据库。
2.  **向量化搜索**: 在需要时，集成 SQLite 的扩展 **`vss-lite`** (Vector Search Similarity)，为本地数据提供向量化索引和相似度搜索能力。

## 决策理由 (Rationale)

这个决策是基于以下几点综合考量的：

1.  **WatermelonDB 的优势**:
    *   **响应式设计**: WatermelonDB 是为构建高性能React/React Native应用而设计的。它的核心特性是"懒加载"和"响应式"，组件只会订阅它们所需的数据变更，任何数据的更改都会自动、高效地触发UI重渲染。这与我们追求流畅UI的目标完美契合。
    *   **原生性能**: 它直接在多线程中与原生SQLite桥接，避免了在JavaScript和原生层之间大量传输数据的性能瓶颈，非常适合处理大规模数据集。
    *   **成熟的生态**: WatermelonDB 经过了社区的检验，相对稳定，并为React Native提供了良好的支持。

2.  **`vss-lite` 的前瞻性**:
    *   **赋能本地RAG**: `vss-lite` 让我们能够在用户的设备上直接对本地数据（如笔记、日程、文件内容）进行向量化存储和检索。这是未来实现高级AI功能（如"我的会议记录里有没有提到关于预算调整的事？"）的基石，而无需将用户隐私数据上传到云端。
    *   **轻量与高效**: 作为一个SQLite扩展，`vss-lite` 非常轻量，能够直接在移动设备上运行，避免了引入重型向量数据库的复杂性。
    *   **与现有技术栈无缝集成**: 它可以直接作用于我们已有的SQLite数据库，无需进行数据迁移或引入新的数据存储系统。

3.  **替代方案的考量**:
    *   **Realm**: 性能优异，但其数据模型和API与React生态的融合不如WatermelonDB自然。其自身的商业闭源性质也带来一定的不确定性。
    *   **PouchDB/CouchDB**: 主要为云同步场景设计，对于我们"本地优先"的策略来说，引入了不必要的复杂性。
    *   **直接使用`react-native-sqlite-storage`**: 虽然可行，但这要求我们手动构建整个响应式数据层和ORM，会耗费大量开发精力，是典型的"重复造轮子"。

## 后果 (Consequences)

- **正面**:
    - 我们获得了一个既能满足当前高性能UI需求，又为未来本地AI功能铺平道路的强大数据底层。
    - 坚持了"数据本地化"的核心原则，保障了用户隐私。
    - 避免了过早引入复杂的AI后端设施，降低了初期的架构和维护成本。
- **负面**:
    - 集成 `vss-lite` 可能需要一些额外的原生编译工作，特别是在Android平台上。
    - 团队成员需要学习WatermelonDB的API和响应式编程范式。
- **风险**:
    - `vss-lite` 相对较新，社区支持和文档可能不如成熟的向量数据库丰富。我们需要投入时间进行研究和测试，并准备好应对潜在的问题。 