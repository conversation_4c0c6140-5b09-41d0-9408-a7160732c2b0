# 北极星指标及关键假设

## 1. 北极星指标 (North Star Metric)

**到2025年底，实现1000名付费用户。**

这是我们衡量成功的唯一标准。所有产品开发、市场推广和运营活动都必须服务于此目标。

## 2. 核心假设 (Key Assumptions)

我们的战略建立在以下三个关键假设之上：

-   **价值假设**: 用户（体制内中青年干部）高度重视数据隐私和安全。一个提供"U盘级私密"的AI助手，能有效解决他们使用云端AI服务的顾虑，因此具备高付费转化潜力。
-   **技术假设**: 在当前主流移动设备上，完全可以实现高性能、100%设备端运行的AI功能（如语音识别、自然语言处理），从而打造出真正的"离线可用、数据不出设备"的核心技术壁垒。
-   **增长假设**: 在体制内圈层，高质量、解决核心痛点的产品具备极强的口碑传播效应。早期的种子用户将成为我们最有力的推广渠道。

## 3. 阶段性执行路线图 (Phased Execution Roadmap)

我们将采用"技术验证 → 核心价值交付 → 商业闭环"的三阶段策略。

---

### **第一阶段：技术能力验证 (2025年6月)**

本月的目标是证明创始人具备独立利用AI完成产品核心架构的能力，为后续融资和产品开发建立信心。

| 周数 | 关键假设 (Key Assumption) | 核心任务 (Core Task) | 交付物/KPI (Deliverable/KPI) |
| :--- | :--- | :--- | :--- |
| **Week 1-2** | AI辅助开发能够显著提升项目管理和代码生成的效率与规范性。 | 1. 重构并完善Taskmaster AI规则引擎。<br>2. 建立项目级开发规范与工作流。 | 1. 一套成熟可用的AI规则与工作流。<br>2. 标准化的项目管理与代码审查流程。 |
| **Week 3** | 端侧AI技术（如OCR）的集成是可行的，并能为未来的核心功能打下基础。 | 1. 技术调研并实现基础的端侧OCR功能。<br>2. 搭建可演示的App基础框架。 | 1. OCR功能POC验证成功。<br>2. App基础框架搭建完成，界面可交互。 |
| **Week 4** | 具备将多个基础功能整合并打包成一个可发布版本应用的能力。 | 1. **(冲刺)** 完成V1.0版本（含日程、便签等）的最终测试与打包。<br>2. **(冲刺)** 务必在本周内将App V1.0提交至苹果和华为应用商店审核。 | 1. 一个功能完整的V1.0版本。<br>2. **KPI**: 应用商店显示"正在审核中"的状态截图。 |

---

### **第二阶段：快速周迭代，交付核心价值 (2025年7月)**

本月进入"闪电战"模式，目标是快速迭代，每周上线一个新功能或显著优化，验证产品价值，提升用户粘性。

| 周数 | 关键假设 (Key Assumption) | 核心任务 (Core Task) | 交付物/KPI (Deliverable/KPI) |
| :--- | :--- | :--- | :--- |
| **Week 1** | "安全"是用户的核心痛点，一个极致安全的拍照功能可以成为首个引爆点。 | **上线：安全会议模式V1**<br>（核心：会场大屏安全拍照，照片加密存储）。 | 1. V1.1版本上线，用户可使用该功能。<br>2. 获得首批种子用户对该功能的反馈。 |
| **Week 2** | 基础功能的便捷性是用户留存的基石，必须持续打磨。 | **上线：日程体验重构**<br>（核心：优化日程创建、视图切换、提醒等交互流程）。 | 1. V1.2版本上线，日程模块用户体验大幅提升。<br>2. **KPI**: 核心用户满意度调研，日程满意度>90%。 |
| **Week 3** | 在安全的基础上提供AI能力，能进一步增强产品吸引力。 | **上线：智能语音笔记**<br>（核心：基于本地STT引擎，实现离线语音转文字笔记）。 | 1. V1.3版本上线，用户可使用离线语音笔记。<br>2. 验证STT引擎在真实使用场景下的性能和准确率。 |
| **Week 4** | "私密保险箱"是"安全"价值主张的自然延伸，可以为后续付费转化铺路。 | **上线：私密保险箱V1**<br>（核心：提供一个独立加密空间，用于存放证件照、密码等敏感信息）。 | 1. V1.4版本上线，用户数据安全感进一步增强。<br>2. 为8月份的Pro付费版打包好一个核心功能。 |

---

### **第三阶段：场景深耕：端到端安全会议解决方案 (2025年8月-9月)**

我们将围绕"安全会议"场景，深挖"会前、会中、会后"全流程的用户痛点，将日程、资料、笔记等基础模块与会议场景深度融合，打造极致的、有付费价值的解决方案。

| 时间     | 场景阶段 | 关键假设 (Key Assumption) | 核心任务 (Core Task) | 交付物/KPI (Deliverable/KPI) |
| :--- | :--- | :--- | :--- | :--- |
| **8月 W1-2** | 用户在同一任务中的角色（主办/协办/参与）不同，其关注点和行动也应完全不同。 | **上线：AI角色识别引擎 V1**<br>（核心：从截图、照片或文本中，初步识别当前用户是主办、协办还是参与者）。 | 1. V1.5版本上线。<br>2. **KPI**: 对核心种子用户的测试样本，角色识别准确率 > 70%。 |
| **8月 W3-4** | 真正的智能助理应能根据用户角色，提供差异化的服务，而不是千篇一律的提醒。 | **上线：差异化任务视图**<br>（核心：主办任务突出步骤和DDL，协办任务突出协作边界，参与任务简化为提醒）。 | 1. V1.6版本上线。<br>2. **KPI**: 核心用户访谈，确认差异化视图的实用性。 |
| **9月 W1-2** | 产品的"大脑"在于理解任务间的逻辑关系，并能根据变化进行推演。 | **上线：任务依赖关系构建**<br>（核心：允许用户手动建立任务间的父子、前后置依赖，为动态重排打下基础）。 | 1. V1.7版本上线。<br>2. **KPI**: X%的新建任务建立了依赖关系。 |
| **9月 W3-4** | "动态重排"是产品智能化的核心体现，是解决用户"救火"痛点的关键。 | **上线：任务动态重排引擎 V1**<br>（核心：当一个父任务或前置任务的时间变更时，自动建议所有关联任务的时间调整方案）。 | 1. V1.8版本上线。<br>2. **KPI**: 在演示中，成功展示会议从周三改到周五后，系统自动提出一系列调整建议的场景。 |

---

### **第四阶段：商业验证与持续增长 (2025年10月 - 12月)**

-   **目标**: 基于早期付费用户的反馈，快速迭代产品，扩大用户基础。
-   **关键成果**:
    1.  根据付费用户对"端到端会议方案"的反馈，持续迭代优化。
    2.  新增1-2个用户高频需要、且符合"U盘级私密"理念的付费功能点。
    3.  通过口碑传播和精准渠道投放，实现用户稳定增长。

## 4. 风险与对策

-   **技术风险**: 本地STT引擎的识别准确率或性能不达标。
    -   **对策**: 1) 优先选择有成熟商业案例的离线SDK。 2) 设计混合模式，允许用户在网络条件好且不介意隐私的情况下，自主选择使用云端引擎以获取更高精度。
-   **市场风险**: 核心用户群的付费意愿低于预期。
    -   **对策**: 1) 在付费前提供限定次数或时长的免费试用。 2) 强化品牌宣传，持续向用户传达数据安全和便捷操作的核心价值。
-   **执行风险**: 单人开发导致进度延迟。
    -   **对策**: 1) 严格遵守周迭代计划，每个周期聚焦单一核心目标。 2) 积极寻求外部技术顾问或短期外包资源，解决特定技术瓶颈。 