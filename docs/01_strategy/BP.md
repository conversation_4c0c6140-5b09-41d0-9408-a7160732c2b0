# 公职猫商业计划书

*最后更新于：2025-06-13*

---

## **一、项目信息**

### **项目名称**
> 公职猫（全国首款AI公职总助）

### **一句话概括**
> 帮干部沉淀个人数据资产的私密助理。

### **项目描述**
> “公职猫”是一款帮中青年干部在信息过载时代，轻松驾驭信息、沉淀个人数据资产的私密助理。
> 
> 如今，每个人都成了信息的“仓鼠”，特别是身处复杂工作环境的中青年干部，每天面对来自微信群、纸质文件、会议和口头指令的海量信息，认知负担不堪重负。我们认为，真正的AI助理不应是另一个需要学习的工具，而应是默契的秘书。
> 
> “公职猫”构建了“**入口-思考-执行**”的全链路智能：
> *   **入口的“最先一公里”**：它能自动识别领导口头交办、解析会议通知、提取微信截图内容，把原本分散在各处的信息，智能整合为清晰、可执行的任务，解决“记得全”的问题。
> *   **思考的“智能中枢”**：它不仅仅是记录，更能像秘书一样思考和编排。例如，当一个会议从周三改到周五，它会自动推演并调整所有后续关联事项的时间节点。这是产品的**大脑**，解决“想得透”的问题。
> *   **执行的“最后一公里”**：它将逐步具备调用工具的能力，在获得您授权后，能完成订票、预定会议室等具体操作，真正闭环任务，解决“办得完”的问题。
> 
> 最终，我们帮助用户构建一个安全、私密、可靠的“**岗位数字分身**”。这个分身不仅能处理当下的繁杂事务，更能将日复一日的工作沉淀为可复用、可检索、可洞察的**个人数据资产**，在关键时刻提供决策支持，真正实现“**U盘级私密，秘书般懂你**”。

---

## **二、想法与洞察**

### **你的用户是谁？用户场景是什么？**
> 我们服务的核心用户，是体制内的中青年干部，尤其是那些集管理与执行于一身，没有秘书却要扛起所有事的“**实干家**”。
> 
> **他们的核心痛点，不是信息太多记不住，而是任务之间关联太强、变动太频繁，导致时间安排永远在“救火”。**
> 
> 一个典型的场景：周三下午的汇报会，突然改到周五上午。对于普通日历App，这只是一个简单的修改。但对于一个真实的干部，这意味着：
> *   原定周三上午的会前沟通要改期。
> *   给领导准备材料的截止时间要重新计算。
> *   周四下午的部门评审可能要提前。
> *   周五上午原本安排的另一项工作需要协调。
> 
> 传统工具只能“记”，无法“算”出这些连锁反应，最终只能靠人脑和笔记本，导致“任务容易断、材料找不到、提醒靠自觉”。
> 
> **“公职猫”的核心，就是解决这个“动态重排”的难题。** 我们通过AI自动识别非结构化信息，不仅能拆解出单点的任务、日程，更能构建任务间的逻辑关系。当一个节点变动，它能像一个经验丰富的秘书一样，自动推演出整个任务链的调整方案，帮你“对齐时间、理清任务、把事做完”。
> 
> 所以，我们解决的不是简单的“提醒”问题，而是“**时间资产的智能调度**”问题。我们让用户从繁杂的日程调整中解放出来，真正提升个人执行力，沉淀长期**数据资产**，从容迈入智能体时代。

### **你为什么会选这个想法来做项目？**
> 这个项目的灵感，来自我十年服务体制内用户过程中，反复观察并确认的一个核心“差异”：**所有系统都在服务“组织”，而没有人真正在服务“人”**。
>
> 尤其是一线的中青年干部，他们是“干活”的中坚，却没有可用的助手，工具反而成为额外负担。我们在落地杭州人大AI助理项目时更深切体会到：单位要的是创新亮点和政绩汇报，而一线用户要的是一个能帮他“扛事”的伙伴。
>
> 这让我深刻认识到，**技术背后是人性**。我们不能只追求技术最牛，更要追求体验最爽；不能只提供冰冷的功能，更要提供温暖的情绪价值。所以，我们不是靠“做关系”起项目，而是坚持靠产品解决真实问题，做一个真正懂用户、有温度的“**伙伴**”。
>
> 我们看到，全国的信息化程度参差不齐，AI的机会不在于替换一代界面，而在于构建“真正可协同、能记忆、懂上下文”的**个人智能体**。它将陪伴用户高效完成事务，并将这些宝贵的经历沉淀为**个人数据资产**。这不仅是工作的记录，更是个人成长、决策和未来发展的基石。
>
> 我相信，我具备实现这个愿景所需的一切：技术和算法背景，连续创业经验，熟悉体制内全链条业务场景，能组建一支既懂人又懂产品、能做事的团队。在智能体时代到来之际，这将是最早可验证、可落地、可规模化的应用突破口。

### **你的项目有什么特别的地方？**
> 市面上的效率工具，如钉钉、飞书或各类日历App，本质上都是**“记录工具”**，它们能记下“你告诉它”的事情。但“公职猫”的核心区别在于，它是一个**“思考伙伴”**，能理解“你没说但需要做”的事情。
>
> 我们的独特性和护城河体现在三个层面：
>
> 1.  **从“记录”到“理解”的进化**：传统工具解决了“记下来”的问题，而我们通过“任务理解+动态重排”能力，解决了“接下来该怎么办”的问题。这不仅是效率的提升，更是从“辅助工具”到“智能助理”的质变。
>
> 2.  **“U盘级私密”构筑的信任壁垒**：我们的用户处理大量敏感信息，数据安全是他们的第一生命线。所有云端SaaS工具在“信任”上都有天然的软肋。而我们“端侧本地化优先”的架构，提供了物理级别的安全感，这是竞品无法模仿的核心优势，也是我们能服务这群用户的唯一前提。
>
> 3.  **从“数据沉淀”到“资产活用”的闭环**：我们不仅帮助用户实现对“时间资产”的**智能调度**，更核心的是，帮助他们安全地**沉淀**“数据资产”，并让这些资产真正“活”起来。通过Graph-RAG等技术，这些沉淀的经验将在未来的决策中被智能调用，反哺于用户，形成一个越用越懂你、越用越增值的正向飞轮。
>
> 因此，用户现在的替代方案是“记忆力+笔记本+一堆App的组合”，而我们提供的是一个All-in-One的、可信赖的、能与他们共同成长的“**岗位数字分身**”。

### **你的反共识观点是什么？**
> 在这个行业，最大的认知误区，就是认为体制内市场做不了ToC。而我坚信一个多数人不同意的观点：
>
> **公职人员，将是中国最早、最稳定、付费意愿最强的个人智能体付费用户群体。**
>
> 所有人看到的，都是他们“不花自己的钱”；而我看到的，是他们背后强大的“**制度性消费能力**”——公家预算、饭卡余额、单位集采、工会福利。他们不是没有付费意愿，只是付费的“通道”不同。一旦你读懂这套消费机制和使用逻辑，一个巨大的蓝海市场就豁然开朗。
>
> 这个群体拥有最理想的用户画像：
> *   **极强的任务刚需**：办文、办会、办事，每一项都与责任挂钩。
> *   **极高的组织粘性**：工具会在一个单位内快速循环使用，形成口碑效应。
> *   **极深的隐性知识**：他们的工作经验和数据难以被通用SaaS复用，构成了天然的护城河。
>
> 因此，当我们还在把他们当成“难搞定”的非标人群时，我们错过的，不仅是中国政务数字化转型的黄金窗口，更是打造“**职业智能体**”这个万亿级赛道的最佳滩头阵地。

### **为什么现在是最好时机？**
> 我们正处在一个百年未有之技术大变革的“**黄金窗口期**”，三大趋势的交汇，让“公职猫”的出现正逢其时：
>
> 1.  **AI的“平权时代”已经到来**：大模型技术正以前所未有的速度从云端走向边缘端。端侧AI的成熟，使得过去必须依赖强大服务器的能力，如今可以在小小的手机上实现。这为我们打造“U盘级私密”的个人智能体，提供了坚实的技术基石。
>
> 2.  **“信任赤字”呼唤隐私回归**：用户对将个人数据无条件上传到云端的警惕性日益增高。一个能将数据牢牢掌握在自己手中的、物理隔离的解决方案，不再是“小众需求”，而是即将爆发的“主流共识”。我们顺应了这一趋势，而不是在旧的云SaaS模式上修修补补。
>
> 3.  **“个人智能体”的范式革命**：我们正站在从“云计算”向“个人计算”+“个人智能体”范式转移的临界点。谁能率先为最有价值的用户群体打造出最可信赖的个人智能体，谁就能定义下一个十年的游戏规则。
>
> 简而言之，技术、市场和用户需求三者已经完美对齐。现在不是要不要做的问题，而是谁能率先做出来、做得最好的问题。时机就在眼前。

---

## **三、项目进展与规划**

### **近三个月进展**
> *   **公司设立**：项目已正式落地杭州市上城区，完成公司主体设立，并获得地方政府初步支持。
> *   **产品研发**：明确了“本地优先、安全可控”的端云协同技术路线，完成日程识别引擎与原型交互设计，已进入App开发阶段。
> *   **技术验证**：测试了本地小模型的可行性，对本地小模型做场景微调，初步实现了部分日程类任务离线识别。
> *   **用户获取**：已有10余位来自地级市、区县、乡镇街道的人大真实用户愿意加入首批试测。

### **技术架构**
> 我们以“**任务理解 + 数据安全**”为核心，构建端云协同的政务智能体架构，攻克日程识别、文档数字化与数据保护等高门槛难题。核心技术包括：
> *   **定制化日程识别引擎**：支持模糊表达的自动拆解，精准识别时间、地点、责任人、流程依赖及关联资料。
> *   **手写批注识别模块**：结合OCR与视觉模型微调，打通从线下到数字系统的最后一环。
> *   **端云协同的数据安全架构**：核心数据本地存储、敏感内容脱敏、嵌套加密，保障隐私前提下提供高质量智能服务。
> *   **Graph-RAG 知识增强生成引擎**：构建“岗位-任务-语境”融合图谱，使大模型具备更真实的上下文记忆与任务理解能力。
> *   **智能体执行系统**：实现外部工具与数据的智能调用，构建“可记忆、可调用、可闭环”的个人智能体执行力机制。

### **收入模式**
> *   **模式**：采用“基础免费 + 增值订阅”的方式。
> *   **近期目标**：预计覆盖100–200万用户，人均300-500元/年，年收入3–10亿元。
> *   **中期展望**：推动“体制内跨区域数据赋能”，年收入3–6亿元。
> *   **长期愿景**：成为“体制内智能体基础设施”关键平台，接口调用收入预估10–20亿元。

---

## **四、团队与法务**

### **团队介绍**
> *   **钱云华 (CEO/产品负责人)**：10年+政务SaaS行业经验，原鸿正数字科技总经理，主导服务全国600余家人大单位，具备从0到1、从1到N的完整经验。
> *   **何振宇 (联合创始人)**：原公司同事，多年合作伙伴。
> *   **张伟 (架构师/技术专家)**：专注于政务语境下的知识库与AI算法攻坚。

### **股权结构**
> *   钱云华：72%
> *   何振宇：5%
> *   张伟：3%
> *   期权池：20%

### **公司信息**
> *   **办公地点**：杭州
> *   **法律主体**：已注册成立
> *   **融资历史**：暂无

---
*本文件由AI助手协助整理与优化。*