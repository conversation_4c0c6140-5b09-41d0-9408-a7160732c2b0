# 项目文档中心

本文档库是公职猫项目所有设计、规划和决策的中央知识库。

## 目录结构

- **[01_strategy](./01_strategy/)**: 项目战略与商业规划
  - `BP.md`: 商业计划书，定义了项目愿景、目标和商业模式。
  - `项目评审报告.md`: 项目立项时的评审记录和结论。

- **[02_requirements](./02_requirements/)**: 需求文档
  - `master_prd.md`: 项目主产品需求文档，综合了战略和具体需求。
  - `prd.md`: 更详细的MVP产品需求文档。

- **[03_design](./03_design/)**: 界面与UX设计
  - 存放UI设计稿、原型链接和用户体验相关的文档。

- **[04_architecture](./04_architecture/)**: 技术架构与方案
  - 存放项目的架构图、技术选型决策和关键技术方案。

- **[05_guides](./05_guides/)**: 开发指南与流程
  - `developer-accounts.md`: 开发者账号配置信息。
  - `development-strategy.md`: AI驱动的开发策略和模式。
  - `release-workflow.md`: 周迭代发布流程和规范。
  - `design-system-guide.md`: 跨终端设计系统指导原则。

---
*请在修改或添加文档时，同步更新此索引文件。* 