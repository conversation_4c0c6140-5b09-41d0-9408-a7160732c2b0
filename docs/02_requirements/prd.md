# 公职猫 – 岗位数字分身 MVP 产品需求文档（PRD）

## 0. 产品本质重新定义

### 0.1 产品定位升级
**公职猫不是写作工具，不是传统的任务管理软件，而是全国首款"岗位数字分身"——智能体生态的底座。**

**核心理念：U盘般私密，秘书般懂你**

我们正在打造全国首款服务体制内中青年干部的个人 AI 总助——公职猫。
它不是写作工具，是能像秘书一样，替你记、替你想、甚至替你做事的数字智能体。
你只需一句话，它能识别任务，补足背景，完成任务、设置提醒，并在真正该干的时候提醒你"要不要我帮你？"
所有数据本地存储、安全可控。你的一次记录，就是个人数据资产的一次沉淀。
在这个每件事都有时限、每个通知都不能忘的岗位上，公职猫是你能信的那一个。
我们想用它，重新定义体制内的高效与信任。

### 0.2 核心价值主张："U盘般私密，秘书般懂你"
- **U盘般私密**：
  - **数据主权归用户所有**：个人隐私数据100%本地存储，永不上云
  - **跨端传输自由**：支持移动端、桌面端数据本地同步，可通过U盘等传统方式传输
  - **完全可控**：用户可随时导出数据，即使服务停止也能正常使用
  - **安全可信**：基础加密保护，严格遵循《个人信息保护法》标准

- **秘书般懂你**：
  - **替你记**：智能解析各类通知、会议、文件，自动提取关键信息
  - **替你想**：基于上下文和历史数据，主动推理和补足任务背景  
  - **替你做事**：生成完整任务链，设置智能提醒，在关键时刻主动询问"要不要我帮你？"
  - **真正懂你**：本地存储个人数据，AI学习工作习惯，越用越懂你

### 0.3 解决的根本问题
基于十年政务系统行业经验和上千家单位的服务洞察，我们发现体制内数字化的根本矛盾：
**系统越建越多，真正的执行还是靠人记、手抄、截图、靠责任心扛。**

比如一个会议通知，里面藏着五个要办的事，没有人提醒你，"下午4点材料必须送过去"。
公职猫要解决的是执行层面的智能化，让每个中青年干部都有一个像U盘一样私密、像秘书一样懂你的数字分身。

### 0.4 业务闭环完整描述
1. **信息接收**：会议通知、工作安排、政策文件等
2. **智能解析**：识别其中的多个隐含任务（如"下午4点材料必须送过去"）
3. **任务生成**：自动生成可闭环的任务节点
4. **背景补足**：基于历史数据和上下文推理
5. **执行提醒**：在合适的时间主动提醒和协助
6. **数据沉淀**：每次记录都是个人数据资产的积累

### 0.5 任务统一模型
- **日程** = 已明确日期的任务
- **便签** = 灵活时间的任务
- **提醒** = 带时间触发的任务
- 所有形态本质上都是"任务"的不同表现形式

### 0.6 战略定位
这是未来智能体生态在体制内的突破口，也是个人数据资产价值化的起点。
我们不是做工具，不是做写作，我们是在做一个"岗位数字分身"。
这是未来智能体生态的底座。真正能替你想、替你干、替你记的搭档。

## 1. 项目概述
公职猫是全国首款专为中国大陆地区体制内中青年干部设计的个人AI助理，通过"懂语境、会拆解、能执行"的智能体能力，帮助用户高效完成"办文、办会、办事"中的繁杂事务。

**核心理念：U盘般私密，秘书般懂你**

* **产品定位**：体制内专用的任务型AI助理
* **核心价值**：从碎片信息中识别任务链，提供闭环执行支持
* **技术特色**：本地为主架构，个人隐私数据100%本地存储
* **安全承诺**：个人隐私数据永不上云，可随时导出（U盘般私密）
* **智能承诺**：本地存储个人数据，AI真正"懂你"（秘书般懂你）
* **隐私合规**：严格遵循《个人信息保护法》标准，云端仅收集经过差分隐私处理的匿名统计数据（设备类型、地区分布至区县级、功能使用频次等），采用数据脱敏、本地聚合、延迟上报等技术确保无法关联到个人身份，用户可完全控制数据收集策略并随时关闭
* **MVP目标**：1个月内实现核心功能，验证"AI真的懂体制内工作"

## 2. 目标用户与核心场景
**主要用户**：体制内中青年干部（综合岗科员、办公室主任、项目秘书等）

**核心场景**：
- 领导口头交办："小王，这个材料下周三前搞定，记得先给老张看看"
- 微信群通知："明天上午9点会议室开会，带上预算方案"
- 会议纪要截图：手写批注的任务安排
- 纸质文件：领导圈改的文件和批示
- 领导日程导入：年度计划、月度安排、周计划等Word/Excel文件
- 重要资料管理：身份证、证件照、简历、证书等个人档案

## 3. MVP 核心功能（P0 - 必须有）

### 3.1 智能信息采集
- **微信截图识别**：专门优化群通知、会议安排的OCR识别
- **语音指令理解**：识别领导口头交办的模糊表达
- **纸质文件拍照**：识别手写批注、圈改内容
- **文字输入**：直接输入任务信息

### 3.2 文档导入与检索（U盘级存取）
- **多格式文档导入**：
  - 支持Word、Excel、PDF、图片等格式
  - 领导日程文件智能识别（年度计划、月度安排、周计划）
  - 批量导入和自动分类
- **全文检索功能**：
  - 本地全文搜索引擎
  - 支持关键词、时间、类型等多维度检索
  - 快速定位相关文档和任务
- **文档预览管理**：
  - 支持各种格式文档预览
  - 文档标签和分类管理
  - 版本历史记录

### 3.3 领导日程识别与管理
- **日程文件解析**：
  - 自动识别Word/Excel中的时间、地点、事项
  - 提取会议安排、重要节点、截止时间
  - 识别周期性安排和重复事项
- **提前规划支持**：
  - 支持年度、季度、月度、周度计划导入
  - 自动生成时间提醒和准备清单
  - 关联相关材料和历史记录

### 3.4 个人重要资料管理
- **证件档案管理**：
  - 身份证正反面存储和OCR识别
  - 证件照片分类管理（一寸、二寸、免冠等）
  - 各类证书扫描和归档（学历、职业资格等）
- **证件照处理功能**：
  - 离线人像抠图和背景替换（白色、红色、蓝色背景）
  - 标准尺寸导出（1寸、2寸、护照照片等）
  - 基础美化功能（可选，V2版本）
  - 技术方案：MediaPipe Selfie Segmentation + Canvas API
- **个人档案维护**：
  - 个人信息快速调用
  - 敏感信息加密保护
- **快速调用功能**：
  - 证件照片快速导出


### 3.5 角色智能识别与事务管理（核心差异化）
- **语境理解**：理解体制内的职级关系、流程规范、时间表达习惯
- **角色智能判断**：通过AI分析用户在具体事务中的角色和职责
  - **语言线索分析**："你来负责"→主办、"配合XX"→协办、"参加"→参与
  - **职务关系分析**：根据部门职能、层级关系、专业领域判断职责
  - **历史行为学习**：分析过往角色、工作模式、协作关系
- **差异化管理**：同一事务，不同角色，不同管理方式
  - **主办事务**：详细步骤拆解、关键节点提醒、协调安排、风险预警
  - **协办事务**：明确配合职责、沟通安排、材料清单、时间节点
  - **参与安排**：简单提醒、基本准备、议程预览、后续行动
- **系统能力沉淀**：工作模式库、角色职责模板、智能学习机制

### 3.6 本地安全存储（U盘般私密）
- **个人数据本地存储**：所有个人隐私数据仅存储在本地设备，实现"U盘般私密"
- **AI个性化支持**：本地存储用户偏好、工作习惯等数据，实现AI"秘书般懂你"效果
- **跨端数据传输**：支持移动端和桌面端数据本地同步，可通过U盘等传统方式传输
- **匿名数据上报**：仅上报设备类型、地区、使用统计等匿名信息
- **基础加密**：本地数据库加密保护
- **数据导出**：支持标准格式导出，可通过U盘等传统方式传输
- **永久可用**：即使服务停止，用户数据仍可正常使用

### 3.7 日程事务管理
- **分类管理**：区分显示简单日程和复杂事务
- **进度跟踪**：
  - 简单日程：未开始、进行中、已完成
  - 复杂事务：办理步骤进度、关键节点提醒
- **时间管理**：
  - 智能提醒：基于截止期限和办理步骤的提醒
  - 相关安排：自动关联配套的会议、沟通等日程
- **工作习惯适配**：使用体制内熟悉的术语和流程

## 4. 技术要求

### 4.1 平台支持
- **移动端**：React Native开发，支持iOS和Android
- **桌面端**：Electron开发，支持Windows、统信UOS
- **数据同步**：移动端和桌面端数据本地同步
- **离线优先**：核心功能可完全离线使用
- **响应性能**：应用启动时间<3秒，功能响应时间<2秒

### 4.2 安全要求
- **个人数据本地化**：用户个人隐私数据100%本地存储
- **隐私合规**：严格遵循《个人信息保护法》标准
- **基础加密**：SQLite数据库加密
- **身份验证**：设备锁屏保护即可
- **传输安全**：HTTPS加密通信
- **详细技术方案**：参见《隐私保护与数据匿名化技术方案》

### 4.3 功能要求
- **OCR识别**：使用平台原生OCR能力
- **语音识别**：使用平台原生语音识别
- **文档处理**：支持Word、Excel、PDF解析
- **AI理解**：混合架构确保离线可用
  - **云端AI优先**：网络可用时调用云端大模型API进行任务理解
  - **本地AI回退**：集成轻量级本地AI模型（如量化版Qwen或类似模型）作为离线回退
  - **规则引擎兜底**：基于关键词匹配和模式识别的规则引擎，确保基础功能离线可用
  - **请求队列机制**：网络恢复时自动同步离线期间的AI请求，优化结果
  - **渐进式降级**：云端AI（最佳效果）→ 本地AI（良好效果）→ 规则引擎（基础效果）
- **本地存储**：SQLite数据库 + 文件系统

## 5. MVP实现策略

### 5.1 功能优先级
- **第一阶段（2周）**：
  - 移动端基础界面和本地存储
  - 文档导入和预览功能
  - 个人重要资料管理
- **第二阶段（2周）**：
  - 桌面端基础功能
  - 微信截图OCR识别
  - 领导日程文件解析
  - 任务拆解和AI集成

### 5.2 成本控制
- **开发成本**：使用React Native（移动端）和Electron（桌面端）及开源组件
- **云端成本**：仅调用AI API，按需付费
- **运营成本**：最小化服务器资源

### 5.3 技术选型
- **移动端框架**：React Native
- **桌面端框架**：Electron + React
- **本地存储**：SQLite + 基础加密
- **文档处理**：react-native-document-picker + 解析库（移动端）/ 原生文件API（桌面端）
- **OCR**：平台原生能力
- **AI服务**：混合架构
  - **云端AI**：阿里云百炼或腾讯云混元API（主要选择）
  - **本地AI**：ONNX Runtime + 量化模型（离线回退）
  - **规则引擎**：自研关键词匹配和模式识别系统（基础保障）

## 6. 成功指标

### 6.1 核心验证指标
- [ ] 能正确导入和检索Word/Excel日程文件（准确率>60%）
- [ ] 能正确识别微信截图中的任务信息（准确率>60%）
- [ ] 能正确识别和分类体制内的日程事务（准确率>60%）
- [ ] 个人重要资料管理功能完整可用
- [ ] 5-10名种子用户认为"有用且安全"（满意度>4.0/5.0）
- [ ] 7天留存率>50%（新增留存指标）

### 6.2 技术指标
- [ ] 文档导入和预览功能稳定
- [ ] 微信截图OCR识别准确率>60%
- [ ] 云端AI响应时间<5秒，本地AI响应时间<10秒
- [ ] 离线模式下基础AI功能可用（规则引擎准确率>40%）
- [ ] 网络恢复后请求队列自动同步成功率>95%
- [ ] 应用启动时间<3秒
- [ ] 数据100%本地存储

### 6.3 用户体验指标
- [ ] 完成5-10名体制内种子用户测试
- [ ] 用户能成功导入和管理日程文件
- [ ] 用户能有效管理个人重要资料
- [ ] 用户认为产品"安全可信"
- [ ] 用户愿意继续使用

---

**核心理念**：公职猫不是一个通用的任务管理工具，而是一个真正理解体制内工作语境的专业AI助理。我们的成功标准是让用户感受到"这个AI真的懂我的工作，而且很安全"。

**MVP承诺**：1个月内实现核心功能，用户数据100%本地存储，真正做到"U盘般私密，秘书般懂你"。

---
*文档版本：v4.2 MVP精简版  |  创建日期：2025-01-14  |  更新日期：2025-01-14* 