# 数据同步方案 V6.0 (最终版)

## 1. 核心设计哲学

本方案旨在实现公职猫产品"U盘级私密"的核心承诺，并为用户提供在任何场景下都极致可靠的数据同步体验。所有设计都严格遵循以下原则：

- **数据主权**：用户数据100%存储于用户自己的设备中，同步过程为端到端加密，任何第三方（包括我们）无法窥探。
- **服务独立性**：产品的核心同步功能不应强制依赖我们中心化的数据服务器。
- **场景全覆盖**：方案必须能应对用户真实、复杂的所有网络环境和物理场景，提供分层、多模式的解决方案。
- **体验可预期**：用户能清晰地理解当前的连接状态，并能自主选择最适合的数据同步方式。

## 2. 总体架构：分层多模式同步系统

为应对复杂多变的现实场景，我们采用一个分层的、多模式的同步架构。系统会优先尝试更自动、更无缝的同步方式，并始终为用户提供保证数据可迁移的最终选择。

---

## 3. 第一层：局域网（LAN）同步

> **核心场景**: 用户正在开会，电脑上正在演示PPT，同时希望用手机作为"遥-控器"，快速查找并展示某个关键文件或笔记，而不需要中断演示。

此层级为最高效、最低延迟的模式，适用于用户的多个设备处于同一网络环境时。

- **模式1.1 (首选): Wi-Fi 自动发现**
  - **技术实现**: mDNS/Bonjour (`_gongzhimall._tcp`)
  - **用户体验**: 用户在手机和电脑上打开公职猫App后，设备将自动在局域网内发现对方并提示连接，用户一键确认即可完成配对和同步，过程完全自动化。

- **模式1.2 (备用): Wi-Fi 扫码直连**
  - **技术实现**: 在发起端生成包含其内网IP地址和端口的二维码。
  - **用户体验**: 当mDNS因网络策略被禁用或失败时，用户可在电脑端显示一个二维码，手机端通过"扫一扫"功能，直接建立TCP连接，完成配对和同步。

---

## 4. 第二层：跨网络（WAN）同步

> **核心场景**: 白天，用户在办公室的电脑上整理了大量的工作笔记。晚上回到家，希望能立刻在自己的手机或家用电脑上，无缝地查阅、编辑这些资料，为第二天的工作做准备。此场景的技术挑战在于办公室（可能是政务网）和家庭网络完全隔离，且用户没有公网IP。

此层级为解决办公室、家庭等不同网络环境间数据同步的核心。

- **模式2.1 (优选): 自动P2P同步 (WebRTC)**
  - **定位**: 跨网络环境下首选的、智能的、自动化的同步方案。
  - **技术实现**: 
    1. **无服务器信令**: 发起端（如办公室电脑）通过WebRTC生成连接"要约(Offer)"，并将其呈现实时**二维码**。接收端（如手机）扫描此二维码即可交换初始连接信息，全程无需中心信令服务器。
    2. **高可靠连接**: 我们将**自行部署和维护STUN和TURN服务器**。设备在扫码后，会优先利用STUN服务器尝试进行NAT穿透，建立直接的P2P连接。如果直连失败，流量将自动、无缝地通过我们的TURN服务器进行加密中继，从而**保证连接成功率接近100%**。
  - **用户体验**: 用户只需一次扫码，即可在不同网络的设备间建立一条"看不见"的加密同步通道，后续数据自动保持同步，体验流畅、无感。

- **模式2.2 (保证可达): 手动数据迁移**
  - **定位**: **并列的可选方案**，以及在任何意外情况下的**终极保障方案**。
  - **技术实现**: 
    1. App内提供"导出数据"功能，将全量或增量数据打包成一个**加密的 `.zip` 文件**。
    2. 调用操作系统的原生"分享/发送到"功能。
  - **用户体验**: 用户可以随时主动将数据包通过任何他喜欢的方式发送出去，例如：
    - **通过微信、QQ等**发送到自己的另一台设备。
    - **通过电子邮件**发送给自己。
    - **保存到U盘**进行物理转移。
    - 在接收设备上，通过公职猫App打开此文件包，即可完成数据导入。此方案给予用户最大的自主权和安全感。

---

## 5. 第三层：离线与近场同步

> **核心场景**: 用户在外出差，或在没有Wi-Fi的会议室，需要紧急将手机上的一个重要文件同步到电脑上进行编辑。此场景的技术挑战在于没有任何可用的网络连接。

此层级覆盖无网络或不便使用网络的极端场景。

- **模式3.1: 蓝牙同步**
  - **技术实现**: `react-native-bluetooth-serial`, `noble`等。
  - **用户体验**: 在完全没有Wi-Fi网络时，用户可通过蓝牙进行设备配对和少量关键数据的同步。

- **模式3.2: USB数据线同步**
  - **技术实现**: ADB (Android) / iProxy (iOS)。
  - **用户体验**: 通过数据线连接电脑和手机，可建立最稳定、最高速的物理连接通道，用于首次全量数据迁移或大规模数据同步。

## 6. 总结

V6.0方案通过一个**全面的、层层递进、互为补充**的策略，构建了一套极致可靠、安全、且覆盖所有用户场景的数据同步架构。它既提供了以WebRTC为核心的先进自动化同步体验，又保留了用户完全可控的手动迁移模式作为最终保障，完美兑现了"U盘级私密，秘书般懂你"的产品承诺。

---
*文档版本：v6.0 精简版  |  更新日期：2025-01-14* 