# 跨平台Assets配置指南

## 📋 概述

本指南介绍公职猫应用的跨平台Assets文件管理机制，确保Android和iOS平台都能正确访问文件预览所需的资源文件。

## 🎯 设计目标

- **统一数据源**：所有Assets文件存储在`mobile/src/assets/`
- **构建时打包**：两个平台都在构建时自动处理Assets文件
- **运行时访问**：提供统一的文件访问接口
- **维护简单**：开发者只需维护一份Assets文件

## 🏗️ 架构设计

### 文件组织结构
```
mobile/src/assets/
├── ofd-viewer.html          # OFD文档预览模板
├── docx-viewer.html         # Word文档预览模板  
├── office-viewer.html       # Excel文档预览模板
├── pptx-viewer.html         # PPT文档预览模板（已弃用）
└── libs/                    # JavaScript库文件
    ├── jszip.min.js         # 文件解压缩
    ├── opentype.min.js      # 字体处理
    ├── x2js.js              # XML解析
    ├── easyjbig2.js         # OFD图像支持
    ├── easyofd.js           # OFD核心库
    └── docx-preview.min.js  # Word预览库
```

### 平台差异对比

| 方面 | Android | iOS | 说明 |
|------|---------|-----|------|
| **数据源** | `mobile/src/assets/` | `mobile/src/assets/` | 统一数据源 |
| **构建处理** | Gradle自动打包 | Xcode脚本复制 | 不同机制，相同效果 |
| **最终位置** | APK内assets目录 | App Bundle内assets目录 | 都在应用包内 |
| **访问方式** | `RNFS.readFileAssets()` | `RNFS.readFile()` | API不同 |
| **路径格式** | `'libs/jszip.min.js'` | `'${MainBundlePath}/assets/libs/jszip.min.js'` | 路径格式不同 |

## 🤖 Android平台配置

### 工作机制
Android使用Gradle的自动打包机制：

1. **源文件位置**：`mobile/src/assets/`
2. **构建时处理**：Gradle自动将assets目录打包到APK
3. **运行时访问**：通过`RNFS.readFileAssets()`直接访问

### 配置要求
- ✅ **无需额外配置**：Gradle默认处理assets目录
- ✅ **自动打包**：构建APK时自动包含所有assets文件
- ✅ **版本控制友好**：只需维护源文件

### 访问代码示例
```typescript
// Android平台访问assets文件
const content = await RNFS.readFileAssets('ofd-viewer.html', 'utf8');
const libContent = await RNFS.readFileAssets('libs/jszip.min.js', 'utf8');
```

### 验证方法
```typescript
import { runAndroidAssetsTest } from '../tests/androidAssetsTest';

// 运行测试验证assets访问
const result = await runAndroidAssetsTest();
console.log('Android Assets测试结果:', result.success);
```

## 📱 iOS平台配置

### 工作机制
iOS使用构建时脚本复制机制：

1. **源文件位置**：`mobile/src/assets/`
2. **构建时处理**：`copy_assets.sh`脚本复制文件到App Bundle
3. **运行时访问**：通过`RNFS.readFile()`访问Bundle内文件

### 配置步骤

#### 1. 确认构建脚本
确保`mobile/ios/copy_assets.sh`存在且可执行：
```bash
chmod +x mobile/ios/copy_assets.sh
```

#### 2. Xcode项目配置
在Xcode中添加Run Script Phase：
1. 打开`GongZhiMallMobile.xcodeproj`
2. 选择Target → Build Phases
3. 添加"New Run Script Phase"
4. 脚本内容：`"${SRCROOT}/copy_assets.sh"`
5. 确保脚本在"Copy Bundle Resources"之前执行

#### 3. 验证配置
构建项目后检查文件是否存在：
```
GongZhiMallMobile.app/assets/
├── ofd-viewer.html
├── docx-viewer.html
├── office-viewer.html
└── libs/
    ├── jszip.min.js
    ├── opentype.min.js
    ├── x2js.js
    ├── easyjbig2.js
    ├── easyofd.js
    └── docx-preview.min.js
```

### 访问代码示例
```typescript
// iOS平台访问assets文件
const htmlPath = `${RNFS.MainBundlePath}/assets/ofd-viewer.html`;
const content = await RNFS.readFile(htmlPath, 'utf8');

const libPath = `${RNFS.MainBundlePath}/assets/libs/jszip.min.js`;
const libContent = await RNFS.readFile(libPath, 'utf8');
```

## 🔧 统一访问接口

为了简化跨平台开发，建议使用统一的文件访问函数：

```typescript
/**
 * 跨平台Assets文件访问
 */
export async function readAssetsFile(relativePath: string): Promise<string> {
  if (Platform.OS === 'android') {
    return await RNFS.readFileAssets(relativePath, 'utf8');
  } else {
    const fullPath = `${RNFS.MainBundlePath}/assets/${relativePath}`;
    return await RNFS.readFile(fullPath, 'utf8');
  }
}

// 使用示例
const htmlContent = await readAssetsFile('ofd-viewer.html');
const jsContent = await readAssetsFile('libs/jszip.min.js');
```

## 📊 性能优化记录

### 文件大小优化
通过移除PPT相关的大型库文件，实现了显著的性能提升：

| 库文件 | 大小 | 状态 | 说明 |
|--------|------|------|------|
| jszip.min.js | ~100KB | ✅ 保留 | 文件解压必需 |
| docx-preview.min.js | ~200KB | ✅ 保留 | Word预览必需 |
| easyofd.js | ~150KB | ✅ 保留 | OFD预览必需 |
| jquery-1.11.3.min.js | ~93KB | ❌ 移除 | PPT相关，已弃用 |
| d3.min.js | ~148KB | ❌ 移除 | PPT相关，已弃用 |
| pptxjs.js | ~794KB | ❌ 移除 | PPT相关，已弃用 |

**总优化效果**：
- 减少约1MB的assets文件大小
- 提升应用启动速度
- 降低内存使用

## 🔍 故障排除

### Android常见问题

#### 1. 文件访问失败
**症状**：`RNFS.readFileAssets()`抛出异常
**原因**：文件未正确打包到APK
**解决方案**：
```bash
# 检查APK内容
unzip -l app-debug.apk | grep assets

# 重新构建项目
cd mobile/android && ./gradlew clean assembleDebug
```

#### 2. 文件内容为空
**症状**：读取到的文件内容为空字符串
**原因**：文件路径错误或文件损坏
**解决方案**：
```typescript
// 使用测试工具验证
import { testFileContentIntegrity } from '../tests/androidAssetsTest';
const isValid = await testFileContentIntegrity('ofd-viewer.html');
```

### iOS常见问题

#### 1. 构建脚本未执行
**症状**：App Bundle中没有assets文件
**原因**：脚本权限或Xcode配置问题
**解决方案**：
```bash
# 检查脚本权限
ls -la mobile/ios/copy_assets.sh

# 设置执行权限
chmod +x mobile/ios/copy_assets.sh

# 手动测试脚本
cd mobile/ios
SRCROOT=$(pwd) BUILT_PRODUCTS_DIR=/tmp/test PRODUCT_NAME=Test ./copy_assets.sh
```

#### 2. 文件路径错误
**症状**：`RNFS.readFile()`找不到文件
**原因**：MainBundlePath路径拼接错误
**解决方案**：
```typescript
// 调试路径
console.log('MainBundlePath:', RNFS.MainBundlePath);
const assetsDir = `${RNFS.MainBundlePath}/assets`;
const exists = await RNFS.exists(assetsDir);
console.log('Assets目录存在:', exists);
```

## 🚀 最佳实践

### 开发流程
1. **添加新文件**：将文件放入`mobile/src/assets/`
2. **测试验证**：在两个平台上测试文件访问
3. **性能检查**：确保文件大小合理
4. **文档更新**：更新相关技术文档

### 维护建议
1. **定期清理**：移除不再使用的assets文件
2. **大小监控**：控制assets总大小在合理范围内
3. **版本管理**：重要变更时更新版本号
4. **测试覆盖**：确保新增文件有对应的测试

### 团队协作
1. **统一标准**：所有开发者使用相同的文件组织方式
2. **变更通知**：assets文件变更时通知团队
3. **文档同步**：及时更新技术文档
4. **问题反馈**：建立问题反馈和解决机制

---

**文档版本**：v2.0  
**最后更新**：2025年7月  
**维护团队**：公职猫开发团队
