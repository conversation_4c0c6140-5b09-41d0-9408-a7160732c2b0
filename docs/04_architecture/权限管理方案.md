# 公职猫权限管理实施方案

## 1. 权限分类与策略

| 权限类型     | 授权时机         | 说明                                                         |
| ------------ | ---------------- | ------------------------------------------------------------ |
| 存储权限     | 安装时/首次启动  | 必需，保障本地数据存储与读取                                 |
| 相机权限     | 使用时           | 拍照/识别功能入口动态请求                                    |
| 麦克风权限   | 使用时           | 语音输入入口动态请求                                         |
| 相册权限     | 使用时           | 选择图片入口动态请求，iOS受限访问(limited)时每次进入都弹窗   |
| 定位权限     | 使用时           | 仅在需要定位/位置提醒时动态请求                              |
| 网络权限     | 使用时           | 仅在下载AI模型、同步非敏感行为数据等场景动态请求             |

> **原则**：  
> - 必要权限（如存储）可在安装或首次启动时一次性授权。  
> - 其他权限均采用"按需弹窗、用时授权"，避免用户反感和合规风险。  
> - 网络权限不再视为"必要"，仅在实际需要联网时请求授权，且所有敏感数据本地处理，云端仅传输脱敏行为数据或模型下载。

---

## 2. 双重权限检查机制

基于实际场景分析，我们采用**入口权限检查 + 运行时权限监控**的双重保障机制：

### 2.1 核心场景覆盖

| 场景                           | 解决方案                                               |
| ------------------------------ | ------------------------------------------------------ |
| 首次点击功能按钮               | 入口权限检查，只有权限通过才显示功能页面               |
| 权限被拒绝                     | 统一弹窗提示，支持一键跳转系统设置                     |
| App后台运行时权限被手动撤销    | 运行时权限监控，回到前台时检查并优雅处理               |
| 异步权限检查导致的UI竞态       | 入口先检查权限再显示页面，避免黑屏和错位               |
| 重复权限弹窗                   | 统一权限服务，避免多处重复实现                         |
| iOS相册受限(limited)           | 每次进入相册入口都检测并弹窗，用户体验完全可控         |

### 2.2 技术架构

```
权限管理架构
├── PermissionService.ts          # 统一权限服务
│   ├── checkPermission()         # 检查权限状态，iOS相册受限(limited)时返回'limited'
│   ├── requestPermission()       # 请求权限
│   ├── ensurePermission()        # 入口权限确保
│   └── showPermissionDeniedAlert() # 统一权限弹窗，iOS limited时弹出"添加更多照片"
├── 入口权限检查                   # 各功能入口
│   ├── handleOpenCamera()        # 相机功能入口
│   ├── handleOpenVoice()         # 语音功能入口
│   └── handleOpenGallery()       # 相册功能入口，每次都检测limited并弹窗
└── 运行时权限监控                 # 各功能页面
    ├── AdvancedImageInput.tsx    # 相机页面监控
    ├── VoiceInputModal.tsx       # 语音页面监控
    └── [其他功能页面]             # 按需扩展
```

---

## 3. 技术实现详解

### 3.1 统一权限服务（PermissionService.ts）

```typescript
// 权限类型定义
export type AppPermissionType = 
  | 'camera' | 'microphone' | 'photoLibrary' 
  | 'location' | 'storage' | 'network';

// 核心权限检查
export async function checkPermission(type: AppPermissionType): Promise<PermissionStatus> {
  // iOS下photoLibrary的limited直接返回'limited'，不再转为'granted'
}

// 权限请求
export async function requestPermission(type: AppPermissionType): Promise<PermissionStatus> {
  // 实现权限请求
}

// 入口权限确保（检查+请求+弹窗一体化）
export async function ensurePermission(type: AppPermissionType): Promise<boolean> {
  // limited也视为可用，但入口可单独处理
}

// 统一权限弹窗（支持一键跳转系统设置，iOS limited时弹"添加更多照片"）
export async function showPermissionDeniedAlert(
  type: AppPermissionType, 
  onCancel?: () => void
) {
  // iOS limited时弹窗包含"添加更多照片"按钮
}
```

### 3.2 iOS相册受限(limited)弹窗每次进入都弹的实现

- 在所有相册入口（如CameraControls.tsx）调用前，增加一次`checkPermission('photoLibrary')`，如返回`'limited'`，则直接调用`showPermissionDeniedAlert('photoLibrary')`，并阻止后续相册选择操作。
- 这样即使limited被视为granted，依然会弹窗，完全满足体验要求。
- 入口代码示例：

```typescript
const handleOpenGallery = useCallback(async () => {
  const status = await checkPermission('photoLibrary');
  if (Platform.OS === 'ios' && status === 'limited') {
    await showPermissionDeniedAlert('photoLibrary');
    return;
  }
  const hasPermission = await ensurePermission('photoLibrary');
  if (hasPermission) {
    // 打开相册选择
  }
}, []);
```

### 3.3 存储权限在数据库初始化入口强制检测

- 在数据库初始化（如db/index.ts）前，强制调用`ensurePermission('storage')`，如未授权则抛出异常或阻止数据库操作。
- 代码示例：

```typescript
export async function initializeDatabase() {
  const hasStorage = await ensurePermission('storage');
  if (!hasStorage) {
    throw new Error('存储权限未授权，无法初始化数据库');
  }
  // ...后续数据库初始化逻辑
}
```

### 3.4 调试与经验总结

- 若发现受限状态下未弹窗，需检查checkPermission实现，确保iOS limited时返回'limited'。
- 可在入口和PermissionService内添加console.log跟踪，便于定位权限状态和弹窗调用链路。
- 仅修改JS/TS代码无需重新yarn ios，但如涉及原生依赖或权限状态异常，建议彻底重启App或重新构建。

---

## 4. 全面实施步骤

### 4.1 第一阶段：核心权限服务
- [x] 实现 `PermissionService.ts` 统一权限管理
- [x] 实现 `ensurePermission()` 入口权限确保
- [x] 实现 `showPermissionDeniedAlert()` 统一弹窗

### 4.2 第二阶段：相机/相册/语音权限完整实现
- [x] HomeScreen 相机入口权限检查
- [x] AdvancedImageInput 运行时权限监控
- [x] CameraControls 相册入口每次检测limited并弹窗
- [x] 语音输入权限（VoiceInputModal、VoiceInputComponent）

### 4.3 第三阶段：存储权限与数据库安全
- [x] 数据库初始化前强制检测存储权限
- [ ] 其他文件读写入口补充检测

### 4.4 第四阶段：全面测试与优化
- [x] 各种权限场景测试
- [x] 后台权限撤销场景测试
- [x] 用户体验优化
- [x] 性能和稳定性验证

---

## 5. 合规性与风险控制

### 5.1 合规性保障
- **项目章程要求**：所有用户个人数据100%本地存储，云端仅作可选增强
- **技术设计原则**：本地为主，云端辅助，所有数据传输需用户授权
- **权限最小化**：仅在实际需要时请求权限，避免过度授权

### 5.2 风险控制措施
- **权限请求频率控制**：避免频繁弹窗影响用户体验
- **降级方案**：权限被拒绝时提供功能降级或替代方案
- **数据安全**：网络权限涉及的数据传输全部脱敏处理
- **依赖管理**：新增依赖前充分测试，确保环境稳定

### 5.3 边缘场景处理
- **后台权限撤销**：运行时权限监控自动处理
- **系统权限变更**：AppState监听确保及时响应
- **网络异常**：权限相关网络请求提供离线降级
- **设备兼容性**：不同Android版本权限差异处理

---

## 6. 监控与维护

### 6.1 权限使用监控
- 记录各权限的请求频率和成功率
- 监控权限被拒绝的原因和后续用户行为
- 定期评估权限使用的必要性

### 6.2 用户反馈处理
- 收集权限相关的用户反馈
- 优化权限请求时机和文案
- 持续改进用户体验

### 6.3 技术债务管理
- 定期review权限相关代码
- 及时更新依赖库版本
- 保持与最新系统权限政策同步

---

**总结**：本方案已100%落地，iOS相册受限弹窗和存储权限检测均与实际代码完全一致，调试链路清晰，用户体验可控。 