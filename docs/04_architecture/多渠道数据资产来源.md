公职猫 App 多渠道数据接入方案说明

# **一、项目背景与目标**

公职猫 App 作为体制内干部的个人智能体助手，需支持多种方式接收用户的日常办公信息，包括文件、图片、语音及网页内容等，以沉淀为结构化数据资产。当前我们已实现文件浏览器导入功能，正在建设微信消息转发接入方案，接下来需拓展多通道输入能力，提升数据采集覆盖率与易用性。

# **二、整体数据输入方式概览**

1. App 内部文件选取上传（已完成）
2. 微信转发消息输入（已规划，使用企业微信客服转发并拉取消息）
3. 系统分享面板 Share Extension 接入
4. 剪切板监听输入
5. 语音输入与语音识别
6. 相机/相册输入（已完成）

# **三、各输入方式的实现路径**

## **1. App 文件资源管理器（已完成）**

• 已使用 React Native 实现文件、图片选择器；支持常见文档格式（PDF, Word, Excel, 图片等）。
• 上传后会转存至本地缓存路径，并生成元数据以供任务调用。

## **2. 微信消息转发（进行中）**

• 用户多选消息转发至“公职猫助手”公众号。
• 企业微信客服 API 拉取转发内容；App 拉取媒体并展示。
• 对文件类（.doc/.pdf/.png 等）进行本地存储及索引化管理。

## **3. 系统分享入口 Share Extension（需开发）**

• 在 iOS 和 Android 上分别实现 Share Extension 插件，注册支持接收 `text/plain`, `image/*`, `application/pdf` 等类型。
• 用户在其它 App 中点击“分享”，选择“公职猫”作为目标即可完成导入。
• 注意避免使用不稳定的第三方库，建议使用原生模块自定义开发。
• 可通过 bridge 将数据传入 React Native 层，写入本地存储或加入任务草稿。

## **4. 剪切板监听（可选辅助）**

• 在 App 启动时监听剪切板内容变化，提示用户“是否保存剪贴内容”。
• 支持文本、链接、文件路径三种类型。
• 适合用于辅助录入文章段落、网页链接等场景。

## **5. 语音输入模块（建议纳入规划）**

• 用户通过语音录入内容，结合语音转文本（ASR）进行智能识别。
• 可使用 iOS 原生语音识别，或引入离线 SDK（如讯飞、阿里）支持中文精准识别。
• 自动识别后形成任务草稿，可进一步提取关键信息（如日期、任务事项等）。
