# 智能动态日程系统设计文档 (V6.0 - 成长型AI秘书)

> **文档状态**: 初稿
> **创建日期**: {{CURRENT_DATE}}
> **负责人**: {{AI_ASSISTANT_NAME}}
> **核心贡献者**: {{USER_NAME}}

---

## 1. 概述与目标

本文档旨在详细阐述"公职猫"应用核心功能之一——**智能动态日程**的完整设计方案。该功能旨在解决传统日历软件在处理现实世界中日程时间不确定性时的僵化问题，通过引入一个与用户共同成长的AI秘书，真正落地产品"秘书般懂你"的核心价值主张。

本文档将记录我们从初步构想到最终方案的完整演进过程，作为后续开发、测试和迭代的"宪法"和知识库。

### 1.2 竞争格局与战略定位 (Competitive Landscape & Strategic Positioning)

为了确保我们的技术架构能够构建真正的、持久的护城河，必须清晰地认识当前的市场竞争格局。

#### 1.2.1 竞品分析：Apple Calendar (iOS 18)

根据最新的信息和功能截图，苹果正通过整合生态能力，将其日历从一个纯粹的工具向一个更智能的助手演进。其核心能力点包括：

-   **智能建议日程 (Siri Suggestions)**：Siri可以自动从邮件、信息和Safari中识别航班、酒店预订等事件，并一键建议添加到日历。
-   **智能规划出行 (Time to Leave)**：整合Apple地图，根据日程地点、实时交通状况和公共交通信息，主动建议用户的最佳出发时间。
-   **智能建议地点 (Location Suggestions)**：基于用户过去的历史日程和重要地点，为新日程提供地点建议。

**结论**：苹果的策略是**利用其强大的生态数据整合能力，提供场景化的"智能提醒"**。它的智能是基于对**结构化信息（如机票订单）的识别**和**公开数据（如交通路况）的查询**。这让它成为一个非常便捷的"信息聚合工具"，但其核心逻辑依然是"提醒"用户去执行一个已经安排好的计划。

#### 1.2.2 我们的核心壁垒与战略选择

我们的战略与苹果形成了根本性差异，这也构成了我们坚实的护城河。我们打造的不是一个信息聚合工具，而是一个**与用户共同成长的"个性化决策伙伴"**。

1.  **决策辅助 vs 信息提醒**:
    -   **苹果**: 提醒你"何时出发"去赶飞机。
    -   **我们**: 在你出差前，通过学习你的习惯，将"打车去机场"、"安检"、"候机"、"星巴克"等一系列行为打包成一个"行程套餐"，并动态管理整个时间流。我们的AI辅助的是**过程决策**，而不仅是**起点提醒**。

2.  **行为学习 vs 数据识别**:
    -   **苹果**: 智能来自于识别"订单"等结构化数据。
    -   **我们**: 智能来自于学习用户处理日程的**行为模式**（日程弹性、通勤习惯、缓冲时间偏好）。这是一种更深层次、更个性化的"懂你"，能处理非结构化的、模糊的现实场景。

3.  **极致隐私 vs 生态绑定**:
    -   **苹果**: 高级功能严重依赖其生态内的数据流转（邮件、地图等）。
    -   **我们**: 以100%本地化计算为基石，为用户提供"U盘级"的隐私安全感，这在我们的目标用户群体中是决定性的优势。

4.  **跨平台无缝体验 vs 平台锁定**:
    -   **苹果**: 最佳体验被锁定在其自家生态内。
    -   **我们**: 解决了用户在Windows办公和个人移动设备间频繁切换的核心痛点。

简而言之，苹果在"**让计划的执行更轻松**"，而我们在"**让计划的制定和调整本身更智能、更个性化**"。

## 2. 设计思想演进全记录

我们的方案经历了从V1.0到V5.0的迭代，这个过程凝聚了产品、技术与用户体验的深度思考，特别是用户提出的关键洞察，对最终方案的形成起到了决定性作用。

### V1.0 - V3.0: 初步探索与深化
- **核心痛点**: 传统日历必须设置固定结束时间，不符合现实。
- **初步方案**: 引入"浮动结束时间"和"一键结束"功能。
- **深化思考**: 增加了对"遗忘"、"冲突"、"无效间隙"等复杂场景的考量，并提出了本地化的`SchedulingAgent`技术架构和分阶段实施的初步设想。

### V4.0: "做取舍"——融入商业模式与MVP思考
- **关键反馈 (用户)**: "自动跳出确认，是不是本身就是一种打扰呢？"、"有些日程可能是有非常明确的时间点的，比如考试？"、"做产品就是做取舍，我们还要有一些商业化考量。"
- **重大调整**:
    1.  **从"主动"到"被动"**: 废除主动提醒，将AI建议变为用户被动触发。
    2.  **引入"日程刚性"**: 区分【刚性日程】与【弹性日程】，明确了AI的干预边界。
    3.  **商业模式与MVP界定**:
        - **MVP (免费基础功能)**: 聚焦做一个"最好的本地化弹性日程本"，功能完全本地化，确保永不失效。
        - **未来增值服务 (订阅制)**: 将需要云端算力的"高级智能调度"作为付费功能。

### V5.0: "成长型AI秘书"——最终方案确立
- **核心洞察 (用户)**: "AI应该像一个新入职的秘书，先观察学习，再提出判断"。
- **最终方案**: 建立了AI与用户共同成长的三阶段模型，完美解决了"如何在不打扰的前提下实现智能"的核心矛盾。
    - **阶段一：静默观察期**
    - **阶段二：试探建议期**
    - **阶段三：默契协作期**

### V6.0: "融合行程规划"——从时间管理到行程规划的升维
- **核心洞察 (用户)**: 解决了"交通时间"等隐性需求，并针对高铁出差等复杂场景，提出了对"信息脱节"的解决方案。
- **最终方案**: 引入 **"行程过渡时间" (Transition Time)** 的概念，并提出了 **"行程套餐" (Trip Package)** 作为应对复杂行程的终极方案。同时，将传感器数据的利用融入AI的成长模型中。

---

## 3. 最终功能设计 (V6.0)

### 3.1. 核心原则：成长型AI

AI的能力将根据与用户的互动和数据积累，分阶段解锁，确保用户体验的平滑和智能服务的精准。

- **第一阶段：【静默观察期】 (Silent Observer)**
  - **行为**: 完全静默，只在后台通过`SchedulingAgent`学习用户处理日程的模式。
  - **用户价值**: 一个极致简洁、无打扰的本地日程本。

- **第二阶段：【试探建议期】 (Probationary Assistant)**
  - **行为**: 在学习到一定程度后，开始通过低打扰方式（如微弱的红点提示）提供基于用户习惯的建议。
  - **用户价值**: 开始感受到AI的"懂你"，建立信任。

- **第三阶段：【默契协作期】 (Trusted Partner)**
  - **行为**: 在建议被高度采纳后，可以更自信地介入复杂场景，提供完整调度方案。
  - **用户价值**: AI成为真正的得力助手。

### 3.2. 功能细则

#### 3.2.1. 日程创建与分类
- 用户在创建日程时，输入`开始时间`和`预计时长`。
- 新增一个**可选的、纯文本的"地点"字段**。
- 用户可将日程标记为 **【刚性日程】** 或 **【弹性日程】**。
  - **刚性**: 开始和结束时间都不可变。AI不干预。
  - **弹性**: 只有开始时间确定。AI的主要作用场景。
- 界面上，弹性日程的结束时间段用渐变或虚线表示。

#### 3.2.2. 日程完成
- 用户可通过主界面的快捷按钮或日程详情页的"我办完了"按钮，手动结束一个弹性日程。
- 系统以当前时间记录为`actualEndTime`。

#### 3.2.3. "行程套餐" (Trip Package)
- **定义**: 一种特殊的事件组合，用于将一系列关联的、连续的子事件打包成一个逻辑整体，以应对高铁出差、航班出行等复杂行程。
- **MVP实现**: 用户可以**手动**将多个独立日程组合成一个"行程套餐"。
- **未来形态**: AI可基于关键词（如"高铁"）或OCR识别结果（如车票截图），智能推荐并一键生成包含所有子事件（如"前往车站"、"候车"、"乘车"等）的完整行程链。

#### 3.2.4. "不经意"的用户偏好学习 (含传感器)
- **学习机制**: `SchedulingAgent`通过观察用户在完成日程后的实际操作来学习偏好，而非主动询问。
- **学习内容**:
  - 用户如何利用碎片时间？
  - 用户如何处理日程冲突和优先级？
  - **通勤模式与习惯**:
    - **手动学习**: 观察用户手动创建的"通勤"、"开车"等日程。
    - **自动学习 (需授权)**: 在用户授权"位置"和"运动"权限后，AI可自动学习用户在不同地点间的通勤耗时和交通方式。
  - **枢纽缓冲时间**: AI可学习用户在机场、高铁站等交通枢纽习惯预留的"站内缓冲时间"。
- **隐私原则**: 所有学习和计算都在本地完成，100%保护用户隐私。

#### 3.2.5. AI建议入口
- AI服务将通过一个"被动"入口提供。
- 例如，在用户完成一个日程后，卡片上出现一个"下一步？"或"💡"的图标，由用户主动点击触发建议列表。
- 在MVP阶段，此入口主要用于未来功能占位，或只提供最简单的建议。

---

## 4. 技术架构设计

### 4.1. 数据模型
对数据库中的`events`表（或类似表）进行如下设计：

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `INTEGER` | 主键 |
| `parentId` | `INTEGER` | 父日程ID (用于行程套餐) |
| `title` | `TEXT` | 日程标题 |
| `startTime` | `DATETIME` | 开始时间 |
| `expectedDuration` | `INTEGER` | 预计时长 (分钟) |
| `actualEndTime` | `DATETIME` | 实际结束时间 (可为空) |
| `isRigid` | `BOOLEAN` | 是否为刚性日程 (true/false) |
| `location` | `TEXT` | 地点 (纯文本) |
| `status` | `TEXT` | 状态 (`pending`, `ongoing`, `completed`) |
| `completionMode` | `TEXT` | 完成方式 (`manual`, `auto_adjusted`) |
| `notes` | `TEXT` | 备注 |

### 4.2. 本地化AI代理: `SchedulingAgent`
- **位置**: `shared/services/SchedulingAgent.ts`，供移动端和桌面端复用。
- **职责**:
  1.  **监控模块**: 监控日程状态和时间变化。
  2.  **学习引擎**: 记录和分析用户行为，建立用户偏好模型（本地存储）。
  3.  **决策引擎**: 根据当前场景和用户偏好模型，生成建议。
  4.  **状态管理器**: 管理AI自身所处的成长阶段（观察期、试探期、协作期）。

### 4.3. 跨模块影响
- **UI**: `HomeScreen.tsx`, `AddScheduleScreen.tsx`, `DayDetailScreen.tsx` 等多个界面需要重构。
- **通知系统**: 需要与`SchedulingAgent`联动，但在MVP阶段，通知功能将被严格限制，以避免打扰。
- **后端与同步**: 后端API和数据同步逻辑需支持新的数据模型。

## 5. 分阶段实施路线图 (Roadmap)

### 阶段一：MVP - 最好的本地化弹性日程本
- **目标**: 跑通核心流程，为AI学习提供数据基础。
- **核心任务**:
  1.  **[DB]** 完成新的数据模型设计与迁移（增加`parentId`, `location`字段）。
  2.  **[API]** 完成后端API对新数据模型的支持。
  3.  **[UI]** 重构日程创建/编辑界面，支持`预计时长`、`刚性/弹性`分类和可选的`地点`输入。
  4.  **[UI]** 实现手动的"我办完了"功能。
  5.  **[UI]** 实现手动的日程组合功能，以支持"行程套餐"的MVP形态。
  6.  **[Agent]** 创建`SchedulingAgent`的基础框架，并在后台**静默学习**用户行为（包括在授权下的传感器数据学习）。

### 阶段二：AI能力上线 - 试探性智能助理
- **目标**: 让用户首次感受到AI的价值。
- **核心任务**:
  1.  **[Agent]** 实现"试探建议期"的解锁逻辑。
  2.  **[UI]** 设计并实现低打扰的AI建议入口（如红点提示）。
  3.  **[Agent]** 实现基于用户习惯的初步建议生成能力（如推荐处理碎片时间的方式、**试探性地建议行程过渡时间**）。
  4.  **[Agent]** 实现对用户反馈（采纳/忽略）的持续学习。

### 阶段三：能力增强与商业化探索
- **目标**: 成为真正的智能伙伴，并探索增值服务。
- **核心任务**:
  1.  **[Agent]** 实现"默契协作期"的解锁逻辑与高级调度能力。
  2.  **[Cloud]** (增值服务) **智能行程规划**: 实现自动化的"行程套餐"生成（如通过OCR识别车票）。
  3.  **[Cloud]** (增值服务) **实时动态行程**: 集成地图API和航班/高铁动态，提供实时调整建议。
  4.  **[Cloud]** (增值服务) 探索团队协作与日程代理功能。

---

## 6. 风险与考量
- **学习冷启动**: AI在第一阶段需要足够的数据才能提供有价值的服务，需要管理好用户的初期期望。
- **隐私与信任**: 必须严格遵守本地化处理原则，在产品设计和宣传上反复强调，并对传感器等敏感权限的请求做清晰、友好的说明。
- **技术复杂性**: `SchedulingAgent`的设计，特别是其学习和决策引擎，是本功能的技术难点。
- **电池消耗**: 持续的后台位置和运动检测可能会增加电池消耗，需要在技术实现上做深度优化。

## 7. 待定与未来讨论
- AI成长阶段的具体解锁阈值（如需要多少数据量）需要后续根据实际测试确定。
- 传感器数据采集的频率、精度与电池消耗的平衡策略。
- 云端增值服务的具体形态和定价策略。 