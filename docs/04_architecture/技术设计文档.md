# 公职猫 - MVP技术设计文档

## 1. 技术架构概述

公职猫采用"本地为主，云端辅助"的混合智能架构，确保用户个人隐私数据100%本地存储，通过规则引擎和轻量级AI实现离线智能，云端仅提供可选的AI增强服务。

### 1.1 架构原则

- **隐私数据本地化**：用户个人隐私数据100%本地存储（详见[《数据隐私与便携性设计方案》](DATA_PRIVACY_AND_PORTABILITY.md)）
- **规则引擎优先**：核心功能基于规则引擎，成本可控
- **轻量级AI辅助**：本地部署小型AI模型，离线可用
- **云端可选增强**：网络可用时提供更强AI能力（用户可关闭）
- **个性化学习**：本地数据支持系统"懂你"，实现真正个性化
- **成本最优**：规则引擎降低对AI的依赖，控制运营成本
- **开发简化**：选择成熟技术栈，适合单人+AI开发
- **快速迭代**：MVP优先，避免过度设计

### 1.2 竞争格局与战略定位 (Competitive Landscape & Strategic Positioning)

为了确保我们的技术架构能够构建真正的、持久的护城河，必须清晰地认识当前的市场竞争格局，特别是来自苹果原生生态的挑战。

#### 1.2.1 竞品分析：Apple Calendar (iOS 18)

苹果在iOS 18中对日历应用进行了重大更新，使其成为一个更强大的基础工具。其核心升级点在于：

- **功能整合**：与"提醒事项"App深度集成，实现了任务与日程的统一视图。
- **界面优化**：提供了全新的、信息密度更高的月视图（如"详情"视图），大幅提升了可用性。

**结论**：苹果的策略是**增强工具的便利性与展示效率**。它变得更美观、更方便，但其本质仍是一个被动的、需要用户主动管理信息的"效率工具"。

#### 1.2.2 我们的核心壁垒与战略选择

面对竞争，我们的核心价值主张与苹果形成鲜明对比。我们打造的不是一个更好的"工具"，而是一个全新的"智能伙伴"。我们的护城河由以下四大核心壁垒构成：

1. **深度智能与主动性 (成长型AI秘书)**

   - **区别**: 我们的AI不仅被动记录，更是主动学习、预测和建议。它能理解"浮动结束时间"、自动规划"行程过渡时间"、将复杂行程打包为"行程套餐"，并基于对用户行为模式的学习，主动预警和优化日程。这是我们与所有"工具型"日历的根本区别。
2. **极致隐私与本地优先 (U盘级私密)**

   - **区别**: 我们为核心用户（政务、国企等）设计的"数据100%本地处理"架构是不可动摇的信任基石。即便苹果强调隐私，其高级AI功能仍依赖云端。我们的"U盘级私密"在特定市场是比任何功能都更具决定性的优势。
3. **跨平台一致性体验**

   - **区别**: 苹果的最佳体验被锁定在其自家生态内。我们的用户常常需要在Windows办公电脑和个人移动设备间切换。公职猫提供无缝、一致的高级智能体验，直接解决跨平台用户的核心痛点。
4. **专业领域知识 (懂体制内)**

   - **区别**: 我们的AI具备深度领域知识，能理解"主办/协办/参与"的权重差异，能识别体制内特有的行文格式和工作流程。这种专业性是通用型产品无法企及的，构成了我们最难被复制的优势。

**战略方向**：我们的技术投入将坚定不移地围绕这四大壁垒展开，确保公职猫在激烈的市场竞争中，始终占据"AI智能伙伴"而非"日历工具"的独特定位。

## 2. 技术栈选择（MVP版本）

### 2.1 前端开发

**移动端**：React Native 0.73+

- 一套代码支持iOS和Android
- 丰富的开源组件生态
- 适合AI辅助开发
- **移动端特色功能**：
  - 原生相机集成，支持文档拍照和OCR
  - 语音识别和语音输入优化
  - 触控手势和大按钮设计
  - 离线优先，弱网环境适配
  - 推送通知和位置提醒

**移动端UI架构设计**：

- **设计系统**：基于亮色主题的统一设计语言，橙色(#FF8C00)品牌色
- **响应式布局**：动态网格系统适配不同屏幕尺寸，确保内容完整显示
- **触觉反馈系统**：三级触觉反馈(Light/Medium/Heavy)增强交互体验
- **手势交互**：垂直滑动切换周视图，支持实时跟随和边界反馈
- **组件化架构**：半日色块、迷你日历、响应式网格等核心业务组件
- **平台适配**：iOS和Android的原生特性充分利用，状态栏融合设计
- **性能优化**：动态高度计算、智能缓存、组件懒加载

**桌面端**：Electron + React 18+

- 跨平台支持Windows、统信UOS
- 复用React组件和业务逻辑
- 更好的文档处理和大屏幕体验
- **桌面端特色功能**：
  - 文件系统深度集成和批量操作
  - 多窗口和大屏幕布局优化
  - 拖拽导入和快捷键支持
  - 文件监控和自动索引更新
  - 复杂文档编辑和预览

### 2.2 本地存储与文档处理

**数据库**：SQLite + better-sqlite3-multiple-ciphers加密

- **加密库选择**：better-sqlite3-multiple-ciphers（支持AES-256-CBC/GCM等多种加密算法）
- **密钥管理**：Electron safeStorage API（系统级安全存储）
- **性能优化**：加密开销<5%，支持高并发读写
- **兼容性**：完全兼容标准SQLite，支持FTS5全文搜索

**文档处理**：

- Word文档：mammoth.js（纯JS库，离线运行）
- Excel文档：xlsx相关库（纯JS库，离线运行）
- PDF文档：原生PDF处理库（离线运行）
- OFD文档：待选型（需寻找支持离线运行的JS库）

### 2.3 四层AI架构

**云端AI层**：处理非敏感数据的复杂AI任务（阿里云百炼/腾讯云混元）
**iOS Foundation Models层**：iOS 18.1+设备的高性能本地AI（Apple Intelligence API）
**其他平台ONNX Runtime层**：Android、Windows、UOS的基础AI能力（量化Qwen模型）
**规则引擎兜底层**：关键词匹配 + 模式识别，确保所有设备基础功能可用

### 2.4 核心功能模块

**OCR识别**：iOS Vision框架 + 其他平台原生OCR能力
**语音识别**：iOS Speech框架 + 其他平台原生语音识别
**全文搜索**：SQLite FTS5全文搜索引擎
**统一AI接口**：抽象层屏蔽平台差异，智能选择最优AI层级
**安装包优化**：核心包<50MB，AI模型按需下载

## 3. 核心模块设计

### 3.1 文档导入与索引模块（DocumentManager）

**功能职责**：

- 支持Word、Excel、PDF、图片等多格式文档导入
- 文档内容解析和文本提取
- 建立文件索引，不复制原文件（节省存储空间）
- 支持按类型、时间、关键词检索文档
- 监控文件系统变化，自动更新索引

**技术实现**：

- **文件索引策略**：只存储文件路径、元数据和解析结果，不复制原文件
- **批量扫描导入**：支持指定文件夹批量建立索引
- **文件系统监控**：使用chokidar等库监控文件变化
- **智能处理**：文件移动、重命名、删除时自动更新索引
- **网络驱动器支持**：支持网络共享文件夹和云盘文件
- **增量更新**：只处理变化的文件，提高效率

**跨设备数据同步策略**：

- **本地文件索引**：仅在本设备有效，存储本地文件路径和元数据
- **同步文件内容**：移动端拍照、录音等文件需要完整同步到其他设备
- **解析结果共享**：文档的AI解析结果可以跨设备共享，避免重复处理
- **统一数据模型**：设计统一的文件数据结构，支持本地文件和同步文件的混合管理
- **设备标识机制**：每个文件记录来源设备，便于冲突处理和数据追溯

### 3.2 领导日程识别模块（ScheduleParser）

**功能职责**：

- 从文档中智能识别日程安排
- 提取时间、地点、参与人员等关键信息
- 识别周期性事件模式
- 生成结构化的日程数据

**技术实现**：

- 规则引擎进行基础信息提取
- 正则表达式辅助时间信息识别
- 可选AI API进行复杂语义理解
- 本地缓存解析结果

### 3.3 个人资料管理模块（PersonalDataManager）

**功能职责**：

- 身份证、照片、证书等个人资料管理
- OCR识别证件信息
- 个人信息快速调用和导出
- 敏感数据加密存储

**技术实现**：

- 平台原生OCR进行文字识别
- better-sqlite3-multiple-ciphers提供数据库级AES-256加密
- Electron safeStorage API管理加密密钥（系统级安全存储）
- 支持密钥轮换和安全备份机制
- 支持多种格式导出（导出时重新加密）

### 3.4 全文搜索模块（SearchEngine）

**功能职责**：

- 全文索引构建和维护
- 支持关键词、模糊搜索
- 按文档类型、时间范围筛选
- 搜索结果高亮显示

**技术实现**：

- SQLite FTS5全文搜索引擎
- 增量索引更新机制
- 搜索结果缓存优化
- 可选AI语义搜索能力

### 3.5 角色智能识别模块（RoleAnalyzer）- 成本最优方案

**功能职责**：

- 从文本中识别日程和事务
- 通过规则引擎和用户引导识别角色职责
- 基于角色提供差异化的管理方式
- 学习和沉淀用户工作模式

**技术实现**：

- **规则引擎为主**：关键词匹配识别主办/协办/参与标识
- **职务关系匹配**：根据用户部门和职务推断角色
- **用户引导机制**：系统推荐 + 用户确认 + 学习记忆
- **历史模式学习**：本地存储用户选择偏好
- **可选AI增强**：网络可用时调用云端AI提升准确率

**成本控制策略**：

- 80%功能通过规则引擎实现，无运营成本
- 用户引导降低对AI准确率的依赖
- 本地学习机制持续优化推荐准确性
- 云端AI仅作为可选增强功能

### 3.6 本地规则引擎（LocalRuleEngine）

**功能职责**：

- 实现关键词匹配和模式识别
- 支持体制内专用词典和职务识别
- 提供基础的文本分类和实体识别
- 支持用户自定义规则和学习

**技术实现**：

- 关键词词典和正则表达式匹配
- 体制内专用术语库和职务关系库
- 用户偏好和工作习惯本地学习
- 置信度评估和结果优化

### 3.7 文件监控与索引模块（FileWatcherManager）

**功能职责**：

- 监控指定文件夹的文件变化
- 自动检测文件的创建、修改、删除、移动
- 增量更新文件索引，避免重复处理
- 处理网络驱动器和云盘文件的特殊情况
- 提供文件可用性检查和修复机制

**技术实现**：

- **文件系统监控**：使用chokidar库监控文件变化
- **批量初始化**：支持指定多个文件夹批量建立索引
- **智能过滤**：忽略临时文件、系统文件等无关文件
- **增量处理**：只处理变化的文件，提高效率
- **错误恢复**：文件不可用时的降级处理和用户提示
- **性能优化**：防抖处理、批量更新、后台处理

**跨设备存储分类**：

- **设备本地文件**：桌面端文件系统中的文档，采用分层访问策略
- **设备间同步文件**：移动端拍照文档、语音记录等，需要完整存储文件内容
- **共享解析数据**：文档解析结果、AI分析结果等，可以跨设备共享
- **用户个人数据**：任务安排、个人设置、工作习惯等，需要完全同步

**分层文件访问策略**：

- **元数据层**：文件名、大小、修改时间等基础信息（始终同步）
- **解析结果层**：AI提取的文本内容、关键信息、文档摘要（始终同步）
- **预览内容层**：文档缩略图、前几页预览等轻量级内容（智能缓存）
- **完整文件层**：原始文件内容（按需传输或用户选择完全同步）

## 4. 数据存储设计

### 4.1 数据库结构

- **文档索引表**：存储本地文件索引和同步文件的统一元数据
- **文件内容表**：存储需要跨设备同步的完整文件内容
- **全文搜索表**：FTS5虚拟表支持全文检索
- **日程事件表**：结构化存储日程信息
- **个人资料表**：加密存储敏感个人信息
- **事务表**：事务信息、角色和状态管理
- **用户偏好表**：存储用户工作习惯、角色选择偏好
- **规则学习表**：存储规则引擎的学习数据和优化参数
- **设备同步表**：记录设备间的数据同步状态和冲突信息

### 4.2 跨设备文件存储策略

- **分层存储机制**：根据访问频率和重要性分层存储文件内容
- **按需下载缓存**：用户查看时实时下载，智能预测和缓存常用文件
- **同步文件存储**：移动端拍照、录音等文件完整存储在应用目录
- **解析结果缓存**：AI处理结果本地缓存，可跨设备共享

## 5. 数据同步方案

- **数据同步方案**：[数据同步方案](./数据同步方案.md)
- **数据加密方案**：[数据库加密技术选型](./数据库加密技术选型.md)

## 6. 核心用户场景

为确保技术方案与用户真实需求紧密贴合，所有设计与开发都应围绕以下核心场景展开：

### 场景一："办公室-家庭"跨网络同步

- **用户故事**:
  - 白天，用户在办公室的电脑上整理了大量的工作笔记和会议纪要。
  - 晚上，用户回到家，希望能立刻在自己的手机或家用电脑上，无缝地查阅、编辑这些资料，为第二天的工作做准备。
- **技术挑战**:
  - 办公室（可能是政务网）和家庭网络完全隔离，无法直接通信。
  - 用户没有公网IP，无法建立传统的服务器-客户端连接。
- **解决方案**:
  - **优选方案 (WebRTC)**: 用户在离开办公室前，在电脑端App生成一个连接二维码；回到家用另一台设备扫描此码，即可建立加密的P2P通道，实现数据自动同步。
  - **备选方案 (手动迁移)**: 用户也可以在办公室将数据导出为一个加密包，通过微信/U盘等方式带回家，在家用设备上导入。

### 场景二：局域网内的多设备协同

- **用户故事**:
  - 用户正在开会，电脑上正在演示PPT，同时希望用手机作为"遥控器"，快速查找并展示某个关键文件或笔记，而不需要中断演示。
- **技术挑战**:
  - 需要设备间的低延迟、高响应的实时通信。
- **解决方案 (mDNS)**:
  - 在同一Wi-Fi下，设备间可以自动发现并建立高速直连。手机上的操作可以实时反映在电脑上，实现无缝协同。

### 场景三：无网络环境下的数据保障

- **用户故事**:
  - 用户在外出差，或在没有Wi-Fi的会议室，需要紧急将手机上的一个重要文件同步到电脑上进行编辑。
- **技术挑战**:
  - 没有任何网络连接。
- **解决方案 (蓝牙/数据线)**:
  - App支持通过蓝牙进行近距离的数据同步，或通过USB数据线建立最稳定可靠的物理连接，保证在任何极端情况下，数据的可用性和可迁移性。

## 7. 风险评估

- **技术风险**：
  - WebRTC在复杂网络环境下的穿透成功率
  - 多平台原生能力差异导致的功能不一致
  - 加密性能对低端设备的影响
- **项目风险**：
  - 单人+AI开发模式的效率和可持续性
  - 市场对个人付费产品的接受度
- **安全风险**：
  - 本地存储的物理安全问题
  - 密钥管理方案的健壮性

## 8. 总结与展望

=======

- **数据同步方案**：[数据同步方案](./数据同步方案.md)
- **数据加密方案**：[数据库加密技术选型](./数据库加密技术选型.md)

## 6. 核心用户场景

为确保技术方案与用户真实需求紧密贴合，所有设计与开发都应围绕以下核心场景展开：

### 场景一："办公室-家庭"跨网络同步

- **用户故事**:
  - 白天，用户在办公室的电脑上整理了大量的工作笔记和会议纪要。
  - 晚上，用户回到家，希望能立刻在自己的手机或家用电脑上，无缝地查阅、编辑这些资料，为第二天的工作做准备。
- **技术挑战**:
  - 办公室（可能是政务网）和家庭网络完全隔离，无法直接通信。
  - 用户没有公网IP，无法建立传统的服务器-客户端连接。
- **解决方案**:
  - **优选方案 (WebRTC)**: 用户在离开办公室前，在电脑端App生成一个连接二维码；回到家用另一台设备扫描此码，即可建立加密的P2P通道，实现数据自动同步。
  - **备选方案 (手动迁移)**: 用户也可以在办公室将数据导出为一个加密包，通过微信/U盘等方式带回家，在家用设备上导入。

### 场景二：局域网内的多设备协同

- **用户故事**:
  - 用户正在开会，电脑上正在演示PPT，同时希望用手机作为"遥控器"，快速查找并展示某个关键文件或笔记，而不需要中断演示。
- **技术挑战**:
  - 需要设备间的低延迟、高响应的实时通信。
- **解决方案 (mDNS)**:
  - 在同一Wi-Fi下，设备间可以自动发现并建立高速直连。手机上的操作可以实时反映在电脑上，实现无缝协同。

### 场景三：无网络环境下的数据保障

- **用户故事**:
  - 用户在外出差，或在没有Wi-Fi的会议室，需要紧急将手机上的一个重要文件同步到电脑上进行编辑。
- **技术挑战**:
  - 没有任何网络连接。
- **解决方案 (蓝牙/数据线)**:
  - App支持通过蓝牙进行近距离的数据同步，或通过USB数据线建立最稳定可靠的物理连接，保证在任何极端情况下，数据的可用性和可迁移性。

## 7. 风险评估

- **技术风险**：
  - WebRTC在复杂网络环境下的穿透成功率
  - 多平台原生能力差异导致的功能不一致
  - 加密性能对低端设备的影响
- **项目风险**：
  - 单人+AI开发模式的效率和可持续性
  - 市场对个人付费产品的接受度
- **安全风险**：
  - 本地存储的物理安全问题
  - 密钥管理方案的健壮性

## 8. 总结与展望
>>>>>>> 037d087 (docs: 完成项目文档重组和结构优化 - 重组项目管理文档到docs/00_project_management/ - 合并重复的技术设计文档 - 移动AI模型文件到mobile/assets/ai_models/ - 重新组织测试报告到test_reports/ - 添加文档重组自动化脚本 - 清理根目录，提升项目结构清晰度)
- **MVP核心**：
  - 规则引擎驱动的离线智能
  - 安全可靠的本地数据存储
  - 便捷的多设备数据同步
- **未来展望**：
  - 增强本地AI能力，减少对云端的依赖
  - 扩展专业知识库，提高智能水平
  - 探索社区驱动的规则和知识共享

---

*文档版本：v4.1 合并版  |  更新日期：2025-06-18*
==============================================

*文档版本：v4.1 合并版  |  更新日期：2025-06-18*
