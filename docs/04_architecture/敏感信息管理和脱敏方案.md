# 公职猫敏感信息管理和脱敏方案

## 📋 问题分析

### 当前发现的敏感信息泄露

#### 1. 后端配置文件 (backend/wechat/config/env.template)
```bash
# 腾讯云API密钥
TENCENT_SECRET_ID=AKID0tmEXfhYCdOxLVSCl7FAuR1Ulofs7D8q
TENCENT_SECRET_KEY=Zt2zwgLuNeiYCest78wYxBMK8jBQUMUe

# 数据库密码
MYSQL_PASSWORD=wechat@gongzhimall123

# 企业微信配置
WECHAT_CORP_ID=ww857dc7bfe97b085b
WECHAT_CORP_SECRET=eQs0lmS8uUgpyDM74XvHHwWuAddq1n_qhdNsBZoB13I

# 极光推送配置
JPUSH_APP_KEY=bd2c958b83dc679759a25664
JPUSH_MASTER_SECRET=59a9af913b1d2dd85f4cbc24

# 其他敏感配置
WECHAT_TOKEN=gongzhimallEnterprise2025
WECHAT_ENCODING_AES_KEY=zlb6q9HgvrGOYlgMR9HNmMNIHo9dJgrQVmbs7G2DJcv
TOKEN_SECRET=gongzhimall-app-2025
FILE_ENCRYPTION_KEY=gongzhimall-file-encryption-2025
```

#### 2. 移动端配置文件 (mobile/src/config/appConfig.ts)
```typescript
// 硬编码的fallback值包含真实敏感信息
WECHAT_CORP_ID: process.env.WECHAT_CORP_ID || (__DEV__ ? 'ww857dc7bfe97b085b' : ''),
WECHAT_CORP_SECRET: process.env.WECHAT_CORP_SECRET || (__DEV__ ? 'eQs0lmS8uUgpyDM74XvHHwWuAddq1n_qhdNsBZoB13I' : ''),
WECHAT_APP_SECRET: process.env.WECHAT_APP_SECRET || '6bea22ca7c5993c747ab3a58de0caf06',
```

#### 3. 华为云配置文件 (mobile/android/app/agconnect-services.json)
```json
{
  "client_secret": "3EE7EDA9105812501120F8BD93BD313AF48DF44A0AFB3EED75E400D0D9DD0D1C",
  "api_key": "DQEDAFEaaiB7OHKPaIXff4LVLu75l/qAp0lOqVUAIqrwD6zWHn9VesLQ+SrrCg+iAD+WhoO7/EgURfR0COEZp5gCaufi3FXdkz7SOw=="
}
```

## 🔧 解决方案

### 阶段1：环境变量管理机制设计

#### 1.1 生产环境配置管理
```bash
# 在生产服务器上设置环境变量
# /etc/environment 或 ~/.bashrc
export TENCENT_SECRET_ID="AKID0tmEXfhYCdOxLVSCl7FAuR1Ulofs7D8q"
export TENCENT_SECRET_KEY="Zt2zwgLuNeiYCest78wYxBMK8jBQUMUe"
export MYSQL_PASSWORD="wechat@gongzhimall123"
export WECHAT_CORP_ID="ww857dc7bfe97b085b"
export WECHAT_CORP_SECRET="eQs0lmS8uUgpyDM74XvHHwWuAddq1n_qhdNsBZoB13I"
export JPUSH_APP_KEY="bd2c958b83dc679759a25664"
export JPUSH_MASTER_SECRET="59a9af913b1d2dd85f4cbc24"
```

#### 1.2 开发环境配置管理
```bash
# 开发环境使用测试配置
export WECHAT_CORP_ID="dev_test_corp_id"
export WECHAT_CORP_SECRET="dev_test_corp_secret"
export MYSQL_PASSWORD="dev_test_password"
export JPUSH_APP_KEY="dev_test_app_key"
```

#### 1.3 部署脚本环境变量注入
```bash
# backend/wechat/deploy/setup-production-env.sh
#!/bin/bash
# 生产环境环境变量设置脚本（仅在服务器上执行）

# 检查是否为生产环境
if [ "$NODE_ENV" != "production" ]; then
    echo "此脚本仅在生产环境执行"
    exit 1
fi

# 设置环境变量（从安全存储读取）
export TENCENT_SECRET_ID="$(cat /secure/tencent_secret_id)"
export TENCENT_SECRET_KEY="$(cat /secure/tencent_secret_key)"
export MYSQL_PASSWORD="$(cat /secure/mysql_password)"
# ... 其他敏感配置
```

### 阶段2：配置文件脱敏

#### 2.1 后端配置模板脱敏
```bash
# backend/wechat/config/env.template (脱敏后)
# 腾讯云API密钥（用于自动化部署）
TENCENT_SECRET_ID=YOUR_TENCENT_SECRET_ID
TENCENT_SECRET_KEY=YOUR_TENCENT_SECRET_KEY

# 数据库连接信息
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=wechat_user
MYSQL_PASSWORD=YOUR_MYSQL_PASSWORD
MYSQL_DATABASE=gongzhimall_wechat

# 企业微信基础信息
WECHAT_CORP_ID=YOUR_WECHAT_CORP_ID
WECHAT_CORP_SECRET=YOUR_WECHAT_CORP_SECRET
WECHAT_AGENT_ID=YOUR_WECHAT_AGENT_ID

# 极光推送配置
JPUSH_APP_KEY=YOUR_JPUSH_APP_KEY
JPUSH_MASTER_SECRET=YOUR_JPUSH_MASTER_SECRET
```

#### 2.2 移动端配置脱敏
```typescript
// mobile/src/config/appConfig.ts (脱敏后)
export const weChatConfig = {
  // 微信企业应用配置（完全从环境变量读取）
  WECHAT_CORP_ID: process.env.WECHAT_CORP_ID || '',
  WECHAT_AGENT_ID: process.env.WECHAT_AGENT_ID || '',
  WECHAT_CORP_SECRET: process.env.WECHAT_CORP_SECRET || '',

  // 微信开放平台配置
  WECHAT_OPEN_PLATFORM: {
    APP_ID: process.env.WECHAT_APP_ID || '',
    APP_SECRET: process.env.WECHAT_APP_SECRET || '',
    UNIVERSAL_LINK: 'https://app.gongzhimall.com/app/',
  },

  // 消息推送配置
  WECHAT_PUSH_TOKEN: process.env.WECHAT_PUSH_TOKEN || '',
  WECHAT_PUSH_ENCODING_AES_KEY: process.env.WECHAT_PUSH_ENCODING_AES_KEY || '',
};

// 极光推送配置（完全从环境变量读取）
export const jpushConfig = {
  appKey: process.env.JPUSH_APP_KEY || '',
  channel: process.env.JPUSH_CHANNEL || 'gongzhimall-official',
  production: process.env.NODE_ENV === 'production',
};
```

### 阶段3：安全配置文件管理

#### 3.1 .gitignore 更新
```gitignore
# 敏感配置文件
backend/wechat/.env
backend/wechat/.env.production
backend/wechat/.env.local
mobile/.env
mobile/.env.production
mobile/.env.local

# 华为云配置文件
mobile/android/app/agconnect-services.json
mobile/android/app/agconnect-services-prod.json

# 证书和密钥文件
*.p12
*.cer
*.pem
*.key
**/证书/**
```

#### 3.2 华为云配置文件处理
```bash
# 将 agconnect-services.json 移出版本控制
git rm --cached mobile/android/app/agconnect-services.json

# 创建模板文件
cp mobile/android/app/agconnect-services.json mobile/android/app/agconnect-services.template.json

# 在模板文件中替换敏感信息为占位符
```

### 阶段4：部署时配置注入

#### 4.1 生产环境部署脚本
```bash
#!/bin/bash
# deploy-with-env.sh

# 从安全存储读取配置
source /secure/production.env

# 生成实际配置文件
envsubst < backend/wechat/config/env.template > backend/wechat/.env
envsubst < mobile/android/app/agconnect-services.template.json > mobile/android/app/agconnect-services.json

# 执行部署
npm run deploy:production

# 清理临时配置文件
rm backend/wechat/.env
rm mobile/android/app/agconnect-services.json
```

#### 4.2 开发环境配置
```bash
# mobile/env.example (开发环境示例)
WECHAT_CORP_ID=dev_test_corp_id
WECHAT_CORP_SECRET=dev_test_corp_secret
WECHAT_APP_ID=dev_test_app_id
WECHAT_APP_SECRET=dev_test_app_secret
JPUSH_APP_KEY=dev_test_app_key
NODE_ENV=development
```

## 🚀 执行计划

### 步骤1：备份当前敏感信息
1. 将真实配置保存到安全位置
2. 记录所有敏感信息的用途和位置

### 步骤2：设置生产环境变量
1. 在生产服务器上设置环境变量
2. 验证环境变量正确加载

### 步骤3：脱敏配置文件
1. 替换模板文件中的敏感信息
2. 更新代码中的硬编码fallback值
3. 移除华为云配置文件

### 步骤4：测试验证
1. 在开发环境测试配置加载
2. 在生产环境验证功能正常

### 步骤5：提交脱敏代码
1. 确认无敏感信息残留
2. 提交到git仓库

## ⚠️ 风险控制

1. **备份策略**：在脱敏前完整备份当前配置
2. **分步执行**：逐个文件处理，避免批量错误
3. **功能验证**：每步修改后验证核心功能
4. **回滚准备**：准备快速回滚方案

## 📝 检查清单

- [ ] 生产环境变量已设置
- [ ] 开发环境变量已配置
- [ ] 配置模板已脱敏
- [ ] 代码中fallback值已移除
- [ ] .gitignore已更新
- [ ] 华为云配置已处理
- [ ] 部署脚本已更新
- [ ] 功能测试通过
- [ ] 安全检查通过
