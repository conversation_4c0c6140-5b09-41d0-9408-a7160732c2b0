# 公职猫AI技术架构与选型方案

## 1. 技术架构概述

公职猫采用"四层AI架构 + 统一适配器"的智能处理方案，通过分层设计实现成本最优、体验最佳的AI能力，确保在不同设备和网络环境下都能提供可靠的智能服务。

### 1.1 设计目标
- **成本最优**：优先使用免费的厂商端侧模型和规则引擎
- **体验分层**：iOS端享受最佳AI体验，其他平台保持一致基础体验
- **隐私保护**：敏感数据仅本地处理，云端仅处理非敏感数据
- **离线可用**：核心功能完全离线运行，网络仅用于增强
- **安装轻量**：核心安装包<50MB，AI模型按需下载
- **响应快速**：本地推理<2秒，云端增强<5秒
- **兼容性强**：支持从最新设备到3年前设备的广泛兼容

### 1.2 四层AI架构设计

```
┌─────────────────────────────────────────────────────┐
│                  业务应用层                          │
├─────────────────────────────────────────────────────┤
│                统一AI调用接口                        │
├─────────────────────────────────────────────────────┤
│ 云端AI层        │ iOS Foundation │ 其他平台ONNX    │
│ (非敏感数据)    │ 优先使用       │ 基础保障        │
│ 可选增强        │ 模型层       │ 层       │
├─────────────────┼───────────────┼─────────────────┤
│              规则引擎兜底层                          │
│         (关键词匹配 + 模式识别)                      │
└─────────────────────────────────────────────────────┘
```

## 2. 分层技术选型

### 2.1 云端AI层（第一层）
**适用场景**：处理非敏感数据的复杂AI任务
**技术选择**：
- **主选方案**：阿里云百炼 / 腾讯云混元
- **备选方案**：OpenRouter API（多模型选择）
- **处理能力**：复杂语义理解、文档摘要、任务拆解

**数据策略**：
- **可处理**：去标识化的文档内容、通用任务描述
- **禁止处理**：个人身份信息、具体人名地名、敏感工作内容
- **用户控制**：用户可完全关闭云端AI功能

**成本控制**：
- 仅在本地AI置信度低时调用
- 缓存策略减少重复调用
- 用户可设置月度使用限额

### 2.2 iOS Foundation Models层（第二层）
**适用场景**：iOS 18.1+设备的高性能本地AI
**技术选择**：
- **Apple Intelligence API**：文本理解、摘要生成
- **Core ML框架**：本地模型推理
- **Vision框架**：OCR和图像理解
- **Speech框架**：语音识别和理解

**设备要求**：
- **最低要求**：iOS 18.1+，A17 Pro芯片
- **推荐配置**：iPhone 15 Pro及以上
- **降级策略**：不支持设备自动降级到第三层

**功能优势**：
- 免费使用，无运营成本
- 隐私保护，数据不离设备
- 性能强劲，响应速度快
- 系统级优化，功耗控制好

### 2.3 其他平台ONNX Runtime层（第三层）
**适用场景**：Android、Windows、UOS平台的基础AI能力
**技术选择**：
- **推理引擎**：ONNX Runtime
- **模型选择**：量化Qwen-1.8B模型
- **模型大小**：压缩后约200MB
- **下载策略**：首次使用时按需下载

**平台适配**：
- **Android**：ONNX Runtime Mobile
- **Windows**：ONNX Runtime Desktop
- **UOS**：ONNX Runtime Linux

**性能优化**：
- INT8量化减少模型大小
- 针对中文NLP任务优化
- 本地缓存推理结果
- 批量处理提升效率

### 2.4 规则引擎兜底层（第四层）
**适用场景**：所有设备的最终保障，确保基础功能可用
**技术选择**：
- **关键词匹配**：体制内专用词典
- **模式识别**：正则表达式和模板匹配
- **职务识别**：部门职能和层级关系库
- **时间解析**：基础时间表达式识别

**核心能力**：
- 政务关键词识别（主办/协办/参与）
- 基础时间和地点提取
- 简单任务分类和优先级判断
- 用户工作习惯学习和记忆

## 3. 统一适配器设计

### 3.1 AI调用抽象层
**设计原则**：
- 统一接口屏蔽平台差异
- 运行时设备能力检测
- 自动选择最优AI层级
- 降级策略确保功能可用

**核心功能**：
- 设备兼容性检测
- AI能力评估和选择
- 请求路由和负载均衡
- 结果融合和置信度评估

### 3.2 智能降级策略
**降级逻辑**：
1. 优先尝试最高可用AI层
2. 失败或置信度低时自动降级
3. 多层结果融合提升准确率
4. 规则引擎确保最终可用

**用户体验**：
- 降级过程对用户透明
- 明确标识当前AI能力级别
- 提供手动切换选项
- 网络恢复时自动升级

## 4. 安装包优化策略

### 4.1 分层下载设计
**核心安装包**（<50MB）：
- 基础应用框架
- 规则引擎和词典
- 基础UI组件
- 数据库和加密模块

**按需下载模块**：
- **ONNX模型文件**（200MB）：首次使用AI功能时下载
- **扩展词典**（10MB）：根据用户工作领域下载
- **语音模型**（50MB）：首次使用语音功能时下载
- **OCR增强**（30MB）：首次使用拍照识别时下载

### 4.2 智能下载策略
**网络感知**：
- WiFi环境自动下载
- 移动网络询问用户
- 弱网环境分片下载
- 下载失败自动重试

**存储管理**：
- 模型文件压缩存储
- 不常用模型自动清理
- 用户可手动管理模型
- 支持外部存储扩展

## 5. 时间处理能力重新设计

### 5.1 timeNLP替代方案
**现状分析**：
- 现代AI模型时间理解能力已经很强
- iOS Foundation Models原生支持时间解析
- 云端AI模型时间处理准确率>95%
- 维护独立时间库增加复杂度

**新的时间处理策略**：
- **AI优先**：优先使用AI模型进行时间理解
- **规则辅助**：基础正则表达式作为兜底
- **上下文理解**：结合文档上下文推断时间
- **智能纠错**：AI自动识别和修正时间错误

### 5.2 时间处理能力对比

| 处理方式 | 准确率 | 复杂度支持 | 维护成本 | 推荐度 |
|----------|--------|------------|----------|--------|
| iOS Foundation Models | 95%+ | 极高 | 无 | ⭐⭐⭐⭐⭐ |
| 云端AI模型 | 98%+ | 极高 | 低 | ⭐⭐⭐⭐⭐ |
| ONNX量化模型 | 85%+ | 高 | 低 | ⭐⭐⭐⭐ |
| 规则引擎 | 70%+ | 中等 | 中等 | ⭐⭐⭐ |
| timeNLP库 | 90%+ | 高 | 高 | ⭐⭐ |

**结论**：移除timeNLP依赖，采用AI优先的时间处理策略

## 6. 性能与成本优化

### 6.1 成本控制策略
**免费资源优先**：
- iOS端充分利用Apple AI能力
- 规则引擎处理80%基础场景
- 云端AI仅处理复杂场景

**智能调用策略**：
- 本地置信度阈值控制
- 用户行为学习优化
- 缓存策略减少重复调用
- 批量处理降低单次成本

### 6.2 性能优化策略
**响应速度**：
- 规则引擎：<100ms
- 本地AI：<2秒
- 云端AI：<5秒
- 混合推理：<3秒

**资源管理**：
- 模型懒加载和卸载
- 内存使用监控和优化
- 后台处理和预加载
- 电池使用优化

## 7. 技术风险与应对

### 7.1 主要技术风险
**设备兼容性风险**：
- iOS老设备无法使用Foundation Models
- Android设备性能差异大
- 应对：完善的降级策略和兼容性测试

**网络依赖风险**：
- 云端AI不可用影响体验
- 模型下载失败影响功能
- 应对：本地AI兜底和离线模式

### 7.2 应对策略
**多层保障**：
- 四层AI架构确保功能可用
- 规则引擎作为最终兜底
- 本地缓存减少网络依赖

**用户控制**：
- 用户可选择AI使用策略
- 透明的功能降级提示
- 完整的离线使用模式

---

**总结：**
新的四层AI架构通过合理的技术选型和分层设计，既充分利用了iOS生态的AI优势，又确保了跨平台的基础体验。通过移除timeNLP等冗余依赖，简化了技术栈，降低了维护成本。安装包优化策略确保了快速安装和按需扩展，为用户提供了灵活的AI体验选择。

---
*文档版本：v3.0 四层架构版  |  更新日期：2025-06-10* 