
# 公职猫移动端接收外部文件分享功能设计文档

- **最后更新**: {{CURRENT_DATE}}
- **负责人**: AI助手 (芝审官)
- **状态**: **方案已更新**

## 1. 需求背景与用户场景 (现象分析)

### 1.1. 用户场景

政务人员在日常工作中，经常需要在不同的应用间流转文件。例如：

- 从“微信”、“钉钉”等即时通讯工具中，接收同事发来的工作文档（.docx, .pdf, .xlsx）。
- 从邮件客户端中，保存重要的通知公告附件。
- 从浏览器中，将下载的政策文件或参考资料直接存入公职猫进行归档和后续处理。

当前，用户需要先将文件保存到本地，再手动打开公职猫App进行导入，操作路径繁琐，效率低下。

### 1.2. 核心问题

应用缺乏一个直接、高效的通道来接收来自其他App分享的数据，这与公职猫作为“秘书般懂你”的智能助手的定位不符，影响了产品的流畅体验和数据聚合能力。

### 1.3. 功能目标

实现一个标准的系统级分享接收功能，允许用户在任何支持分享的应用中，直接将文件、文本或链接“分享”到公职猫App，并由App进行后续的解析和处理。

## 2. 技术方案与架构设计 (逻辑链回溯)

### 2.1. 核心技术选型

**更新**: 经POC验证，原定方案中使用的 `react-native-receive-sharing-intent` 库与项目现有其他依赖存在兼容性冲突。为确保项目的长期稳定性和可控性，我们决定采用**自定义原生模块 + TypeScript桥接**的方案。

- **核心方案**: 通过编写自定义原生模块（Native Modules）并用TypeScript进行桥接，实现对系统分享功能的深度集成。
  - **选型理由**:
    - **解决兼容性**: 从根本上解决第三方库与项目现有依赖的兼容性问题。
    - **极致的可控性**: 提供最大的灵活性和可控性，确保功能与App的深度融合，便于未来扩展。
    - **轻量化**: 避免引入新的第三方依赖，减小应用体积和潜在的维护成本。
    - **性能更优**: 数据在原生层直接处理，只将必要信息通过桥传递，性能更高。

### 2.2. 平台特定实现方案

#### 2.2.1. Android平台

1.  **机制**: 沿用Android系统的`Intent`和`Intent Filter`机制。
2.  **配置文件**: 在 `android/app/src/main/AndroidManifest.xml` 中，为 `MainActivity` 添加处理分享的 `<intent-filter>`。
3.  **自定义原生模块**:
    - 创建一个Java/Kotlin类（例如 `SharingModule`），继承自 `ReactContextBaseJavaModule`。
    - 在 `MainActivity` 的 `onCreate` 和 `onNewIntent` 方法中捕获分享的 `Intent`。
    - 对`Intent`中的数据（文件URI、文本等）进行解析，并通过 `DeviceEventManagerModule` 向JS层发送一个事件（例如 `onShareReceived`）。
    - `SharingModule` 提供一个方法（例如 `getInitialShare`），供JS层在冷启动时调用，以获取启动App时传入的分享数据。
4.  **声明支持的数据类型 (MIME Type)**:
    - `text/*`: 接收文本和链接。
    - `image/*`, `video/*`, `application/pdf`, 等具体文件类型。
    - `*/*`: 提供最大的文件兼容性。
5.  **数据流**: 第三方App分享 -> Android系统路由`Intent`到公职猫 -> `MainActivity`接收`Intent`并通知自定义的`SharingModule` -> `SharingModule`解析数据并通过事件发送给JS层。

#### 2.2.2. iOS平台

1.  **机制**: 沿用iOS的 **Share Extension** (分享扩展) 和 **App Groups** (应用组)。
2.  **Share Extension**:
    - 在Xcode中为项目创建一个新的 "Share Extension" Target。它将在系统分享面板中显示公职猫图标。
3.  **App Groups**:
    - 用于在主应用和分享扩展之间安全地共享数据。分享的文件将被保存在App Group的共享容器中。
4.  **自定义原生模块**:
    - 创建一个Swift/Objective-C的`NativeModule`（例如 `SharingModule`）。
    - **Share Extension逻辑**: 接收到分享数据后，将其保存到App Group共享目录，然后通过自定义URL Scheme (`gongzhimall://share`) 唤醒主应用。
    - **主应用逻辑**: 在 `AppDelegate` 中处理URL Scheme的唤醒事件。
    - `SharingModule` 提供一个事件发射器，当被`AppDelegate`通知有新的分享时，它会从App Group读取数据，并通过事件将数据发送到JS层。同时，也提供一个`getInitialShare`方法处理冷启动。
5.  **数据流**: 第三方App分享 -> 用户选择公职猫 -> Share Extension被激活，将文件存入App Group -> 扩展通过URL Scheme唤醒主应用 -> `AppDelegate`通知`SharingModule` -> `SharingModule`从App Group读取数据并发送事件给JS层。

### 2.3. JavaScript层逻辑

1.  **统一监听**: 在App的根组件（如 `App.tsx`）中，引入自定义的 `SharingModule` 和 `NativeEventEmitter`。
2.  **事件处理**:
    - 使用 `NativeEventEmitter` 监听原生模块发送的 `onShareReceived` 事件，以处理App运行时接收到的分享（热启动/前台场景）。
    - App启动时，调用 `SharingModule.getInitialShare()` 方法，检查是否有待处理的分享数据（冷启动场景）。
3.  **数据解析**:
    - 从事件或方法回调中获取分享的文件数组，每个文件对象包含由我们原生模块定义的`filePath`, `text`, `mimeType`等信息。
4.  **路由与响应**:
    - (与原方案一致) 根据`mimeType`判断文件类型，弹出统一的“文件接收”处理模态框。
    - 模态框中显示文件名、大小等信息，并提供“保存到收件箱”等操作选项。
    - 将文件从临时目录（共享目录）拷贝到应用内部的安全存储位置。

## 3. 风险与B计划 (最后一环人格)

- **风险点1**: **原生开发复杂度**。编写和维护自定义原生模块需要对Android和iOS平台有深入了解，增加了开发和维护成本。
  - **应对**: 编写详细的开发文档，确保关键原生代码有清晰的注释。优先封装平台特定逻辑在原生层，保持JS层API的简洁和一致性。
- **风险点2**: **React Native桥接性能**。分享非常大的文件可能对RN桥造成性能压力。
  - **应对**: 在原生层处理文件IO。只通过桥传递必要的文件元数据（如文件路径、MIME类型、大小），而不是文件内容本身。JS层根据路径直接操作文件。
- **风险点3**: **系统版本升级维护**。React Native、Android、iOS系统版本升级时，可能需要同步更新原生模块代码以保证兼容性。
  - **应对**: 在项目依赖升级流程中，加入对自定义原生模块的兼容性测试环节。
- **B计划**: 如果自定义原生模块开发周期过长，备选方案是寻找一个更轻量、与项目兼容性更好的第三方库（如 `react-native-share-menu`），或者对原定的 `react-native-receive-sharing-intent` 库进行Fork和修复以解决其兼容性问题。

## 4. 验收标准 (任务完成自查)

- ✅ **功能层面**:
  - 可以在微信中成功分享文本、链接、图片、PDF和Word文档到公职猫App。
  - 可以在系统文件管理器中成功分享文件到公职猫App。
  - App能正确识别分享的内容类型，并弹出对应的处理界面。
  - 文件被成功保存到App的内部存储中。
- ✅ **体验层面**:
  - 分享过程流畅，无明显卡顿。
  - 接收界面清晰友好，操作指引明确。
  - 对不支持的文件类型或分享失败有明确的错误提示。
- ✅ **代码层面**:
  - 原生配置（AndroidManifest.xml, Info.plist等）正确无误。
  - JS层的事件处理逻辑健壮，覆盖冷热启动场景。
  - **自定义的原生模块和JS桥接代码清晰、健壮，并有必要的注释。**
  - 代码遵循项目现有规范。 