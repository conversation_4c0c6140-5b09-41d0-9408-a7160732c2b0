---
description: 本文档详细阐述了公职猫App中，如何将来自企业微信客服的消息安全、可靠、高效地转发至移动端和桌面端，并实现用户身份的自动绑定和消息的跨端同步。
---
# 企业微信消息统一转发与跨端同步技术方案

## 1. 方案目标

本方案旨在解决以下核心问题：

- **用户身份自动绑定**：用户首次从公职猫App进入企业微信客服时，系统需自动完成App用户与微信用户的身份关联，无需用户手动操作。
- **消息安全转发**：确保用户在企业微信客服中发送的所有消息（文本、图片、文件等），都能安全、无遗漏地转发至App后端。
- **跨端实时同步**：后端收到的消息需能实时推送至用户所有已登录的设备（移动端、桌面端），并保持多端消息状态一致。
- **高效增量同步**：支持客户端在冷启动或断线重连后，高效拉取离线消息，避免全量拉取。
- **用户数据隐私保护**：严格遵守用户文件内容不在云端做任何持久化存储的原则。

## 2. 核心架构

本方案采用“企业微信自建应用 + **腾讯云轻量应用服务器** + JPush推送”的核心架构。

- **企业微信自建应用**：作为权限和能力的载体，获取客服账号的API权限，并接收Webhook事件回调。
- **腾讯云轻量应用服务器**：作为常驻后台服务，通过公网IP提供Webhook接收点，处理来自企业微信的数据回调，并负责文件的下载、临时缓存和安全中转。
- **JPush推送服务**：负责将新消息通知实时从后端推送至各客户端。
- **云端数据库 (MySQL分支)**：仅存储消息的索引信息（如 `message_id`、`user_id`、`message_type`、`timestamp`）和用户绑定关系。对于文件类消息，会**临时记录**其加密存储路径和过期时间，**绝不长期存储消息原文或文件内容本身**。
- **App客户端**：负责生成唯一用户ID，处理用户绑定流程，与后端的双向消息同步，并从**我方服务器**安全下载文件。

## 3. 用户身份自动绑定方案

### 3.1. 绑定流程概述

此方案为用户提供了无感、安全的“一键绑定”体验，用户全程无需手动复制粘贴任何信息。其核心是利用企业微信客服链接的 `scene`和 `scene_param`两个参数，实现场景识别与用户身份的安全传递。

1. **用户ID生成**：用户首次启动App时，客户端会生成一个全局唯一的用户ID（`user_uuid`），并进行本地持久化。
2. **请求绑定链接**：当用户在App内点击“绑定微信”时，客户端会向后端API发起请求，为该 `user_uuid`生成一个专属的企业微信客服链接。
3. **生成专属链接 (后端)**：

   * **步骤一：获取基础链接**：后端首先调用企业微信的 `kf/add_contact_way`接口，并传递一个固定的场景值 `scene`（例如"binding"）。企业微信返回一个不包含任何自定义参数的基础客服链接。
   * **步骤二：生成加密参数**：后端将用户的 `user_uuid`和一个24小时的过期时间戳打包成JSON，然后使用AES-256-CBC算法进行加密，生成一个安全的、加密的字符串。
   * **步骤三：构建最终链接**：后端将上一步生成的加密字符串作为 `scene_param`参数，拼接到第一步获取的基础链接后，形成最终的、完整的绑定链接（形如 `https://work.weixin.qq.com/kfid/xxxx?scene_param=xxxxxx`），并返回给客户端。
4. **用户跳转**：App获取到该链接后，引导用户点击。用户点击后，系统会自动跳转至企业微信并打开对应的客服聊天界面。
5. **服务端自动验证**：

   * 在用户进入客服会话的瞬间，企业微信服务器通过 `enter_session`事件的Webhook，将**`Scene`**和** `SceneParam`**两个字段同时发送到我方后端服务器。
   * 我方后端通过 `Scene`字段识别出这是绑定场景，然后提取 `SceneParam`字段的值（即加密的用户信息）。
   * 后端调用 `processKfBindingToken`函数，对该加密信息进行解密和验证，包括检查令牌时效性、`user_uuid`的合法性，并处理重绑或更新逻辑。
6. **完成绑定与反馈**：

   * 验证通过后，系统在数据库中正式建立 `user_uuid`与微信用户 `external_userid`的绑定关系。
   * 绑定成功后，客服会自动发送一条欢迎消息，告知用户“已成功绑定，可以开始转发消息”。
   * 同时，后端通过JPush等推送服务，向用户所有已登录的App设备（移动端、桌面端）发送绑定成功的通知。

### 3.2. 技术细节与优势

- **唯一性保障**：`user_uuid`确保了每个App用户的唯一性，从根本上解决了并发绑定可能引发的身份混淆问题。
- **安全性**：绑定令牌经过加密，即使在传输过程中被截获也无法被破解，有效防止了中间人攻击。
- **实现简单**：相对于复杂的二维码生成、扫码识别等方案，此方案流程清晰，实现成本更低。

## 4. 日常使用：消息的跨端同步机制

绑定成功后，系统即进入日常使用模式。

### 4.1. 用户操作与消息触发

在日常使用中，用户的主要操作是在微信中将需要处理的聊天记录（无论是与好友的单聊，还是群聊内容）转发至已绑定的“公职猫”企业微信客服账号。这是所有后续消息同步和处理流程的起点。

一旦用户完成转发操作，企业微信后端会立即通过Webhook将这些消息逐条推送至我们的轻量服务器，从而触发整个跨端同步机制。

为实现高效、可靠的消息同步，我们采用基于**“最新已同步消息ID”**的增量同步机制。

### 4.2. 表结构设计

在云端用户设备表中，为每个已登录的设备记录一个 `last_synced_id`字段。

- **`user_device_bindings` 表 (示例)**
  - `id` (PK)
  - `user_uuid` (FK)
  - `device_id` (设备唯一标识)
  - `platform` ('ios', 'android', 'desktop')
  - **`last_synced_id`** (BIGINT, 记录该设备已同步的最新消息ID)
  - `...` (其他设备信息)

### 4.3. 同步流程

1. **实时推送**：当轻量服务器接收到新消息并处理后，会通过JPush向该用户所有在线设备实时推送新消息。
2. **增量拉取**：当客户端冷启动、网络重连或收到拉取指令时，会向服务端发起同步请求，并携带本地存储的 `last_synced_id`。
   - **Request**: `GET /api/sync/messages?since_id={last_synced_id}&limit=100`
3. **服务端处理**：服务端根据 `since_id`查询SQL，返回所有大于该ID的新消息（元数据），并按 `message_id`升序排列。
4. **客户端更新**：客户端接收到消息列表后，更新本地数据库，并将返回的最新消息ID更新为本地的 `last_synced_id`。

### 4.4. 批量消息处理

当用户在微信中集中发送多条消息（如一次性转发多条聊天记录）时：

- 企业微信Webhook会以多条回调的形式推送到轻量服务器。
- 轻量服务器会将这些消息按顺序、批量写入数据库。
- 客户端的增量拉取机制（`limit`参数）天然支持分批次拉取大量消息，避免单次请求数据量过大。

### 4.5. 非文本消息（文件、图片等）的处理流程

为严格遵守**“U盘级私密”**的核心原则，系统采用**“服务器代理下载 + 临时加密缓存”**的方案。

1. **服务器实时下载**：

   - 后端服务器在从Webhook接收到包含 `MediaId`（有效期3天）的非文本消息时，会**立即在后台启动下载任务**。
   - 服务器使用 `access_token`和 `MediaId`直接调用企业微信API，将文件下载并以**加密形式临时存储在服务器本地**。
   - 同时，后端会在数据库中记录该文件的加密存储路径及过期时间（3天后）。
2. **推送新消息通知**：

   - 文件成功下载并加密存储后，后端服务会立即通过JPush向用户所有在线设备推送一条**“新消息通知”**。
   - 此通知中包含消息的元数据，以及一个用于从我方服务器下载该文件的**一次性、有时效性的下载令牌**。
3. **客户端安全下载**：

   - App客户端（无论在线或离线后上线）通过增量同步机制获取到新消息的元数据和下载令牌。
   - 客户端使用该令牌调用我方服务器提供的下载接口，进行文件下载。
   - 服务器验证令牌有效后，从本地加密存储中读取文件，解密后通过安全的流式传输方式发送给客户端。
4. **阅后即焚，自动清理**：

   - **下载即焚**：一旦用户成功下载文件，服务器会**立即删除**该文件的临时缓存。
   - **过期自毁**：如果用户在文件到达后的**3天内未下载**，服务器的定时清理任务也会**自动将该过期文件删除**。
   - 此“阅后即焚”机制确保了用户文件在服务端的停留时间最短，最大化地保障了用户的数据隐私安全。

此方案在解决了大文件处理问题的同时，通过服务器中转和临时缓存策略，延续了“U-Disk-level privacy”的设计精髓。

## 5. 总结

本方案通过整合企业微信自建应用、**常驻后台服务**、增量同步等机制，构建了一套安全、可靠且高效的消息转发与同步系统。其核心优势在于：

- **绑定流程清晰可靠**：解决了自动绑定的核心难题。
- **数据隐私绝对保障**：通过服务器临时加密缓存和“阅后即焚”机制，严格保障用户数据安全。
- **同步机制高效**：通过增量同步和指令推送，兼顾实时性与离线场景，节省资源。
- **架构稳定可靠**：基于轻量应用服务器，无冷启动、执行时长等限制，能更好地处理大批量、大文件任务。
