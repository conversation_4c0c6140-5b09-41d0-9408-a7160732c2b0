# 公职猫 - 数据隐私与便携性设计方案

## 1. 核心设计原则

本方案旨在将"U盘般私密"的核心产品理念转化为具体、可执行的技术设计。所有关于用户数据的处理都必须严格遵守以下原则：

- **数据主权归用户所有**: 用户的个人数据（日程、笔记、文档、个人资料等）是其私有资产。本产品是管理这些资产的工具，而非数据的所有者。
- **本地优先，云端辅助**: 核心功能必须在完全离线的情况下可用。云端服务仅作为可选的、用户明确授权的增强功能。
- **透明可控**: 用户必须能够清晰地理解其数据是如何存储的，并能以一种简单、通用的方式导出和迁移所有数据。

## 2. 本地存储与加密方案

### 2.1 数据库选择

- **统一数据库**: 所有结构化和半结构化数据，包括元数据、任务、日程、笔记内容以及语义向量，都统一存储在用户本地的单个SQLite数据库文件中。
- **选择理由**:
  - **零依赖**: SQLite是文件型数据库，无需额外服务或网络连接，完美契合离线优先原则。
  - **高性能**: 对本地单用户应用来说，性能绰绰有余。
  - **成熟生态**: 拥有丰富的扩展，如用于加密和向量搜索的库。

### 2.2 静态数据加密 (Encryption at Rest)

- **加密标准**: 整个SQLite数据库文件必须使用 `better-sqlite3-multiple-ciphers` 库进行AES-256级别加密。这确保了即使用户的设备失窃，数据库文件本身也无法被直接读取。
- **加密时机**: 数据库在创建时即被加密，所有后续读写操作都在加密状态下进行。

### 2.3 密钥管理

- **安全存储**: 加密数据库的密钥本身绝不能以明文形式存储在文件系统或代码中。
- **系统级保护**: 必须使用Electron的 `safeStorage` API进行存储。该API利用操作系统的安全模块（如Windows的DPAPI，macOS的Keychain）来保护密钥，将其与当前用户和设备绑定。

## 3. 本地语义搜索引擎架构

为了实现"无需用户打标签，即存即搜"的智能体验，我们在MVP阶段就构建本地语义搜索能力。

### 3.1 技术组件

1. **加密SQLite数据库**: 作为数据的最终载体。
2. **向量搜索扩展**: 集成 `sqlite-vss` 或类似库，为SQLite提供存储和搜索向量（Vector）的能力。
3. **本地嵌入模型 (Embedding Model)**: 集成一个轻量级的、可在设备上直接运行的AI模型，负责将文本转换为语义向量。

### 3.2 工作流程

1. **自动向量化**: 当用户创建或导入任何文本内容（如笔记、日程、文档OCR结果）时，系统会在后台自动调用本地嵌入模型，将该文本内容转换成一个数学向量。
2. **关联存储**: 原始文本和生成的语义向量一同存储在加密的SQLite数据库的相应表中。
3. **语义查询**: 当用户输入搜索查询时，查询文本同样被转换为一个查询向量。系统会在数据库中执行向量相似度搜索，找出与查询向量在"语义"上最接近的记录。
4. **用户体验**: 这个过程对用户完全透明。用户感受到的是一个能"理解"他们意图的智能搜索引擎，而非简单的关键词匹配。

## 4. 数据便携性 (U盘级导出/导入)

这是"U盘般私密"理念的用户体验核心。我们必须提供一种既对用户友好，又在技术上完备的数据迁移方案。

### 4.1 双受众导出设计

导出的数据包需要同时服务两类"受众"：**用户本人**和**公职猫软件**。

- **格式**: 导出的产物是一个标准的 `.zip` 压缩包。
- **命名**: 建议命名为 `公职猫数据备份_[YYYYMMDD_HHMMSS].zip`。

### 4.2 导出包内容结构

`.zip` 包解压后，包含以下两部分：

**Part 1: 人类可读内容 (Human-Readable)**

这是为了让用户能直观地查看和管理自己的数据，建立信任感。

```
/公职猫数据备份/
├── 日程/
│   ├── 2025-06-16_关于XX会议的安排.docx
│   └── ...
├── 笔记/
│   ├── 一个重要的想法.docx
│   └── ...
├── 我的文档/
│   ├── [原始文件名].pdf
│   └── [原始文件名].docx
└── 个人资料/
    ├── 身份证/
    │   ├── 正面.jpg
    │   └── 背面.jpg
    └── ...
```

- **格式**: 用户的笔记、日程等内容将被转换为用户最熟悉的 `.docx` (Word) 格式。
- **原始文件**: 用户导入的原始文件（PDF、图片等）将被完整地保留其原始格式。
- **目的**: 用户可以完全脱离公职猫软件，依然拥有和使用自己的核心数据。

**Part 2: 机器可读内容 (Machine-Readable)**

这是为了让公职猫软件能够完美地恢复所有数据和它们之间的关联。

```
/公职猫数据备份/
└── .gongzhimall/
    ├── manifest.json
    └── assets/
        └── [资源文件哈希]
```

- **`manifest.json`**: 这是一个核心的索引文件，以JSON格式存储了所有数据的元数据、关系、标签、语义向量等。它描述了所有人类可读文件之间的关联和应用的内部状态。
- **`assets/`**: 存放一些无法直接用Word表示的二进制资源。
- **目的**: 在另一台设备上进行"导入"操作时，软件通过读取 `manifest.json`来精确地重建用户的整个工作空间。
- **对用户隐藏**: `.gongzhimall` 文件夹在主流操作系统中默认是隐藏的，普通用户不会感知到它的存在，避免造成困扰。

### 4.3 导入流程

1. 用户在新的设备上选择"从备份导入"。
2. 选择 `.zip` 数据包。
3. 软件解压数据包，读取 `.gongzhimall/manifest.json`。
4. 根据 `manifest.json`中的描述，将所有数据（包括Word文档内容、原始文件和各种元数据）重新写入新设备本地的加密SQLite数据库中，重建应用状态。

## 5. 总结

本方案通过**加密SQLite**、**本地语义搜索**和**双受众导出**三大支柱，完整地实现了"U盘般私密，秘书般懂你"的核心承诺。它确保了用户数据的绝对安全与主权，提供了超越关键词搜索的智能体验，并以一种极为友好的方式赋予了用户自由迁移数据的能力。
