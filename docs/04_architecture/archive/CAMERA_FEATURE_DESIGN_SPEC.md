# 公职猫相机功能核心设计规范 (v1.0)

## 1. 核心理念

始终围绕 **"U盘级私密，秘书般懂你"** 的核心理念。相机功能不是一个独立的工具，而是AI总助能力的延伸。它必须是 **智能的、高效的、场景化的**，并且在交互上达到 **"微信级"** 的简单直观。

## 2. 核心用户场景分析

我们的相机必须能 **自动、静默地** 识别并适应以下核心场景，无需用户手动切换。

| 场景       | 关键挑战           | 智能优化策略                               | 顶部状态栏提示图标 |
| :--------- | :----------------- | :----------------------------------------- | :----------------- |
| **会议模式** | 投影/PPT，对比度高 | 自动曝光补偿、锐化文字、抑制摩尔纹、为梯形校正做准备 | 🖥️                 |
| **文档模式** | 书本/纸质文件，光照不均 | 自动光线补偿、增强对比度、锐化边缘           | 📄                 |
| **屏幕模式** | 拍摄电脑/Pad，摩尔纹   | 调整相机参数（如快门），最大限度避免条纹和摩尔纹 | 💻                 |
| **通用模式** | 其他日常场景       | 平衡的默认参数，保证通用性和可靠性         | (无图标)           |

**注意**：实时文本识别(`Live Text`)不在取景框中对用户可见，仅作为后续OCR处理的基础能力。

## 3. 核心界面布局：三个一级动作

界面采用极简设计，只保留三个核心一级动作，无需额外的模式切换或控件：

### 布局结构
```
┌─────────────────────────────────┐
│                          [📷x3] │ ← 右上角：连续拍照缩略图+计数
│                                 │
│                                 │
│                                 │
│                                 │
│                                 │
│                                 │
│                                 │
│                                 │
│                                 │
│                                 │
│ [📁]               [⚪]         │ ← 底部：选照片(左) + 拍照/录像按钮(中)
└─────────────────────────────────┘
```

### 三个一级动作详解

1.  **选照片** (左下角 `📁`)：
    -   点击进入相册选择界面
    -   支持多选照片导入进行OCR处理
    -   图标样式：半透明白色方形图标

2.  **拍照/录像** (底部居中 `⚪`)：
    -   **点击**：拍照，照片飞入右上角缩略图
    -   **长按**：录像，按钮变为红色方块，上方显示计时器
    -   无需模式切换文字或额外控件

3.  **连续拍照缩略图** (右上角)：
    -   显示本次拍摄的照片数量 (如 `📷x3`)
    -   点击进入 **批量预览界面 (`CaptureReview.tsx`)**
    -   支持批量OCR处理、保存等操作

## 4. 核心交互工作流：连续快拍与批量处理

这是为了满足用户需要连续拍摄大量文件/PPT的核心需求，**严禁采用"拍一张预览一张"的模式**。

1.  **连续拍摄**：
    -   用户可 **连续、快速点击快门**。
    -   每次点击后，照片以流畅动画飞入右上角缩略图。
    -   缩略图上通过 **数字角标** 实时显示已拍摄数量。
    -   整个过程 **不中断** 用户取景和拍摄。

2.  **批量预览与处理**：
    -   用户完成拍摄后，点击右上角的缩略图进入 **批量预览界面 (`CaptureReview.tsx`)**。
    -   界面以 **图片廊** 形式呈现本次所有照片，可左右滑动浏览。
    -   支持 **单张上滑删除** 和 **一键全选/取消**。
    -   提供批量操作入口：**[OCR识别]**、**[存为多页笔记]**、**[保存到相册]**。

## 5. 关键UI/UX细节规范

| 功能点 | 规范 | 平台兼容性 |
| :--- | :--- | :--- |
| **界面简洁性** | **移除所有不必要的控件**：无闪光灯按钮（自适应场景）、无拍照/录像模式切换文字、无多余设置项。 | iOS & Android |
| **快门声音** | **必须默认关闭**，尤其是在会议模式下，避免尴尬。 | iOS & Android |
| **快门按钮** | - **拍照**：外圈为白色，具有较高的透明度（如`rgba(255,255,255,0.3)`），营造高级感。<br>- **录像**：长按触发录像时，**按钮形态发生显著变化，给予用户强烈的安全感**：1. **外圈消失，内圆变为一个实心红色方块**。2. 按钮所有动画效果（如呼吸动画）必须停止，提供一个稳定、清晰的视觉锚点。 | iOS & Android |
| **选照片按钮** | 位于左下角，半透明白色方形图标（`📁`），点击进入系统相册多选界面。 | iOS & Android |
| **连续拍照缩略图** | 位于右上角，显示格式为`📷x数量`，点击进入批量预览界面。当无照片时隐藏。 | iOS & Android |
| **录像计时** | **录像开始时，必须在快门按钮正上方出现一个红色的、格式为`MM:SS`的计时器**。这是给予用户"录制成功"安全感的核心元素，**不可或缺**。 | iOS & Android |
| **技术实现** | `react-native-vision-camera`的`Frame Processor`方案具备良好的跨平台兼容性。 | iOS & Android |

## 6. 任务拆分建议 (P0 / P1)

### P0 (立即执行 - 交互骨架)
- [ ] **任务53.25.1**: 重构界面布局，实现三个一级动作的精确定位（选照片左下角、拍照按钮居中、缩略图右上角）。
- [ ] **任务53.25.2**: 重构快门交互，实现连续快拍和"飞入右上角缩略图+角标计数"的动画效果。
- [ ] **任务53.25.3**: 创建`CaptureReview.tsx`组件，实现批量图片廊的预览、滑动、删除和选择功能。
- [ ] **任务53.25.4**: 实现选照片功能，集成系统相册多选界面。
- [ ] **任务53.25.5**: 修复快门按钮样式，使其外圈透明度符合"高级感"要求，并添加录像时的红色样式。
- [ ] **任务53.25.6**: 移除所有不必要的UI控件（闪光灯、模式切换文字等），确保界面极简。
- [ ] **任务53.25.7**: 确保任何情况下拍照都默认静音。

### P1 (近期优化 - 智能核心)
- [ ] **任务53.25.8**: 集成`Frame Processor`，开发基于亮度、对比度、边缘检测的轻量级场景分析引擎。
- [ ] **任务53.25.9**: 实现场景自动识别与相机参数的静默调整。
- [ ] **任务53.25.10**: 在顶部状态栏实现场景提示微图标的显示。 