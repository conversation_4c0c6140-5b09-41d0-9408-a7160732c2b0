# 公职猫 - 隐私保护与数据匿名化技术方案

## 1. 隐私保护架构概述

公职猫采用"本地为主，匿名上报"的隐私保护架构，确保用户个人隐私数据100%本地存储，云端仅收集经过严格匿名化处理的统计数据用于产品优化。

### 1.1 隐私保护原则
- **数据最小化**：仅收集产品功能必需的数据
- **本地优先**：个人隐私数据优先本地处理和存储
- **匿名化处理**：云端数据经过不可逆匿名化处理
- **用户控制**：用户完全控制数据收集和上报策略
- **透明可审计**：所有数据处理过程公开透明

## 2. 数据分类与处理策略

### 2.1 个人隐私数据（100%本地存储）
**数据类型**：
- 用户个人信息（姓名、身份证、联系方式等）
- 工作内容和文档（会议纪要、任务安排、文件内容等）
- 个人偏好设置（工作习惯、界面配置等）
- 设备本地文件索引和路径信息

**技术实现**：
- **本地存储**：SQLite数据库 + better-sqlite3-multiple-ciphers AES-256加密
- **密钥管理**：Electron safeStorage API（桌面端）/ iOS Keychain / Android Keystore（移动端）
- **加密算法**：支持AES-256-CBC/GCM，可配置加密强度
- **数据隔离**：个人数据与匿名数据完全分离
- **离线可用**：核心功能完全离线运行

### 2.2 云端AI处理数据（可选功能）
**数据类型**：
- 去标识化的文档内容（移除个人身份信息、具体人名地名）
- 通用任务描述（不包含敏感工作内容）
- 匿名化的文本片段（用于AI理解和处理）

**处理原则**：
- 用户可完全关闭云端AI功能
- 仅在本地AI置信度低时调用
- 严格的数据脱敏和去标识化处理
- 不存储任何处理过的数据

### 2.3 匿名统计数据（可选云端收集）
**数据类型**：
- 设备信息（操作系统类型、版本、设备型号等）
- 地理位置（省市区县级别，不包含具体地址）
- 功能使用统计（功能点击次数、使用时长等）
- 性能指标（应用启动时间、响应时间等）
- 错误日志（不包含用户数据的技术错误信息）

**匿名化技术**：
- **差分隐私**：添加数学噪声保护个体隐私
- **数据聚合**：本地预聚合后再上报
- **时间延迟**：随机延迟上报时间，防止时序关联
- **标识符移除**：完全移除设备唯一标识符

## 3. 匿名化技术方案

### 3.1 差分隐私技术
**技术原理**：通过向原始数据添加数学噪声，确保单个用户的数据无法被识别
**实施方案**：
- 采用拉普拉斯机制为统计数据添加噪声
- 设置合理的隐私预算参数（epsilon值）
- 确保噪声添加后数据仍具有统计意义

### 3.2 本地数据聚合
**技术原理**：在本地设备上预先聚合数据，只上报统计结果而非原始数据
**实施方案**：
- 按日/周/月聚合使用统计数据
- 功能使用次数本地汇总后上报
- 性能指标本地计算平均值和分布
- 移除具体使用时间和行为序列

### 3.3 数据脱敏处理
**地理位置脱敏**：
- 精确坐标模糊到区县级别
- 移除具体地址和POI信息
- 保留省市区县行政区划用于地域分析

**设备信息脱敏**：
- 保留操作系统类型和大版本号
- 设备型号泛化处理（如iPhone → iOS设备）
- 完全移除设备唯一标识符和序列号

### 3.4 延迟上报机制
**技术原理**：通过随机延迟上报时间，防止基于时序的用户行为关联
**实施方案**：
- 设置30分钟到24小时的随机延迟窗口
- 批量合并多个时间点的数据
- 打乱上报顺序，模糊时序信息

## 4. 法规合规实现

### 4.1 中国《个人信息保护法》合规
**合规要求**：
- **知情同意**：明确告知数据收集目的和范围
- **最小必要**：仅收集必要的匿名统计数据
- **安全保障**：采用加密等安全措施保护数据
- **用户权利**：支持查看、删除、导出个人数据

**技术实现**：
- **同意管理系统**：记录用户对不同数据类型的同意状态
- **同意界面**：清晰展示数据收集目的和范围
- **撤回机制**：用户可随时撤回同意并删除已收集数据
- **状态持久化**：本地安全存储用户同意状态



## 5. 用户控制机制

### 5.1 数据收集控制
**控制面板功能**：
- **分类控制**：用户可分别控制分析数据、性能数据、错误报告的收集
- **一键关闭**：提供完全关闭所有数据收集的选项
- **数据查看**：用户可查看已收集的匿名统计数据
- **保留期设置**：用户可设置数据保留时间（默认30天）

### 5.2 透明度报告
**报告内容**：
- **数据类型**：明确列出收集的数据类型和用途
- **匿名化方法**：详细说明使用的隐私保护技术
- **用户权利**：清晰展示用户享有的数据权利
- **安全措施**：公开数据保护的技术措施

## 6. 安全技术实现

### 6.1 本地数据加密
**加密方案**：
- **加密库**：better-sqlite3-multiple-ciphers（支持多种SQLCipher兼容算法）
- **算法选择**：采用AES-256-CBC/GCM加密算法（可配置）
- **密钥管理**：Electron safeStorage API（桌面端）自动生成和管理256位密钥
- **数据保护**：数据库级别加密，所有敏感个人数据透明加密存储
- **密钥安全**：密钥由系统安全存储保护，不可导出，支持密钥轮换
- **性能优化**：加密开销<5%，支持并发访问和FTS5全文搜索

### 6.2 安全传输
**传输安全**：
- **协议选择**：使用HTTPS协议加密传输
- **数据完整性**：添加校验和确保数据完整性
- **端点安全**：API端点采用安全认证机制
- **传输最小化**：仅传输必要的匿名统计数据

## 7. 审计与监控

### 7.1 隐私审计日志
**审计机制**：
- **操作记录**：记录所有隐私相关操作的时间戳和详情
- **同意状态跟踪**：记录用户同意状态的变更历史
- **数据操作日志**：记录数据收集、导出、删除等操作
- **审计报告**：定期生成隐私操作审计报告

### 7.2 合规性检查
**自动检查机制**：
- **同意状态检查**：验证是否获得用户有效同意
- **数据最小化检查**：确保仅收集必要数据
- **加密状态检查**：验证数据加密是否正常工作
- **保留期检查**：确保数据保留符合设定策略
- **用户权利检查**：验证用户权利功能是否可用

## 8. 风险评估与应对

### 8.1 隐私风险
**风险**：匿名数据被重新识别
**应对**：多层匿名化 + 差分隐私 + 数据聚合

**风险**：本地数据泄露
**应对**：设备级加密 + 访问控制 + 安全存储

### 8.2 合规风险
**风险**：法规变更导致不合规
**应对**：持续监控法规变化 + 灵活的技术架构

**风险**：数据传输限制
**应对**：数据本地化 + 可选的云端服务

## 9. 总结

公职猫的隐私保护技术方案通过"本地为主，匿名上报"的架构设计，结合差分隐私、数据聚合、延迟上报等先进技术，确保用户个人隐私数据的绝对安全，同时满足产品优化的数据需求。该方案严格遵循中国《个人信息保护法》，为用户提供完全透明和可控的隐私保护体验。 