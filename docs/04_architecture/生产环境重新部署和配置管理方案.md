# 公职猫生产环境重新部署和配置管理方案

## 📋 当前状况分析

### 现有问题
1. **敏感信息泄露**：配置文件包含真实生产环境密钥
2. **部署文件冗余**：deploy文件夹包含多次部署产生的历史文件
3. **生产环境混乱**：需要彻底清理现有部署和数据库
4. **环境变量管理不规范**：缺乏统一的环境变量注入机制

### 目标
1. **安全第一**：彻底解决敏感信息泄露问题
2. **环境清理**：重新部署干净的生产环境
3. **规范化管理**：建立标准的配置和部署流程
4. **自动化部署**：优化部署脚本和流程

## 🚀 解决方案

### 阶段1：腾讯云环境变量注入方案设计

#### 1.1 推荐方案：systemd环境文件 + PM2
```bash
# 在腾讯云服务器上创建安全的环境变量文件
sudo mkdir -p /etc/gongzhimall
sudo touch /etc/gongzhimall/production.env
sudo chmod 600 /etc/gongzhimall/production.env
sudo chown root:root /etc/gongzhimall/production.env

# 编辑环境变量文件
sudo nano /etc/gongzhimall/production.env
```

#### 1.2 生产环境变量文件内容
```bash
# /etc/gongzhimall/production.env
NODE_ENV=production

# 腾讯云配置
TENCENT_SECRET_ID=AKID0tmEXfhYCdOxLVSCl7FAuR1Ulofs7D8q
TENCENT_SECRET_KEY=Zt2zwgLuNeiYCest78wYxBMK8jBQUMUe

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=wechat_user
MYSQL_PASSWORD=wechat@gongzhimall123
MYSQL_DATABASE=gongzhimall_wechat

# 企业微信配置
WECHAT_CORP_ID=ww857dc7bfe97b085b
WECHAT_CORP_SECRET=eQs0lmS8uUgpyDM74XvHHwWuAddq1n_qhdNsBZoB13I
WECHAT_AGENT_ID=1000002
WECHAT_TOKEN=gongzhimallEnterprise2025
WECHAT_ENCODING_AES_KEY=zlb6q9HgvrGOYlgMR9HNmMNIHo9dJgrQVmbs7G2DJcv

# 极光推送配置
JPUSH_APP_KEY=bd2c958b83dc679759a25664
JPUSH_MASTER_SECRET=59a9af913b1d2dd85f4cbc24

# 应用密钥
TOKEN_SECRET=gongzhimall-app-2025
FILE_ENCRYPTION_KEY=gongzhimall-file-encryption-2025
WECHAT_BINDING_SECRET=gongzhimall_binding_secret_2025

# 服务器配置
PORT=3000
SERVER_DOMAIN=wechat.api.gongzhimall.com
FILE_STORAGE_PATH=/var/www/cache
```

#### 1.3 PM2生态系统配置
```javascript
// backend/wechat/ecosystem.config.js (更新后)
module.exports = {
  apps: [{
    name: 'gongzhimall-wechat',
    script: './index.js',
    cwd: '/var/www/gongzhimall/backend/wechat',
    instances: 1,
    exec_mode: 'fork',
    
    // 从安全文件加载环境变量
    env_file: '/etc/gongzhimall/production.env',
    
    // 日志配置
    log_file: '/var/log/gongzhimall/combined.log',
    out_file: '/var/log/gongzhimall/out.log',
    error_file: '/var/log/gongzhimall/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // 进程管理
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s',
    
    // 健康检查
    health_check_url: 'http://localhost:3000/health',
    health_check_grace_period: 3000,
  }]
};
```

### 阶段2：部署文件整理和优化

#### 2.1 保留的核心部署文件
```
deploy/
├── README.md                    # 保留：文档说明
├── one-click-deploy.sh         # 保留：主要部署脚本
├── production-deploy.sh        # 保留：生产环境专用
├── deploy-config.json          # 保留：部署配置
├── setup-env.sh               # 更新：环境设置脚本
└── monitor.js                 # 保留：监控脚本
```

#### 2.2 需要清理的文件
```
deploy/
├── auto-deploy.sh             # 删除：功能重复
├── auto-rollback.sh           # 删除：暂不需要
├── deploy.js                  # 删除：过于复杂
├── deploy-to-server.sh        # 删除：功能重复
├── env-vault.js               # 删除：复杂度过高
├── manage-env.sh              # 删除：功能重复
├── test-deployment.sh         # 删除：测试用
└── env-check/                 # 删除：检查逻辑过于复杂
```

#### 2.3 优化后的一键部署脚本
```bash
#!/bin/bash
# backend/wechat/deploy/one-click-deploy.sh (优化版)

set -e

echo "🚀 公职猫微信转发服务 - 生产环境部署"
echo "=================================="

# 检查运行环境
if [ "$USER" != "root" ]; then
    echo "❌ 请使用root用户执行部署脚本"
    exit 1
fi

# 检查环境变量文件
if [ ! -f "/etc/gongzhimall/production.env" ]; then
    echo "❌ 环境变量文件不存在: /etc/gongzhimall/production.env"
    echo "请先运行: bash setup-env.sh"
    exit 1
fi

# 设置变量
PROJECT_DIR="/var/www/gongzhimall"
SERVICE_DIR="$PROJECT_DIR/backend/wechat"
BACKUP_DIR="/var/backups/gongzhimall/$(date +%Y%m%d_%H%M%S)"

echo "📁 项目目录: $PROJECT_DIR"
echo "🔧 服务目录: $SERVICE_DIR"
echo "💾 备份目录: $BACKUP_DIR"

# 创建备份
echo "📦 创建备份..."
mkdir -p "$BACKUP_DIR"
if [ -d "$PROJECT_DIR" ]; then
    cp -r "$PROJECT_DIR" "$BACKUP_DIR/"
fi

# 停止现有服务
echo "⏹️  停止现有服务..."
pm2 stop gongzhimall-wechat || true
pm2 delete gongzhimall-wechat || true

# 清理旧部署
echo "🧹 清理旧部署..."
rm -rf "$PROJECT_DIR"

# 克隆最新代码
echo "📥 克隆最新代码..."
git clone https://github.com/your-repo/gongzhimall.git "$PROJECT_DIR"
cd "$SERVICE_DIR"

# 安装依赖
echo "📦 安装依赖..."
npm install --production

# 启动服务
echo "🚀 启动服务..."
pm2 start ecosystem.config.js --env production

# 健康检查
echo "🔍 健康检查..."
sleep 10
if curl -f http://localhost:3000/health; then
    echo "✅ 部署成功！服务正常运行"
    pm2 save
else
    echo "❌ 健康检查失败，正在回滚..."
    pm2 stop gongzhimall-wechat
    if [ -d "$BACKUP_DIR/gongzhimall" ]; then
        cp -r "$BACKUP_DIR/gongzhimall" "$PROJECT_DIR"
        cd "$SERVICE_DIR"
        pm2 start ecosystem.config.js --env production
    fi
    exit 1
fi

echo "🎉 部署完成！"
```

### 阶段3：生产环境清理方案

#### 3.1 数据库清理脚本
```bash
#!/bin/bash
# scripts/clean-production-database.sh

echo "⚠️  警告：此操作将清理生产环境数据库"
echo "请确认您要继续 (输入 'YES' 确认):"
read confirmation

if [ "$confirmation" != "YES" ]; then
    echo "操作已取消"
    exit 1
fi

# 备份现有数据库
echo "📦 备份现有数据库..."
mysqldump -u wechat_user -p gongzhimall_wechat > "/var/backups/gongzhimall_wechat_$(date +%Y%m%d_%H%M%S).sql"

# 清理数据库
echo "🧹 清理数据库..."
mysql -u wechat_user -p << EOF
DROP DATABASE IF EXISTS gongzhimall_wechat;
CREATE DATABASE gongzhimall_wechat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EOF

echo "✅ 数据库清理完成"
```

#### 3.2 文件系统清理
```bash
#!/bin/bash
# scripts/clean-production-files.sh

echo "🧹 清理生产环境文件..."

# 清理缓存文件
rm -rf /var/www/cache/*

# 清理日志文件
rm -rf /var/log/gongzhimall/*

# 清理临时文件
rm -rf /tmp/gongzhimall*

echo "✅ 文件系统清理完成"
```

### 阶段4：代码脱敏执行计划

#### 4.1 脱敏检查清单
- [ ] backend/wechat/config/env.template
- [ ] mobile/src/config/appConfig.ts
- [ ] mobile/src/config/wechatBindingConfig.ts
- [ ] mobile/android/app/agconnect-services.json
- [ ] 所有包含硬编码敏感信息的文件

#### 4.2 脱敏执行步骤
1. **备份当前配置**
2. **设置生产环境变量**
3. **执行代码脱敏**
4. **更新.gitignore**
5. **测试验证**
6. **提交脱敏代码**

## 📝 执行时间表

### 第1天：环境准备
- [ ] 在腾讯云服务器设置环境变量文件
- [ ] 清理deploy文件夹
- [ ] 优化部署脚本

### 第2天：生产环境清理
- [ ] 备份现有数据和配置
- [ ] 清理生产环境数据库和文件
- [ ] 验证清理结果

### 第3天：代码脱敏和部署
- [ ] 执行代码脱敏
- [ ] 测试脱敏后的配置加载
- [ ] 重新部署到生产环境
- [ ] 功能验证

## ⚠️ 风险控制

1. **完整备份**：所有操作前进行完整备份
2. **分步验证**：每个步骤后验证功能正常
3. **回滚准备**：准备快速回滚方案
4. **监控告警**：部署后持续监控服务状态

## 🔧 后续优化

1. **CI/CD集成**：集成到自动化部署流水线
2. **配置加密**：考虑使用HashiCorp Vault等工具
3. **多环境支持**：支持开发、测试、生产环境
4. **监控完善**：完善服务监控和告警机制
