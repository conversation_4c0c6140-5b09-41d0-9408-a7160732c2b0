# 公职猫多端本地消息同步技术方案

> **【重要通知】**
>
> **根据最新项目规划，多端同步功能已调整为 MVP（最小可行产品）上线后的远期规划。**
>
> **当前阶段将暂停所有与跨设备同步相关的设计与实现工作，团队将集中资源，优先完成移动端的单机核心功能。本文档目前仅作为远期技术储备参考。**

## 一、现有本地数据库架构

### 1. 移动端（React Native）
- **数据库方案**：WatermelonDB（底层为SQLite，支持高性能本地存储）
- **已建表结构**：
  - `capture_sessions`：采集会话（含状态、时间等）
  - `media`：多媒体文件（图片、视频等，含元数据、关联会话ID）
  - `media_processing_queue`：媒体处理任务队列
- **特点**：
  - 支持复杂对象、关系型数据
  - 可扩展消息/对话表
  - 具备本地加密能力（可二次开发）

### 2. 桌面端（Electron）
- **数据库方案**：better-sqlite3-multiple-ciphers（支持多种加密算法的SQLite）
- **密钥管理**：自研keyManager，支持安全存储/轮换/验证
- **表结构**：目前仅有测试表（test_encryption），可灵活扩展
- **特点**：
  - 数据100%本地存储
  - 强加密，密钥本地管理
  - 支持自定义表结构

---

## 二、消息/对话数据结构建议

### 1. 消息表（建议结构，移动端/桌面端统一）
| 字段名         | 类型         | 说明                   |
| -------------- | ------------ | ---------------------- |
| id             | string/uuid  | 消息唯一ID             |
| session_id     | string       | **后台上下文ID（对用户透明）** |
| sender         | string       | 发送方（user/assistant）|
| type           | string       | 消息类型（text/image/audio/card/url/等）|
| content        | string/json  | 消息内容/富文本/卡片   |
| media_uri      | string       | 图片/音频等本地路径     |
| created_at     | number       | 时间戳                 |
| status         | string       | 状态（sent/received/failed）|
| ext            | string/json  | 扩展字段（如推荐操作）  |

> **补充说明：**
> - ChatScreen 页面只展示"用户（user）"与"App本人（assistant）"之间的所有消息，所有消息按 created_at 时间倒序合并展示。
> - 不包括 App 内部 AI 代理之间、或未来与外部服务/代理之间的消息。
> - 业务筛选逻辑：仅当 sender 为 'user' 或 'assistant'，且仅这两者互为通信双方的消息才会出现在主聊天界面。

### 2. 合并/同步关键字段
- `id`：全局唯一，防止重复
- `created_at`：时间戳，合并时排序
- `sender`：区分用户/AI/设备
- `status`：同步/冲突解决辅助

### 3. 上下文驱动的后台逻辑模型
- **核心思想**：用户只与一个单一的、按时间排序的聊天界面（`ChatScreen`）交互。不存在任何显性的"会话切换"操作。
- **后台上下文管理**：`session_id` 作为一个对用户不可见的内部标识，用于将相关联的消息（如由同一任务、同一张图片引发的连续对话）在逻辑上归类。
- **上下文继承**：当用户与某条历史消息或某个业务对象（如任务笔记）进行交互时，该对象的上下文ID将被传递给后台的AI决策系统。
- **AI决策辅助**：AI决策系统利用传入的上下文ID，可以查询相关的历史消息，从而生成更准确、更具连贯性的回复。
- **统一的时间线**：所有新生成的消息，无论其后台上下文ID是什么，都会被追加到单一聊天视图的最末端，确保用户体验的连续性和简洁性。
- **业务边界说明**：主聊天界面仅聚合展示"用户-公职猫App"之间的所有消息，任何系统内部、代理间或外部服务的消息均不会出现在此界面。

---

## 三、多端同步机制设计

### 1. 本地存储
- 移动端：WatermelonDB（可直接扩展消息表）
- 桌面端：better-sqlite3-multiple-ciphers（新建消息表，结构与移动端一致）

### 2. 同步方式
- **局域网直连同步**（推荐）：
  - 手机与电脑在同一WiFi下，自动发现对方（如react-native-zeroconf、Bonjour、mDNS）
  - 建立加密WebSocket/HTTP连接，交换未同步消息
  - 按`id`和`created_at`合并，去重
- **扫码/文件导入导出**（备选）：
  - 支持导出加密消息包，另一端扫码/导入合并
- **冲突解决**：
  - 以`created_at`为主，`id`去重
  - 可扩展CRDT算法，支持更复杂场景

### 3. 数据安全
- 本地数据库加密（WatermelonDB/SQLite加密扩展、桌面端已实现多重加密）
- 传输数据端到端加密（如AES，密钥可通过扫码/手动输入协商）

### 4. 典型同步流程

1. 用户在任一端（手机/电脑）创建消息，写入本地数据库
2. 检测到同一WiFi下另一端在线，自动建立加密连接
3. 双方交换未同步消息（按`id`、`created_at`）
4. 合并后写入本地数据库，UI自动刷新
5. 离线时可用导出/扫码/文件同步，保证数据不丢失

---

## 四、后续开发建议

- **实现单一时间线UI**：基于已统一的消息模型，使用`FlatList`和`TextInput`等基础组件自建聊天界面（`ChatScreen.tsx`），确保体验细节对标主流IM应用。
- **实现后台上下文逻辑**：开发后台业务逻辑，使AI在响应时能接收并利用上下文参数，但前端UI始终保持单一视图。
- **数据库是基础**：所有交互最终都应持久化到本地的WatermelonDB中。
- **远期规划**：多端同步、更复杂的上下文关联等功能，可在MVP版本稳定后，基于当前架构再行扩展。

---

如需具体表结构DDL、同步协议伪代码或关键实现细节，可随时补充！ 