# 公职猫 - AI总助项目主产品需求文档 (Master PRD)

## 1. 项目愿景与战略目标 (来自BP)
- **项目愿景**: 打造全国首款专为中国大陆地区体制内用户设计的个人AI助理，成为"U盘级私密，秘书般懂你"的数字伙伴。
- **目标市场**: 面向约1000万潜在用户（公务员、事业单位、国企中青年干部），这是一个巨大的市场机会。
- **核心战略**: 从高信任、高刚需的体制内个人用户切入，以"任务链闭环"和"绝对数据安全"为核心建立长期护城河。
- **商业模式**: 初期采用"基础免费+增值订阅"的ToC模式，远期扩展至数据服务和组织服务。
- **MVP目标**: 1个月内实现核心功能，交付一个能够验证"AI真的懂体制内工作"且"绝对安全"的最小可行产品，并获取首批种子用户的高度认可。

## 2. MVP 核心功能 (来自PRD)

### 2.1 基础平台与架构 (P0)
- **平台支持**:
  - **移动端**: 基于React Native，支持iOS & Android。
  - **桌面端**: 基于Electron，支持Windows & 统信UOS。
- **核心架构**:
  - **数据100%本地化**: 所有用户敏感数据使用加密的SQLite数据库存储在本地设备。
  - **离线优先**: 核心功能必须支持完全离线使用。
  - **混合AI架构**:
    - **云端AI**: 在线时，优先调用云端大模型API，以获得最佳理解效果。
    - **本地AI**: 离线或网络不佳时，回退至本地轻量级AI模型。
    - **规则引擎**: 作为最终兜底方案，确保最基础的关键词和模式匹配功能在任何情况下都可用。

### 2.2 智能信息采集 (P0)
- **多源输入**: 支持通过文字、语音、微信截图、拍照（纸质文件）四种方式录入信息。
- **核心识别能力**:
  - 优化对微信聊天截图（尤其是会议通知）的OCR识别。
  - 理解并解析来自领导的口头、模糊语音指令。
  - 识别纸质文件上的手写批注和圈改内容。

### 2.3 核心数据管理 (P0)
- **文档中心 (本地U盘)**:
  - 支持Word, Excel, PDF, 图片等常见格式文档的导入、分类和本地全文检索。
  - 能够智能解析Word/Excel格式的领导日程文件（年度/月度/周度计划）。
- **个人档案库**:
  - 安全存储和管理个人重要资料，如身份证、证件照、简历、各类证书等。
  - 支持敏感信息加密和快速调用。

### 2.4 差异化任务管理 (核心功能) (P0)
- **角色智能识别**:
  - 基于自然语言、上下文和历史行为，智能判断用户在具体事务中的角色（主办、协办、参与）。
- **差异化管理**:
  - **主办事务**: 提供详细的步骤拆解、关键节点提醒、风险预警等深度管理。
  - **协办事务**: 聚焦于明确配合职责、提供材料清单和提醒截止时间。
  - **参与事务**: 提供简单的议程提醒和基本准备清单。
- **日程与事务**:
  - 提供日历视图，统一管理日程和事务。
  - 具备关键节点和截止日期的智能提醒功能。

## 3. 技术栈选型 (来自PRD)
- **移动端**: React Native
- **桌面端**: Electron + React
- **本地数据库**: SQLite (需加密)
- **AI模型**:
  - **云端**: 备选阿里云百炼或腾讯云混元
  - **本地**: ONNX Runtime + 量化后的模型 (如Qwen)
- **文档处理**: 使用成熟的开源库进行解析。

## 4. MVP验收标准 (来自PRD)
- **功能性**: 核心功能全部可用，关键信息识别（日程文件、截图）准确率 > 60%。
- **性能**: 应用启动 < 3秒，AI响应（云/本地）< 5/10秒。
- **安全性**: 数据100%本地存储，符合隐私合规要求。
- **用户认可**: 获得5-10名种子用户"有用且安全"的正面反馈 (评分 > 4.0/5.0)。
- **留存**: 种子用户的7日留存率 > 50%。

---
*此文档是项目WBS分解的核心输入，综合了BP的战略方向和PRD的具体需求。* 